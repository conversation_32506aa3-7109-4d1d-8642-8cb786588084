<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TDR0006_填充颜色"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">

    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>
    <div class="content-main">
      <!-- 雪碧图播放 -->
      <audio src="./audio//right.mp3" class="animationAudio" data-syncaudio="audio2" id="animationAudio"></audio>
      <!-- 生成图片后的音频 -->
      <audio src="./audio/photo.mp3" id="audioPhoto" class="audioPhoto" data-syncaudio="audioPhoto"></audio>
      <!-- 点击涂色后的音效 -->
      <audio src="./audio/complete.mp3" class="audioComplete" id="audioComplete" data-syncaudio="audioComplete"></audio>
      <!-- 画笔音效 -->
      <audio class="back" src="./audio/back.mp3" data-syncaudio="audioBack"></audio>
      <!-- 胜利的音频 -->
      <audio src="./audio/photo.mp3" class="photowin" data-syncaudio="audioPhotoWin" loop="loop"></audio>
      <!-- 画笔 -->
      <div class="brush">
        <ul></ul>
      </div>
      <!-- 填色图片列表 -->
      <div class="picList">
        <div class="listBg"></div>
        <ul></ul>
      </div>
      <!-- 虚拟图片 -->
      <ul class="listImg"></ul>
      <!-- 涂色画笔 -->
      <!-- <img class="picBtnImg" src="" alt=""> -->
      <!-- 小手 -->
      <!-- <div class="hand"></div> -->
      <!-- 像素格 -->
      <div class="boxList">
        <ul class="boxUl"></ul>
      </div>
    </div>
    <!-- 老师提示面板 -->
    <div class="right-top-hint"><img src="./image/hint.png" alt=""> Hint</div>
    <div class="doneTip">
      <p>Show a hint to help S</p>
      <span class="btn hint-btn" data-syncactions="hintBtn">Hint for S</span>
    </div>
    <!-- done合成照片 -->
    <div class="noTip" id="noTip">
      <span class="btn done-btn" data-syncactions="doneBtn">
        <img src="./image/done.png" alt="">
      </span>
      <img src="./image/not_done.png" alt="" class="not-done">
    </div>

    <!-- 拍照 -->
    <div class="resultMask resultHide">
      <div class="contentMask">
      </div>
      <!-- 胜利彩带 -->
      <div class="resultWin resultHide">
        <div class="resultAPos_1 resultAPos"></div>
        <div class="resultAPos_2 resultAPos"></div>
        <div class="resultAPos_3 resultAPos"></div>
        <div class="resultAPos_4 resultAPos"></div>
        <div class="resultAPos_5 resultAPos"></div>
        <div class="resultAPos_6 resultAPos"></div>
        <div class="resultAPos_7 resultAPos"></div>
        <div class="resultAPos_8 resultAPos"></div>
      </div>
      <!-- 胜利图标 -->
      <div class='winResult resultHide'>
        <img src='./image/result_perfect.png' class='perfect'>
      </div>
    </div>
    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
