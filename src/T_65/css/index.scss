@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/ribbonWin/style.scss';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}

.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 2.2rem;
	position: absolute;
    right: 0px;
	.desc {
    top: 1.3rem;
    color: #75341E;
	}
	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
}
.container {
	background-size: auto 100%;
	position: relative;
  .content-main{
    .brush{
      // position: relative;
      // margin: initial;
      width: 3.11rem;
      height: 10.80rem;
      background-size: 100% 100%;
      ul{
        position: absolute;
        left: 0;
        top: 50%;
        transform: translate(0,-50%);
        li{
          height: 1.49rem;
          margin-left: -4rem;
          cursor: pointer;
          width: 6.14rem;
          height: 1.49rem;
          overflow: hidden;
          img{
            width: 100%;
          }
        }
        .on{
          margin-left: -3.2rem;
        }
      }
    }
    // 填色图片
    .picList{
      ul>li{
        position: absolute;
        background-size: 100%;
        // 涂色画笔
        .picBtn{
          position: absolute;
          top:50%;
          left:50%;
          transform:translate(-50%,-50%);
          width: 3.77rem;
          height: 3.15rem;
          display: none;
          animation: picStyle 0.5s both ease-in;
          img{
            width: 100%;
            transform: rotate(130deg);
            margin: 0 0 0 50px;
          }
        }
      }
    }

    // 像素格
    .boxList {
      position: absolute;
      left: 1.08rem;
      top: .3rem;
      width: 17rem;
      height: 9.8rem;
      z-index: -1;
      .boxUl {
        position: absolute;
        left: .2rem;
        top: .7rem;
        width: 16rem;
        height: 8.4rem;
        display: flex;
        flex-wrap: wrap;
        li {
          // background: red;
          margin-left: .01rem;
          width: .39rem;
          height: .39rem;
        }
      }
    }
    //小手点击动画
    .hand{
      position: absolute;
      left: 1rem;
      top: .2rem;
      margin-left: -0.35rem;
      background: url('../image/hands.png');
      background-size: 7.2rem 1.8rem;
      animation: handClick 1s steps(4) infinite;
      cursor: pointer;
      opacity: 1;
      z-index: 99;
      width: 1.8rem;
      height: 1.8rem;
      display: none;
    }
  }
  // 老师提示面板
  .right-top-hint {
		display: none;
		position: absolute;
		right: 0;
		top: 0;
		width:1.95rem;
		height:0.67rem;
		background:rgba(70,70,70,1);
		opacity:0.63;
		border-bottom-left-radius: 0.22rem;
		color: #fff;
		line-height:0.67rem;
		font-size: 0.43rem;
		img {
			width: 0.51rem;
			height: 0.53rem;
			margin-left: 0.22rem;
		}
  }
  .doneTip{
    width: 7.91rem;
    height: 1rem;
    border-radius: 0.2rem .2rem 0 0;
    position: absolute;
    margin-left: -3.5rem;
    left:50%;
    bottom: 0;
    background: rgba(255,255,255,.7);
    display: none;
    font-size: .3rem;
    p{
      height: 100%;
      width: 5.14rem;
      line-height: 1rem;
      text-align: center;
      padding-left: 0.53rem
    }
    .btn {
      position: absolute;
      top: .2rem;
      height: .23rem;
      padding: 0.17rem 0.26rem;
      color:#fff;
      text-align: center;

      line-height: 0.23rem;
      border-radius: .3rem;
      cursor: pointer;
    }
    .hint-btn{
      background: #f1a91e;
      left: 5.6rem;
    }
  }
  .noTip{
    position: absolute;
    right: 1rem;
    bottom: 1rem;
    img{
      width: 2.11rem;
      height: .6rem;
    }
    .done-btn{
      cursor: pointer;
      display: none;
    }
  }


  // 拍照
  .resultMask{
    .contentMask{
      position: absolute;
      left: 3.5rem;
      top: 1.2rem;
      width: 11.2rem;
      height: 6.3rem;
      background: #fff;
      border: 1rem solid #fff;
      // transform: translate(-50%,-50%);
      // overflow: hidden;
      animation: ptoStyle .5s  1 forwards;
      -webkit-animation: ptoStyle .5s  1 forwards;
      // box-shadow: 0 0 .4rem rgba(255, 255, 255, 0.8);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      ul>li{
        position: absolute;
      }
    }
  }

  .resultMask {
    position: absolute;
    .winBook {
      overflow: hidden;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 13.32rem;
      height: 8.42rem;
      transform: translate(-50%, -50%);
      background: url('../image/result_win.png');
      background-size: 100% 100%;
      .perfect{
        position: absolute;
        left: 0.45rem;
        bottom: 0.33rem;
        z-index: 101;
        width: 4.12rem;
        height: 2.72rem;
      }
      .result_real{
        position: absolute;
        top: 50%;
        left: 50%;
        width: 8.32rem;
        height: 6.59rem;
        transform: translate(-50%, -50%);
      }
    }
  }
  .winResult{
    position: absolute;
    width: 4.12rem;
    height: 2.72rem;
    bottom: .7rem;
    left: .5rem;
    img{
      width: 100%;
    }
  }
}
.listBg{
  position: absolute;
  top: 0;
  right: 0;
  width: 16.09rem;
  height: 10.8rem;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.shakeLeft{
  animation: zoomLarge .1s both ease-in;
  li{
    cursor: pointer;
  }
}
.listImg{
  display: none;
}
.shake{
	animation: shakeUp 0.4s both ease-in;
}

.scaleL{
  animation: optionUD 1s  infinite alternate;
	-webkit-animation: optionUD 1s  infinite alternate;
}
@keyframes zoomLarge {
  0% {
      transform:scale(1)
  }

  50% {
      transform:scale(1.1)
  }

  100% {
      transform:scale(1)
  }
}

@keyframes shakeUp{
	0% {
        transform: translateX(10px);
    }
    20% {
        transform: translateX(-10px);
    }
    40% {
        transform: translateX(10px);
    }
    60% {
        transform: translateX(-10px);
    }
    80% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0px);
    }
}

@keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}
@-webkit-keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}

@keyframes ptoStyle{
	0%{
		transform: scale(1.2,1.2) rotate(0deg);
		// transform-origin:left top;
	}
	100%{
		transform: scale(1.1,1.1) rotate(-3deg);
		// transform-origin:left top;
	}
}

@-webkit-keyframes ptoStyle{
	0%{
		transform: scale(1.2,1.2) rotate(0deg);
		// transform-origin:left top;
	}
	100%{
		transform: scale(1.1,1.1) rotate(-3deg);
		// transform-origin:left top;
	}
}

@keyframes picStyle{
  0%{
    transform: translate(-50%,-50%);
  }
  20%{
    transform: translate(-60%,-50%);
  }
  40%{
    transform: translate(-50%,-50%);
  }
  60%{
    transform: translate(-40%,-50%);
  }
  60%{
    transform: translate(-30%,-50%);
  }
  100%{
    transform: translate(-50%,-50%);
  }
}

@keyframes optionUD {
	  0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(8px);
    }
}
@-webkit-keyframes optionUD {
	0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(5px);
    }
}
