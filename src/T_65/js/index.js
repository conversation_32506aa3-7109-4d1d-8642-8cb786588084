"use strict"
import '../../common/js/common_1v1.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: '1' // 是否有授权按钮 1：是 0：否
  }

  // 左右背景图片预加载
  if(configData.leftImg == ''){
    $(".brush").css({
      'background-image':'url(./image/left_bg.png)'
    })
  } else {
    $('.brush').css('background-image','url('+ configData.leftImg +')')
  }
  if(configData.rightImg == ''){
    $(".listBg").css({
      'background-image':'url(./image/right_bg.png)'
    })
  } else {
    $('.listBg').css('background-image','url('+ configData.rightImg +')')
  }
  let options = configData.source.options, //图片列表
      optionLength = configData.source.optionLength, //选项上限
      audio = configData.source.audio, //题干音频
      colorList = configData.source.colorList,//颜色选值
      clickAble = true,
      // childPosArr = [
      //   {
      //     indeximg:0,
      //     index: 1,
      //     imgType:1,
      //   },
      //   {
      //     indeximg:1,
      //     index: 2,
      //     imgType:1,
      //   },
      //   {
      //     indeximg:2,
      //     index: 3,
      //     imgType:1,
      //   }
      // ], //存储所有被选中的元素
      childPosArr = [], //存储所有被选中的元素
      userType = window.frameElement && window.frameElement.getAttribute('user_type'), //用户身份学生还是老师
      isReconType = 0,
      isType = '0', //画笔类型
      isIndex= 0, //雪碧图显示画笔帧数
      isImgIndex = 0, //雪碧图的index
      imgIndex = 0,
      ListLength = '0',
      time = 1000,
      ListIndex = 0,
      showImg = false,
      workIsEnd = 1,
      scaleTime = null;
  //添加音频
  $('.example-audio audio').attr('src', audio);
  //生成像素表格
  renderPx();
  function renderPx() {
    let liHtml = '';
    for (let i = 1; i < 801; i++) {
      liHtml += `
				<li class="pos_${i}"></li>	`
    }
    $('.boxUl').html(liHtml);
  }

   //判断是学生还是老师显示底部按钮和mask
   isShowBtn();
   function isShowBtn() {
     if (isSync) {
       if (userType == 'tea') {
         $(".doneTip").show();
       } else {
         $(".doneTip").hide();
       }
     } else {
       var hrefParam = parseURL("http://www.example.com");
       if (top.frames[0] && top.frames[0].frameElement) {
         hrefParam = parseURL(top.frames[0].frameElement.src)
       }
       var role_num = hrefParam.params['role']

       function parseURL(url) {
         var a = document.createElement('a')
         a.href = url
         return {
           source: url,
           protocol: a.protocol.replace(':', ''),
           host: a.hostname,
           port: a.port,
           query: a.search,
           params: (function () {
             var ret = {},
               seg = a.search.replace(/^\?/, '').split('&'),
               len = seg.length,
               i = 0,
               s
             for (; i < len; i++) {
               if (!seg[i]) {
                 continue;
               }
               s = seg[i].split('=')
               ret[s[0]] = s[1]
             }
             return ret
           })(),
           file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
           hash: a.hash.replace('#', ''),
           path: a.pathname.replace(/^([^\/])/, '/$1'),
           relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
           segments: a.pathname.replace(/^\//, '').split('/')
         }
       }
       if (role_num == '1' || role_num == undefined) {
         $(".doneTip").show();
       } else if (role_num == '2') {
         $(".doneTip").hide();
       }
     }
   }



  // 初始化画笔
  let images = [
    {
      "img":"./image/img01.png"
    },
    {
      "img":"./image/img02.png"
    },
    {
      "img":"./image/img03.png"
    },
    {
      "img":"./image/img04.png"
    },
    {
      "img":"./image/img05.png"
    },
    {
      "img":"./image/img06.png"
    },
    {
      "img":"./image/img07.png"
    },
    {
      "img":"./image/img08.png"
    },
    {
      "img":"./image/img09.png"
    },
    {
      "img":"./image/img10.png"
    },
    {
      "img":"./image/img11.png"
    },
    {
      "img":"./image/img12.png"
    }
  ]
  brushFn()
  function brushFn(){
    let str = '';
    for (let i = 0; i < colorList.length; i++) {
      let strText = '';
      if(i == 0) {
        strText = `<li data-syncactions="bursh-${i}" data-type="${colorList[i].name}" data-key="${i}"><div class="hand"></div>`
      } else {
        strText = `<li data-syncactions="bursh-${i}"  data-type="${colorList[i].name}" data-key="${i}">`
      }
      str += `${strText}<img src="${images[colorList[i].name-1].img}"></li>`
    }
    $(".brush ul").html(str)
  }
  picListFn()
  // 初始化填色图片列表
  function picListFn(){
    let str = '';
    for (let i = 0; i < options.length; i++) {
      let currentPosition = options[i].position,
      left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base ,
      top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base;
      let item = options[i],
          imgWidth = item.natWidth/colorList.length/100,
          imgHeight = item.natHeight/100;
      str += `<li class="optionLi"  data-index="${i}" data-syncactions="drawing-${i}" data-width="${imgWidth}"
      data-height="${imgHeight}"
      data-top="${top}"
      data-left="${left}"
      data-audio="${item.audio}"
      style="left:${left}rem; top:${top}rem;
      background:url('${options[i].img}');
      width:${imgWidth}rem;
      height:${imgHeight}rem;
      background-size:${(colorList.length+1)*100}% 100%;
      background-position:-${(colorList.length+1)*100}% 100%;"><span class="picBtn"><img src=""></span></li>`
    }
    $(".picList ul").html(str);
    //点击图像
    picDrawFn()
    ListLength = options.length;
  }

  // 画笔点击
  $(".brush ul li").on('click touchstart', function (e) {
    let dataKey = $(this).attr("data-key");
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if(clickAble) {
        clickAble = false
        if($(this).hasClass("on")) {
          workIsEnd = 0
        } else {
          workIsEnd = 1
          isReconType = $(this).attr("data-type");
        }
        if (!isSync) {
          $(this).trigger('syncBrushClick');
          return
        }
          SDK.bindSyncEvt({
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            // funcType: 'audio',
            syncName: 'syncBrushClick',
            otherInfor: {
              type:isReconType,
              Index:dataKey,
              childPosArr: childPosArr,
              workIsEnd:workIsEnd
            },
            recoveryMode: '1'
          });
      }
  })
  $(".brush ul li").on('syncBrushClick', function (e,message) {
    //断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor;
      let childArr = obj.childPosArr;
      let type = obj.type;
      let Index = obj.Index;
      isType = type;
      isIndex = Index;
      childPosArr = childArr;
      workIsEnd = obj.workIsEnd
      if(!childArr.length) {
        if(obj.workIsEnd != 0) {
          $(".brush li").eq(Number(Index)).addClass("on");
          $(".picList ul li").eq(0).addClass("scaleL");
          scaleTime = setInterval(function(){
            ListIndex++;
            $(".picList ul li").eq(ListIndex).addClass("scaleL");
            if(ListIndex == $(".picList ul li").length){
              clearInterval(scaleTime);
            }
          },600)
        }
      } else {
        workIsEnd = obj.workIsEnd
          setDrawFn(type,Index,childArr)
      }
      clickAble = true
      SDK.setEventLock();
      return
    }
    let $audio = $('.back')[0];
    // 重置index个数
    clearInterval(scaleTime);
    ListIndex = 0;
    $audio.currentTime = 0;
    if($(this).hasClass("on")) {
      SDK.pauseRudio({
        index: $audio,
        syncName: $('.back').attr("data-syncaudio")
      })
      $(this).removeClass("on");
      isReconType = 0;
      isType = 0;
      $(".picList ul li").removeClass("scaleL");
      $(".picList li").find(".picBtn img").attr('src','');
    } else {
      SDK.playRudio({
        index: $audio,
        syncName: $('.back').attr("data-syncaudio")
      })
      // 未涂色需添加动画
      if(!showImg){
        $(".picList ul li").eq(0).addClass("scaleL");
          scaleTime = setInterval(function(){
            ListIndex++;
            $(".picList ul li").eq(ListIndex).addClass("scaleL");
            if(ListIndex == $(".picList ul li").length){
              clearInterval(scaleTime);
            }
          },600)
      }
      isType = $(this).attr("data-type");
      isIndex = $(this).attr("data-key");
      $(this).addClass("on").siblings().removeClass("on");
      $(".picList li").find(".picBtn img").attr('src', images[isType-1].img)
    }
    clickAble = true
    SDK.setEventLock();
  })
  //点击图像
  function picDrawFn(){
    // 时间触发
    $(".optionLi").on('click touchstart', function (e) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if(isType != 0 && clickAble) {
        clickAble = false
        if (!isSync) {
          $(this).trigger('syncDrawClick');
          return
        }
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'syncDrawClick',
          otherInfor: {
            type:isType,
            Index:isIndex,
            childPosArr: childPosArr
          },
          recoveryMode: '1'
        });
      }
    })
    $(".optionLi").on('syncDrawClick',function (e,message) {
      clearInterval(scaleTime);
      //去掉雪碧图抖动
      $(".picList ul li").removeClass("scaleL");
      //断线重连
      if (isSync && message && message.operate == 5) {
        let obj = message.data[0].value.syncAction.otherInfor;
        let childArr = obj.childPosArr;
        let type = obj.type;
        let Index = obj.Index;
        isType = type;
        isIndex = Index;
        childPosArr = childArr;
        if (childArr) {
          setDrawFn(type,Index,childArr);
        }
        clickAble = true
        SDK.setEventLock();
        return;
      }

      let $audioIndex = $('.audioComplete')[0];
      $audioIndex.currentTime = 0;
        let $this = $(this);
        let positon = -((Number(isIndex)+1)*100)+'%';
        // let positon = -(isIndex*100)+'%';
        if(isType != 0) {
          $this.find('.picBtn').show();
          let imgInterval = setInterval(function(){
            showImg = true;
            // 拍照可点击状态
            $(".not-done").hide();
            $(".done-btn").show();
            $this.css({
              backgroundPosition: positon + ' 100%'
            })
            if(showImg){
              audioCompleteFn()
              $this.find('.picBtn').hide();
              clearInterval(imgInterval);
            }
            $this.addClass("isCheck");
            clickAble = true
          },500)

          // 记录画笔和图片的位置
          let $index = isIndex;
          let arrIndex = '';
          isImgIndex = $this.attr("data-index");
          if($this.hasClass("isCheck")){ //已经填色
            for(var i = 0; i<childPosArr.length; i++) {
              let item = childPosArr[i]
              if(item.indeximg == isImgIndex) { //画笔颜色
                arrIndex = i;
              }
            }
            childPosArr[Number(arrIndex)].index = $index
            // clickAble = true
          } else {
            childPosArr.push(
            {
              indeximg:isImgIndex,
              index: $index,
              imgType:isType
            })
          }
        }
        SDK.setEventLock();

    })
  }
  // setDrawFn(1)
  // 雪碧图选择过程中掉线恢复
  function setDrawFn(type,index,childArr) {
    clearInterval(scaleTime);
    $(".picList ul li").removeClass("scaleL");
    for(var i = 0; i<childArr.length; i++) {
      let indexImg = Number(childArr[i].indeximg);
      let indexT = Number(childArr[i].index);
      let positon = -((Number(indexT)+1)*100)+'%';
      // let positon = -((index)*100)+'%';
      $(".picList ul li").eq(indexImg).css({
        backgroundPosition: positon + ' 100%'
      })
      $(".picList ul li").eq(indexImg).addClass("isCheck");
    }
    showImg = true;
    // 拍照可点击状态
    $(".not-done").hide();
    $(".done-btn").show();
    isIndex = index;
    isType = type
    if(workIsEnd != 0) {
      $(".brush li").eq(isIndex).addClass("on");
      $(".picList li").find(".picBtn img").attr('src', images[type-1].img);
    }
    if(workIsEnd == 0){
      isType = 0;
    }
  }

  // 图片加载完成后视频播放
  function audioCompleteFn() {
    if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
      SDK.playRudio({
        index: $('.audioComplete')[0],
        syncName: $('.audioComplete').attr("data-syncaudio")
      })
    }
  }

  // 提交照片
  var btnClickTimer = true;
  $('.done-btn').on("click touchstart", function (e) {
    if(clickAble){
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if (btnClickTimer) {
        btnClickTimer = false;
        if (!isSync) {
          $(this).trigger('syncBtnClick');
          return;
        }
        // if (window.frameElement.getAttribute('user_type') == 'stu') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(this).data('syncactions'),
            eventType: 'click',
            method: 'event',
            syncName: 'syncBtnClick',
            otherInfor: {
              type:isType,
              Index:isIndex,
              childPosArr: childPosArr,
              workIsEnd:workIsEnd
            },
            recoveryMode: '1'
          });
        // }
      }
    }
  });

  $('.done-btn').on('syncBtnClick', function (e, message) {
    //断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor;
      let childArr = obj.childPosArr;
      let type = obj.type;
      let Index = obj.Index;
      isType = type;
      isIndex = Index;
      childPosArr = childArr;
      workIsEnd = obj.workIsEnd
      if (childArr) {
        setDrawFn(type,Index,childArr);
        setPhoto(message);
      }
      SDK.setEventLock();
      btnClickTimer = true;
      // return
    }
    // endedFn();
      $(".picBtn").hide();
      $('.audioPhoto').attr('src', "./audio/photo.mp3");
      document.getElementById("audioPhoto").loop = false;
      SDK.playRudio({
        index: $('.audioPhoto')[0],
        syncName: $('.audioPhoto').attr("data-syncaudio")
      })
      setPhoto(message);
      SDK.setEventLock();
      btnClickTimer = true;

  });

  //提交拍照
  function setPhoto(message) {
    let scaleWidth = 1920/1120,
        scaleHeight = 1080/620;
    if (configData.bg == '') {
      $(".contentMask").css({
        'background-image': 'url(./image/right_bg.png)'
      })
    } else {
      $(".contentMask").css({
        'background-image': `url(${configData.rightImg})`
      })
    }
    $(".contentMask").append($(".picList").clone())
    // 雪碧图展示
    let picLength = $(".contentMask .picList li");
    // clearInterval(timeFn);

    // 去掉右侧背景图
    $(".listBg").hide();
    for(let i=0; i < picLength.length; i++) {
      picLength.eq(i).css({
        width: picLength.eq(i).attr("data-width")/scaleWidth + 'rem',
        height: picLength.eq(i).attr("data-height")/scaleHeight + 'rem',
        left:picLength.eq(i).attr("data-left")/scaleWidth + 'rem',
        top:picLength.eq(i).attr("data-top")/scaleHeight + 'rem',
      })
    }
    if (isSync && message && message.operate == 5) { //断线重连
      console.log('断线重连后')
      completedFn()
      $(".picList li").find(".picBtn img").hide();
    } else {
      // 播放雪碧图音效
      audioTimeFn()
    }
    $('.resultMask').css({
      'z-index':'100'
    })
    $('.resultMask').show().find('.resultWin').show();

  }

  // 动画彩带
  function aniTnumFn() {
    let aniTNum = 1;
    $('.resultAPos_'+aniTNum).css({
        animation: 'resultAPosAn 5s infinite linear'
    });
    let aniT = null;
    clearInterval(aniT);
    aniT = setInterval(function () {
        let mad = Math.random()*5;
        if (aniTNum<8) {
            aniTNum ++;
            $('.resultAPos_'+aniTNum).css({
                animation: 'resultAPosAn '+(mad+1)+'s infinite linear'
            });
        } else {
            aniTNum = 1;
        }
    },300);
  }

  //雪碧图播放动效
  function audioTimeFn() {
    let picLength = $(".contentMask .picList li");
    if(picLength.length > imgIndex) {
      $('.animationAudio').attr('src', options[imgIndex].audio);
      setTimeout(function(){
        picLength.eq(imgIndex).addClass('shakeLeft');
        imgIndex++;
        audioFn()
      },time)
    }
  }


  // 初始化雪碧图播放音频
  function audioFn() {
    var video =  $('.animationAudio')[0];
    let picLength = $(".contentMask .picList li");

    if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
      SDK.playRudio({
        index: video,
        syncName: $('.animationAudio').attr("data-syncaudio")
      })
    }

    // 监听到播放结束后
    video.onended = function(){
      if(video){
        audioTimeFn()
      }
      // 最后一帧动画播放完开发加载彩带和胜利音频
      if(picLength.length == imgIndex) {
        completedFn()
      }
    }

  }
  //加载彩带和胜利音频
  function completedFn(){
    console.log("断线重连后---")
    $('.audioPhoto').attr('src', "./audio/photowin.mp3");
    document.getElementById("audioPhoto").loop = true;
    SDK.playRudio({
      index: $('.audioPhoto')[0],
      syncName: $('.audioPhoto').attr("data-syncaudio")
    })
    aniTnumFn();
    $(".winResult").show();
  }


  //老师点击 hint 按钮
  let btnStatus = true;
  $(".hint-btn").on("click touchstart", function (e) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if (btnStatus) {
        btnStatus = false;
        if (!isSync) {
          $(this).trigger("btnClick");
          return;
        }
        if (window.frameElement.getAttribute('user_type') == 'tea') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data("syncactions"),
            eventType: 'click',
            method: 'event',
            syncName: 'btnClick',
            otherInfor: '',
            recoveryMode: '1'
          });
        }
      }
  })
  $(".hint-btn").on('btnClick', function (e, message) {
    $('.hand').show();
    $('.right-top-hint').show();
    clickAble = false
    setTimeout(function () {
      $('.hand').hide();
      $('.right-top-hint').hide();
      btnStatus = true;
      clickAble = true;
    }, 3000)
    SDK.setEventLock();
  });


  //答题结束触发发送星星
  function starFun() {
    if (!isSync) {
      return false;
    }
    console.log('进入发星')
    var classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
    var support1v1h5star = parent.window.h5SyncActions.classConf.serverData.objCourseInfo.support1v1h5star;
    var device = parent.window.h5SyncActions.classConf.h5Course.device;
    if (window.frameElement.getAttribute('user_type') == 'stu' && classStatus == 2) {
      if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
        console.log("学生答题正确后发星星")
        SDK.bindSyncStart({
          type: "newH5StarData",
          num: 1
        });
      }

    }
  }

})
