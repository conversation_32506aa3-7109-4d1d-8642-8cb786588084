<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TDR0006_填充颜色</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">TDR0006_填充颜色</h3>

      <!-- 公共区域 -->
      <!-- 背景/描述 -->
  <div class="c-group">
    <div class="c-title">背景/标题</div>
    	<!-- 上传图片 -->
		<div class="c-area upload img-upload radio-group c-group-add">
      <div class="field-wrap">
        <label class="field-label"  for="">上传左侧背景</label>
        <label for="leftImg" class="btn btn-show upload" v-if="configData.leftImg==''?true:false">上传</label>
        <label for="leftImg" class="btn upload re-upload" v-if="configData.leftImg!=''?true:false">重新上传</label>
        <span class='txt-info'><em>*尺寸：311X1080。文件大小≤50KB</em></span>
        <input type="file"  v-bind:key="Date.now()" class="btn-file" id="leftImg" size="311*1080"  v-on:change="imageUpload($event,configData,'leftImg',50, -1)">
      </div>
      <p class="serialNum">（序号1）</p>
      <div class="img-preview" v-if="configData.leftImg!=''?true:false">
        <img v-bind:src="configData.leftImg" alt=""/>
        <div class="img-tools">
          <span class="btn btn-delete" v-on:click="configData.leftImg=''">删除</span>
        </div>
      </div>
    </div>
    <div class="c-area upload img-upload radio-group c-group-add">
      <div class="field-wrap">
        <label class="field-label"  for="">上传右侧背景</label>
        <label for="rightImg" class="btn btn-show upload" v-if="configData.rightImg==''?true:false">上传</label>
        <label for="rightImg" class="btn upload re-upload" v-if="configData.rightImg!=''?true:false">重新上传</label>
        <span class='txt-info'><em>*尺寸：1609X1080。文件大小≤50KB</em></span>
        <input type="file"  v-bind:key="Date.now()" class="btn-file" id="rightImg" size="1609*1080" v-on:change="imageUpload($event,configData,'rightImg',50,-1)">
      </div>
      <p class="serialNum">（序号2）</p>
      <div class="img-preview" v-if="configData.rightImg!=''?true:false">
        <img v-bind:src="configData.rightImg" alt=""/>
        <div class="img-tools">
          <span class="btn btn-delete" v-on:click="configData.rightImg=''">删除</span>
        </div>
      </div>
    </div>
    <p class="group-tit">若不上传，则显示默认图片</p>
    <!-- 描述 -->
    <div class="c-area">
      <label>一级标题 字符：≤40</label>
      <input type="text" class='c-input-txt' placeholder="请在此输入描述" v-model="configData.desc" maxlength='40'>
    </div>
  </div>

  <!-- 基础埋点 -->
  <div class="c-group">
    <div class="c-title">基础埋点</div>
    <div class="c-area ">
      <div class="field-wrap" style="display: none;">
        <label class="field-label" style="width: 100px;">教学环节</label>
        <select id="teachPart" v-model="configData.log.teachPart" style="width: 150px;">
          <option name="optive" value="-1">请选择</option>
          <option v-for="item in teachInfo" name="optive" :value="item.id">{{item.process_name}}</option>
        </select>
      </div>
      <div class="field-wrap">
        <label class="field-label" style="width: 100px;">建议教学时长</label>
        <select id="teachTime" v-model="configData.log_editor.isTeachTimeOther" style="width: 150px;">
          <option name="optive" value="-1">请选择</option>
          <option name="optive" value="30">0.5分钟</option>
          <option name="optive" value="60">1分钟</option>
          <option name="optive" value="90">1.5分钟</option>
          <option name="optive" value="120">2分钟</option>
          <option name="optive" value="150">2.5分钟</option>
          <option name="optive" value="180">3分钟</option>
          <option name="optive" value="-2">其他</option>
        </select>
        <br>
        <div class="field-wrap" v-show="configData.log_editor.isTeachTimeOther=='-2'">
          <label class="field-label" style="width: 100px;"></label>
          <input type="number" v-model="configData.log_editor.TeachTimeOtherM" style="width: 50px;"> 分
          <input type="number" v-model="configData.log_editor.TeachTimeOtherS" style="width: 50px;"> 秒(整数)
        </div>
      </div>
      <div class="field-wrap" style="display: none;">
        <label class="field-label" style="width: 100px;">题目属性</label>
        <label class="field-label" style="width: 150px;" v-if="configData.log.tplQuestionType=='2'">客观判断正误</label>
        <select style="width: 150px;" v-model="configData.log.tplQuestionType" v-else>
          <option name="optive" value="-1">请选择</option>
          <option name="optive" value="0">无题目</option>
          <option name="optive" value="1">主观判断正误</option>
        </select>
      </div>
    </div>
  </div>


  <!-- TG -->
  <div class="c-group">
    <div class="c-title">添加TG</div>
    <div class="c-area">
      <div class="c-well" v-for="(item,index) in configData.tg">
        <div class="well-title">
          <p>TG {{index+1}}</p>
          <span class="dele-tg-btn" v-on:click="deleTg(item)" v-show="configData.tg.length>1"></span>
        </div>
        <div class="well-con">
          <label>标题</label>
          <input type="text" class='c-input-txt' placeholder="请在此输入TG标题" v-model="item.title">
          <label>内容 <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em></label>
          <textarea name="" cols="56" rows="2" placeholder="请在此输入TG内容" v-model="item.content"></textarea>
        </div>
      </div>
      <button type="button" class="add-tg-btn" v-on:click="addTg" >+</button>
    </div>
  </div>
  <!-- level -->
  <div class="c-group">
    <div class="c-title">添加分层 H 教学</div>
    <div class="c-area">
      <div class="c-well" v-for="(item,index) in configData.level.high">
        <div class="well-title">
          <p>H {{index+1}}</p>
          <span class="dele-tg-btn" v-on:click="deleH(item)" v-show="configData.level.high.length>1"></span>
        </div>
        <div class="well-con">
          <label>标题</label>
          <input type="text" class='c-input-txt' placeholder="请在此输入 H 标题" v-model="item.title">
          <label>内容
            <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
          </label>
          <textarea name="" cols="56" rows="2" placeholder="请在此输入 H 内容" v-model="item.content"></textarea>
        </div>
      </div>
      <button type="button" class="add-tg-btn" v-on:click="addH">+</button>
    </div>
  </div>

  <div class="c-group">
    <div class="c-title">添加分层 L 教学</div>
    <div class="c-area">
      <div class="c-well" v-for="(item,index) in configData.level.low">
        <div class="well-title">
          <p>L {{index+1}}</p>
          <span class="dele-tg-btn" v-on:click="deleL(item)" v-show="configData.level.low.length>1"></span>
        </div>
        <div class="well-con">
          <label>标题</label>
          <input type="text" class='c-input-txt' placeholder="请在此输入 L 标题" v-model="item.title">
          <label>内容
            <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
          </label>
          <textarea name="" cols="56" rows="2" placeholder="请在此输入 L 内容" v-model="item.content"></textarea>
        </div>
      </div>
      <button type="button" class="add-tg-btn" v-on:click="addL">+</button>
    </div>
  </div>

      <!-- 配置画笔颜色 -->
      <div class="c-group">
        <div class="c-title">配置画笔颜色（序号3）</div>
        <div class="c-area upload img-upload">
          <label>说明：</label>
          <label>- 共12种颜色可供选择，最多配置4个颜色。</label>
          <label><em>注:（默认值为1：红色） <br>1：红色&nbsp;&nbsp;  2：黄色&nbsp;&nbsp;  3：蓝色&nbsp;&nbsp;   4：绿色&nbsp;&nbsp;  5：橙色&nbsp;&nbsp;  6：紫色<br>
            7：浅蓝&nbsp;&nbsp;  8：浅黄&nbsp;&nbsp;  9：深绿&nbsp;&nbsp;  10：深红  11：粉色  12：咖啡色</em></label>
          <div class="field-wrap">
            <div class="c-well" v-for="(item,index) in configData.source.colorList">
              <div class="well-title">
                <p>颜色{{index+1}}：</p>
                <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.colorList.length>2"></span>
              </div>
              <p>
                <span>颜色色值</span>
                <select v-model="configData.source.colorList[index].name" class="selectIndex">
                  <option v-for="conPos in configData.source.updata" name="optive" :value="conPos">{{conPos}}</option>
                </select>
              </p>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-on:click="addBox()" v-show='configData.source.colorList.length<configData.source.optionLength?true:false'>+</button>
        </div>
      </div>

      <!-- 选项图片 -->
      <div class="c-group">
        <div class="c-title">选项图片（序号4）</div>
        <div class="c-area upload img-upload">
          <label>说明：</label>
          <label>- 若配置n个颜色，则每个雪碧图为（n+1）帧。</label>
          <label>- 雪碧图的第一帧 为“未填色”的图片。</label>
          <label>- 雪碧图的每帧颜色图片设计，需要和配置的画笔颜色顺序一致。</label>
          <label>- 1～4个选项图片。</label>
          <div class="field-wrap" v-for="(item,index) in configData.source.options">
            <div class="c-well">
              <div class="well-title"><p>选项{{index+1}}</p></div>
              <span class='txt-info'>上传图片&nbsp;&nbsp;&nbsp;</span>
              <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
              <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
              <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤50KB </em></span>
              <input type="file" v-bind:key="Date.now()" class="btn-file" size="" :id="'content-pic-'+index" @change="imageUpload($event,item,'img',50,index)">

              <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>1"></span>
              <div class="img-preview" v-if="item.img">
                <img v-bind:src="item.img" alt=""/>
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                </div>
              </div>

              <label>位置<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="number" class="c-input-txt input60" v-model="item.position">数字，1～800</label>
              <!-- 上传声音 -->
              <div class="field-wrap">
                <label class="field-label"  for="">教学音频&nbsp;&nbsp;
                  <label :for="'audio-upload-'+index" class="btn btn-show upload" v-if="item.audio==''?true:false">上传</label>
                  <label  :for="'audio-upload-'+index" class="btn upload re-upload mar" v-if="item.audio!=''?true:false">重新上传</label>
                  <em>大小：≤20KB  非必填</em>
                </label>
                <div class="audio-preview" v-show="item.audio!=''?true:false">
                  <div class="audio-tools">
                    <p v-show="item.audio!=''?true:false">{{item.audio}}</p>
                  </div>
                  <span class="play-btn" v-on:click="play($event)">
                    <audio v-bind:src="item.audio"></audio>
                  </span>
                </div>
                <span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio='',item.natWidth='', item.natHeight=''">删除</span>
                <input type="file" :id="'audio-upload-'+index" class="btn-file upload" size="" accept=".mp3" v-on:change="audioUpload($event,item,'audio',20)" v-bind:key="Date.now()">
              </div>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            img: '',
            position:'',
            audio:''
          })">+</button>
        </div>
      </div>

			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.png?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
