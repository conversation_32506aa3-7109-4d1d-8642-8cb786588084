var configData = {
  // bg: '//cdn.51talk.com/apollo/images/45b47340bc6dbcc03a210161cacd254b.jpg',
  bg: './image/45b47340bc6dbcc03a210161cacd254b.jpg',
  desc: 'AIPRS0002_AI呈现帧动画',
  title: '',
  sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
  tg: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "1111111111111111111"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
  ],
  level: {
    high: [{
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      }
    ],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    options:[
      {
        // img: "//cdn.51suyang.cn/apollo/public/json/1-1111.json", //动态图
        img: "./image/1-1111.json", //动态图

      },
      {
        // img: "//cdn.51suyang.cn/apollo/public/json/2-2222.json", //动态图
        img: "./image/2-2222.json", //动态图
      },
      {
        // img: "//cdn.51suyang.cn/apollo/public/json/3-33333.json", //动态图
        img: "./image/3-33333.json", //动态图

      },
      {
        // img: "//cdn.51suyang.cn/apollo/public/json/4-44444.json", //动态图
        img: "./image/4-44444.json", //动态图
      }
    ]
  }
};
(function(pageNo) { configData.page = pageNo })(0)
