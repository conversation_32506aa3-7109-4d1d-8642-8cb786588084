"use strict";
import "../../common/js/common_1v1.js";
// import '../../common/js/commonFunctions.js'
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  //  var script = document.createElement("script"); //创建新的script节点
  // script.setAttribute("type", "text/javascript");
  // script.setAttribute("src", "//cdn.51talk.com/apollo/public/js/vconsole.min.3.3.0.js");
  // document.body.appendChild(script); //添加到body节点的末尾

  // script.addEventListener('load', function() {
  //     var vConsole = new VConsole();
  //     console.log('vconsole已正常启用');
  // }, false);

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: "0", // 是否有授权按钮 1：是 0：否
  };
  if (configData.bg == "") {
    $(".container_bg").css({
      "background-image": "url(./image/bj.jpg)",
    });
  }else {
    $(".container_bg").css({
      "background-image": `url(${configData.bg})`,
    });
  }
  if (SDK.getUserType() == "tea") {
  }
  let
    options = configData.source.options, //素材
    isCheck = true, //是否可点击
    isPlaySound = true, //是否播放声音
    isKeys = []; //选中后的key集合（用于断线重连）

  //初始化
  var animations = 0;
  // window.generalTplData({type: 'nextNum', num:1})  0开始
  // window.generalTplData({type: 'play',num:1})
  // window.generalTplData({type: 'close'})
    //获取动画的宽高，并初始化动画
    function getdata() {
      console.log("item getdata");
      let item;
      if (messageNum === undefined) return;
      item = messageNum;
      console.log("item getdata", item);
      $.get(item.img, {}, function (res) {
        if (res.w) {
          console.log("宽高", res.w, res.h);
          $(`#animationDiv`).css({
            width: res.w / 100 + "rem",
            height: res.h / 100 + "rem",
          });
          // 初始化动画
          animations = lottie.loadAnimation({
            //初始化
            container: document.getElementById(`animationDiv`), //在哪个dom容器中生效
            renderer: "svg", //渲染方式svg
            loop: true, //循环
            autoplay: false, //自动播放
            path: item.img, //动画数据
          });
          animations.play(); //播放动画
          animations.addEventListener('complete', function() {
              console.log("动画播放完成");

          });
        }
      });
    }

  var messageNum;
  window.generalTplData = function (message) {
    messageNum = options[message.num];
    console.log("messageNum",messageNum)

    if(message.actionType == "nextNum") {
      if(animations != 0) {
        animations.destroy(); // 销毁动画实例
      }
      // getdata(options[message.num])
     

      if (!isSync) {
        // getdata();
       
        $(`#nextNum`).on("getdata", getdata);
        $(`#nextNum`).trigger("getdata");

        return;
      }
      SDK.bindSyncEvt({
        index:$('#nextNum').data('syncactions'),
        eventType: "dragEnd",
        method: "drag",
        syncName: "syncDemoClick",
        otherInfor: {
        },
        recoveryMode: "1",
      });
      SDK.setEventLock();
      



    }
    //根据不同协议调不同方法
    if (message.actionType == "play") {
      //播放动画
      if(animations != 0) {
        animations.destroy(); // 销毁动画实例
      }
      // curtainAnimations(message.num);

    
      if (!isSync) {
        
        $(`#curtain_buttons`).on("curtainAnimations", curtainAnimations);
        $(`#curtain_buttons`).trigger("curtainAnimations");

        return;
      }
      SDK.bindSyncEvt({
        index:$('#curtain_buttons').data('syncactions'),
        eventType: "dragEnd",
        method: "drag",
        syncName: "curtainAnimationsA",
        recoveryMode: "1",
      });
      SDK.setEventLock();

      trackify.pind_send({
        key: message.num,
        time: new Date().getTime(),
      });

    }else {
      if (message.actionType == "close") {
        console.log("close",animations)
        if(animations != 0) {
          animations.destroy(); // 销毁动画实例
        }
      }
      if (!isSync) {
        // close_curtain()
        $(`#close_curtain`).on("close_curtain", close_curtain);
        $(`#close_curtain`).trigger("close_curtain");

        return;
      }
      SDK.bindSyncEvt({
        index:$('#close_curtain').data('syncactions'),
        eventType: "dragEnd",
        method: "drag",
        syncName: "closeCsurtainA",
        recoveryMode: "1",
      });
      SDK.setEventLock();
    }
    
    
  };

  $("#nextNum").on("syncDemoClick", function (e) {
    console.log("item getdata");
    let item;
    if (messageNum === undefined) return;
    item = messageNum;
    console.log("item getdata", item);
    $.get(item.img, {}, function (res) {
      if (res.w) {
        console.log("宽高", res.w, res.h);
        $(`#animationDiv`).css({
          width: res.w / 100 + "rem",
          height: res.h / 100 + "rem",
        });
        // 初始化动画
        animations = lottie.loadAnimation({
          //初始化
          container: document.getElementById(`animationDiv`), //在哪个dom容器中生效
          renderer: "svg", //渲染方式svg
          loop: true, //循环
          autoplay: false, //自动播放
          path: item.img, //动画数据
        });
        animations.play(); //播放动画
        animations.addEventListener('complete', function() {
            console.log("动画播放完成");

        });
      }
    });
    SDK.setEventLock();
  });

  // window.generalTplData({type: 'play', num:0})


   function curtainAnimations() {
      // messageNum = options[num];
      getdata();
      console.log("curtainAnimations")
      $("#curtain1").animate({ width: 0 }, 1000);
      $("#curtain2").animate({ width: 0 }, 1000);
      SDK.setEventLock();
   }
   $("#curtain_buttons").on("curtainAnimationsA", function (e) {
      getdata();
      console.log("curtainAnimations")
      $("#curtain1").animate({ width: 0 }, 1000);
      $("#curtain2").animate({ width: 0 }, 1000);
      SDK.setEventLock();
   })
   // 老师预览的时候
   function close_curtain(){
    console.log("close_curtain")
    $("#curtain1").animate({width:'48%'},1000);
    $("#curtain2").animate({width:'48%'},1000);
   }
   $("#close_curtain").on("closeCsurtainA", function (e) {
      console.log("close_curtain")
      $("#curtain1").animate({width:'48%'},1000);
      $("#curtain2").animate({width:'48%'},1000);
   })

   $("#closeBtn").on("click", function (e, message) {
      window.generalTplData({actionType: 'close'})
    });
   $("#openBtn").on("click", function (e, message) {
      window.generalTplData({actionType: 'play', num:0})
   });
   $("#openBtn1").on("click", function (e, message) {
    window.generalTplData({actionType: 'play', num:1})
    });
    $("#openBtn2").on("click", function (e, message) {
      window.generalTplData({actionType: 'play', num:2})
    });
    $("#openBtn3").on("click", function (e, message) {
      window.generalTplData({actionType: 'play', num:3})
    });

});
