<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>PSO0002AI_幕布转场AI</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">PSO0002AI_幕布转场AI</h3>

			<% include ./src/common/template/common_head %>

       <!-- 动画组合 -->
       <div class="c-group">
        <div class="c-title">动画组合</div>
        <div class="c-area upload img-upload details-filed-wrap">
          <div class="field-wrap animation-upload" v-for="(item,index) in configData.source.options">
            <div class="field-title">动画组合{{index+1}}</div>
            <div class="c-well">
              <!-- <label v-if="!item.isImgShow" class="title-label isAnimation">
                <span class="title-t">动画</span>
                <span class="details-btn" v-on:click="addAnimationFn(index)">添加动画</span>
              </label> -->
              <!-- 上传动画 -->
              <div class="label-img">
                <label>动画
                  <span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.options.length>1?true:false'></span>
                </label>
                <div class="details">
                  <span class='txt-info'>json动画<em>*</em>
                    <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
                    <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤5000KB </em></span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1" :id="'content-pic-'+index" @change="imageUpload($event,item,'img',5000)">
                  </span>
                </div>
                <div class="img-preview" v-if="item.img">
                  <!-- <img src="https://cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
                  <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                  </div>
                </div>

              </div>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-on:click="addSele()" v-if='configData.source.options.length<4?true:false'>+</button>
        </div>
       </div>
			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
