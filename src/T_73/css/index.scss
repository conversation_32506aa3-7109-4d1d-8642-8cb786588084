@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";

#__vconsole .vc-switch {
  right: auto !important; /* 覆盖默认的右侧定位 */
  left: 0;                 /* 设置左侧位置为0 */
  top: auto !important;                  /* 设置顶部位置为0 */
}
@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}
.hide {
  display: none;
}
.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;
  .desc {
    top: 0.6rem;
  }
  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}
#wrapper {
  text-align: center;
  margin: 0 auto;
  padding: 0px;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 20;
}
#effect {
  /* background-color:#610B0B; */
  position: relative;
  width: 100%;
  height: 100%;
  box-shadow: 0px 0px 10px 0px #610b0b;
}
#effect .bg {
  width: 100%;
  height: 100%;
}

#curtain1 {
  top: 0px;
  position: absolute;
  left: 0px;
  height: 100%;
  width: 48%;
  // opacity: 0.6;
}
#curtain2 {
  top: 0px;
  position: absolute;
  height: 100%;
  width: 48%;
  right: 0px;

  // opacity: 0.6;
}
#curtain_buttons {

}

.container_bg {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  top: 0px;
  position: absolute;
  left: 0px;
  height: 100%;
  width: 100%;
}
#animationDiv {
  // 实现页面剧中
  position: absolute;
  bottom: -13%;
  left: 51%;
  transform: translate(-50%, -50%);
  
}

