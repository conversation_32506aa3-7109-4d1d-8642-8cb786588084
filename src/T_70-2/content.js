var configData0 = {
  // bg: "https://ccss6.51talk.com/text/ccs/1429181/25/data/64954c20d3c3000e1b000519.jpg",
  bg: "./image/64954c20d3c3000e1b000519.jpg",
  desc: "",
  title: "",
  leftImg: "",
  rightImg: "",
  sizeArr: ["", "", "900*600", "1350*600"],
  tg: [{
    title: "教学时长：2分钟",
    content: ""
  }, {
    title: "教学目标",
    content: "学员能够通过文章的上下文及细节，选择正确的词语。"
  }, {
    title: "教学步骤",
    content: "1. 讲解互动游戏规则：请根据文段内容，从所给的4个词语中，选出正确的三个词语，拖拽到相应空格处，将文段补充完整。\n<br>2. 授权学生进行回答。\n<br>答案：doctor；gives；bottles。\n<br>3. 答案具体分析将在下一页面进行呈现。"
  }],
  level: {
    high: [{
      title: "",
      content: ""
    }],
    low: [{
      title: "",
      content: ""
    }]
  },
  source: {
    isVideo: "1",//是否有题干音频（无：1  有：2）
    videoUrl: "",//音频文件
    videoX: "",//音频横向位置
    videoY: "",//音频纵向位置
    videoDuration: "",//题干声音长度
    openWay: "1",//是否是默认起点位置（是：1  否：2）
    isEnding: "2",//是否出现终局页（是：1 否：2）
    // endFigure: "https://cdn.51talk.com/apollo/json/93f0832ba9481ef76140f42e4b20a521.json",//结局页雪碧图
    endFigure: "./image/93f0832ba9481ef76140f42e4b20a521.json",//结局页雪碧图
    endVideo: "",//结局页音频
    endDuration: "",//结局音频时长
    options: [{
      isRightAnswers: "1", //是否是正确答案（是：1  否：2）
      // img: "https://ccss6.51talk.com/text/ccs/1429181/25/data/64954c48a6e9b23a1c0004cb.png",
      img: "./image/64954c48a6e9b23a1c0004cb.png",
      natWidth: 290,//选项图片的宽度
      natHeight: 99,
      imgStartX: 295,//图片起点横向位置
      imgStartY: 673,
      imgStartWidth: 290,//图片起点的宽度
      imgStartHeight: 97,
      imgEndX: 143,//图片终点横向位置
      imgEndY: 258,
      imgEndWidth: 290,//图片终点的宽度
      imgEndHeight: 97,
      areaX: 143,//正确区域横向位置
      areaY: 258,
      areaWidth: 290,//正确区域宽度
      areaHeight: 97
    }, {
      isRightAnswers: "2",
      // img: "https://ccss6.51talk.com/text/ccs/1429181/25/data/64954d7da6e9b23a1c0004cd.png",
      img: "./image/64954d7da6e9b23a1c0004cd.png",
      natWidth: 242,
      natHeight: 99,
      imgStartX: 670,
      imgStartY: 672,
      imgStartWidth: 242,
      imgStartHeight: 97,
      imgEndX: "",
      imgEndY: "",
      imgEndWidth: "",
      imgEndHeight: "",
      areaX: "",
      areaY: "",
      areaWidth: "",
      areaHeight: ""
    },

     {
      isRightAnswers: "1",
      // img: "https://ccss6.51talk.com/text/ccs/1429181/25/data/64954d7da6e9b23a1c0004cd.png",
      img: "./image/64954d7da6e9b23a1c0004cd.png",
      natWidth: 252,
      natHeight: 99,
      imgStartX: 1366,
      imgStartY: 672,
      imgStartWidth: 252,
      imgStartHeight: 98,
      imgEndX: 1472,
      imgEndY: 381,
      imgEndWidth: 324,
      imgEndHeight: 186,
      areaX: 1472,
      areaY: 351,
      areaWidth: 280,
      areaHeight: 186
    }]
  },
};
var configData = {
  bg: './image/bj.png',
  desc: 'xxxxxxxxxxxxxxxxxxxxxxx',
  leftImg: 'image/leftImg.png',
  rightImg: 'image/rightImg.jpg',
  sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
  tg: [{
    content: "1",
    title: "1"
  }
  ],
  level: {
    high: [{
      content: "1",
      title: "1"
    }
    ],
    low: [{
      content: "1",
      title: "1"
    }]
  },
  source: {
    isVideo: '2', //是否有题干音频（无：1  有：2）
    videoUrl: './audio/photo.mp3', //音频文件
    videoX: '10', //音频横向位置
    videoY: '200', //音频纵向位置
    videoDuration: '', //题干声音长度
    openWay: '2', //是否是默认起点位置（是：1  否：2）
    isEnding: '1', //是否出现终局页（是：1 否：2）
    // endFigure: "https://cdn.51talk.com/apollo/json/93f0832ba9481ef76140f42e4b20a521.json",//结局页雪碧图
    endFigure: "./image/93f0832ba9481ef76140f42e4b20a521.json",//结局页雪碧图
    endVideo: './audio/resultWin.mp3', //结局页音频
    endDuration: '1200', //结局音频时长
    options: [
      {
        isRightAnswers: '1', //是否是正确答案（是：1  否：2）
        img: './image/a.png', //上传选项图片
        natWidth: 401, //选项图片的宽度
        natHeight: 328, //选项图片的高度
        imgStartX: 1450, //图片起点横向位置
        imgStartY: 180, //图片起点纵向位置
        imgStartWidth: 401, //图片起点的宽度
        imgStartHeight: 328, //图片起点的高度
        imgEndX: 638, //图片终点横向位置
        imgEndY: 202, //图片终点纵向位置
        imgEndWidth: 395, //图片终点的宽度
        imgEndHeight: 468, //图片终点的高度
        areaX: 600, //正确区域横向位置
        areaY: 170, //正确区域纵向位置
        areaWidth: 700, //正确区域宽度
        areaHeight: 700, //正确区域高度
      },
      {
        isRightAnswers: '1', //是否是正确答案（是：1  否：2）
        img: './image/b.png', //上传选项图片
        natWidth: 361, //选项图片的宽度
        natHeight: 288, //选项图片的高度
        imgStartX: 200, //图片起点横向位置
        imgStartY: 600, //图片起点纵向位置
        imgStartWidth: 361, //图片起点的宽度
        imgStartHeight: 288, //图片起点的高度
        imgEndX: 958, //图片终点横向位置
        imgEndY: 202, //图片终点纵向位置
        imgEndWidth: 324, //图片终点的宽度
        imgEndHeight: 251, //图片终点的高度
        areaX: 600, //正确区域横向位置
        areaY: 170, //正确区域纵向位置
        areaWidth: 700, //正确区域宽度
        areaHeight: 700, //正确区域高度
      },
      {
        isRightAnswers: '1', //是否是正确答案（是：1  否：2）
        img: './image/c.png', //上传选项图片
        natWidth: 401, //选项图片的宽度
        natHeight: 328, //选项图片的高度
        imgStartX: 1400, //图片起点横向位置
        imgStartY: 550, //图片起点纵向位置
        imgStartWidth: 401, //图片起点的宽度
        imgStartHeight: 328, //图片起点的高度
        imgEndX: 638, //图片终点横向位置
        imgEndY: 450, //图片终点纵向位置
        imgEndWidth: 324, //图片终点的宽度
        imgEndHeight: 251, //图片终点的高度
        areaX: 600, //正确区域横向位置
        areaY: 170, //正确区域纵向位置
        areaWidth: 700, //正确区域宽度
        areaHeight: 700, //正确区域高度
      },
      {
        isRightAnswers: '1', //是否是正确答案（是：1  否：2）
        img: './image/d.png', //上传选项图片
        natWidth: 328, //选项图片的宽度
        natHeight: 401, //选项图片的高度
        imgStartX: 200, //图片起点横向位置
        imgStartY: 180, //图片起点纵向位置
        imgStartWidth: 328, //图片起点的宽度
        imgStartHeight: 401, //图片起点的高度
        imgEndX: 886, //图片终点横向位置
        imgEndY: 523, //图片终点纵向位置
        imgEndWidth: 395, //图片终点的宽度
        imgEndHeight: 468, //图片终点的高度
        areaX: 600, //正确区域横向位置
        areaY: 170, //正确区域纵向位置
        areaWidth: 700, //正确区域宽度
        areaHeight: 700, //正确区域高度
      }
    ],
    // options:[
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/oranges.png', //上传选项图片
    //     natWidth: '200', //选项图片的宽度
    //     natHeight: '200', //选项图片的高度
    //     imgStartX: '100', //图片起点横向位置
    //     imgStartY: '750', //图片起点纵向位置
    //     imgStartWidth: '200', //图片起点的宽度
    //     imgStartHeight: '200', //图片起点的高度
    //     imgEndX: '150', //图片终点横向位置
    //     imgEndY: '100', //图片终点纵向位置
    //     imgEndWidth: '220', //图片终点的宽度
    //     imgEndHeight: '220', //图片终点的高度
    //     areaX: '50', //正确区域横向位置
    //     areaY: '50', //正确区域纵向位置
    //     areaWidth: '520', //正确区域宽度
    //     areaHeight: '500', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/watermelon.png', //上传选项图片
    //     natWidth: '214', //选项图片的宽度
    //     natHeight: '217', //选项图片的高度
    //     imgStartX: '360', //图片起点横向位置
    //     imgStartY: '800', //图片起点纵向位置
    //     imgStartWidth: '214', //图片起点的宽度
    //     imgStartHeight: '217', //图片起点的高度
    //     imgEndX: '290', //图片终点横向位置
    //     imgEndY: '270', //图片终点纵向位置
    //     imgEndWidth: '200', //图片终点的宽度
    //     imgEndHeight: '200', //图片终点的高度
    //     areaX: '50', //正确区域横向位置
    //     areaY: '50', //正确区域纵向位置
    //     areaWidth: '520', //正确区域宽度
    //     areaHeight: '500', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/small_watermelon.png', //上传选项图片
    //     natWidth: '160', //选项图片的宽度
    //     natHeight: '210', //选项图片的高度
    //     imgStartX: '620', //图片起点横向位置
    //     imgStartY: '720', //图片起点纵向位置
    //     imgStartWidth: '160', //图片起点的宽度
    //     imgStartHeight: '210', //图片起点的高度
    //     imgEndX: '800', //图片终点横向位置
    //     imgEndY: '260', //图片终点纵向位置
    //     imgEndWidth: '160', //图片终点的宽度
    //     imgEndHeight: '210', //图片终点的高度
    //     areaX: '710', //正确区域横向位置
    //     areaY: '220', //正确区域纵向位置
    //     areaWidth: '450', //正确区域宽度
    //     areaHeight: '420', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/cake.png', //上传选项图片
    //     natWidth: '178', //选项图片的宽度
    //     natHeight: '178', //选项图片的高度
    //     imgStartX: '800', //图片起点横向位置
    //     imgStartY: '800', //图片起点纵向位置
    //     imgStartWidth: '178', //图片起点的宽度
    //     imgStartHeight: '178', //图片起点的高度
    //     imgEndX: '950', //图片终点横向位置
    //     imgEndY: '240', //图片终点纵向位置
    //     imgEndWidth: '178', //图片终点的宽度
    //     imgEndHeight: '178', //图片终点的高度
    //     areaX: '710', //正确区域横向位置
    //     areaY: '220', //正确区域纵向位置
    //     areaWidth: '450', //正确区域宽度
    //     areaHeight: '420', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/prizza.png', //上传选项图片
    //     natWidth: '215', //选项图片的宽度
    //     natHeight: '215', //选项图片的高度
    //     imgStartX: '1020', //图片起点横向位置
    //     imgStartY: '720', //图片起点纵向位置
    //     imgStartWidth: '215', //图片起点的宽度
    //     imgStartHeight: '215', //图片起点的高度
    //     imgEndX: '850', //图片终点横向位置
    //     imgEndY: '420', //图片终点纵向位置
    //     imgEndWidth: '215', //图片终点的宽度
    //     imgEndHeight: '215', //图片终点的高度
    //     areaX: '710', //正确区域横向位置
    //     areaY: '220', //正确区域纵向位置
    //     areaWidth: '450', //正确区域宽度
    //     areaHeight: '420', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/sandwich.png', //上传选项图片
    //     natWidth: '173', //选项图片的宽度
    //     natHeight: '188', //选项图片的高度
    //     imgStartX: '1300', //图片起点横向位置
    //     imgStartY: '760', //图片起点纵向位置
    //     imgStartWidth: '173', //图片起点的宽度
    //     imgStartHeight: '188', //图片起点的高度
    //     imgEndX: '1450', //图片终点横向位置
    //     imgEndY: '100', //图片终点纵向位置
    //     imgEndWidth: '173', //图片终点的宽度
    //     imgEndHeight: '188', //图片终点的高度
    //     areaX: '1330', //正确区域横向位置
    //     areaY: '60', //正确区域纵向位置
    //     areaWidth: '500', //正确区域宽度
    //     areaHeight: '500', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '1', //是否是正确答案（是：1  否：2）
    //     img: './image/chocolate.png', //上传选项图片
    //     natWidth: '207', //选项图片的宽度
    //     natHeight: '217', //选项图片的高度
    //     imgStartX: '1580', //图片起点横向位置
    //     imgStartY: '770', //图片起点纵向位置
    //     imgStartWidth: '207', //图片起点的宽度
    //     imgStartHeight: '217', //图片起点的高度
    //     imgEndX: '1450', //图片终点横向位置
    //     imgEndY: '300', //图片终点纵向位置
    //     imgEndWidth: '207', //图片终点的宽度
    //     imgEndHeight: '217', //图片终点的高度
    //     areaX: '1330', //正确区域横向位置
    //     areaY: '60', //正确区域纵向位置
    //     areaWidth: '500', //正确区域宽度
    //     areaHeight: '500', //正确区域高度
    //   },
    //   {
    //     isRightAnswers: '2', //是否是正确答案（是：1  否：2）
    //     img: './image/sketch02-3.png', //上传选项图片
    //     imgStartX: '1100', //图片起点横向位置
    //     imgStartY: '960', //图片起点纵向位置
    //     imgStartWidth: '300', //图片起点的宽度
    //     imgStartHeight: '100', //图片起点的高度
    //   },
    // ],
  },
};
(function (pageNo) { configData.page = pageNo })(0)
