@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.container{
	// background-image: url(../image/1.jpg);
	position: relative;
    // font-family:"ARLRDBD";

}
audio{
	width: 0;
	height: 0;
	opacity: 0;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.mainArea{
	width: 16.4rem;
	height: 7.45rem;
	position: relative;	
	top:2.15rem;
	left: 50%;
	margin-left: -8.2rem;
	.list{
		width: 1.64rem;
		height: 1.49rem;
		float: left;
		box-sizing: border-box;
		position: relative;
		.audioList{
            position: absolute;
            width: 1.45rem;
            height: 1.34rem;
            background: url('../image/btn-audio-bg.png') no-repeat;
            background-size: 100% 100%;
            z-index: 10;
			left: 50%;
			top: 50%;
            transform: translateX(-50%) translateY(-50%);
            cursor: pointer;
            img{
                position: absolute;
                top: 0.3rem;
                left: 0.3rem;
                width: 0.83rem;
                height: 0.8rem;
            }
            audio{
                width:0;
                height: 0;
                opacity: 0;
                position:absolute;
            }
        }
	}
}




