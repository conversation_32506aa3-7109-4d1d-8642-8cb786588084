"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/teleprompter.js";
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
// import {
//   resultWin,
//   resultHide
// } from '../../common/template/ribbonWin/index.js';

import {
  templateScale,
  handClick,
  handHide,
} from "../../common/template/hint/index.js";

$(function () {
  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      item: configData.source.options.length || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_INPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.LISTEN,
    },
  });
  console.log("更新时间：3122124");

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  //是否在控制器显示功能按钮
  window.h5Template = {
    hasDemo: "1", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "2", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式
  };

  // 左右背景图片预加载
  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bj.jpg)",
    });
  } else {
    $(".container").css("background-image", "url(" + configData.bg + ")");
  }
  let classStatus = 1; //教室状态
  let userType =
    window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师
  let isFirst = true;
  let options = configData.source.options.slice(0, 4), //图片列表
    isCheck = true, //是否可点击选择
    time = 2700, //消失终局特效的时间
    firstDataIndex = null, // 存储当前第一个选中的组内序号
    firstKey = null, // 存储当前第一个选中的key值
    twoKey = null, //存储当前第二个选中的key值
    curvOne = [], // 存储当前第一个选中的坐标
    isSyncKey = null, //断线重连第一次点击时的key值
    isSyncFirstKey = [], //断线重连消除后的第一个key值集合
    isSyncLastKey = [], //断线重连消除后的第二个key值集合
    completeArray = [], //存储选中后的内容（图片、标题、音频）
    hintIndex = 0, //hint模式选中的key值
    isHint = false, //是否是hint模式 true:hint模式
    half = 2, //链接时距离的倍数 默认为2，hint为1.45
    timeInt = null, // 定时器
    isAnyOneOut = false, //是否断线  true 已断线
    isDemo = false, //是否是demo模式
    ismatelSound = true, //音效是否播放
    nextOptions = configData.source.options.slice(4), // 剩下的为未展示元素
    eliminatedKeys = [], // 记录被消除的li索引
    currentPage = 0, // 当前显示的页码
    itemsPerPage = 4; // 每页显示的卡片数量
  // 存储所有Lottie动画实例
  const lottieInstances = {};
  // 创建两个全局爆炸动画实例
  let explosionAnimation1 = null;
  let explosionAnimation2 = null;

  const isStu = isSync ? userType === "stu" : true;
  const isTea = isSync ? userType === "tea" : false;

  const animationMap = new Map();

  const toLoadAnimationJSON = $.ajax;

  const onClickAnimationP = toLoadAnimationJSON("./image/two-click.json");
  const explosionToHideAnimationP = "./image/XXL.json";
  //判断用户角色，显示不同功能
  isShowBtn();

  //判断用户角色，显示不同功能(如老师的底部文字提示，学生的预习模式等)
  function isShowBtn() {
    if (isSync) {
      //同步模式
      classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == 0 && userType == "stu") {
        $(".funcMask").show(); //预习模式和倒计时
      }
    } else {
      //非同步模式
      var hrefParam = parseURL("http://www.example.com");
      if (top.frames[0] && top.frames[0].frameElement) {
        hrefParam = parseURL(top.frames[0].frameElement.src);
      }
      var role_num = hrefParam.params["role"];

      function parseURL(url) {
        var a = document.createElement("a");
        a.href = url;
        return {
          source: url,
          protocol: a.protocol.replace(":", ""),
          host: a.hostname,
          port: a.port,
          query: a.search,
          params: (function () {
            var ret = {},
              seg = a.search.replace(/^\?/, "").split("&"),
              len = seg.length,
              i = 0,
              s;
            for (; i < len; i++) {
              if (!seg[i]) {
                continue;
              }
              s = seg[i].split("=");
              ret[s[0]] = s[1];
            }
            return ret;
          })(),
          file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ""])[1],
          hash: a.hash.replace("#", ""),
          path: a.pathname.replace(/^([^\/])/, "/$1"),
          relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ""])[1],
          segments: a.pathname.replace(/^\//, "").split("/"),
        };
      }
      if (role_num == "1" || role_num == "2" || role_num == undefined) {
        $(".funcMask").show();
      }
    }
  }
  /**
   *  开始游戏
   *  2种途径可开启游戏，1.老师通过控制器点击开始 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击开始
  window.SDK.actAuthorize = function (message) {
    if (isSync) {
      // 添加手型样式
      $(".img-list").addClass("img-list-cursor");
      if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
        //老师显示下方提示条
        $(".doneTip").removeClass("hide");
      }

      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        //第一次授权才显示倒计时
        if (isFirst) {
          SDK.reportTrackData(
            {
              action: "CK_FT_INTERACTION_STARTBUTTON",
              data: {},
            },
            USER_TYPE.TEA
          );
          isFirst = false;
          $(".funcMask").show();
          threeTwoOne(); //321倒计时
          // gameProcessBidSync(); //上报游戏数据
        }
      }
    }
  };
  //2.非课中模式，如预习，学生点击开启
  let startBtnStatus = true; //开始按钮是否可点击(预习模式)
  $(".startBtn").on("click touchstart", function (e) {
    $(".img-list").addClass("img-list-cursor");
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (startBtnStatus) {
      startBtnStatus = false;
      threeTwoOne(); //321倒计时
    }
  });

  /**
   *  demo模式
   *  2种途径可开启demo模式，1.老师通过控制器点击提示 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击提示
  window.SDK.actDemo = function (message) {
    demoFun(false); //demo模式
    //老师上传一份空白cldata,来覆盖以前的数据。
    if (isSync) {
      SDK.bindSyncEvt({
        index: "clear",
        eventType: "click",
        method: "event",
        syncName: "clear", //SDK.js中对其特殊处理
        recoveryMode: "1",
      });
    }
    SDK.setEventLock();
  };
  //2.非课中模式，如预习，学生点击开启
  $(".demo-btnStu").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    demoFun(true); //demo模式
  });

  // 3 2 1倒计时
  function threeTwoOne() {
    let q = 1;
    $(".startBox").hide().siblings(".timeChangeBox").show().find(".numberList");
    SDK.playRudio({
      index: $(".timeLowAudio_" + q).get(0),
      syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
    });
    let audioPlay = setInterval(function () {
      q++;
      if (q > 4) {
        clearInterval(audioPlay);
        //3210倒计时结束
        SDK.setEventLock();
        $(".funcMask").hide();
        $(".timeChangeBox").hide();
        // optionsInit(timeIndex); //本轮游戏选项初始化
        // addEvent(); //泡泡选项添加事件
        // sorceTimeInit(); //进度条初始化
        timeStartFn(); //计时器倒计时
        // gameProcessBidSync(); //上报游戏数据
      } else {
        // 播放倒计时声音
        SDK.playRudio({
          index: $(".timeLowAudio_" + q).get(0),
          syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
        });
        $(".numberList").css({
          "background-position-x": -(1.5 * (q - 1)) + "rem",
        });
      }
    }, 1000); // @WARNING
  }

  //计时器倒计时
  function timeStartFn() {
    window.localStorage.setItem("countDownTime", time); //将本轮倒计时存储在缓存中
    if (timeInt) {
      clearInterval(timeInt);
      timeInt = null;
    }
    timeInt = setInterval(function () {
      if (isAnyOneOut) {
        //如断线则停止游戏
        clearInterval(timeInt);
        soundControl().pause(); //停止声音
      }
      time--; //本轮游戏时间
      // isEndGameFun(time); //本轮游戏是否结束
      window.localStorage.setItem("countDownTime", time); //每秒更新一次时间
      // moveItemW = moveItemW - everLen; //每秒更新一次进度条长度    新进度条长度 = 原进度条长度 - 每秒移动距离
      // $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);
    }, 1000);

    // 重置数据
    resetFn();
  }

  /**
   * demo hint 模式
   * showfuncMask 是否是学生
   */
  // demoFun()
  function demoFun(showfuncMask) {
    // 重置数据
    resetFn();
    isDemo = true;
    // 添加模版缩放模式
    templateScale({
      show: true,
      className: $(".container"),
    });
    if (showfuncMask) {
      //是否显示学生用的预习模式弹窗
      $(".funcMask").hide();
    }
    // 是否为hint模式
    isHint = true;
    // 点击第一个模块的位置
    hintMoveFn();
    // 执行队列触发事件
    $(".img-list li")
      .eq(0)
      .delay(1000)
      .queue(function () {
        half = 1.45;
        // 第一个元素触发事件
        $(".img-list li").eq(0).trigger("syncItemClick");
        $(this).dequeue();
      })
      .delay(2000)
      .queue(function () {
        half = 1.45;
        //点击能跟第一个消除的位置
        hintMoveFn();
        setTimeout(function () {
          //第二次触发事件
          $(".img-list li").eq(hintIndex).trigger("syncItemClick");
          // hint 隐藏小手
          handHide(true);
        }, 1500);
        $(this).dequeue();
      });
  }

  /**
   * 退出demo模式,恢复正常模式
   * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
   */
  function demoOut(showfuncMask, $this, isFirstList) {
    // 消除后1.5s后恢复初始化状态（hint模式消失）
    setTimeout(function () {
      // hint 隐藏
      half = 2;
      templateScale({
        className: $(".container"),
        show: false,
      });

      // 重置数据
      resetFn();
      // 恢复hint状态
      isHint = false;
      (hintIndex = 0),
        // 恢复隐藏的列表
        $this.show();
      isFirstList.show();
      // 重置相关状态
      $this.parent().removeClass("successful-list");
      isFirstList.parent().removeClass("successful-list");

      $(".img-list li").removeClass("on");

      // isFirstList.css({
      //   'transform': 'translate(0)',
      //   'transition': 'transform 1ms ease 1ms'
      // })
      // $this.css({
      //   'transform': 'translate(0)',
      //   'transition': 'transform 1ms ease 1ms'
      // })
      //告知控制器取消demo模式
      if (userType == "tea") {
        SDK.bindSyncCtrl({
          type: "tplDemoOut",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [],
          },
        });
      }
      if (
        showfuncMask ||
        (classStatus == 0 && userType == "stu") ||
        !userType
      ) {
        //是否显示学生用的预习模式弹窗
        $(".funcMask").show();
      }
      //退出demo模式
      isDemo = false;
    }, 1500);
  }

  /**
   * hint移动位置
   */
  function hintMoveFn() {
    let postionTop = $(".img-list ul li").eq(hintIndex).offset().top,
      postionLeft = $(".img-list ul li").eq(hintIndex).offset().left;
    handClick(postionTop - 25, postionLeft - 15);
  }

  /**
   *重置demo模式选中的数据
   */
  function resetFn() {
    firstDataIndex = null; // 存储当前第一个选中的组内序号
    firstKey = null; // 存储当前第一个选中的key值
    (isSyncKey = null), //断线重连第一次点击时的key值
      (isSyncFirstKey = []); //断线重连消除后的第一个key值集合
    isSyncLastKey = []; //断线重连消除后的第二个key值集合
    completeArray = [];
    $(".img-list ul").find("li").removeClass("non-clickable");
  }

  /**
   * 初始化列表渲染
   */
  optionsListTemplate();
  function optionsListTemplate() {
    let str = "",
      templateDataOne = [], //位置一的数据
      templateDataTwo = [], //位置二的数据
      templateData = []; //合并后的数据
    // 重组数组
    for (let i = 0; i < options.length; i++) {
      let data = options[i];
      if (data.imgType == 1) {
        data.imgNew = data.img;
      }
      // 选项图1
      templateDataOne.push({
        img: data.img, //图片
        position: data.positionOne, //选项1的位置
        dataIndex: i, //选项图1和选项图2是否属于同一类并能消除
        cover: data.cover, //是否是默认封面
        index: 1, //标示是否是选项图1
        title: data.title, //文本内容
        audio: data.audio, //音频
        audioTime: data.audioTime, //音频时长
      });
      // 选项图2
      templateDataTwo.push({
        img: data.imgNew,
        position: data.positionTwo, //选项2的位置
        dataIndex: i, //选项图1和选项图2是否属于同一类并能消除
        cover: data.cover,
        index: 2, //标示是否是选项图2
        title: data.title,
        audio: data.audio,
        audioTime: data.audioTime, //音频时长
      });
    }
    // 合并数组
    templateData = templateDataOne.concat(templateDataTwo);

    // 对序号进行排序（默认根据[1,3,5,7,2,4,6,8],如果排序编辑器改变此逻辑也需变更）
    templateData.sort(function (a, b) {
      // 如果是a是偶数,b是奇数
      if (a.position % 2 === 0 && b.position % 2 !== 0) {
        return 1;
      }
      // 如果是a是奇数,b是偶数
      if (a.position % 2 !== 0 && b.position % 2 === 0) {
        return -1;
      }
      // 如果是a,b都是偶数或者奇数
      if (
        (a.position % 2 === 0 && b.position % 2 === 0) ||
        (a.position % 2 !== 0 && b.position % 2 !== 0)
      ) {
        return a.position - b.position;
      }
    });
    for (let i = 0; i < templateData.length; i++) {
      let data = templateData[i];
      str += `<li
                data-cover="${data.cover}"
                data-position="${data.position}"
                data-cover-index="${data.index}"
                data-index="${data.dataIndex}"
                data-syncactions="onLine-${i}"
                data-title="${data.title}"
                data-img="${data.img}"
                data-audio="${data.audio}"
                data-audio-time="${data.audioTime}"
                data-key="${i}"
                data-showtitle="${options[data.dataIndex].showTitle || '1'}">
                <div class="s-list">
                  <div class="success-img">
                    <img src="${data.img}" class="dataimg" />
                    <span class="data-title">${data.title}</span>
                  </div>
                </div>
                <div class="lottie-wrapper">
                  <div class="lottie-1-container"></div>
                  <div class="lottie-2-container"></div>
                </div>
            </li>`;
    }
    // 根据组数个数动态改变容器宽度
    $(".img-list ul").css({
      width: (400 * options.length) / 100 + "rem",
    });
    $(".img-list ul").html(str);
  }

  //初始化爆炸动画
  initExplosionAnimations();

  /**
   * 选择泡泡事件
   */
  $(".img-list li").on("click touchstart", function (e) {
    //demo模式不能点击
    if (isDemo) {
      return;
    }
    if ($(this).hasClass("non-clickable")) {
      return;
    }
    if (!isCheck) {
      return;
    }
    // 当前是否是选中状态
    if ($(this).hasClass("on") && isStu) {
      return;
    }
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation(); //阻止事件进一步传播
    //禁止老师点击
    if (isSync) {
      classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus != 0 && userType == "tea") {
        SDK.setEventLock();
        return false;
      }
    }
    // 本地模式
    if (!isSync) {
      $(this).trigger("syncItemClick");
      return;
    }

    /**
     * 执行逻辑
     */
    syncItemEventFn($(this));

    if (window.frameElement.getAttribute("user_type") == "stu") {
      SDK.bindSyncEvt({
        sendUser: "",
        receiveUser: "",
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "syncItemClick",
        otherInfor: {
          isSyncKey: isSyncKey, //是否没消除且只选中了一个
          isSyncFirstKey: isSyncFirstKey, //第一次选中的key值集合
          isSyncLastKey: isSyncLastKey, //第二次选中的key值集合
          completeArray: completeArray, //所有消除后的数据集合
          firstDataIndex: firstDataIndex, //存储当前第一个选中的组内序号
        },
        recoveryMode: "1",
      });
    }
  });

  $(".img-list li").on("syncItemClick", function (e, message) {
    // 断线重连
    recoverList(message, $(this));
    SDK.setEventLock();
  });

  /**
   * 列表执行逻辑
   */
  async function syncItemEventFn($this) {
    ismatelSound = false;
    let curveTwo = [];
    // 消除后的不可点击
    if ($this.hasClass("non-clickable")) {
      return;
    }
    if (isCheck) {
      // 当前是否是选中状态
      if ($this.hasClass("on")) {
        return;
      }

      onClickAnimationP
        .then((onClickAnimationJSON) => {
          const key = $this.data("key");
          const animationContainer = $this
            .find(".lottie-wrapper .lottie-1-container")
            .get(0);

          // 让lottie只加载一次
          const ani =
            animationMap.get(key) ||
            (function () {
              const animate = lottie.loadAnimation({
                container: animationContainer,
                renderer: "svg",
                loop: false,
                autoplay: false,
                // path: './assets/animation/on-click.json',
                animationData: onClickAnimationJSON,
              });

              // 播放完成后将元素隐藏否则会留下白点
              animate.addEventListener("complete", () => {
                animationContainer.style.display = "none";
              });

              return animate;
            })();

          animationMap.set(key, ani);

          // 重复播放时 上一次播放完毕后会被隐藏 所以在播放前把元素显示
          animationContainer.style.display = "block";

          ani.stop(); // 暂停并回到第0帧

          ani.play();
        })
        .fail((err) => {
          console.error("加载on-click特效动画文件失败", err);
        });
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_ITEM",
          stuData: {
            item: $this.data("key") + 1,
          },
        },
        USER_TYPE.STU
      );
      // 是否开始点击链接
      if (firstDataIndex) {
        ismatelSound = true;
        // 是否为同一组内的元素
        if ($this.attr("data-index") == firstDataIndex) {
          //正确消除
          console.log("选择消除正确");
          SDK.reportTrackData(
            {
              action: "CK_FT_ANSWER_RESULT",
              data: {
                result: "right",
              },
            },
            USER_TYPE.TEA
          );
          // 当前二次选中的key值
          twoKey = $this.attr("data-key");
          isSyncKey = null;
          SDK.setEventLock();
          if (isStu) {
            isCheck = false;
          }
          // 获取当前坐标
          curveTwo.push($this.offset().top);
          curveTwo.push($this.offset().left);
          $this.addClass("on");
          // 连对音频
          rightWrongAudio(true).right();
          await onSuccessAnimation(firstKey, twoKey);
          // 消除后的数据存储
          completeDataFn($this);
          // 消除事件
          // moveFn(curvOne, curveTwo, $this);
          recordEliminatedKeys(firstKey, twoKey);
          // 如果全部消除完毕
          completeArray.length == configData.source.options.length && removeCompleteFn(true)
          // hint模式第二次消除后相关逻辑
          if (isHint) {
            const $current = $this.find(".s-list");
            const $first = $(".img-list li").eq(firstKey).find(".s-list");
            demoOut(false, $current, $first);
          }
          SDK.setEventLock();
          // 记录被消除的元素，以便后续补充新元素
        } else {
          //消除错误
          console.log("选择错误");
          SDK.reportTrackData(
            {
              action: "CK_FT_ANSWER_RESULT",
              data: {
                result: "wrong",
              },
            },
            USER_TYPE.TEA
          );
          if (isStu) {
            isCheck = false;
          }
          isSyncKey = null;
          SDK.setEventLock();
          // 第二次选择清除所有的选中状态
          $(".img-list li").removeClass("on");
          // 选中错误时的标识
          $this.addClass("error error-ani");
          $(".img-list li").eq(firstKey).addClass("error error-ani");
          // 消除时错误音效
          rightWrongAudio().wrong();
          // 执行队列清除动画
          setTimeout(function () {
            $(".img-list li").removeClass("error error-ani");
            isCheck = true;
          }, 500);
        }
        // 重置第一次选中的内容
        firstDataIndex = null;
        curvOne = [];
        SDK.setEventLock();
      } else {
        ismatelSound = true;
        console.log("第一次点击");
        $this.addClass("on");
        // 获取当前的key值
        isSyncKey = $this.attr("data-key");
        SDK.setEventLock();
        // 点击时的音频
        $(".soundAudio").attr("src", $this.attr("data-audio"));
        rightWrongAudio(true).dragAudio();
        SDK.reportTrackData(
          {
            action: "CK_FT_INTERACTION_AUDIOPLAY",
            data: {
              audio: $this.data("key") + 1,
            },
          },
          USER_TYPE.TEA
        );
        //隐藏小手
        setTimeout(function () {
          handHide(true);
        }, 3600); //注意调用handClick后 不低于
        // 获取hint模式下的匹配泡泡的key值
        for (let i = 0; i < $(".img-list li").length; i++) {
          let data = $(".img-list li").eq(i);
          if (data.attr("data-index") == $this.attr("data-index")) {
            // hint模式时获取跟第一个匹配的泡泡的key值
            hintIndex = data.attr("data-key");
          }
        }

        // 第一次点击泡泡的事件
        firstClickFn($this);
      }
    }
  }

  /**
   * 创建或获取Lottie动画实例
   * @param {string} key - 动画实例的唯一标识符
   * @param {string} selector - 选择器，用于定位动画容器
   * @param {Object} jsonData - 动画JSON数据
   * @param {boolean} loop - 是否循环播放
   * @returns {Promise<Object>} - 返回动画实例
   */
  async function initOrGetLottieAnimation(
    key,
    selector,
    jsonData,
    loop = false
  ) {
    // 创建新的动画实例
    lottieInstances[key] = await lottieAnimations.init(
      null,
      jsonData,
      selector,
      loop
    );

    return lottieInstances[key];
  }

  /**
   * 初始化两个爆炸动画实例
   */
  async function initExplosionAnimations() {

    let ids = ['explosion-animation-1', 'explosion-animation-2']
    // 创建两个容器元素
    ids.forEach(id => {
      if(!$(`#${id}`).length) {
        $('body').append(`<div id="${id}" class="explosion-animation" style="display:none;position:absolute;z-index:999;"></div>`);
      }
    })

    async function initSingleExplosionAnimation(id) {
      const animation = await lottieAnimations.init(
        null,
        explosionToHideAnimationP,
        `#${id}`,
        false
      );

      animation.addEventListener("complete", () => {
        lottieAnimations.stop(animation);
        $(`#${id}`).hide();
      });

      return animation;
    }

    // 初始化两个动画实例
    [explosionAnimation1, explosionAnimation2] = await Promise.all([
      initSingleExplosionAnimation('explosion-animation-1'),
      initSingleExplosionAnimation('explosion-animation-2')
    ]);
  }


  /**
   * 处理单个元素的爆炸动画效果
   * @param {number} key - 元素的键值
   * @param {Object} animation - 要使用的动画实例
   * @returns {Promise<void>}
   */
  async function handleElementAnimation(key, animation) {
    const $el = $(".img-list li").eq(key);
    const $sList = $el.find(".s-list");
    const position = $el.offset();

    // 隐藏元素
    $sList.hide();
    $el.addClass("non-clickable");
    $el.removeClass("on");

    // 设置动画位置，使其比原元素大1.5倍并居中
    const container = animation === explosionAnimation1 ?
      $('#explosion-animation-1') : $('#explosion-animation-2');

    const scale = 1.8; // 放大倍数
    const originalWidth = $el.width();
    const originalHeight = $el.height();
    const newWidth = originalWidth * scale;
    const newHeight = originalHeight * scale;

    container.css({
      top: position.top - (newHeight - originalHeight) / 2,
      left: position.left - (newWidth - originalWidth) / 2,
      width: newWidth,
      height: newHeight,
      display: 'block'
    });

    // 播放动画
    lottieAnimations.play(animation);
  }

  /**
   * 当配对成功时播放爆炸动画
   * @param {number} key1 - 第一个元素的键值
   * @param {number} key2 - 第二个元素的键值
   */
  async function onSuccessAnimation(key1, key2) {
    // 同时处理两个元素的动画
    await handleElementAnimation(key1, explosionAnimation1);
    await handleElementAnimation(key2, explosionAnimation2);

    isCheck = true;
    console.log("动画结束", new Date().getTime());
  }

  /**
   *
   * @param {*}断线重连--恢复列表数据
   */
  function recoverList(message, $this) {
    // 断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor,
        isSyncKey = obj.isSyncKey, //是否没消除且只选中了一个
        firstKey = obj.isSyncFirstKey, //第一次选中的key值集合
        lastKey = obj.isSyncLastKey, //第二次选中的key值集合
        imgListLi = $(".img-list li");
      if (isSyncKey) {
        //只选中一个
        imgListLi.eq(isSyncKey).addClass("on");
        firstClickFn(imgListLi.eq(isSyncKey));
      }

      //已经消除一对泡泡，此次需隐藏对应的泡泡
      for (let i = 0; i < lastKey.length; i++) {
        imgListLi.eq(Number(lastKey[i].key)).find(".s-list").hide();
      }
      for (let i = 0; i < firstKey.length; i++) {
        imgListLi.eq(Number(firstKey[i].key)).find(".s-list").hide();
      }

      //所有消除后的数据集合
      completeArray = obj.completeArray;
      // 存储当前第一个选中的组内序号
      firstDataIndex = obj.firstDataIndex;
      // 赋值第一次和第二次的key集合
      isSyncFirstKey = firstKey;
      isSyncLastKey = lastKey;
      // 是否消除完毕
      if (completeArray.length == configData.source.options.length) {
        console.log("已经消除完毕");
        // 消除完毕后的展示
        removeCompleteFn(false);
      }

      SDK.setEventLock();
      return;
    }
    syncItemEventFn($this);
  }

  /**
   * 第一次点击后的事件
   */
  function firstClickFn($this) {
    // 获取第一次点击的序号和key值
    firstDataIndex = $this.attr("data-index");
    firstKey = Number($this.attr("data-key"));
    // 获取当前坐标
    curvOne.push($this.offset().top);
    curvOne.push($this.offset().left);

    SDK.setEventLock();
  }

  /**
   *
   * @param {*} 消消乐消除开始状态的数据存储
   */

  function completeDataFn($this) {
    let completeImg = "", //消除后的图片
      completeTitle = "", //文本内容
      completeAudio = "", //视频
      cover = $this.attr("data-cover"), //1:封面1  2: 封面2
      coverIndex = $this.attr("data-cover-index"), //1:选项图1  2:选项图2
      isThis = ""; //消除后的内容
    // if(cover == 2 && coverIndex == 2) {//选中的是否是封面
    if (cover == 2 && coverIndex == 2) {
      //选中的是否是封面
      isThis = $this;
    } else if (cover == 1 && coverIndex == 1) {
      isThis = $(".img-list li").eq(twoKey);
    } else {
      isThis = $(".img-list li").eq(firstKey);
    }

    // 消除泡泡后获取对应的数据
    completeImg = isThis.attr("data-img");
    completeTitle = isThis.attr("data-title");
    completeAudio = isThis.attr("data-audio");

    // 断线重连消除后纪录的key值
    isSyncLastKey.push({
      key: $this.attr("data-key"),
    });
    isSyncFirstKey.push({
      key: firstKey,
    });

    //存储消除后的数据
    completeArray.push({
      img: completeImg,
      title: completeTitle,
      audio: completeAudio,
      showTitle: isThis.attr("data-showtitle") || '1', // 添加showTitle字段
    });
  }

  /**
   * 正确与错误逻辑（音频播放）
   */
  function rightWrongAudio(type) {
    // soundControl().pause(); //停止
    return {
      // 正确答案
      right: function () {
        const audio = $(".rightAudio")[0];
        audio.currentTime = 0; // 重置声音时间为0，否则点击过快时这个声音上一次没播完 下一次不会播放
        SDK.playRudio({
          index: audio,
          syncName: $(".rightAudio").attr("data-syncaudio"),
        });
        // $(".rightAudio")[0].onended = function() {
        //     // soundControl().play();
        //     SDK.playRudio({
        //       index: $(".soundAudio")[0],
        //       syncName: $(".soundAudio").attr("data-syncaudio")
        //     })
        // }
      },
      // 错误答案
      wrong: function () {
        $(".wrongAudio")[0].currentTime = 0;
        SDK.playRudio({
          index: $(".wrongAudio")[0],
          syncName: $(".wrongAudio").attr("data-syncaudio"),
        });
        $(".wrongAudio")[0].onended = function () {
          // soundControl().play();
        };
      },
      //选中时
      dragAudio: function () {
        $(".dragAudio")[0].currentTime = 0;
        SDK.playRudio({
          index: $(".dragAudio")[0],
          syncName: $(".dragAudio").attr("data-syncaudio"),
        });
        $(".dragAudio")[0].onended = function () {
          if (type) {
            setTimeout(function () {
              if (isPlaySound) {
                $(".soundAudio")[0].currentTime = 0;
                SDK.playRudio({
                  index: $(".soundAudio")[0],
                  syncName: $(".soundAudio").attr("data-syncaudio"),
                });
              } else {
                SDK.pauseRudio({
                  index: $(".soundAudio")[0],
                  syncName: $(".soundAudio").attr("data-syncaudio"),
                });
              }
            }, 200);
          }
        };
      },
    };
  }

  async function showPerfectAnimation() {
    await feedbackAnimation('feedKey1');
    await renderResultCards();
    $(".result-success").show();
    // 显示或隐藏翻页按钮
    updatePaginationButtons();
    SDK.setEventLock();
  }

  /**
   * 消除完毕后的展示
   * isShowWin:是否显示特效
   */
  function removeCompleteFn(isShowWin) {
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_COMPLETE",
        data: {
          result: "success",
        },
      },
      USER_TYPE.TEA
    );
    // isShow 断线重连后不显示特效
    if (isShowWin) {
      setTimeout(showPerfectAnimation, 500);
    } else {
      time = 0;
    }

    //如果是老师 告知控制器，将授权状态改为老师控制，classstatus为6
    if (
      userType == "tea" &&
      (SDK.getClassConf().h5Course.classStatus == "5" ||
        SDK.getClassConf().h5Course.classStatus == "1")
    ) {
      SDK.bindSyncCtrl({
        type: "gameOverToCtrl",
        data: {
          CID: SDK.getClassConf().course.id + "", //教室id 字符串
          operate: "1",
          data: [
            {
              key: "classStatus",
              value: "6",
              ownerUID: SDK.getClassConf().user.id,
            },
          ],
        },
      });
    }
  }

  /**
   * 渲染结果卡片
   */
  async function renderResultCards() {
    // 清空当前显示的卡片
    $(".result-success ul li").empty();

    // 筛选只有showTitle='1'的卡片
    const filteredArray = completeArray.filter(item => item.showTitle === '1');

    const totalPages = Math.ceil(filteredArray.length / itemsPerPage);
    const start = currentPage * itemsPerPage;
    const end = Math.min(start + itemsPerPage, filteredArray.length);

    console.log("渲染结果卡片", currentPage, start, end);

    // 只渲染当前页的卡片
    for (let i = start; i < end; i++) {
      const value = filteredArray[i];
      const localIndex = i - start; // 当前页内的索引位置
      const n = i * 10; // 去除一下gif和png的缓存

      const resultStr = $(`<div class="list">
                        <img src="${value.img}" class="img" />
                        <p class="complete-title">${value.title}</p>
                        <div class="example-audio" id="audioExample-${i}" data-index="${i}">
                          <audio src="${
        value.audio
      }" data-syncaudio="audioExample-${i}"></audio>
                        </div></div>`);
      $(".result-success ul li").eq(localIndex).append(resultStr);

      try {
        // 使用现有的initOrGetLottieAnimation函数初始化动画
        await initOrGetLottieAnimation(
          `laba${i}`,
          `#audioExample-${i}`,
          './image/laba.json',
          false // 不循环播放
        );
        console.log(`已初始化laba${i}动画:`, lottieInstances[`laba${i}`]);
      } catch (error) {
        console.error(`初始化laba${i}动画失败:`, error);
      }
    }
  }

  /**
   * 更新翻页按钮状态
   */
  function updatePaginationButtons() {
    // 筛选只有showTitle='1'的卡片
    const filteredArray = completeArray.filter(item => item.showTitle === '1');
    const totalPages = Math.ceil(filteredArray.length / itemsPerPage);

    // 如果结果数量小于等于每页显示数量，隐藏翻页按钮
    if (totalPages <= 1) {
      $(".leftBtn, .rightBtn").hide();
      return;
    }

    // 否则显示翻页按钮
    $(".leftBtn, .rightBtn").show();

    // 更新左右按钮状态
    if (currentPage === 0) {
      $(".leftBtn").addClass("disable-left");
    } else {
      $(".leftBtn").removeClass("disable-left");
    }

    if (currentPage >= totalPages - 1) {
      $(".rightBtn").addClass("disable-right");
    } else {
      $(".rightBtn").removeClass("disable-right");
    }
  }

  /**
   * 结局点播事件
   */
  let isPlaySound = true;
  $(".result-success ul").on("click touchstart", "li", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_CARD",
        teaData: {
          item: $(this).index() + 1,
        },
      },
      USER_TYPE.TEA
    );
    if (!isSync) {
      $(this).trigger("synResultClick");
      return;
    }
    // if (window.frameElement.getAttribute('user_type') == 'tea') {
    SDK.bindSyncEvt({
      sendUser: "",
      receiveUser: "",
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "synResultClick",
      otherInfor: {
        index: $(this).index(),
        completeArray: completeArray,
      },
      recoveryMode: "1",
    });
    // }
  });

  $(".result-success ul").on("synResultClick", "li", function (e, message) {
    // 断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor;
      let index = obj.index;
      completeArray = obj.completeArray;
      // 消除完毕后的展示
      removeCompleteFn(false);
      $(".result-success ul li").eq(index).addClass("on");

      SDK.setEventLock();
      return;
    }
    let audio = $(this).find("audio")[0];
    const currentIndex = $(this).find(".example-audio").attr("data-index");
    // 开始播放
    if (isPlaySound) {
      audio.currentTime = 0;
      lottieAnimations.play(lottieInstances[`laba${currentIndex}`]);
      SDK.playRudio({
        index: audio,
        syncName: $(this).find("audio").attr("data-syncaudio"),
      });
    } else {
      //停止播放
      lottieAnimations.stop(lottieInstances[`laba${currentIndex}`]);
      SDK.pauseRudio({
        index: audio,
        syncName: $(this).find("audio").attr("data-syncaudio"),
      });
    }

    // 播放结束
    audio.onended = function () {
      lottieAnimations.stop(lottieInstances[`laba${currentIndex}`]);
      isPlaySound = true;
    }.bind(this);

    // 确保只注册一次
    // audioEndedEventListenerMap.get(audio) || (function () {
    //   audio.addEventListener('ended', onAudioEnded);
    //   audioEndedEventListenerMap.set(audio, true);
    // })();
    // 选中元素添加状态
    $(this).addClass("on").siblings().removeClass("on");
    SDK.setEventLock();
  });

  // 一个audio是否注册过ended事件的映射
  // const audioEndedEventListenerMap = new Map();

  // function onAudioEnded (ev) {
  //   const gif = $(ev.target).closest(".example-audio").find(".gif");
  //   const png = $(ev.target).closest(".example-audio").find(".png");
  //   gif.hide();
  //   png.show();
  //   isPlaySound = true;
  // }

  function recordEliminatedKeys(firstKey, twoKey) {
    eliminatedKeys.push(firstKey, twoKey);
    // 只要有未展示元素且有被消除的位置，就补
    if (eliminatedKeys.length === 4 && nextOptions.length > 0) {
      console.log("recordEliminatedKeys-------", eliminatedKeys);
      // 直接添加新元素
      addNewOptionsToEliminated();
    }
  }

  function addNewOptionsToEliminated() {
    //1.从nextOptions取出前两个元素（如果有一个就取一个）
    const newOptionsCount = Math.min(2, nextOptions.length);
    const selectedOptions = nextOptions.slice(0, newOptionsCount);

    //2.从nextOptions移除取出的元素
    nextOptions = nextOptions.slice(newOptionsCount);

    //3.把取出前两个元素拆分成4个元素 逻辑看下optionsListTemplate方法
    const templateData = [];

    // 针对每个选项创建两个元素（选项图1和选项图2）
    selectedOptions.forEach((option, index) => {
      const dataIndex = options.length + index; // 新的组内序号

      // 处理imgNew属性
      if (option.imgType == 1) {
        option.imgNew = option.img;
      }

      // 选项图1
      templateData.push({
        img: option.img,
        cover: option.cover || 1,
        title: option.title,
        audio: option.audio,
        audioTime: option.audioTime,
        showTitle: option.showTitle || '1', // 添加showTitle属性
        index: 1, // 标示是选项图1
        coverIndex: 1,
      });

      // 选项图2
      templateData.push({
        img: option.imgNew,
        cover: option.cover || 2,
        title: option.title,
        audio: option.audio,
        audioTime: option.audioTime,
        showTitle: option.showTitle || '1', // 添加showTitle属性
        index: 2, // 标示是选项图2
        coverIndex: 2,
      });
    });
    templateData.forEach((item, index) => {
      updateLiWithOption(item, eliminatedKeys[index]);
    });

    //5.清空eliminatedKeys
    eliminatedKeys = [];
  }

  function updateLiWithOption(option, key) {
    const $li = $(".img-list li").eq(key);

    // 完全重置元素状态
    $li.removeClass("non-clickable on error error-ani");

    // 更新数据属性
    $li.attr("data-cover", option.cover);
    $li.attr("data-cover-index", option.index); // 使用传入的index
    $li.attr("data-title", option.title);
    $li.attr("data-img", option.img);
    $li.attr("data-audio", option.audio);
    $li.attr("data-audio-time", option.audioTime);
    $li.attr("data-showtitle", option.showTitle || '1'); // 添加showTitle属性

    // 更新显示内容
    $li.find(".dataimg").attr("src", option.img);
    $li.find(".data-title").text(option.title);
    // 显示被隐藏的元素
    $li.find(".s-list").fadeIn(800);
  }

  let leftBtnClick = false;
  $(".leftBtn").syncbind(
    "click touchstart",
    function (dom, next) {
      if (leftBtnClick) {
        return;
      }
      if (dom.is(".disable-left")) {
        return;
      }
      leftBtnClick = true;
      if (!isSync) {
        next(false);
      } else {
        next();
      }
    },
    function () {
      if (currentPage > 0) {
        currentPage--;
        renderResultCards();
        updatePaginationButtons();
        // 移除结果卡片的选中效果
        $(".result-success ul li").removeClass("on");
      }
      leftBtnClick = false;
      SDK.setEventLock();
    }
  );

  let rightBtnClick = false;
  $(".rightBtn").syncbind(
    "click touchstart",
    function (dom, next) {
      if (rightBtnClick) {
        return;
      }
      if (dom.is(".disable-right")) {
        return;
      }
      rightBtnClick = true;
      if (!isSync) {
        next(false);
      } else {
        next();
      }
    },
    function () {
      // 筛选只有showTitle='1'的卡片
      const filteredArray = completeArray.filter(item => item.showTitle === '1');
      const totalPages = Math.ceil(filteredArray.length / itemsPerPage);
      if (currentPage < totalPages - 1) {
        currentPage++;
        renderResultCards();
        updatePaginationButtons();
        // 移除结果卡片的选中效果
        $(".result-success ul li").removeClass("on");
      }
      rightBtnClick = false;
      SDK.setEventLock();
    }
  );
});
