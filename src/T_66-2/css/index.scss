@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import "../../common/template/ribbonWin/style.scss";
@import "../../common/template/hint/style.scss";

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

body {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: fixed;
}

// 功能遮罩层
.funcMask,
.tea-stu-not-in {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 21;
  display: none;

  .startBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 7.02rem;
    width: 10.5rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0.5rem;

    .demoTextBox {
      width: 9.26rem;
      height: 3.9rem;
      position: absolute;
      left: 50%;
      top: 0.64rem;
      transform: translate(-50%);
      border-radius: 0.3rem;
      background: rgba(0, 0, 0, 0.2);
      text-align: center;

      .startMsg {
        width: 6.75rem;
        height: 2.88rem;
        margin-top: 0.4rem;
      }
    }

    .demoBtnBox {
      width: 5.72rem;
      height: 1.14rem;
      transform: translate(-50%);
      // background: rgba(0, 0, 0, 0.2);
      // text-align: center;
      position: absolute;
      left: 50%;
      bottom: 0.75rem;

      .demo-btnStu {
        width: 2.14rem;
        height: auto;
        cursor: pointer;
        display: none;
      }

      .startBtn {
        width: 2.03rem;
        height: auto;
        cursor: pointer;
        display: inline-block;
        margin-right: 0;
        margin-left: 1.55rem;
      }
    }
  }

  .timeChangeBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 4.8rem;
    width: 7.2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.5rem;

    .timeBg {
      width: 3.79rem;
      height: 3.84rem;
      position: absolute;
      top: 1rem;
      background: url(../image/timeBg.png) no-repeat;
      background-size: 100% 100%;
      left: 50%;
      margin-left: -1.9rem;
      top: 50%;
      margin-top: -1.92rem;

      .numberList {
        width: 1.5rem;
        height: 1.5rem;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        top: 0;
        margin: auto;
        background: url(../image/number1.png) no-repeat;
        background-size: 6rem 100%;
        background-position-x: 0.1rem;
      }
    }
  }
}

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;
  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.subject {
  .img-list {
    display: flex;
    justify-content: center;
    margin-top: 1.7rem;
    ul {
      width: 8rem;
      // position: relative;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      li {
        position: relative;
        width: 3.44rem;
        height: 3.44rem;
        margin-bottom: 0.44rem;
        border-radius: 0.3rem;
        // border-bottom: .15rem rgba($color: #000, $alpha: 0.2) solid;

        .lottie-wrapper {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 21;
          pointer-events: none;

          .lottie-1-container,
          .lottie-2-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;

            &.lottie-2-container {
              // 该动画太小 把它放大到原来的2倍
              transform: scale(2.8);
            }

            svg {
              position: absolute;
              top: 0;
              left: 0;
            }
          }
        }

        .s-list {
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          width: 3.44rem;
          height: 3.44rem;
          background: #fffaec;
          // border-radius: .3rem;
          border-radius: 0.44rem;
          // border:.1rem #FFFAEC solid;
          //border-bottom: .15rem rgba($color: #000, $alpha: 0.2) solid;
          // box-shadow: 0.02rem 0.045rem 0.3rem 0.01rem rgba(10,10,10,.25);
          // cursor: not-allowed;
          cursor: pointer;
          z-index: 1;
          text-align: center;
          .success-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 3.44rem;
            height: 3.44rem;
            border-radius: 0.3rem;
            z-index: 5;
            overflow: hidden;
            .dataimg {
              width: 3.44rem;
              height: 3.44rem;
            }
          }
          .img-bj {
            position: absolute;
            top: -2.8rem;
            left: -2.6rem;
            width: 8.6rem;
            height: 8.6rem;
            display: none;
            z-index: 1;
          }
          .data-title {
            background: url("../image/round_bj.png");
            background-size: 100%;
            width: 4.18rem;
            height: 0.78rem;
            line-height: 0.78rem;
            text-align: center;
            font-size: 0.47rem;
            color: #33506c;
            // position: absolute;
            // left: 0;
            // bottom: 0;
            // z-index: 10;
            margin-left: 0.18rem;
            overflow: hidden;
            font-style: normal;
            display: none;
          }
        }
      }
      .successful-list {
        .s-list {
          height: 3.9rem;
          z-index: 20;
          .success-img {
            left: -0.6rem;
            top: -0.6rem;
            background: #fffaec;
            border-radius: 0.3rem;
            width: 4.54rem;
            height: 4.54rem;
          }
          .img-bj {
            display: block;
          }
          .data-title {
            display: block;
          }
        }
      }
      .on {
        @mixin toggle-on-rect {
          width: 3.94rem;
          height: 3.94rem;
          top: -0.2rem;
          left: -0.3rem;
        }
        .s-list {
          @include toggle-on-rect();
          z-index: 20;
          overflow: hidden;
          border-bottom: none;
          outline: 0.1rem solid #ffc71f;
          .success-img {
            // border:.1rem #FFC71F solid;
            width: 3.94rem;
            height: 3.94rem;
            .dataimg {
              width: 3.94rem;
              height: 3.94rem;
            }
          }
        }

        .lottie-wrapper {
          @include toggle-on-rect();
        }
      }

      @keyframes error-ani {
        0% {
          transform: rotateZ(15deg);
        }
        20% {
          transform: rotateZ(-15deg);
        }
        40% {
          transform: rotateZ(15deg);
        }
        60% {
          transform: rotateZ(-15deg);
        }
        80% {
          transform: rotateZ(15deg);
        }
        100% {
          transform: rotateZ(0deg);
        }
      }

      .error {
        .s-list {
          // border:.1rem red solid;
          // border会占用元素的盒模型，导致出现和消失时元素会突然出现距离为边框宽度x2的偏移 使用outline没有这个问题
          outline: 0.1rem solid red;
        }
      }
      .error-ani {
        animation: error-ani 0.5s both ease-in;
        -webkit-animation: error-ani 0.5s both ease-in;
        -moz-animation: error-ani 0.5s both ease-in;
        -o-animation: error-ani 0.5s both ease-in;
        -ms-animation: error-ani 0.5s both ease-in;
      }
    }
  }
  .img-list-cursor > ul > li > .s-list {
    cursor: pointer;
  }
}

//终局特效
// .perfectBox {
//   overflow: hidden;
//   position: absolute;
//   top: 50%;
//   left: 50%;
//   width: 7.34rem;
//   height: 7.33rem;
//   transform: translateX(-50%) translateY(-50%);
//   // background-color: red;

//   .light {
//       position: absolute;
//       z-index: 100;
//       width: 6.87rem;
//       height: 6.87rem;
//       animation: rotation 6s linear infinite;
//   }

//   @keyframes rotation {
//       from {
//           -webkit-transform: rotate(0deg);
//       }

//       to {
//           -webkit-transform: rotate(360deg);
//       }
//   }

//   .perfect {
//       position: absolute;
//       top: 1.2rem;
//       z-index: 101;
//       width: 7.34rem;
//       height: 4.53rem;
//   }

//   .numBox {
//       position: absolute;
//       bottom: 0;
//       width: 3.7rem;
//       height: 1.38rem;
//       background-color: #fff;
//       border-radius: .68rem;
//       z-index: 102;
//       transform: translateX(-50%);
//       left: 50%;

//       img {
//           width: 1.11rem;
//           height: 1.17rem;
//           position: absolute;
//           left: 0.46rem;
//           top: 50%;
//           transform: translateY(-50%);
//       }

//       label {
//           display: inline-block;
//           width: 0.03rem;
//           height: 0.7rem;
//           background: rgba(147, 147, 147, 1);
//           position: absolute;
//           left: 1.93rem;
//           top: 50%;
//           transform: translateY(-50%);
//       }

//       .score {
//           position: absolute;
//           left: 2.27rem;
//           top: 50%;
//           transform: translateY(-50%);
//           color: rgba(255, 108, 15, 1);
//           font-size: 0.91rem;
//       }
//   }
// }
.win-perfect-container {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 100;
  display: none;
  background: rgba(0, 0, 0, 0.4);
}

// 终局列表
.result-success {
  display: none;
  .lrbtn-bg {
    width: 1rem;
    height: 1.26rem;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    display: none;
  }

  .leftBtn {
    left: 0.2rem;
    z-index: 300;
    background: url(../image/leftBtn.png) no-repeat;
    background-size: auto 100%;
  }

  .disable-left {
    z-index: 300;
    background: url(../image/left-d.png) no-repeat;
    background-size: auto 100%;
    cursor: not-allowed !important;
  }

  .rightBtn {
    z-index: 300;
    right: 0.2rem;
    background: url(../image/right.png) no-repeat;
    background-size: auto 100%;
  }

  .disable-right {
    z-index: 300;
    background: url(../image/right-d.png) no-repeat;
    background-size: auto 100%;
    cursor: not-allowed !important;
  }
  .success-mask {
    position: fixed;
    z-index: 200;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba($color: #000, $alpha: 0.6);
  }
  .content {
    position: absolute;
    top: 50%;
    left: 1.3rem;
    transform: translateY(-50%);
    z-index: 201;

    ul {
      display: flex;
      width: 16.56rem;
      flex-wrap: nowrap;
      justify-content: space-between;
      li {
        display: block;
   
        .list {
          box-sizing: border-box;
          width: 3.84rem;
          height: 6.44rem;
          background: #87cf84;
          border: 0.05rem solid #247821;
          border-radius: 0.2rem;
          text-align: center;
          padding: 0.2rem 0.2rem 0.3rem 0.2rem;
          cursor: pointer;

          .img {
            box-sizing: border-box;
            display: block;
            width: 100%;
            height: 3.44rem;
            border-radius: 0.2rem;
            border: 0.05rem solid #247821;
            overflow: hidden;
          }
          .complete-title {
            margin-top: 0.08rem;
            width: 3.34rem;
            height: 1.2rem;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            overflow: hidden;
            font-weight: bold;
            font-size: 0.5rem;
            color: #ffffff;
            line-height: 0.6rem;
            font-style: normal;
            word-break: break-word;
            overflow-wrap: break-word;
            white-space: normal;
          }
          // 音频播放
          .example-audio {
            width: 0.96rem;
            height: 0.96rem;
            margin: .26rem auto auto auto ;

            >audio{
              display: none;
            }
          }
        }
      }
      .on {
        .list {
          border-color: #ffca02;
          box-shadow: 0 0 0.3rem 0.1rem rgba(255, 202, 2, 0.7);
        }
      }
    }
  }
}
