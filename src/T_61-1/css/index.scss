@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
body{
	overflow: hidden;
}

.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 2.2rem;
	position: absolute;
    right: 0px;
	.desc {
		top: 0.6rem;
	}
	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
}
.container {
	background-size: auto 100%;
	position: relative;
	 //记分台
	 .intSatge {
        position: absolute;
        bottom: 2.4rem;
        left: 1.7rem;
        width: 1.7rem;
        height: .6rem;
        border-radius: .3rem;
        .countImg {
            position: absolute;
            left: -1.1rem;
            top: -.65rem;
            width: 2.15rem;
            height: 1.67rem;
            background-size: 2rem 100%;
			background-position-x: 0rem;
			background-repeat: no-repeat;
            transform: scale(.65);
            z-index: 1;
        }
        .wrong {
            background-position-x: -10.75rem;
        }
        .score {
            width: 100%;
            height: 100%;
            border-radius: .3rem;
            position: absolute;
            left: 0;
            top: 0;
            font-size: .46rem;
            color: #f9bc2e;
            box-sizing: border-box;
            padding-left: .7rem;
			line-height: .6rem;
			font-weight:400;
			color:rgba(255,100,14,1);
        }
    }
    // 计时器
    .time {
        position: absolute;
        left: 1.3rem;
        bottom: 1.05rem;
        height: 1.2rem;
        width: 5.5rem;
        .proprogressBarBox {
            position: absolute;
            left: .5rem;
            bottom: 0;
            width: 5rem;
            height: .65rem;
            border: .1rem solid #00a884;
            border-radius: 1rem;
            background: #fff;
            .proprogressBar {
                width: 100%;
                height: 100%;
                background: #f9bc2e;
                border-radius: 1rem;
                transition: .5s;
            }
        }
        .watch {
            position: absolute;
            left: -.4rem;
            top: 0.1rem;
            width: 2.32rem;
            height: 1.56rem;
			background-image: url('../image/horn-sprite.png');
			background-size: 18.69rem 1.56rem;
		}
    }
	// 遮罩层
    .mask,.tea-stu-not-in {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
		z-index: 21;
		display: none;
        .startBox {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 5.2rem;
            width: 7.78rem;
            background: rgba(0,0,0,.8);
            border-radius: .5rem;
            .startFont {
				width:1.70rem;
                height: auto;
                position: absolute;
				left:50%;
				transform: translate(-50%);
            }
            .startBtn {
                width: 1.85rem;
                height: auto;
                position: absolute;
                left: 4.28rem;
                bottom: .65rem;
                cursor: pointer;
			}
			.hint-btnStu{
				width: 2.29rem;
                height: auto;
                position: absolute;
                left: 1.61rem;
                bottom: .65rem;
                cursor: pointer;
			}
			.startMsg {
				width: 5.51rem;
				height: 0.59rem;
				position: absolute;
				top: 40%;
				left: 50%;
				transform: translate(-50%, -50%);
			}
        }
        .timeChangeBox {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 4.8rem;
            width: 7.2rem;
            background: rgba(0,0,0,.8);
            border-radius: .5rem;
            .timeBg {
                width:3.79rem;
                height: 3.84rem;
                position: absolute;
                top: 1rem;
                background: url(../image/timeBg.png) no-repeat;
                background-size: 100% 100%;
                left: 50%;
                margin-left: -1.9rem;
                top: 50%;
                margin-top: -1.92rem;
                .numberList {
                    width: 1.5rem;
                    height: 1.5rem;
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    top:0;
                    margin: auto;
                    background: url(../image/number1.png) no-repeat;
                    background-size: 6rem 100%;
                    background-position-x: .1rem;
                }
            }
        }
	}
	//终局页
	.endGame {
		position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
		z-index: 20;
		background:rgba(0,0,0,0.5);
		display: none;
		.endGameScore {
			width:4.63rem;
			height:1.52rem;
			background:rgba(255,255,255,1);
			border-radius:0.77rem 0.76rem 0.76rem 0.75rem;
			position: absolute;
			left: 50%;
			transform: translate(-50%);
			img {
				width: 1.21rem;
				height: 1.27rem;
				position: absolute;
				left: 0.75rem;
				top:0.11rem;
			}
			label {
				display: inline-block;
				width: 0.03rem;
				height: 0.7rem;
				background:rgba(147,147,147,1);
				position: absolute;
				left: 2.39rem;
				top: 0.41rem;
			}
			.score {
				position: absolute;
				left: 2.84rem;
				top: 0.15rem;
				font-weight:400;
				color:rgba(255,108,15,1);
			}
		}
		.hiddenContain{
			overflow: hidden;
			width: 85%;
			position: absolute;
			top: 50%;
			left: 50%;
			text-align: center;
			transform: translate(-50%, -50%);
			height: 6.44rem;
			ul {
				height: 6.9rem;
				overflow-x: auto;
				overflow-y: hidden;
				white-space: nowrap;
				li {
					width: 4.5rem;
					height: 6.44rem;
					background:rgba(255,255,255,1);
					border-radius: 0.76rem;
					display: inline-block;
					margin-right: 0.9rem;
					// background-size: 90%;
					background-origin: content;
					background-repeat: no-repeat;
					background-position: center center;
					opacity: 0;
				}
	
			}
		}
		.scrollBtn {
			position: absolute;
			bottom: 0.5rem;
			left: 50%;
			transform: translate(-50%);
			span {
				display: inline-block;
				width: 1.36rem;
    			height: 1.36rem;
				cursor: pointer;
				left: .92rem;
				
			}
			.leftScroll {
				background: url(../image/left.png) no-repeat;
				background-size: contain;
			}
			.rightScroll {
				background: url(../image/right.png) no-repeat;
				background-size: contain;
			}
		}
		
	}

}

.big{
	transform: scale(1.05);
}

.content {
	width: 16.4rem;
	height: 4.5rem;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
	margin-left: 1.4rem;
	position: absolute;
	bottom: .9rem;
}
.main {
	width: 100%;
	height: 100%;
	.optionUl {
		position: relative;
		width: 100%;
		height: 100%;
		li {
			position: absolute;
			cursor: pointer;
			background-size: 100% 100%;
			background-repeat: no-repeat;	
			display: none;	
		}
		.no-choose-right {
			padding: 0.3rem;
			border:0.05rem solid rgba(255,255,0,1);
			border-radius:0.58rem;
			animation: imType1 1.5s 1 forwards;

		}
	}
	.right-top-hint {
		display: none;
		position: absolute;
		right: 0;
		top: 0;
		width:1.95rem;
		height:0.67rem;
		background:rgba(70,70,70,1);
		opacity:0.63;
		border-bottom-left-radius: 0.22rem;
		color: #fff;
		line-height:0.67rem;
		font-size: 0.43rem;
		img {
			width: 0.51rem;
			height: 0.53rem;
			margin-left: 0.22rem;
		}
	}
}
.doneTip{
    width: 7.91rem;
    height: 1rem;
	border-radius: 0.2rem .2rem 0 0;
	position: absolute;
	margin-left: -3.5rem;
	left:50%;
	bottom: 0;
	background: rgba(255,255,255,.7);
	display: none;
	font-size: .3rem;
	p{
		height: 100%;
		width: 5.14rem;
		line-height: 1rem;
		text-align: center;
		padding-left: 0.53rem
	}
	.btn {
		position: absolute;
		top: .2rem;
		height: .23rem;
		padding: 0.17rem 0.26rem;
		color:#fff;
		text-align: center;
		
		line-height: 0.23rem;
		border-radius: .3rem;
		cursor: pointer;
	}
	.hint-btn{
		background: #f1a91e;
		left: 5.6rem;
	}
}
.boxList {
	position: absolute;
	left: 1.08rem;
	top: .3rem;
	width: 17rem;
	height: 9.8rem;
	z-index: -1;
	.boxUl {
		position: absolute;
		left: .2rem;
		top: .7rem;
		width: 16rem;
		height: 8.4rem;
		display: flex;
		flex-wrap: wrap;
		li {
			// background: red;
			margin-left: .01rem;
			width: .39rem;
			height: .39rem;
		}
	}
}
//小手点击动画
.hand{
	position: absolute;
	left: 1.4rem;
	top: 1.3rem;
	margin-left: -0.35rem;
	background: url('../image/hands.png');
	background-size: 7.2rem 1.8rem;
	cursor: pointer;
	opacity: 0;
	z-index: 99;
	width: 0px;
	height: 0px;
}
.handAnimation{
	opacity: 1;
	width:1.8rem;
	height:1.8rem;
	left: 5.5rem;
	top: 6rem;
	animation-name:handLclick, handHide;
	animation-duration:1s, 1S;
	animation-timing-function:steps(4, end), linear;
    animation-delay:1s,4s;
    animation-iteration-count:3, 1;
    animation-fill-mode:forwards, forwards;
	animation-direction:normal, normal;
	
	
	-webkit-animation-name:handLclick, handHide;
	-webkit-animation-duration:1s, 1S;
	-webkit-animation-timing-function:steps(4, end), linear;
    -webkit-animation-delay:1s,4s;
    -webkit-animation-iteration-count:3, 1;
    -webkit-animation-fill-mode:forwards, forwards;
	-webkit-animation-direction:normal, normal;
}
@keyframes handLclick {
	0%{background-position: 0 0;}
	100%{background-position:133% 0;}
}
@-webkit-keyframes handLclick {
	0%{background-position: 0 0;}
	100%{background-position:133% 0;}
}
@keyframes handHide {
	0%{opacity: 0;width: 0px;}
	100%{opacity: 0;width: 0px;}
}
@-webkit-keyframes handHide {
	0%{opacity: 0;width: 0px;}
	100%{opacity: 0;width: 0px;}
}

//喇叭动效
.hornAnimation{
	animation: hornPlay  1s steps(3) infinite;
	-webkit-animation: hornPlay  1s steps(3) infinite;
}
@keyframes hornPlay {
	0%{background-position: 0 0;}
	100%{background-position:18.69rem 0;}
}
@-webkit-keyframes hornPlay {
	0%{background-position: 0 0;}
	100%{background-position:18.69rem 0;}
}

//选项上下抖动
.optionUDAni {
	animation: optionUD linear 1s 2s  infinite alternate;
	-webkit-animation: optionUD linear 1s 2s  infinite alternate;
}
@keyframes optionUD {
	0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(10px);
    }
}
@-webkit-keyframes optionUD {
	0% {
        transform: translateY(0px);
    }
    100% {
        transform: translateY(10px);
    }
}
//静态图正确反馈
.imgType1Right {
	animation: imType1 1.5s 1 forwards;
}
@keyframes imType1 {
	0% {
        transform: scale(1.1, 1.1);
	}
    100% {
		transform: scale(0, 0);
		
		// width:0;
        // height:0;
        // overflow:hidden;
    }
}
@keyframes imType1 {
	0% {
		transform: scale(1.1, 1.1);	
	}
    100% {
		transform: scale(0, 0);
		// width:0;
        // height:0;
        // overflow:hidden;
    }
}
//2帧雪碧图正确反馈
.imgType2Right {
	animation: imgType2 0.5s steps(2)  1 forwards;
}
@keyframes imgType2 {
	0%{background-position: 0 0;transform: scale(1, 1);};
	100%{background-position:200% 0;transform: scale(0, 0);}
}
@-webkit-keyframes imgType2 {
	0%{background-position: 0 0;transform: scale(1, 1);};
	100%{background-position:200% 0;transform: scale(0, 0);}
}
//3帧雪碧图正确反馈
.imgType3Right {
	animation: imgType3 0.5s steps(3)  1 forwards;
}
@keyframes imgType3 {
	0%{background-position: 0 0;transform: scale(1, 1);};
	100%{background-position:150% 0;transform: scale(0, 0);}
}
@-webkit-keyframes imgType3 {
	0%{background-position: 0 0;transform: scale(1, 1);};
	100%{background-position:150% 0;transform: scale(0, 0);}
}
//错误反馈动画
.shake{
	animation: shakeUp 0.4s both ease-in;
}
@keyframes shakeUp{
	0% {
        transform: translateX(10px);
    }
    20% {
        transform: translateX(-10px);
    }
    40% {
        transform: translateX(10px);
    }
    60% {
        transform: translateX(-10px);
    }
    80% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0px);
    }
}
* { 
    touch-action: pan-y; 
}
