"use strict"
import '../../common/js/common_1v1.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function() {
    let staticData = configData.source;
    let audioList = staticData.audioList; //上传语音
    let imgList = staticData.imgList; //上传图片
    let frequencyList = staticData.frequencyList; //每轮数据
    let timeInt = null; // 定时器
    let timeIndex = 0; //取第几轮的数据
    let time = 20; //每轮时间  默认20
    let userType = window.frameElement && window.frameElement.getAttribute('user_type'); //用户身份学生还是老师
    let classStatus = 1; //教室状态
    let score = 0; //分数
    let isAnyOneOut = false;
    let rightArr = {
        allRightArr: [],
        currentRightNum: 0
    }; //存储全部正确选项 和 已答对正确选项个数（如果全部答对停止计时切换下一轮）
    //判断是否使用默认背景图片
    let isFirst = true;
    if (configData.bg == '') {
        $(".container").css({
            'background-image': 'url(./image/defaultBg.jpg)',
        })
    }
    //生成像素表格
    renderPx();

    function renderPx() {
        let liHtml = '';
        for (let i = 1; i < 801; i++) {
            liHtml += `
				<li class="pos_${i}"></li>	`
        }
        $('.boxUl').html(liHtml);
    }
    //小手位置初始化
    initHand();

    function initHand() {
        let firtstPosition = frequencyList[0].options[0].position;
        if (firtstPosition) {
            let left = ($('.pos_' + firtstPosition).offset().left - $('.container').offset().left) / window.base + 2 + 'rem';
            let top = ($('.pos_' + firtstPosition).offset().top - $('.container').offset().top) / window.base + 2 + 'rem';
            $('.hand').css({
                left: left,
                top: top
            });
        }
    }

    //计分台、正确反馈声音配置
    sorceRightAudio();

    function sorceRightAudio() {
        $(".countImg").css({
            'background-image': 'url(' + staticData.countImg + ')'
        });
        $(".rightAudio").attr("src", staticData.rightAudio);
    }
    //积分台和计时器初始化
    function sorceTimeInit() {
        rightArr.currentRightNum = 0;
        $('.scoreNum').html(score);
        moveItemW = proprogressBarW / window.base; // 获取时间轴长度
        everLen = moveItemW / time // 每秒移动距离
        moveItemW = proprogressBarW / window.base
        everLen = moveItemW / time;
        $('.proprogressBar').css('width', '100%').attr("data_width", moveItemW);
    }


    //轮数初始化(选项、声音、时间)
    let timePlay = null;

    function optionsInit(index, tip) {
        if (index > frequencyList.length - 1) {
            return;
        }
        hideOptionArr = [];
        let options = frequencyList[index].options;
        let audio = frequencyList[index].audio;
        time = frequencyList[index].time;
        let preList = [],
            preImgs = [];
        $(".optionUl").html("");
        options.forEach(function(img, imgIndex) {
            if (img.result == 1) {
                rightArr.allRightArr.push(imgList[img.src - 1].image);
            }
            let liEle = "";
            let currentPosition = img.position;
            let left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
            liEle = $('<li  class="optionLi"></li>');
            liEle.attr({
                result: img.result,
                'data-syncactions': 'freq' + index + 'bubble' + imgIndex + ''
            });
            liEle.css({
                left: left,
                top: top,
                'background-image': 'url(' + imgList[img.src - 1].image + ')',
            });
            $(".optionUl").append(liEle)
            $(".optionLi").fadeIn(500 * imgIndex).addClass("optionUDAni");
            preList.push(imgList[img.src - 1].image);
        });
        $.when(preloadImg(preList, preImgs)).done(
            function() {
                preImgs.forEach(function(item, index) {
                    initImg($(".optionLi").eq(index), item);
                })
            }
        );
        $(".frequencyAudio").attr("src", audioList[audio - 1].audio);
        timePlayFun().pause(); //先停止一次
        timePlayFun().play(tip); //调用播放
        optionClickStus = true;
    }
    //播放喇叭处选项语音
    function timePlayFun() {
        return {
            'play': function(tip) {
                setTimeout(() => {
                    if (timeInt || tip) {
                        $(".frequencyAudio")[0].currentTime = 0; //从头播放
                        SDK.playRudio({
                            index: $(".frequencyAudio")[0],
                            syncName: $(".frequencyAudio").attr("data-syncaudio")
                        })
                        $(".watch").addClass("hornAnimation");
                    }
                }, 200);

            },
            'pause': function() {
                clearInterval(timePlay);
                SDK.pauseRudio({
                    index: $(".frequencyAudio")[0],
                    syncName: $(".frequencyAudio").attr("data-syncaudio")
                })
                timePlay = null;
                $(".watch").removeClass("hornAnimation");
            }
        }
    }
    // 3 2 1倒计时
    function threeTwoOne() {
        let q = 1;
        $('.startBox').hide().siblings('.timeChangeBox').show().find('.numberList');
        SDK.playRudio({
            index: $('.timeLowAudio_' + q).get(0),
            syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
        })
        let audioPlay = setInterval(function() {
            q++;
            if (q > 4) {
                clearInterval(audioPlay);
                //这个地方才是3210倒计时结束
                SDK.setEventLock();
                $('.mask').hide();
                $('.timeChangeBox').hide();
                optionsInit(timeIndex);
                addEvent();
                sorceTimeInit();
                timeStartFn();
                gameProcessBidSync();
            } else {
                // $('.timeLowAudio_' + q).get(0).play();
                SDK.playRudio({
                    index: $('.timeLowAudio_' + q).get(0),
                    syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
                })
                $('.numberList').css({
                    'background-position-x': -(1.5 * (q - 1)) + 'rem'
                })
            }
        }, 1000)
    }
    //倒计时
    let proprogressBarW = $('.proprogressBar').width();
    let moveItemW, everLen;

    function timeStartFn() {
        window.localStorage.setItem('countDownTime', time);
        if (timeInt) {
            clearInterval(timeInt);
            timeInt = null;
        }
        timeInt = setInterval(function() {
            if (isAnyOneOut) {
                clearInterval(timeInt);
                timePlayFun().pause();
            }
            time--;

            isEndGameFun(time);
            window.localStorage.setItem('countDownTime', time);
            moveItemW = moveItemW - everLen;
            $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);
        }, 1000);
    };
    //游戏过程中上报数据方便重连（切换下一轮、点击选项）
    function gameProcessBidSync() {
        if (isSync) {
            SDK.bindSyncEvt({
                index: 'runing',
                eventType: 'click',
                method: 'event',
                syncName: 'syncRuning',
                otherInfor: {
                    timeIndex: timeIndex,
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    hideOptionArr: hideOptionArr
                },
                recoveryMode: '1'
            });
        }
    };
    //初始化(停止点击、清除计时器、开始下局)
    function isEndGameFun(time) {
        if (rightArr.currentRightNum >= $(".optionLi[result='1']").length || time <= -1) {
            optionClickStus = false;
            if (timeInt) {
                clearInterval(timeInt);
                timeInt = null;
            }
            timePlayFun().pause();
            if (time <= -1) {
                $(".optionLi[result='1']").not('.already-hide').addClass("no-choose-right");
            } else {
                $(".optionLi").addClass("imgType1Right");
            }
            setTimeout(function() {
                timeIndex++;
                //最后一轮已结束
                if (timeIndex > frequencyList.length - 1) {
                    endGameappendImg();
                } else {
                    startNext();
                }
            }, 1500);
        }
    }

    //结束之后切换下一轮
    function startNext() {
        setTimeout(function() {
            optionsInit(timeIndex);
            addEvent();
            sorceTimeInit();
            timeStartFn();
            gameProcessBidSync();
        }, 1500)
    };

    //终局页面去重动态插入图片
    function endGameappendImg() {
        window.localStorage.removeItem('countDownTime');
        rightArr.allRightArr = Array.from(new Set(rightArr.allRightArr));
        let preImgs = [];
        $.when(preloadImg(rightArr.allRightArr, preImgs)).done(
            function() {
                preImgs.forEach(function(item, index) {
                    var scale = item.naturalWidth / item.naturalHeight;
                    $(".endGame .scrollUl li").eq(index).css({
                        'background-image': 'url(' + item.src + ')',
                        'opacity': 1,
                    });
                    endGameImgStyle($(".endGame .scrollUl li").eq(index), scale, item.naturalWidth, item.naturalHeight);
                })
                if (rightArr.allRightArr.length <= 3) {
                    $(".scrollBtn").hide();
                }
            }
        );

        timePlayFun().pause();
        $('.scoreNum').html(score);
        $(".endGame").show();
        if (!isSync) {
            $(".overBtn").trigger("gameOver");
            return;
        }
        if (isSync) {
            startFun();
            SDK.bindSyncEvt({
                index: "overBtn",
                eventType: "mygameevent",
                method: 'event',
                syncName: "gameOver",
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    time: time,
                    timeIndex: timeIndex,
                },
                recoveryMode: '1'
            });
        }
    }
    //终局之后触发发星
    function startFun() {
        if (!isSync) {
            return false;
        }
        var support1v1h5star = parent.window.h5SyncActions.classConf.serverData.objCourseInfo.support1v1h5star;
        classStatus = SDK.getClassConf().h5Course.classStatus;
        var device = parent.window.h5SyncActions.classConf.h5Course.device;
        if (window.frameElement.getAttribute('user_type') == 'stu' && classStatus == 2) {
            if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
                SDK.bindSyncStart({
                    type: "newH5StarData",
                    num: score
                });
            }
        }
    }


    //初始化   静态图（1） 1帧（2） 2帧（3） 样式
    function initImg(ele, img) {
        let naturalWidth = img.naturalWidth / 100;
        let naturalHeight = img.naturalHeight / 100;
        if (staticData.imgType == 1) {
            ele.css({
                backgroundSize: '100% 100%',
                width: naturalWidth + 'rem',
                height: naturalHeight + 'rem'
            });
        }
        if (staticData.imgType == 2) {
            ele.css({
                width: naturalWidth / 2 + 'rem',
                height: naturalHeight + 'rem',
                backgroundSize: '200% 100%'
            })
        }
        if (staticData.imgType == 3) {
            ele.css({
                width: naturalWidth / 3 + 'rem',
                height: naturalHeight + 'rem',
                backgroundSize: '300% 100%'
            });
        }
    }

    function endGameImgStyle(ele, scale, width, height) {
        console.log(scale)
        if (staticData.imgType == 1) {
            if (scale < 1) { //瘦高型
                if (height > ele.height()) {
                    ele.css({
                        backgroundSize: 'auto 100%',
                        backgroundPosition: '50% 50%'
                    })

                } else {
                    ele.css({
                        backgroundSize: 'auto auto',
                        backgroundPosition: '50% 50%'
                    })
                }

            } else { //胖矮型
                if (width > ele.width()) {
                    ele.css({
                        backgroundSize: '100% auto',
                        backgroundPosition: '50% 50%'
                    })

                } else {
                    ele.css({
                        backgroundSize: 'auto auto',
                        backgroundPosition: '50% 50%'
                    })
                }
            }
        }
        if (staticData.imgType == 2) {
            ele.css({
                backgroundSize: '200% ' + 200 / scale * 0.69 + '%',
                backgroundPosition: '0% 50%'
            })
        }
        if (staticData.imgType == 3) {
            ele.css({
                backgroundSize: '300% ' + 300 / scale * 0.69 + '%',
                backgroundPosition: '0% 50%'
            });
        }
        timePlayFun().pause();
    }

    //正确反馈   静态图（1） 1帧（2） 2帧（3） 动画
    function diffImgAni(self) {
        if (staticData.imgType == 1) {
            self.addClass('imgType1Right');
        }
        if (staticData.imgType == 2) {
            self.addClass('imgType2Right');
        }
        if (staticData.imgType == 3) {
            self.addClass('imgType3Right');
        }
    }

    //判断是学生还是老师显示底部按钮和mask
    isShowBtn();

    function isShowBtn() {
        if (isSync) {
            classStatus = SDK.getClassConf().h5Course.classStatus;
            if (userType == 'tea') {
                $(".doneTip").show();
            } else {
                $(".doneTip").hide();
            }
            if (classStatus == 0 && userType == "stu") {
                $(".mask").show();
            }
        } else {
            // $(".doneTip").show();
            // $(".mask").show();
            // $(".doneTip").show();
            var hrefParam = parseURL("http://www.example.com");
            if (top.frames[0] && top.frames[0].frameElement) {
                hrefParam = parseURL(top.frames[0].frameElement.src)
            }
            var role_num = hrefParam.params['role']

            function parseURL(url) {
                var a = document.createElement('a')
                a.href = url
                return {
                    source: url,
                    protocol: a.protocol.replace(':', ''),
                    host: a.hostname,
                    port: a.port,
                    query: a.search,
                    params: (function() {
                        var ret = {},
                            seg = a.search.replace(/^\?/, '').split('&'),
                            len = seg.length,
                            i = 0,
                            s
                        for (; i < len; i++) {
                            if (!seg[i]) {
                                continue;
                            }
                            s = seg[i].split('=')
                            ret[s[0]] = s[1]
                        }
                        return ret
                    })(),
                    file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
                    hash: a.hash.replace('#', ''),
                    path: a.pathname.replace(/^([^\/])/, '/$1'),
                    relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
                    segments: a.pathname.replace(/^\//, '').split('/')
                }
            }
            if (role_num == '1' || role_num == undefined) {
                $(".doneTip").show();
                $(".mask").show();
            } else if (role_num == '2') {
                $(".doneTip").hide();
                $(".mask").show();
            }
        }
    }

    //提示逻辑
    function hintFun(showMask) {
        optionsInit(0, 'tip');
        $(".optionLi").eq(0).show().siblings().hide();
        if (showMask) {
            $(".mask").hide();
        }
        $('.right-top-hint').show();
        setTimeout(function() {
            $('.hand').addClass("handAnimation");
        }, 2000);
        $(".hand").on('animationend  webkitAnimationEnd', function() {
            timePlayFun().pause();
            diffImgAni($(".optionLi").eq(0));
            $(".optionLi").on('animationend webkitAnimationEnd', function() {
                $(".optionUl").html('');
                $('.hand').removeClass("handAnimation");
                $('.right-top-hint').hide();
                btnStatus = true;
                if (showMask) {
                    $(".mask").show();
                }
            })

        })
    }

    //掉线页面恢复（点击选项、结束）
    function rebackPage(data) {
        return {
            'whenClick': function(data) {
                console.log('whenClick')
                isFirst = false;
                $(".doneTip").hide();
                rightArr.currentRightNum = data.currentRightNum;
                rightArr.allRightArr = data.allRightArr;
                time = data.time;
                score = data.score;
                timeIndex = data.timeIndex;
                optionsInit(timeIndex);
                addEvent();
                sorceTimeInit();
                let tempTime = window.localStorage.getItem('countDownTime');
                if (tempTime) {
                    time = tempTime;
                    moveItemW = moveItemW - (frequencyList[timeIndex].time - time) * everLen;
                    $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);
                }
                hideOptionArr = data.hideOptionArr;
                if (hideOptionArr && hideOptionArr.length > 0)
                    hideOptionArr.forEach(function(item) {
                        $(".optionLi[data-syncactions='" + item + "']").hide();
                    })
                timeStartFn();
                gameProcessBidSync();
                SDK.setEventLock();
            },
            'whenEnd': function(data) {
                console.log('whenEnd')
                isFirst = false;
                $(".doneTip").hide();
                rightArr.currentRightNum = data.currentRightNum;
                rightArr.allRightArr = data.allRightArr;
                time = data.time;
                score = data.score;
                timeIndex = data.timeIndex;
                endGameappendImg();
                SDK.setEventLock();
            },
        }
    }

    //点击气泡事件
    let optionClickStus = true;
    let hideOptionArr = []; //已点击的选项
    function addEvent() {
        $(".optionLi").on("click touchstart", function(e) {
            if (e.type == "touchstart") {
                e.preventDefault()
            }
            e.stopPropagation();
            //禁止老师点击
            if (isSync) {
                classStatus = SDK.getClassConf().h5Course.classStatus;
                if (classStatus != 0 && window.frameElement.getAttribute('user_type') == 'tea') {
                    return false;
                }
            }
            if (optionClickStus) {
                optionClickStus = false;
                if (!isSync) {
                    $(this).trigger("syncItemClick");
                    return;
                }
                SDK.bindSyncEvt({
                    sendUser: '',
                    receiveUser: '',
                    index: $(e.currentTarget).data("syncactions"),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'syncItemClick',
                    otherInfor: {
                        timeIndex: timeIndex,
                        score: score,
                        currentRightNum: rightArr.currentRightNum,
                        allRightArr: rightArr.allRightArr
                    },
                    recoveryMode: '1'
                });
            }
        });
        //点击选项
        $('.optionLi').on('syncItemClick', function(e, message) {
            let self = $(this);
            let result = self.attr("result");
            //播放音效
            if (result == 1) {
                rightWrong(self).right();
            } else {
                rightWrong(self).wrong();
            }
            self.on('animationend webkitAnimationEnd', function() {
                self.removeClass('shake');
                SDK.setEventLock();
                optionClickStus = true;
            });
            //继续播放
            gameProcessBidSync();
        });
    }
    //正确与错误逻辑(停止选项音、播放反馈音，改变分数、自增答对个数)
    function rightWrong(self) {
        timePlayFun().pause(); //停止
        return {
            'right': function() {
                score++;
                self.addClass("already-hide");
                diffImgAni(self);
                rightArr.currentRightNum++;
                hideOptionArr.push(self.data("syncactions"))
                $('.scoreNum').html(score);
                // $(".rightAudio")[0].play();
                SDK.playRudio({
                    index: $(".rightAudio")[0],
                    syncName: $(".rightAudio").attr("data-syncaudio")
                })
                $(".rightAudio")[0].onended = function() {
                    if (timeInt) {
                        timePlayFun().play();
                    }

                }
            },
            'wrong': function() {
                self.addClass('shake');
                // $(".wrongAudio")[0].play();
                SDK.playRudio({
                    index: $(".wrongAudio")[0],
                    syncName: $(".wrongAudio").attr("data-syncaudio")
                })
                $(".wrongAudio")[0].onended = function() {
                    if (timeInt) {
                        timePlayFun().play();
                    }
                }
            }
        }
    }

    //点击按钮滚动展示图片
    let scrollBtnStatus = true;
    $(".leftScroll,.rightScroll").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (scrollBtnStatus) {
            scrollBtnStatus = false;
            if (!isSync) {
                $(this).trigger("btnScroll");
                return;
            }
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'btnScroll',
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    time: time,
                    timeIndex: timeIndex,
                },
                recoveryMode: '1'

            });
        }
    })
    $(".leftScroll,.rightScroll").on("btnScroll", function(e, message) {
        let scrollLeft = ($(".scrollUl li").width() + 0.9 * window.base + 0.3 * window.base) * 3;
        let curScrollLeft = $(".scrollUl").scrollLeft();
        let self = $(this);
        if (self.hasClass("leftScroll")) {
            scrollLeft = -(($(".scrollUl li").width() + 0.9 * window.base + 0.3 * window.base) * 3);
        }
        console.log(scrollLeft)
        console.log(curScrollLeft + scrollLeft)
        if (self.css('opacity') == 0.5) {

        } else {
            $(".scrollUl").animate({
                scrollLeft: curScrollLeft + scrollLeft
            }, function() {
                self.css({
                    opacity: 0.5
                })
                self.siblings().css({
                    opacity: 1
                })
            });
        }



        //滚动处断线重连
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                rebackPage().whenEnd(obj)
            }
        }
        scrollBtnStatus = true;
        SDK.setEventLock();
    })
    //结束反馈事件
    $(".overBtn").on("gameOver", function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == '5') {
                rebackPage().whenEnd(obj);
                return;
            }
        }
        SDK.setEventLock();
    });
    //进行中反馈事件
    $(".runing").on("syncRuning", function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                console.log('点击处断线重连')
                console.log(message)
                //直接恢复页面
                rebackPage().whenClick(obj);
            }
        }
        SDK.setEventLock();
    });

    //老师点击 hint 按钮
    let btnStatus = true;
    $(".hint-btn").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (btnStatus) {
            btnStatus = false;
            if (!isSync) {
                $(this).trigger("btnClick");
                return;
            }
            if (window.frameElement.getAttribute('user_type') == 'tea') {
                SDK.bindSyncEvt({
                    sendUser: '',
                    receiveUser: '',
                    index: $(e.currentTarget).data("syncactions"),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'btnClick',
                    otherInfor: '',
                    recoveryMode: '1'
                });
            }
        }
    })
    $(".hint-btn").on('btnClick', function(e, message) {
        var showMask = false;
        hintFun(showMask);
        SDK.setEventLock();
    });
    //学生点击 hint
    $(".hint-btnStu").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        var showMask = true;
        hintFun(showMask);
    });
    //学生点击开始
    let startBtnStatus = true;
    $(".startBtn").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (startBtnStatus) {
            startBtnStatus = false;
            threeTwoOne();
        }
    });
    //控制器通知模板触发授权开始游戏
    window.SDK.actAuthorize = function(message) {
        console.log('模板收到授权')
        console.log(message)
        if (isSync) {
            if (message && message.operate == 5) {
                isFirst = false;
            }
            if (message && message.type == 'practice') {
                //第一次授权才显示倒计时
                if (isFirst) {
                    isFirst = false;
                    $(".mask").show();
                    $(".optionUl").html("");
                    sorceTimeInit();
                    threeTwoOne();
                    gameProcessBidSync();
                }
                $(".doneTip").hide();
            }
        }
    }
    /**
     * 断线后对端暂停处理
     */
    window.SDK.memberChange = function(message) {
        if (isSync && timeInt) {
            // 进入教室
            if (message.state == 'enter') {
                if (message.role == 'tea') {
                    $(".mask").hide();
                }
                $(".tea-stu-not-in").hide();
                isAnyOneOut = false;
                timePlayFun().play();
                timeStartFn();
            } else if (message.state == 'out') { // 退出教室
                $(".tea-stu-not-in").show(); //限制任何一方退出  都不能操作
                isAnyOneOut = true;
                if (timeInt) {
                    clearInterval(timeInt);
                    timePlayFun().pause();
                }
            }
        }
    }
});
//预加载图片的方法
function preloadImg(list, imgs) {
    var def = $.Deferred(),
        len = list.length;
    $(list).each(function(i, e) {
        var img = new Image();
        img.src = e;
        if (img.complete) {
            imgs[i] = img;
            len--;
            if (len == 0) {
                def.resolve();
            }
        } else {
            img.onload = (function(j) {
                return function() {
                    imgs[j] = img
                    len--;
                    if (len == 0) {
                        def.resolve();
                    }
                };
            })(i);
            img.onerror = function() {
                len--;
                console.log('fail to load image');
            };
        }
    });
    return def.promise();
};
