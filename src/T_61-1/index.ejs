<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="LCH0001_听力泡泡"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">
    <section class="commom">
        <div class="desc">123132</div>
    </section>

    <section class="main">
        <div class="right-top-hint"><img src="./image/hint.png" alt=""> Hint</div>
        <!-- 正确错误音效 -->
        <audio src="" class="rightAudio" data-syncaudio="rightAudio"></audio>
        <audio src="./audio/wrong.mp3" class="wrongAudio" data-syncaudio="wrongAudio"></audio>
        <!-- 选项 -->
        <ul class="optionUl"></ul>
        <!-- 小手 -->
        <div class="hand"></div>
        <!-- 像素格 -->
        <div class="boxList">
            <ul class="boxUl"></ul>
        </div>
        <!-- 计分 -->
        <div class="intSatge">
            <div class="countImg"></div>
            <span class="score">x <span class="scoreNum">0</span></span>
        </div>
        <!-- 计时器 -->
        <div class="time">
            <div class="proprogressBarBox">
                <p class="proprogressBar"></p>
            </div>
            <div class="watch"></div>
            <audio class="frequencyAudio" data-syncaudio="frequencyAudio" loop="loop"></audio>
        </div>
         <!-- 遮罩层 -->
         <div class="mask">
            <div class="startBox">
                <img src="./image/startFont.png" class="startFont"/>
                <img src="./image/startMsg.png" class="startMsg"/>
                <img src="./image/hintBtn.png" class="hint-btnStu"/>
                <img src="./image/startBtn.png" class="startBtn">
            </div>
            <div class="timeChangeBox hide">
                <div class="timeBg">
                    <div class="numberList"></div>
                    <audio src="./audio/timeLow.mp3" class="timeLowAudio_1" data-syncaudio="timeLowAudio_1"></audio>
                    <audio src="./audio/timeLow.mp3" class="timeLowAudio_2" data-syncaudio="timeLowAudio_2"></audio>
                    <audio src="./audio/timeLow.mp3" class="timeLowAudio_3" data-syncaudio="timeLowAudio_3"></audio>
                    <audio src="./audio/timehigh.mp3" class="timeLowAudio_4"  data-syncaudio="timeLowAudio_4"></audio>
                </div>
            </div>
        </div>
        <!-- 终局页 -->
        <div class="endGame">
            <div class="endGameScore">
                <img src="./image/countImg.png" alt="">
                <label></label>
                <span class="score">x <span class="scoreNum">0</span></span>
            </div>
            <div class="hiddenContain">
              <ul class="scrollUl">
                  <li></li>
                  <li></li>
                  <li></li>
                  <li></li>
                  <li></li>
                  <li></li>
              </ul>
            </div>
            <div class="scrollBtn">
                <span class="leftScroll" data-syncactions="leftScroll"></span>
                <span class="rightScroll" data-syncactions="rightScroll"></span>
            </div>
        </div>
        <div class="tea-stu-not-in"></div>
        <div class="overBtn" data-syncactions="overBtn"></div>
        <div class="outBtn" data-syncactions="outBtn"></div>
        <div class="runing" data-syncactions="runing"></div>
    </section>
    <div class="doneTip">
      <p>Show hint,then authorize to start.</p>
      <span class="btn hint-btn" data-syncactions="hintBtn">Hint for s</span>
    </div>
    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
