<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TS004_排序_纯图_大图</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TS004_排序_纯图_大图</div>
			<div class="c-group">
				<div class="c-title">背景/标题</div>
				<div class="c-area upload img-upload radio-group">
					<div class="field-wrap">
						<label class="field-label"  for="">上传图片</label><label for="bg-upload" class="btn btn-show upload" v-if="configData.bg==''?true:false">上传</label><label  for="bg-upload" class="btn upload re-upload" v-if="configData.bg!=''?true:false">重新上传</label><span class='txt-info'>（尺寸：1920X1080，大小：≤100KB）</span>
						<input type="file"  v-bind:key="Date.now()" class="btn-file" id="bg-upload" size="1920*1080" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData,'bg',100)">
					</div>
					<div class="img-preview" v-if="configData.bg!=''?true:false">
						<img v-bind:src="configData.bg" alt=""/>
						<div class="img-tools">
							<span class="btn btn-delete" v-on:click="configData.bg=''">删除</span>
						</div> 
					</div>
					<div class="c-area">
						<label>标题 （显示位置：1）字符：≤40</label>
						<input type="text" class='c-input-txt' placeholder="请在此输入描述" v-model="configData.desc" maxlength='40'>
					</div>
				</div>
			</div>
			<div class="c-group">
				<div class="c-title">添加TG</div>
				<div class="c-area">
					<div class="c-well" v-for="(item,index) in configData.tg">
						<div class="well-title">
							<p>TG {{index+1}}</p>
							<span class="dele-tg-btn" v-on:click="deleTg(item)"></span>
						</div>
						<div class="well-con">
							<label>标题</label>
							<input type="text" class='c-input-txt' placeholder="请在此输入TG标题" v-model="item.title">
							<label>内容 <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em></label>
							<textarea name="" cols="56" rows="2" placeholder="请在此输入TG内容" v-model="item.content"></textarea>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addTg" >+</button>
				</div>
			</div>
			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>问题 （显示位置：2）字符：≤56（非必填）</label>
					<input type="text" class='c-input-txt' placeholder="请在此输入问题" maxlength='56' v-model="configData.source.title">
					<label>选项 <em>( 请按正确顺序录入数据 )</em></label>
					<div class="c-well" v-for="(item,index) in configData.source.seleList">
						<div class="well-title">
							<p>选项{{index+1}}</p>
							<span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.seleList.length>3?true:false'></span>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span class='txt-info'>（尺寸：530X340，大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="530*340" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
							<div class="field-wrap">
								<label class="field-label"  for="">上传声音</label><div class="audio-preview" v-show="item.audio!=''?true:false">
									<div class="audio-tools">
										<p v-show="item.audio!=''?true:false">{{item.audio}}</p>
										<img src="" alt="" v-show="item.audio==''?true:false">
									</div>
									<span class="play-btn" v-on:click="play($event)">
										<audio v-bind:src="item.audio"></audio>
									</span>
								</div><span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio=''">删除</span><label v-bind:for="'audio-upload'+index" class="btn btn-show upload" v-if="item.audio==''?true:false">上传</label><label  v-bind:for="'audio-upload'+index" class="btn upload re-upload mar" v-if="item.audio!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)</span>
								<input type="file" v-bind:id="'audio-upload'+index" class="btn-file upload" size="" volume="50" accept=".mp3" v-on:change="audioUpload($event,item,'audio')" v-bind:key="Date.now()">
							</div>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele()" v-show='configData.source.seleList.length<6?true:false'>+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
