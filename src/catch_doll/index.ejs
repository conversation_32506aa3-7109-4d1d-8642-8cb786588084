<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="cache-control" content="max-age=60">
    <meta name="format-detection" content="telephone=no">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1, maximum-scale=1, user-scalable=0">
    <title>抓娃娃(<%= new Date().getFullYear()%>-<%= new Date().getMonth()+1 %>-<%= new Date().getDate() %>)</title>
    <link rel="stylesheet" href="css/index.css?v=<%=new Date().getTime()%>">
    <script src="../../jquery-2.1.1.min.js"></script>
    <script>
        if (!window.jQuery) {
            document.write('<script src="/assets/js/jquery/2.1.1/jquery.min.js"><\/script>');
        } 
    </script>
    <script src="js/tracking.js?v=<%=new Date().getTime()%>"></script>
    <script src="js/sdk.js?v=<%=new Date().getTime()%>"></script>
    <script src="js/fit.js?v=<%=new Date().getTime()%>"></script>
</head>
<body>
<div class="container" id="container" data-syncresult='show-result-1'>
    <section class="commom">
        <!-- <div class="title"></div>
        <p class="title-text"></p> -->
    </section>
    
    <section class="stu hide">
        <div class="stage">
            <div class="curtain"><img/ src="./image/curtain.png"></div>
            <div class="box">
                <div class="animate"></div>
                <div class="rod rodMove"></div>
                <div class="move_box box_move">
                    <div class="line"></div>
                    <div class="move"></div>
                    <div class="claw">
                        <img src="./image/claw.png" class="init_img" alt="">
                        <img src="./image/left.png" class="left_img" alt="">
                        <img src="./image/right.png" class="right_img" alt="">
                    </div>
                </div>
                <div class="glass"></div>
            </div>
            <div class="table">
                <div class="button" data-syncactions="ButtonItem"></div>
                <div class="rocker"></div>
            </div>
        </div>
    </section>

</div>
<script type="text/javascript" src="content.js?v=<%=new Date().getTime()%>"></script>
<script type="text/javascript" src="js/index.js?v=<%=new Date().getTime()%>"></script>
</body>
</html>
