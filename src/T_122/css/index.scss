@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/multyDialog/style.scss';
@import "../../common/template/avatarUpWall/style.scss";

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.container {
  position: relative;
}

audio {
  width: 0;
  height: 0;
  opacity: 0;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;

  .mask {
    display: none;
    position: relative;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 101;

    .textPic{

      .laba{
        transform: translate(-50%, -50%);
        cursor: pointer;
        width: 1.08rem;
        height: 1.08rem;
      }
    }
  }

  @keyframes breath {
    0% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1.05);
    }
  }
}

.hide {
  opacity: 0;

}

