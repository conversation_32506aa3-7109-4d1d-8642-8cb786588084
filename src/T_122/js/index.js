"use strict";
import "../../common/js/common_1v1.js";
import "../../common/template/multyDialog/index.js";
import "../../common/js/teleprompter.js";
import "../../common/template/avatarUpWall/index.js";

import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js";
import { getMemory, setMemory } from "../../common/template/memory/index.js";
import "../../common/js/lottie.js";

$(function () {
  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      item: configData.source.options.length,
      knowledge: configData.source.options.map(item => item.knowledgePoints).join(","),
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_INPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.LISTEN,
    },
  });
  const h5SyncActions = parent.window.h5SyncActions;
  const isSync = h5SyncActions && h5SyncActions.isSync;
  const isStudent =
    window.frameElement &&
    window.frameElement.getAttribute("user_type") === USER_TYPE.STU;
  // 从localStorage获取最新的缓存数据
  let memoryData = getMemory();
  // 全局配置和状态
  window.h5Template = { hasPractice: "1" };
  const options = configData.source.options;
  const lottieInstances = {}; // 存储所有Lottie动画实例
  // 设置背景
  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/bg_53.jpg)" });
  }

  // 动画管理模块
  const animationManager = {
    stopLottieAnimation(animId) {
      if (lottieInstances[animId]) {
        lottieAnimations.stop(lottieInstances[animId]);
        return true;
      }
      return false;
    },

    stopAllAnimations() {
      Object.keys(lottieInstances).forEach((id) => {
        this.stopLottieAnimation(id);
      });
    },

    async initLottieAnimation(selector, jsonUrl, animId) {
      try {
        lottieInstances[animId] = lottieInstances[animId]
          ? lottieInstances[animId]
          : await lottieAnimations.init(
              lottieInstances[animId],
              jsonUrl,
              selector
            );
        lottieAnimations.play(lottieInstances[animId]);
        return animId;
      } catch (error) {
        console.error(`初始化Lottie动画失败: ${selector}`, error);
        return null;
      }
    },

    async initLabaAnimation() {
      if (lottieInstances.laba) return
      lottieInstances.laba = await lottieAnimations.init(
          lottieInstances.laba,
          "./image/laba.json",
          ".laba",
          false
      );
    },

    playLabaAnimation() {
      const audio = $(".audio").get(0);
      if (audio) {
        lottieAnimations.play(lottieInstances.laba);
        audio.currentTime = 0;
        SDK.playRudio({
          index: audio,
          syncName: $(".audio").attr("data-syncaudio"),
        });
        audio.onended = () => {
          lottieAnimations.stop(lottieInstances.laba);
          SDK.setEventLock();
        };
      }
    },
  };

  // 样式管理模块
  const styleManager = {
    isJsonFile(url) {
      if (!url) return false;
      const extension = url.split(".").pop().toLowerCase();
      return extension === "json";
    },

    createStyleObject(
      width,
      height,
      left,
      top,
      imageUrl,
      shouldSetBackground = true
    ) {
      const css = {
        position: "absolute",
        width: `${width / 100}rem`,
        height: `${height / 100}rem`,
      };

      // 如果 left 和 top 未提供，则居中展示
      if (left === "" || top === "") {
        css.left = "50%";
        css.top = "50%";
        css.transform = "translate(-50%, -50%)";
      } else {
        css.left = left ? `${left / 100}rem` : "0rem";
        css.top = top ? `${top / 100}rem` : "0rem";
      }

      if (shouldSetBackground && imageUrl) {
        css.backgroundImage = `url(${imageUrl})`;
        css.backgroundPosition = "center";
        css.backgroundRepeat = "no-repeat";
        css.backgroundSize = "contain";
      }

      return css;
    },

    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        if (!url) {
          reject(new Error("无效的图片URL"));
          return;
        }

        if (this.isJsonFile(url)) {
          $.getJSON(url)
            .done((data) => {
              const width = data.width || data.w;
              const height = data.height || data.h;
              resolve({ width, height });
            })
            .fail(() => reject(new Error("JSON文件加载失败")));
        } else {
          const img = new Image();
          img.onload = () => resolve({ width: img.width, height: img.height });
          img.error = () => reject(new Error("图片加载失败"));
          img.src = url;
        }
      });
    },

    async applyElementStyle(selector, dimensions, location, imageUrl, animId) {
      try {
        const isJson = this.isJsonFile(imageUrl);
        const shouldSetBackground = !isJson;
        $(selector).css(
          this.createStyleObject(
            dimensions.width,
            dimensions.height,
            location.x,
            location.y,
            imageUrl,
            shouldSetBackground
          )
        );
        if (isJson) {
          await animationManager.initLottieAnimation(
            selector,
            imageUrl,
            animId
          );
        }
      } catch (error) {
        console.error(`设置元素样式失败: ${selector}`, error);
      }
    },
  };

  // 页面管理模块
  const pageManager = {
    async setStyle() {
      const stylePromises = [];
      // 处理选项元素
      options.forEach((item, i) => {
        stylePromises.push(
          styleManager
            .getImageDimensions(item.initPic)
            .then((dimensions) => {
              styleManager.applyElementStyle(
                `[data-syncactions="initPic${i}"]`,
                dimensions,
                item.initPicLocation,
                item.initPic,
                `initPic${i}`
              );
            })
            .catch((error) => console.error(`处理初始图片失败: ${i}`, error))
        );
        // 查看设置的canChooseAgain是否为0
        if (
          memoryData.selectedArr.includes(item.id)
        ) {
          //禁止点击事件
          $(`[data-syncactions="initPic${i}"]`).off("click touchstart");
          //样式设置为不可点击
          $(`[data-syncactions="initPic${i}"]`).css({
            cursor: "not-allowed",
            filter: "brightness(0.5)",
            "pointer-events": "none" // 添加pointer-events:none彻底禁用点击事件
          });
        } else {
          //可点击元素增加一个呼吸效果
          $(`[data-syncactions="initPic${i}"]`).css({
            animation: "breath 1.1s infinite",
            "animation-direction": "alternate",
            "animation-timing-function": "ease-in-out",
          });
        }
      });
      try {
        await Promise.all(stylePromises);
      } catch (error) {
        console.error("设置样式时发生错误:", error);
      }
    },

    eleShow() {
      const elements = options
        .map(
          (item, i) =>
            `<div class="clickableEl" data-syncactions="initPic${i}" data-memory-id="${item.id}"></div>`
        )
        .join("");
      $(".main").append(elements);
      this.setStyle();
    },
  };

  // 事件处理模块
  const eventManager = {
    initClickEvents() {
      $(document).on("click touchstart", ".clickableEl", function (e) {
        if (e.type == "touchstart") {
          e.preventDefault();
        }
        // 获取当前点击元素的索引和对应的配置项
        const clickedIndex = $(e.currentTarget).data("syncactions").replace("initPic", "");
        const clickedOption = options[clickedIndex];
        SDK.reportTrackData({
          action:'CK_FT_INTERACTION_ITEM', //事件名称
          data:{}, // 老师和学生  都需要上报的数据
          teaData:{},  // 只有老师端会上报的数据
          stuData:{
            item:Number(clickedIndex)+1
          },  // 只有学生端会上报的数据
        },USER_TYPE.STU)
        if (!isSync) {
          if(clickedOption && clickedOption.canChooseAgain == "0"){
            memoryData.selectedArr.push(
              $(e.currentTarget).attr("data-memory-id")
            );
          }
          $(this).trigger("changePosSync");
          return;
        }
        //学生在教室 push数据并触发同步事件
        if (isStudent && clickedOption && clickedOption.canChooseAgain == "0") {
          memoryData.selectedArr.push(
            $(e.currentTarget).attr("data-memory-id")
          );
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "changePosSync",
          recoveryMode: "1",
          otherInfor: {
            ...memoryData,
          },
        });
      });
      $(document).on("changePosSync", ".clickableEl", (e, message) => {
        const param = isSync ? SDK.syncData : memoryData;
        setMemory(param);
        SDK.reportTrackData({
          action:'CK_FT_INTERACTION_COMPLETE', //事件名称
          data:{result:'success'}, // 老师和学生  都需要上报的数据
          teaData:{},  // 只有老师端会上报的数据
          stuData:{},  // 只有学生端会上报的数据
        },USER_TYPE.TEA)
        // 获取被点击的元素索引
        const clickedIndex = $(e.target)
          .data("syncactions")
          .replace("initPic", "");
        const clickedOption = options[clickedIndex];

        //展示蒙层
        if (clickedOption.isShowMask == "1") {
          $(".mask").css({
            "background-color": "rgba(0, 0, 0, 0.8)",
          });
        }
        $(".mask").show();
        //停止所有可点击元素的呼吸效果动画
        $(".clickableEl").css({
          animation: "none",
          "animation-direction": "normal",
          "animation-timing-function": "ease",
        });

        // 渲染弹窗图片
        styleManager
          .getImageDimensions(clickedOption.popupPic)
          .then((dimensions) => {
            styleManager.applyElementStyle(
              ".popupPic",
              dimensions,
              clickedOption.popupPicLocation,
              clickedOption.popupPic,
              "popupPic"
            );
          })
          .catch((error) => console.error("处理弹窗图片失败:", error));

        // 渲染文字图片（如果有）
        if (clickedOption.textPic) {
          styleManager
            .getImageDimensions(clickedOption.textPic)
            .then((dimensions) => {
              $(".textPic").show();
              styleManager.applyElementStyle(
                ".textPic",
                dimensions,
                clickedOption.textPicLocation,
                clickedOption.textPic,
                "textPic"
              );
            })
            .catch((error) => console.error("处理文字图片失败:", error));
        } else {
          $(".textPic").hide();
        }
        // 设置并播放音频（如果有）
        if (clickedOption.contentAudio) {
          //有文字图片 就初始化laba动画
          if (clickedOption.textPic) {
            (async () => {
              await animationManager.initLabaAnimation();
            })();
            $(".laba").syncbind(
              "click touchstart",
              function (dom, next) {
                if (!isSync) {
                  next(false);
                } else {
                  next();
                }
              },
              function () {
                animationManager.playLabaAnimation();
              }
            );
          }
          // 设置音频源
          const audioEl = $(".audio");
          audioEl.attr("src", clickedOption.contentAudio);
          audioEl.attr("data-syncaudio", `audioOpt${clickedIndex}`);
          //判断下喇叭动画有没有
          if (lottieInstances.laba) {
            animationManager.playLabaAnimation();
          } else {
            eventManager.playAudio(audioEl);
            SDK.setEventLock();
          }
        }
      });
    },
    playAudio(el) {
      return new Promise(async (resolve, reject) => {
        let audio = el.get(0);
        audio.currentTime = 0;
        SDK.playRudio({
          index: audio,
          syncName: el.attr("data-syncaudio"),
        });
        el.on("ended", () => {
          resolve();
        });
      });
    },
  };

  pageManager.eleShow();
  eventManager.initClickEvents();
});
