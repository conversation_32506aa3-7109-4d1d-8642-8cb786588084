<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>DXYTC0001FT_多选1弹窗FT</title>
    <link rel="stylesheet" href="form/css/style.css"/>
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
</head>
<body>
<div id="container">
    <div class="edit-form">
        <div class="h-title">DXYTC0001FT_多选1弹窗FT</div>

        <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <!-- IP对话组件 -->
        <% include ./src/common/template/multyDialog/form.ejs %>
        <!-- 记忆组件 -->
        <% include ./src/common/template/memory/form.ejs %>
        <div class="c-group">
            <div class="c-title">多选1弹窗设置（最多6个）</div>

            <div class="c-area upload img-upload">
                <ul>
                    <li v-for="(item, index) in configData.source.options">
                        <div class="c-well">
                  <span
                          class="dele-tg-btn"
                          style="z-index: 9; position: relative"
                          v-show="configData.source.options.length > 1"
                          v-on:click="deleConfig(item)"
                  ></span>
                            <!-- 初始状态 -->
                            <div class="field-wrap" style="margin-top: 10px">
                                <label class="field-label" style="width: 100px"
                                >初始状态{{ index + 1 }}*</label
                                >
                                <select v-model="item.id" style="width: 175px">
                                    <option
                                            v-for="(memItem,index) in getAvailableOptions(item)"
                                            :value="memItem.id"
                                    >
                                        ID:{{ memItem.id }} &nbsp;&nbsp;名称:{{ memItem.name }}
                                    </option>
                                </select>
                            </div>
                            <div class="field-wrap">
                                <label class="field-label">初始图片{{ index + 1 }}*</label>
                                <span class="field-content">
                      <label
                              :for="'content-pic-initPic'+index"
                              class="btn btn-show upload"
                              v-if="!item.initPic"
                      >上传</label
                      >
                      <label
                              :for="'content-pic-initPic'+index"
                              class="btn upload re-upload"
                              v-if="item.initPic"
                      >重新上传</label
                      >
                    </span>
                                <div class="txt-info">
                                    <em
                                    >JPG、PNG、json格式，最大尺寸不超过1920 x
                                        1080，≤200Kb</em
                                    >
                                </div>
                                <input
                                        type="file"
                                        v-bind:key="Date.now()+'initPic'"
                                        class="btn-file"
                                        :id="'content-pic-initPic'+index"
                                        size="1920*1080"
                                        accept=".jpg,.png,.json"
                                        @change="feedbackUpload($event,item,'initPic',200)"
                                />
                            </div>
                            <div class="img-preview" v-if="item.initPic">
                                <img
                                        :src="item.initPic.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.initPic"
                                        alt=""
                                />
                                <div class="img-tools">
                      <span
                              class="btn btn-delete"
                              @click="delPrew(item,'initPic')"
                      >删除</span
                      >
                                </div>
                            </div>
                            <div class="field-wrap-item">
                                <span>初始图片位置: &nbsp;&nbsp;</span>
                                X:<input
                                        type="number"
                                        class="c-input-txt"
                                        style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                                        oninput="if(value>1920)value=1920;if(value<0)value=0"
                                        v-model="item.initPicLocation.x"
                                />
                                Y:<input
                                        type="number"
                                        class="c-input-txt"
                                        style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                                        oninput="if(value>1080)value=1080;if(value<0)value=0"
                                        v-model="item.initPicLocation.y"
                                />
                                <br/>
                            </div>
                            <!-- 弹窗图片 -->
                            <div class="field-wrap" style="margin-top: 20px">
                                <label class="field-label">弹窗内容{{ index + 1 }}*</label>
                                <span class="field-content">
                      <label
                              :for="'content-pic-popupPic'+index"
                              class="btn btn-show upload"
                              v-if="!item.popupPic"
                      >上传</label
                      >
                      <label
                              :for="'content-pic-popupPic'+index"
                              class="btn upload re-upload"
                              v-if="item.popupPic"
                      >重新上传</label
                      >
                    </span>
                                <div class="txt-info">
                                    <em
                                    >JPG、PNG、json格式，最大尺寸不超过1920 x
                                        1080，≤200Kb</em
                                    >
                                </div>
                                <input
                                        type="file"
                                        v-bind:key="Date.now()+'popupPic'"
                                        class="btn-file"
                                        :id="'content-pic-popupPic'+index"
                                        size="1920*1080"
                                        accept=".jpg,.png,.json"
                                        @change="feedbackUpload($event,item,'popupPic',200)"
                                />
                            </div>
                            <div class="img-preview" v-if="item.popupPic">
                                <img
                                        :src="item.popupPic.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.popupPic"
                                        alt=""
                                />
                                <div class="img-tools">
                      <span
                              class="btn btn-delete"
                              @click="delPrew(item,'popupPic')"
                      >删除</span
                      >
                                </div>
                            </div>
                            <div class="field-wrap-item">
                                <span>弹窗内容位置: &nbsp;&nbsp;</span>
                                X:<input
                                        type="number"
                                        class="c-input-txt"
                                        style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                                        oninput="if(value>1920)value=1920;if(value<0)value=0"
                                        v-model="item.popupPicLocation.x"
                                />
                                Y:<input
                                        type="number"
                                        class="c-input-txt"
                                        style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                                        oninput="if(value>1080)value=1080;if(value<0)value=0"
                                        v-model="item.popupPicLocation.y"
                                />
                                <br/>
                            </div>
                            <!-- 文字图片 -->
                            <div class="field-wrap" style="margin-top: 20px">
                                <label class="field-label">文字图片{{ index + 1 }}</label>
                                <span class="field-content">
                      <label
                              :for="'content-pic-textPic'+index"
                              class="btn btn-show upload"
                              v-if="!item.textPic"
                      >上传</label
                      >
                      <label
                              :for="'content-pic-textPic'+index"
                              class="btn upload re-upload"
                              v-if="item.textPic"
                      >重新上传</label
                      >
                    </span>
                                <div class="txt-info">
                                    <em>JPG、PNG格式，最大尺寸不超过500 x 500，≤50Kb</em>
                                </div>
                                <input
                                        type="file"
                                        v-bind:key="Date.now()+'textPic'"
                                        class="btn-file"
                                        :id="'content-pic-textPic'+index"
                                        size="500*500"
                                        accept=".jpg,.png"
                                        @change="feedbackUpload($event,item,'textPic',50)"
                                />
                            </div>
                            <div class="img-preview" v-if="item.textPic">
                                <img :src="item.textPic" alt=""/>
                                <div class="img-tools">
                      <span
                              class="btn btn-delete"
                              @click="delPrew(item,'textPic')"
                      >删除</span
                      >
                                </div>
                            </div>
                            <div class="field-wrap-item" style="margin-top: 10px">
                                <span>文字图片位置: &nbsp;&nbsp;</span>
                                X:<input
                                        type="number"
                                        class="c-input-txt"
                                        style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                                        oninput="if(value>1920)value=1920;if(value<0)value=0"
                                        v-model="item.textPicLocation.x"
                                />
                                Y:<input
                                        type="number"
                                        class="c-input-txt"
                                        style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                                        oninput="if(value>1080)value=1080;if(value<0)value=0"
                                        v-model="item.textPicLocation.y"
                                />
                                <br/>
                            </div>
                            <!-- 上传声音 -->
                            <div class="field-wrap" style="margin-top: 20px">
                                <label class="field-label" for="contentAudio">内容音频</label>
                                <span>
                      <label
                              :for="'audio-upload-contentAudio'+index"
                              class="btn btn-show upload"
                              v-if="item.contentAudio==''?true:false"
                      >上传</label
                      >
                      <label
                              :for="'audio-upload-contentAudio'+index"
                              class="btn upload re-upload mar"
                              v-if="item.contentAudio!=''?true:false"
                      >重新上传</label
                      >
                    </span>
                                <div style="color: red">MP3、wav格式，≤50Kb</div>

                                <div
                                        class="audio-preview"
                                        v-show="item.contentAudio!=''?true:false"
                                >
                                    <div class="audio-tools">
                                        <p v-show="item.contentAudio!=''?true:false">
                                            {{ item.contentAudio }}
                                        </p>
                                    </div>
                                    <span class="play-btn" v-on:click="play($event)">
                        <audio v-bind:src="item.contentAudio"></audio>
                      </span>
                                </div>
                                <span
                                        class="btn btn-audio-dele"
                                        v-show="item.contentAudio!=''?true:false"
                                        v-on:click="item.contentAudio=''"
                                >删除</span
                                >
                                <input
                                        type="file"
                                        :id="'audio-upload-contentAudio'+index"
                                        class="btn-file upload"
                                        size=""
                                        accept=".mp3,.wav"
                                        v-on:change="audioUpload($event,item,'contentAudio',50)"
                                        v-bind:key="Date.now()+'contentAudio'"
                                />
                            </div>
                            <div style="margin-top: 20px">
                                <span>半透明蒙层</span>
                                <label class="inline-label" :for="'isShowMask'+index"
                                       style="display: inline-block;margin-left: 10px"><input type="radio"
                                                                                              :name="'isShowMask'+index"
                                                                                              value="1"
                                                                                              v-model="item.isShowMask">
                                    显示</label>
                                <label class="inline-label" :for="'isShowMask'+index"
                                       style="display: inline-block;margin-left: 10px"><input type="radio"
                                                                                              :name="'isShowMask'+index"
                                                                                              value="0"
                                                                                              v-model="item.isShowMask">
                                    不显示</label>
                            </div>
                            <!-- 知识点 -->
                            <div class="field-wrap" style="margin-top: 20px">
                                <label for="knowledgePoints">知识点*</label>
                                <input
                                        class="c-input-txt"
                                        type="text"
                                        style="margin: 0 10px;width:220px !important;display: inline-block;"
                                        placeholder="请填写知识点"
                                        v-model="item.knowledgePoints"
                                />
                            </div>
                            <!-- 是否可以再次选择 -->
                            <div class="field-wrap" style="margin-top: 20px">
                                <label for="canChooseAgain">是否可以再次选择</label>
                                <select v-model="item.canChooseAgain" style="width: 250px;" disabled>
                                    <option value="1">不限制</option>
                                    <option value="0">选择后不可再选（前端置灰状态）</option>
                                </select>
                            </div>

                            <button
                                    class="add-tg-btn"
                                    style="margin-top: 20px"
                                    v-on:click="addConfig"
                                    v-if="index < 5 && index === configData.source.options.length -1"
                            >
                                +
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <!-- 头像上墙 -->
        <% include ./src/common/template/avatarUpWall/form %>
        <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
        <div class="show-fixed">
            <div class="show-img">
                <img
                        src="form/img/preview.jpg?v=<%= new Date().getTime() %>"
                        alt=""
                />
            </div>
            <ul class="show-txt">
                <li><em>图片格式：</em>JPG/PNG/GIF</li>
                <li><em>声音格式：</em>MP3/WAV</li>
                <li><em>视频格式：</em>MP4</li>
                <li>带有" * "号为必填项</li>
            </ul>
        </div>
    </div>
</div>
</body>
<script src="form/js/form.js?v=<%= new Date().getTime() %>"></script>
</html>
