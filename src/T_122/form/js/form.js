//var domain = 'http://172.16.0.107:9011/pages/159/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
import { getMemoryForm, setMemoryForm } from "../../../common/template/memory/form.js";
import {
  avatarUpWallData,
  avatarUpWallSend,
  teacherChange,
  studentChange,
  initializeAvatarUpWall
} from "../../../common/template/avatarUpWall/form.js";
var domain = '';
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: "",
    },
  ],
  messageLocationX: "", // 消息内容位置x
  messageLocationY: "", // 消息内容位置y
  roleLocationX: "100", // 角色位置x
  roleLocationY: "600", // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2", // 播放完之后状态
  scale: 100, //缩放比例  1-500
  autoNext: "1", // 是否自动播放下一条对话框
  hiddenStatus: "1", // 播放完是否应藏的状态
};
var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    tImgX: "",
    tImgY: "",
    instructions: [{
      commandId: '-1'
    }],
    tg: [{
      title: "",
      content: "",
    }],
    level: {
      high: [{
        title: "",
        content: ""
      }],
      low: [{
        title: "",
        content: "",
      }]
    },
    source: {
      dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
      options: [
        {
          id: '', // 选项id 记忆组件对应的id
          initPic: '', // 初始图片
          initPicLocation: {
            x: '',
            y: ''
          },
          popupPic: '', // 弹窗图片
          popupPicLocation: {
            x: '',
            y: ''
          },
          isShowMask: '1', // 是否展示蒙层 1是 0否
          textPic: '', // 文字图片
          textPicLocation: {
            x: '',
            y: ''
          },
          contentAudio: '', // 内容音频
          knowledgePoints: '', // 知识点
          canChooseAgain: '0' // 是否可以再次选择 0 不限制 1 选择后不可再选（前端置灰状态）
        }
      ]
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
    },
    avatarUpWallData: avatarUpWallData()
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [], //交互提示标签
};
$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    console.log(res,'res');
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (Data.configData.source && Data.configData.source.options) {
        Data.configData.source.options.forEach(item => {
          if (!item.textImageLocation) item.textImageLocation = { x: '', y: '' };
        });
      }

      // todo IP组件初始化数据
      if (!Data.configData.source.dialogs) {
        Data.configData.source.dialogs = JSON.parse(
          JSON.stringify(dialogsInitData)
        );
      }
      if (!Data.configData.source.dialogs.scale) {
        Data.configData.source.dialogs.scale = 100;
      }
      if (!Data.configData.source.dialogs.autoNext) {
        Data.configData.source.dialogs.autoNext = "2";
      }
      if (!Data.configData.source.dialogs.hiddenStatus) {
        Data.configData.source.dialogs.hiddenStatus = "1";
      }
      if (
        Data.configData.source.dialogs.roleLocationX === "" ||
        Data.configData.source.dialogs.roleLocationX === undefined
      ) {
        Data.configData.source.dialogs.roleLocationX = "100";
      }
      if (
        Data.configData.source.dialogs.roleLocationY === "" ||
        Data.configData.source.dialogs.roleLocationY === undefined
      ) {
        Data.configData.source.dialogs.roleLocationY = "600";
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [{
            title: "",
            content: ""
          }],
          low: [{
            title: "",
            content: "",
          }]
        }
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        }
        Data.configData.log_editor = {
          isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }
      }
      if(!Data.configData.instructions){
        Data.configData.instructions = [{
            commandId: '-1'
        }]
      }
      console.log(Data)
      initializeAvatarUpWall(Data);
    }
    // 获取记忆表单数据 这里获取同时会放入Data.configData.source.memoryForm
    getMemoryForm(Data)
  },
  error: function (res) {
    console.log(res)
  }
});

new Vue({
  el: '#container',
  data: Data,
  mounted: function() {
    this.getDynamicInstructions();
  },
  methods: {
    getDynamicInstructions: function() {
      var that = this;
      getDynamicInstructions(function(res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function() {
        addInstruction(this.configData);
    },
    removeInstruction: function(index) {
        removeInstruction(index, this.configData);
    },
    validateInstructions: function() {
        return validateInstructions(this.configData);
    },
    feedbackUpload: function (e, item, attr, fileSize) {
      console.log(e, item, attr, fileSize);
      const file = e.target.files[0];
      if (file.type === "image/png" || file.type === "image/jpeg") {
        this.imageUpload(e, item, attr, fileSize);
      } else {
        this.lottieUpload(e, item, attr, fileSize);
      }
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr, 'json');
        } else {
        }
      };
      reader.readAsText(file);
    },

// 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },

// 添加对话框
    addDialog: function () {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: "",
      });
    },
// 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },


    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },

//ip组件音频上传
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e);
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为：" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "KB上限，请检查后上传！"
        );
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height + 1 > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
          size.join("或") +
          ", 但上传图片尺寸为：" +
          data.width +
          "*" +
          data.height
        );
      }
      return checkSize;
    },
    deleConfig(item) {
      this.configData.source.options.remove(item);
    },
    addConfig() {
      this.configData.source.options.push( {
        id: '', // 选项id 记忆组件对应的id
        initPic: '', // 初始图片
        initPicLocation: {
          x: '',
          y: ''
        },
        popupPic: '', // 弹窗图片
        popupPicLocation: {
          x: '',
          y: ''
        },
        isShowMask: '1', // 是否展示蒙层 1是 0否
        textPic: '', // 文字图片
        textPicLocation: {
          x: '',
          y: ''
        },
        contentAudio: '', // 内容音频
        knowledgePoints: '', // 知识点
        canChooseAgain: '0' // 是否可以再次选择 1是 0否
      })
    },

    // 获取可用选项（排除已选ID）
    getAvailableOptions: function(currentItem) {
      // 获取所有已被选择的ID列表（排除当前项的ID）
      const selectedIds = this.configData.source.options
        .filter(item => item !== currentItem && item.id !== '')
        .map(item => item.id);

      // 返回未被选中的选项
      if (!this.configData.source.memoryForm) {
        return [];
      }
      return this.configData.source.memoryForm.filter(item =>
        !selectedIds.includes(item.id)
      );
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width >= data.width && height >= data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
        e.target.value = '';
      }
      return checkSize;
    },
    validate: function () {
      const dialogs = this.configData.source.dialogs;
      if (dialogs.roleImg) {
        for (let index = 0; index < dialogs.messages.length; index++) {
          const item = dialogs.messages[index];
          const {text, audio} = item;
          if (!text || !audio) {
            return alert("请上传对话内容");
          }
        }
      }
      let options = this.configData.source.options
      let mag = ''
      options.forEach(function (item, index) {
        if (item.id === '') {
          mag = '请选择初始状态'
        } else if (item.initPic === '') {
          mag = '请上传初始图片'
        } else if (item.popupPic === '') {
          mag = '请上传弹窗图片'
        } else if (item.knowledgePoints === '') {
          mag = '请填写知识点'
        }
      })
      if (mag) {
        alert(mag)
        return;
      }
      return true;
    },
    onSend: function () {
      var data = this.configData;
      console.log('data',data.source.options);
      avatarUpWallSend(data);
      //计算"建议教学时长"
      if (data.log_editor.isTeachTimeOther == '-2') { //其他
        data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长")
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther
      }
      if (this.validate() && this.validateInstructions()) {
        // 将数据保存到localStorage
        setMemoryForm(data)
        console.log('提交数据-------')
        console.log(data)
        var _data = JSON.stringify(data);
        $.ajax({
          url: domain + 'content?_method=put',
          type: 'POST',
          data: {
            content: _data
          },
          success: function (res) {
            window.parent.postMessage('close', '*');
          },
          error: function (err) {
            console.log(err)
          }
        });
      } else {
        // alert("带有"*"号为必填项！");
      }
    },
    postData: function (file, item, attr, filetype) {
      var FILE = 'file';
      let bg = arguments.length > 2 ? arguments[2] : null;
      var oldImg = item[attr];
      var data = new FormData();
      data.append('file', file);
      if (oldImg != "") {
        data.append('key', oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: 'post',
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          item[attr] = domain + res.data.key;
        },
        error: function (err) {
          item[attr] = '';
        }
      })
    },
    audioUpload: function (e, item, attr) {
      //校验规则
      //var _type = this.rules.audio.sources.type;

      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      if ((size / 1024).toFixed(2) > 500) {
        console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      } else {
        console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      }
      if ((!isNaN(parseInt($(e.target).attr('volume')))) && (size / 1024).toFixed(2) > parseInt($(e.target).attr('volume'))) {
        alert("您上传的音频大小为" + (size / 1024).toFixed(2) + "KB, 超过" + $(e.target).attr('volume') + "K上限，请检查后上传！");
        e.target.value = '';
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      this.postData(file, item, attr);
    },
    addTg: function (item) {
      this.configData.tg.push({title: '', content: ''});
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    addH: function () {
      this.configData.level.high.push({title: '', content: ''});
    },
    addL: function (item) {
      this.configData.level.low.push({title: '', content: ''});
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    delPrew: function (item, prop) {
      item[prop] = ''
    },
    teacherChange: function(data) {
      teacherChange(data);
    },
    studentChange: function(data) {
      studentChange(data);
    },
  }
});
// 定义数组的remove方法，确保在使用前就已经定义
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
