var configData = {
  bg: '',
  desc: '',
  title: '',
  tg: [
    {
      content: "c",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
  ],
  level: {
    high: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      }],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "",
          audio: "",
        },
      ],
      messageLocationX: "", // 消息内容位置x
      messageLocationY: "", // 消息内容位置y
      roleLocationX: "100", // 角色位置x
      roleLocationY: "600", // 角色位置y
      roleImg: "", // 角色图片
      playAfterStauts: "2", // 播放完之后状态
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框
      hiddenStatus: "1", // 播放完是否应藏的状态
    },
    options: [
      {
        id: '1', // 选项id 记忆组件对应的id
        initPic: 'assets/images/Doll_turn1.json', // 初始图片
        initPicLocation: {
          x: '100',
          y: '100'
        },
        popupPic: 'assets/images/Doll_turn1.json', // 弹窗图片
        popupPicLocation: {
          x: '',
          y: ''
        },
        isShowMask: '0', // 是否展示蒙层 1是 0否
        textPic: 'assets/images/text.png', // 文字图片
        textPicLocation: {
          x: '150',
          y: '150'
        },
        contentAudio: 'assets/audios/01.mp3', // 内容音频
        knowledgePoints: '345', // 知识点
        canChooseAgain: '0' // 是否可以再次选择 1是 0否
      },
      {
        id: '1', // 选项id 记忆组件对应的id
        initPic: 'assets/images/1.png', // 初始图片
        initPicLocation: {
          x: '500',
          y: '500'
        },
        popupPic: 'assets/images/Doll_turn1.json', // 弹窗图片
        popupPicLocation: {
          x: '699',
          y: '699'
        },
        isShowMask: '1', // 是否展示蒙层 1是 0否
        textPic: '', // 文字图片
        textPicLocation: {
          x: '200',
          y: '300'
        },
        contentAudio: 'assets/audios/01.mp3', // 内容音频
        knowledgePoints: 'knowledgePoints', // 知识点
        canChooseAgain: '0' // 是否可以再次选择 1是 0否
      }
    ]
  }
};
