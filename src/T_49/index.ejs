<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TSA0001_解救艾玛"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult='show-result-1'>
    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>
    
    <section class="main">
        <audio src="audio/down.mp3" class="audioDown" data-syncaudio="audioDown"></audio>
        <audio src="audio/up.mp3" class="audioUp" data-syncaudio="audioUp"></audio>
        <audio src="audio/win.mp3" class="audioWin" data-syncaudio="audioWin"></audio>
        <div class="answBtn" >The student's answer: <span class="ansright ansRafter" data-syncactions="ansright"></span><span class="answrong ansLafter" data-syncactions="answrong"></span></div>
        <div class="timeArea">
            <!-- <div class="time"></div>-->
            <!-- <div class="timeBtn" data-syncactions="timeBtn"></div>     -->
        </div>
        
        <div class="mainArea">
            <div class="left">
                <div class="goUpBOx">
                    <div class="line"></div>
                    <div class="king hide"></div>
                    <div class="stand"></div>
                </div>
                <div class="door hide" ></div>
                <div class="singelFamily"></div>
            </div>
            <div class="right">
                <!-- <ul class="picArea">
                    <li class="list"><img src="assets/images/1.jpg" alt=""></li>
                    <li class="list"><img src="" alt=""></li>
                </ul>
                <div class="text">ggrtty</div>
                <div class="audioList" data-syncactions="audioBtn">
                    <img src="image/btn-audio.png" />
                    <audio class="audioSrc" webkit-playsinline controls src=""></audio>
                </div> -->
                <!-- <div class="page">
                    <span class="current">1</span>
                    <span class="totalPage"></span>
                </div> -->
            </div>
        </div>
    </section>

</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
