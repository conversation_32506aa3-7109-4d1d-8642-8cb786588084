"use strict"
import '../../common/js/common_1v1.js'
import './drag.js'
$(function () {
  window.h5Template = {
    hasPractice: '0'
  }
  let h5SyncActions = parent.window.h5SyncActions;
  let options = configData.source.options;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  let source = configData.source
  let goHigh = 4.97 / options.length   //每次爬杆距离长度
  // let lock = true   //倒计时结束后，老师无操作，则倒计时恢复到初始状态的标识
  let startAnsIndex = 0     //答题进度
  // let timer = null;
  if (configData.bg == '') {
    $(".container").css({ 'background-image': 'url(./image/bg.jpg)' })
  }

  const page = {
    showOption: function () {
      let html = ''
      html = `<ul class="picArea">
                  <li class="list"><img src="${options[0].image}" alt=""></li>
              </ul>
              <div class="text">ggrtty</div>
              <div class="audioList" data-syncactions="audioBtn">
                  <img src="image/btn-audio.png" />
                  <audio class="audioSrc" webkit-playsinline controls src="${options[0].audio}"  data-syncaudio="audioSrc"></audio>
              </div>
              <div class="page">${1 + ' / ' + options.length}</div> `
      $(".right").append(html)

    },
    showStyle: function (index) {
      // $('.time').text(options[index].time + "s")
      $(".page").text(index - 0 + 1 + "/" + options.length)
      if (options[index].isMax == 2) {
        $(".right").css({
          width: '7.4rem',
          height: '6.8rem',
          right: '1.4rem'
        })
        if (options[index].image == '') {
          $(".text").text(options[index].text)
          $(".list").addClass('hide')
          $(".text").removeClass('hide textMiddle').addClass("bigText").css({width: '7.4rem', height: '6.8rem',top:'0'})
          $(".audioList").css({ bottom: '0.05rem' })
          $(".page").css({ bottom: '.2rem' })
          //console.log("111111")
        } else {
          $(".list").removeClass('hide')
          $(".list").eq(0).find("img").attr('src', options[index].image)
          if (options[index].text == '') {
            $(".picArea").removeClass('hide').css({ top: '0', width: '7.4rem', height: '5.52rem' })
            $(".text").addClass("hide")
            $(".picArea").removeClass('hide').css({ top: '1.08rem' })
            $(".audioList").css({ bottom: '.3rem' })
            $(".page").css({ bottom: '.45rem' })
          } else {
            $(".picArea").removeClass('hide').css({ top: '0', width: '7.4rem', height: '5.52rem' })
            $(".text").removeClass("hide  bigText").css({ top: '5.7rem', width: '7.4rem', height: '1.3rem' })
            $(".text").text(options[index].text).addClass("textMiddle")
            $(".page").css({ bottom: '1.6rem' })
            $(".audioList").css({ bottom: '1.35rem' })
          }
        } 
      } else {
        $(".list").removeClass('hide')
        $(".list").eq(0).find("img").attr('src', options[index].image)
        $(".text").addClass('hide')
        $(".right").css({
          width: '7.4rem',
          height: '6.8rem',
          right: '1.4rem'
        })
        $(".picArea").css({
          width: '7.4rem',
          height: '6.8rem',
          top:0
        })
        $(".audioList").css({ bottom: '0.05rem' })
        $(".page").css({ bottom: '0.2rem' })
      }
      if (options[index].audio == '') {
        $(".audioList").addClass('hide')
      } else {
        $(".audioList").removeClass('hide')
        $(".audioList").find("audio").attr('src', options[index].audio)
      }
    },
    init: function () {
      this.showOption()
      this.showStyle(0)
      $(".answBtn").drag()
      if (isSync&&window.frameElement.getAttribute('user_type') == 'stu') {
        $(".answBtn").addClass('hide')
      }
    }
  }
  page.init()

  let isRight = true

  let $audioUp = document.getElementsByClassName('audioUp')[0]
  let $audioWin = document.getElementsByClassName('audioWin')[0]
  $('.ansright').on("click touchstart", function (e) {
    if (e.type = "touchstart") {
      e.preventDefault()
    }
    if (isRight) {
      isRight = false
      startAnsIndex++
      if (!isSync) {
        $(this).trigger("ansRightSync")
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'ansRightSync',
          otherInfor: {
            startAnsIndex: startAnsIndex
          },
          recoveryMode: '1'
        });
      }
    }
  })

  $('.ansright').on("ansRightSync", function (e, message) {
    $(".king").removeClass("hide")
    $(".stand").addClass("hide")
    if (!isSync) {
      startAnsIndex = startAnsIndex - 0
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      startAnsIndex = obj.startAnsIndex - 0
      if (message == undefined || message.operate == 1) {

      } else {
        recovery(startAnsIndex, 'end')
        SDK.setEventLock()
        return
      }
    }
    $(this).removeClass('ansRafter')
    if (startAnsIndex >= options.length) {
      // $audioWin ? $audioWin.play() : "";
      SDK.playRudio({
        index: $audioWin,
        syncName: $('.audioWin').attr("data-syncaudio")
      })
      $(".king").addClass('hide')
      $(".door").removeClass('hide')
      if (isSync&&window.frameElement.getAttribute('user_type') == 'tea') {
        $(".answBtn").addClass('hide')
      }
      if(!isSync){
        $(".answBtn").addClass('hide')
      }
      
      $(".singelFamily").addClass('family')
    } else {
      new Promise((resolve, reject) => {
        setTimeout(() => {
          // $audioUp ? $audioUp.play() : "";
          SDK.playRudio({
            index: $audioUp,
            syncName: $('.audioUp').attr("data-syncaudio")
          })
          $(".king").css({
            bottom: startAnsIndex * goHigh + "rem",
            transition: '0.2s'
          })
          resolve()
        }, 200)
      }).then(() => {
        setTimeout(() => {
          if (options[startAnsIndex - 1].audio != '') {
            let audioEle = $('.audioSrc').get(0)
            // audioEle.pause()
            SDK.pauseRudio({
              index: audioEle,
              syncName: $('.audioSrc').attr("data-syncaudio")
            })
            $('.audioList img').attr('src', './image/btn-audio.png')
          }
          page.showStyle(startAnsIndex)
          $(this).addClass('ansRafter')
          isRight = true
          SDK.setEventLock()
        }, 1300)
      })
    }

  })


  let $audioDown = document.getElementsByClassName('audioDown')[0]
  let isWrong = true
  $('.answrong').on("click touchstart", function (e) {
    if (e.type = "touchstart") {
      e.preventDefault()
    }
    if (isWrong) {
      isWrong = false
      if (!isSync) {
        $(this).trigger("ansWrongSync")
        return
      }
      if (isSync&&window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'ansWrongSync',
          otherInfor: {
            startAnsIndex: startAnsIndex
          },
          recoveryMode: '1'
        });
      }
    }
  })

  $('.answrong').on("ansWrongSync", function (e, message) {
    $(".king").removeClass("hide")
    $(".stand").addClass("hide")
    if (!isSync) {
      startAnsIndex = startAnsIndex - 0
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      startAnsIndex = obj.startAnsIndex - 0
      if (message == undefined || message.operate == 1) {

      } else {
        recovery(startAnsIndex, 'end')
        SDK.setEventLock()
        return
      }
    }
    $(this).removeClass('ansLafter')
    new Promise((resolve, reject) => {
      setTimeout(() => {
        $(".king").css({
          bottom: startAnsIndex * goHigh + goHigh + "rem",
          transition: '0.2s'
        })
        $(".leg").css({
          bottom: startAnsIndex * goHigh + goHigh + "rem",
          transition: '0.2s'
        })
        resolve()
      }, 200)
    }).then(() => {
      setTimeout(() => {
        //$audioBg.currentTime=0;
        // $audioDown ? $audioDown.play() : "";
        SDK.playRudio({
          index: $audioDown,
          syncName: $('.audioDown').attr("data-syncaudio")
        })
        $(".king").css({
          bottom: startAnsIndex * goHigh + "rem",
          transition: '0.5s ease-out'
        })
        $(".leg").css({
          bottom: startAnsIndex * goHigh + "rem",
          transition: '0.5s ease-out'
        })
        $(this).addClass('ansLafter')
        isWrong = true
        SDK.setEventLock()
      }, 800)
    })
  })


  
  //恢复机制

  function recovery(startAnsIndex, type) {
    if (startAnsIndex >= options.length) {
      $(".king").addClass('hide')
      $(".singelFamily").addClass('family')
      $(".door").removeClass('hide')
      if (window.frameElement.getAttribute('user_type') == 'tea') {
          $(".answBtn").addClass('hide')    
      }

      page.showStyle(startAnsIndex - 1)
    } else {
      $(".king").css({ bottom: startAnsIndex * goHigh + "rem" })
      page.showStyle(startAnsIndex)
    }
  }


  // 音频播放
  let audioEle;
  let play = false
  let audioPlay = function (message, currentPage) {
    audioEle = $('.audioSrc').get(0)
    if (!isSync) {
      // audioEle.play()
      SDK.playRudio({
        index: audioEle,
        syncName: $('.audioSrc').attr("data-syncaudio")
      })
      $('.audioList img').attr('src', './image/btn-audio.gif')
    } else {
      if ($(window.frameElement).attr('id') === 'h5_course_self_frame') {
        if (message == undefined || message.operate == 1) {
          // audioEle.play()
          SDK.playRudio({
            index: audioEle,
            syncName: $('.audioSrc').attr("data-syncaudio")
          })
          $('.audioList img').attr('src', './image/btn-audio.gif')
        }
      }
    }
  }
  let audioPause = function (message, currentPage) {
    if (!isSync) {
      // audioEle.pause()
      SDK.pauseRudio({
        index: audioEle,
        syncName: $('.audioSrc').attr("data-syncaudio")
      })
    } else {
      if (message == undefined || message.operate == 1) {
        // audioEle.pause()
        SDK.pauseRudio({
          index: audioEle,
          syncName: $('.audioSrc').attr("data-syncaudio")
        })
      }
    }
    $('.audioList img').attr('src', './image/btn-audio.png')
  }
  let currentPage = 0;
  let audioClick = true;
  $('.audioList').on('click', function (e) {
    if (audioClick) {
      audioClick = false
      if (!isSync) {
        $(this).trigger('syncAudioClick')
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'syncAudioClick',
          funcType: 'audio'
        })
      } else {
        $(this).trigger('syncAudioClick')
      }
    }

  })

  $('.audioList').on('syncAudioClick', function (e, message) {
    if (play) {
      audioPause(message, currentPage)
    } else {
      audioPlay(message, currentPage)
      audioEle.onended = function () {
        $('.audioList img').attr('src', './image/btn-audio.png')
      }
    }
    SDK.setEventLock()
    audioClick = true
  })
})



