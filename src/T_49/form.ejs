<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TSA0001_解救艾玛</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TSA0001_解救艾玛</div>
			
			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">设置学习内容（必填）</div>
				<div class="c-area upload img-upload">
					<span>学习内容的组数量：2～6组 。(请先选择内容格式，否则无法上传素材)</span>
					<ul>
						<li v-for="(item, index) in configData.source.options">	
							
							<div class="c-well">
								<div class="field-wrap">
									<label class="field-label" >内容格式：</label>
									<input type="radio" name="" value=2 v-model="item.isMax"> 小尺寸图 、文本 （二者选填）<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<input type="radio" name="" value=1 v-model="item.isMax"> 大尺寸图 （禁用上传文本）<br>
								</div>
								<div class="field-wrap">
									<label class="field-label"  for="">图片{{index+1}}</label>
									<span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤50KB  <br>
										小图尺寸大小:740x552 <br>大图尺寸大小:740x680 
									</em><span class="dele-tg-btn" @click="delOption(item)" v-show="configData.source.options.length>2"></span></span> 
									<input type="file"  :disabled="item.isMax==''" v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" :size="item.isMax==2?'740*552':'740*680'" @change="imageUpload($event,item,'image',50)"  accept="image/png,image/gif,image/jpeg">
								</div>
		
								<div class="field-wrap">
									<label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.image">上传</label>
									<label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.image">重新上传</label>
								</div>
								<div class="img-preview" v-if="item.image">
									<img :src="item.image" alt=""/>
									<div class="img-tools">
										<span class="btn btn-delete" @click="delPrew(item)">删除</span>
									</div>
								</div>	
								<label :for="'content-text-'+index">文本{{index+1}}&nbsp;&nbsp;<em>上传图片时，字符数量0～50个；若不上传图片，字符数量0～100个 </em></label>
								<input type="text" class='c-input-txt' :disabled="item.isMax==1"  placeholder="请在此输入文本" v-model="item.text" maxlength="100">
								<!-- 上传声音 -->
								<div class="field-wrap">
									<label class="field-label"  for="">内容声音&nbsp;&nbsp;<em>文件大小≤50KB</em></label>
									<div>
										<label :for="'audio-upload-'+index" class="btn btn-show upload" v-if="item.audio==''?true:false">上传</label>
										<label  :for="'audio-upload-'+index" class="btn upload re-upload mar" v-if="item.audio!=''?true:false">重新上传</label>
									</div>
									<div class="audio-preview" v-show="item.audio!=''?true:false">
										<div class="audio-tools">
											<p v-show="item.audio!=''?true:false">{{item.audio}}</p>
										</div>
										<span class="play-btn" v-on:click="play($event)">
											<audio v-bind:src="item.audio"></audio>
										</span>
									</div>
									<span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio=''">删除</span>
									<input type="file" :id="'audio-upload-'+index" :disabled="item.isMax==''" class="btn-file upload" size="" accept=".mp3" v-on:change="audioUpload($event,item,'audio',50)" v-bind:key="Date.now()">
								</div>
								<!-- 时间 -->
								<!-- <div class="field-wrap">
									<label>每组学习内容的计时&nbsp;&nbsp;<em>不能超过30s</em></label>
									<input type="text" class='c-input-txt' maxlength="" placeholder="请在此输入≤30的正整数" v-model="item.time">
								</div>	 -->
							</div>
						</li>
					</ul>
					<button v-if="configData.source.options.length<6" type="button" class="add-tg-btn" @click="addSele" >+</button>	
				</div>
				
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
