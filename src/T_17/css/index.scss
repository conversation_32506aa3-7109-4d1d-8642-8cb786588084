@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.hide{
    display: none;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.container{
    // background: url(../image/defaultBg.png) no-repeat;
    background-size: auto 100%;
	position: relative;
    // font-family:"ARLRDBD";

}
.stage{
	width: 100%;
    height: 100%;
    position: relative;
    height: 100%;
    .card_list {
        width: 100%;
        height: 3.75rem;
        position: absolute;
        left: 0;
        top: 4rem;

        ul {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            li {
                height: 100%;
                width:5rem;
                margin-left: .5rem;
                margin-right: .5rem;
                border-radius: .3rem;
                border: 2px solid #fff;
                overflow: hidden;
                transition: transform .5s;
                z-index: 2;
                position: relative;
                cursor: pointer;
                img {
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    right: 0;
                    margin: auto;
                    width: 100%;
                    height: 100%;
                    z-index: 0;
                }
                .span {
                    position: absolute;
                    left: 0;
                    bottom: 0rem;
                    display: block;
                    width: 100%;
                    height: .8rem;
                    background: rgba(225, 225, 225, .7);
                    z-index: 3;
                    text-align: center;
                    line-height: .8rem;
                    font-size: .6rem;
                }
                .font_width {
                    height: .8rem;
                    font-size: .6rem;
                    white-space:nowrap;
                    opacity: 0;
                }
                .hide {
                    display: none;
                }
            }
            .changeMax {
                transform: scale(1.3);
                z-index: 3;
            }
        }
    }
    .font {
        width: 100%;
        height: 1rem;
        position: absolute;
        left: 0;
        bottom: .8rem;
        font-size: .8rem;
        text-align: center;
        line-height: 1rem;
        span {
            display: inline-block;
            width: 2rem;
            height: .05rem;
            background: black;
        }
    }
}

