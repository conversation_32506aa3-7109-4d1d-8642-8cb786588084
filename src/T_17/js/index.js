"use strict"
import '../../common/js/common_1v1.js'

$(function () {
    window.h5Template = {
    	hasPractice: '1' 
    }
    let h5SyncActions = parent.window.h5SyncActions;
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

    if (configData.bg == '') {
        $(".container").css({ 'background-image': 'url(./image/defaultBg.png)' })
    }

    /**
    * 创建dom元素
    */
    let cardHtml = '';
    for (let i = 0; i < configData.source.cardList.length; i++) {
        cardHtml += `<li data-syncactions="item_${i}">
            <audio src="./audio/click.mp3" data-syncaudio="audioClik" />
            <img src="${configData.source.cardList[i].img}"/>
            <span class='span ${configData.source.cardList[i].font==""?"hide":""}'>${configData.source.cardList[i].font}</span>
            <span class="font_width">${configData.source.cardList[i].font}</span>
        </li>`
    }
    let fontCon = '';
    for (let j = 0; j<configData.source.font.length; j++) {
        if (configData.source.font[j].hasOwnProperty('attributes') && configData.source.font[j].attributes.hasOwnProperty('underline')) {
            fontCon += `<span></span>`
        } else {
            fontCon += configData.source.font[j].insert
        }
    }
    $('.card_list ul').html(cardHtml);
    $('.font').html(fontCon);
    for (let i = 0; i<$('li').length; i++) {
        if ($('li').eq(i).find('.font_width').width()>$('li').width()) {
            $('li').eq(i).find('.span').css({
                'font-size':'.4rem'
            })
        }
    }
    if (configData.source.cardList.length>3) {
        $('.card_list ul').css({
            'height':'4.2rem'
        })
        $('.card_list ul li').css({
            'width':'3.3rem'
        })
    } 
    let itemClick = true;
    $('.card_list li').on('click touchstart', function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (itemClick) {
            itemClick = false;
            if (!isSync) {
                $(this).trigger('buttonClick')
                return;
            }
            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                funcType: 'audio',
                syncName: 'buttonClick',
                otherInfor: {
                    itemIndex: $(e.currentTarget).data('syncactions')
                },
                recoveryMode: '1'
            });
        }
    })
    $('.card_list li').on('buttonClick', function (e, message) {
        // $(this).find('audio').get(0).play();
        SDK.playRudio({
            index: $(this).find('audio').get(0),
            syncName:  $(this).find('audio').attr("data-syncaudio")
        })
        $(this).addClass('changeMax').siblings().removeClass('changeMax');
        itemClick = true;
        SDK.setEventLock();
    })
})
