@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import '../../common/template/multyDialog/style.scss';

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;

  .desc {
    top: 0.6rem;
  }

  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.container {
  background-size: auto 100%;
  position: relative;

  .content-main {

    #game-container {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .start-screen {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 98;
      .search {
        position: absolute;
        width: 12rem;
        height: 7.87rem;
        top: 0.5rem;
        left: 3.47rem;
        background-image: url('../image/start-c.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;

        .start-fruit {
          position: absolute;
          width: 10rem;
          height: 2.2rem;
          top: 4.88rem;
          left: 1.04rem;
          display: flex;
          justify-content: space-between;

          li {
            width: 2.2rem;
            height: 2.2rem;
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            .anim-container-start{
              width: 100%;
              height: 100%;
            }
            .img-start{
              display: block;
              width: 100%;
              // height: 100%;
            }
          }
        }
      }

      .start-button {
        position: absolute;
        width: 3.18rem;
        height: 1.16rem;
        left: 7.76rem;
        top: 8.77rem;
        background-image: url('../image/start-b.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
        // display: none;
      }
    }

    #main-scene {
      position: relative;
      width: 100%;
      height: 100%;
      // background-color: #ffffff;
      overflow: hidden;
    }

    .pe {
      pointer-events: none;
    }

    .item {
      position: absolute;
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .item img {
      display: block;
      width: 100%;
      height: 100%;
    }

    .item .clickImg {
      display: block;
      width: 100%;
      height: 100%;
    }

    .item.found {
      pointer-events: none;
      // animation: foundEffect 2s infinite;
    }

    @keyframes foundEffect {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.8;
      }
    }

    @keyframes shake {

      0%,
      100% {
        transform: translateX(0);
      }

      25% {
        transform: translateX(-0.05rem);
      }

      50% {
        transform: translateX(0.05rem);
      }

      75% {
        transform: translateX(-0.05rem);
      }
    }

    @keyframes hintShake {

      0%,
      100% {
        transform: translateX(0);
      }

      25% {
        transform: translateX(-8px);
      }

      50% {
        transform: translateX(8px);
      }

      75% {
        transform: translateX(-8px);
      }
    }

    #target-word {
      position: absolute;
      display: none;
      z-index: 97;
    }

    #target-bar {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
    }

    .target-cell {
      position: relative;
      box-sizing: border-box;
      width: 1rem;
      height: 1rem;
      border-radius: 0.1rem;
      border: 0.05rem solid #FFFFFF;
      background-color: #ffffff;
    }

    .target-cell img {
      display: block;
      width: 90%;
      height: 90%;
      padding: 5%;
    }

    .target-cell .anim-container {
      display: block;
      width: 90%;
      height: 90%;
      padding: 5%;
    }

    .target-cell .checkmark {
      position: absolute;
      width: 0.56rem;
      height: 0.46rem;
      left: 0.17rem;
      top: 0.22rem;
      // width: 0.5rem;
      // height: 0.4rem;
      // left: 0.6rem;
      // top: -0.16rem;
      opacity: 0;
      background-image: url('../image/correct.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
    }

    .target-cell.found .checkmark {
      opacity: 1;
    }

    #modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.7);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    #modal-content {
      position: relative;
      width: 8.4rem;
      height: 6.6rem;
      // background-color: #FFA65F;
      border-radius: 0.3rem;
    }

    .correct-animation {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    #modal-item-img {
      display: block;
      width: 100%;
      height: 100%;
    }

    .microphone {
      position: absolute;
      width: 2.6rem;
      height: 1.8rem;
      left: 2.63rem;
      bottom: 0rem;
      z-index: 1000;
    }

    .star {
      position: absolute;
      z-index: 1000;
      display: none;
    }

    .correct {
      position: absolute;
      width: 1.04rem;
      height: 1.04rem;
      left: 6.52rem;
      bottom: 0.44rem;
      background: url("../image/correct-yuan.png") no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      display: none;
      z-index: 1000;
    }

    .star-animation {
      position: absolute;
      pointer-events: none;
      animation: starFade 1s forwards;
    }

    @keyframes starFade {
      0% {
        transform: scale(0.5);
        opacity: 0;
      }

      50% {
        transform: scale(1.2);
        opacity: 1;
      }

      100% {
        transform: scale(1.5);
        opacity: 0;
      }
    }

    //小手点击动画
    .handsss {
      width: 1.8rem;
      height: 1.8rem;
      background: url('../image/hands.png');
      background-size: 7.2rem 1.8rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.7);
      animation: handClick 0.8s steps(4) infinite;
      opacity: 1;
      cursor: pointer;
      z-index: 1000;
      display: none;
      pointer-events: none;
    }

    @keyframes handClick {
      0% {
        background-position: 0 0;
      }

      100% {
        background-position: 133% 0;
      }
    }

    @-webkit-keyframes handClick {
      0% {
        background-position: 0 0;
      }

      100% {
        background-position: 133% 0;
      }
    }
  }
}
