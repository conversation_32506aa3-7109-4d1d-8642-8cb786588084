"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import "../../common/template/multyDialog/index.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量

const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
    SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      item: configData.source.options.length || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.SPEAK,
    },
  });
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "3", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };

  let options = configData.source.options, //正确项
    interferenceOptions = configData.source.interferenceOptions, //干扰项
    collectionDisplay = configData.source.collectionDisplay, //收集框是否显示
    collectionPosition = configData.source.collectionPosition, //收集框显示位置
    userType =
      window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师

  let foundItems = [];
  let lastClickTime = new Date().getTime();
  let hintInterval;
  let currentHintItem = null;
  let clickAble = false; //item是否可点击
  let soundClick = true;
  let microphone = $(".microphone"); //麦克风
  let microphoneJson = null; //麦克风动画
  let starJson = null; //麦克风动画
  let correctJson = null; //正确动画
  // 缓存动画对象
  const animationsCache = {};

  // 检查是否为JSON动画
  function isJsonAnimation(path) {
    return typeof path == "string" && path.toLowerCase().endsWith(".json");
  }
  // 初始化开始界面
  startFun();
  function startFun() {
    let startList = options.slice(0, 4);
    startList.forEach((item) => {
      const itemElement = $(
        '<li class="start-fruit-li" data-id="' +
          item.id +
          '" data-correct="true" data-syncactions="startItemtions' +
          item.id +
          '"></li>'
      );

      if (isJsonAnimation(item.clickImg)) {
        // 为JSON动画创建容器
        const animContainer = $('<div class="anim-container-start"></div>');
        animContainer.attr("id", "anim-start-" + item.id);
        itemElement.append(animContainer);

        // 异步加载动画
        setTimeout(async () => {
          const animObj = await lottieAnimations.init(
            animObj,
            item.clickImg,
            "#anim-start-" + item.id,
            true
          );

          // 缓存动画对象以便后续使用
          animationsCache["anim-start-" + item.id] = animObj;

          // 播放一次动画
          lottieAnimations.play(animObj);
        }, 100);
      } else {
        const img = $("<img class='img-start'>").attr("src", item.clickImg);
        itemElement.append(img);
      }
      $(".start-fruit").append(itemElement);
    });
  }
  // 初始化游戏
  initGame();
  async function initGame() {
    // moduleAuthorizationFn("teaOnly", "11");
    // 添加正确的物品到场景
    options.forEach((item) => {
      const itemElement = $(
        '<li class="item" data-id="' +
          item.id +
          '" data-correct="true" data-syncactions="startItemtions' +
          item.id +
          '"></li>'
      );
      itemElement.css({
        left: item.positionX / 100 + "rem",
        top: item.positionY / 100 + "rem",
        zIndex: item.zIndex,
        transform: `scale(${item.scale / 100})`,
      });

      if (isJsonAnimation(item.clickImg)) {
        // 为JSON动画创建容器
        const animContainer = $('<div class="anim-container"></div>');
        animContainer.attr("id", "anim-" + item.id);
        itemElement.append(animContainer);

        // 异步加载动画
        setTimeout(async () => {
          const animObj = await lottieAnimations.init(
            animObj,
            item.clickImg,
            "#anim-" + item.id,
            true
          );

          // 缓存动画对象以便后续使用
          animationsCache["anim-" + item.id] = animObj;

          // 获取动画尺寸并应用到容器
          $.getJSON(item.clickImg, function (data) {
            itemElement.css({
              width: data.w / 100 + "rem",
              height: data.h / 100 + "rem",
            });
          });

          // 播放一次动画
          lottieAnimations.play(animObj);
        }, 100);
      } else {
        const img = $("<img>").attr("src", item.clickImg);
        // 图片加载完成后设置物品尺寸
        img.on("load", function () {
          const naturalWidth = this.naturalWidth;
          const naturalHeight = this.naturalHeight;
          // 设置物品div的尺寸为图片的实际尺寸
          $(this)
            .parent()
            .css({
              width: naturalWidth / 100 + "rem",
              height: naturalHeight / 100 + "rem",
            });
        });
        itemElement.append(img);
      }
      $("#main-scene").append(itemElement);
    });

    // 添加干扰项到场景
    interferenceOptions.forEach((item) => {
      const itemElement = $(
        '<li class="item pe" data-id="' +
          item.id +
          '" data-correct="false" data-syncactions="startItemtions' +
          item.id +
          '"></li>'
      );
      itemElement.css({
        left: item.positionX / 100 + "rem",
        top: item.positionY / 100 + "rem",
        zIndex: item.zIndex,
        transform: `scale(${item.scale / 100})`,
      });
      if (isJsonAnimation(item.clickImg)) {
        // 为JSON动画创建容器
        const animContainer = $('<div class="anim-container"></div>');
        animContainer.attr("id", "anim-error-" + item.id);
        itemElement.append(animContainer);

        // 异步加载动画
        setTimeout(async () => {
          const animObj = await lottieAnimations.init(
            null,
            item.clickImg,
            "#anim-error-" + item.id,
            true
          );

          // 缓存动画对象以便后续使用
          animationsCache["anim-error-" + item.id] = animObj;

          // 获取动画尺寸并应用到容器
          // 获取动画尺寸并应用到容器
          $.getJSON(item.clickImg, function (data) {
            itemElement.css({
              width: data.w / 100 + "rem",
              height: data.h / 100 + "rem",
            });
          });

          // 播放一次动画
          lottieAnimations.play(animObj);
        }, 100);
      } else {
        const img = $("<img>").attr("src", item.clickImg);
        // 图片加载完成后设置物品尺寸
        img.on("load", function () {
          const naturalWidth = this.naturalWidth;
          const naturalHeight = this.naturalHeight;
          // 设置物品div的尺寸为图片的实际尺寸
          $(this)
            .parent()
            .css({
              width: naturalWidth / 100 + "rem",
              height: naturalHeight / 100 + "rem",
            });
        });
        itemElement.append(img);
        $("#main-scene").append(itemElement);
      }
    });

    // 初始化目标物品栏
    options.forEach((item) => {
      const cell = $(
        '<div class="target-cell" data-id="' + item.id + '"></div>'
      );
      if (isJsonAnimation(item.clickImg)) {
        // 为JSON动画创建容器
        const animContainer = $('<div class="anim-container"></div>');
        animContainer.attr("id", "target-anim-" + item.id);
        cell.append(animContainer);

        // 异步加载动画
        setTimeout(async () => {
          const animObj = await lottieAnimations.init(
            null,
            item.clickImg,
            "#target-anim-" + item.id,
            true
          );

          // 缓存动画对象以便后续使用
          animationsCache["target-anim-" + item.id] = animObj;

          // 播放一次动画
          lottieAnimations.play(animObj);
        }, 100);
      } else {
        // 处理普通图片
        const img = $("<img>").attr("src", item.clickImg);
        cell.append(img);
      }
      const checkmark = $('<div class="checkmark"></div>');

      cell.append(checkmark);
      $("#target-bar").append(cell);
      if (collectionDisplay == "0") {
        $("#target-word").hide();
      } else {
        $("#target-word").show();
      }
      if (collectionPosition == "0") {
        $("#target-word").css({
          left: "17.8rem",
          top: "1.8rem",
          width: "1rem",
          height: "8.7rem",
        });
        $("#target-bar").css({
          "flex-direction": "column",
        });
        $(".target-cell").css({
          "margin-bottom": "0.1rem",
        });
      } else if (collectionPosition == "1") {
        $("#target-word").css({
          left: "5.26rem",
          top: "0.36rem",
          width: "8.7rem",
          height: "1rem",
        });
        $("#target-bar").css({
          "flex-direction": "row",
        });
        $(".target-cell").css({
          "margin-right": "0.1rem",
        });
      } else if (collectionPosition == "2") {
        $("#target-word").css({
          left: "5.26rem",
          top: "9.54rem",
          width: "8.7rem",
          height: "1rem",
        });
        $("#target-bar").css({
          "flex-direction": "row",
        });
        $(".target-cell").css({
          "margin-right": "0.1rem",
        });
      }
    });
    // if (isSync) {
    //   moduleAuthorizationFn("teaOnly", "12");
    // }
    // clickAble = true;

    // 启动提示计时器
    startHintTimer();
  }
  // 点击start按钮
  $(".start-button").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();

    if (!isSync) {
      $(this).trigger("syncStartClick");
      return;
    }
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_STARTBUTTON",
        data: {},
      },
      USER_TYPE.TEA
    );
    SDK.bindSyncEvt({
      sendUser: "",
      receiveUser: "",
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "syncStartClick",
      funcType: "audio",
    });
  });
  $(".start-button").on("syncStartClick", function (e, message) {
    $(".start-screen").hide();
    moduleAuthorizationFn("teaOnly", "12");
    clickAble = true;
    SDK.setEventLock();
  });
  // 为物品添加点击处理程序
  $("#main-scene li").on("click touchstart", function (e) {
    console.log("点击了物品1");
    if (clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation();

      if (soundClick) {
        soundClick = false;
        SDK.reportTrackData(
          {
            action: "CK_FT_INTERACTION_ITEM",
            stuData: {
              item: $(this).index() + 1,
            },
          },
          USER_TYPE.STU
        );
        if (!isSync) {
          $(this).trigger("syncItemClick");
          return;
        }
        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "syncItemClick",
          funcType: "audio",
        });
      }
    }
  });
  // 为物品添加点击处理程序
  $("#main-scene li").on("syncItemClick", function (e, message) {
    moduleAuthorizationFn("teaOnly", "11");
    // 重置提示计时器
    resetHintTimer();
    console.log("点击了物品", $(this).data("id"));
    const itemId = $(this).data("id");
    const isCorrect = $(this).data("correct") == true;

    if (isCorrect) {
      // 处理正确物品的点击
      if (foundItems.includes(itemId)) {
        return; // 已经找到，不做任何处理
      }
      // 标记物品为已找到
      foundItems.push(itemId);
      $(this).addClass("found");

      let itemObj = options.find((item) => item.id == itemId);
      $(this).css("transform", `scale(${Number(itemObj.scale / 100) + 0.2})`);
      setTimeout(() => {
        $(this).css("transform", `scale(${Number(itemObj.scale) / 100})`);
      }, 300);

      // 查找物品数据
      const itemData = options.find((item) => item.id == itemId);

      const div1Rect = document
        .getElementById("main-scene")
        .getBoundingClientRect();
      const div2Rect = this.getBoundingClientRect();

      // 获取缩放比例
      const style = window.getComputedStyle(this);
      const matrix = new DOMMatrix(style.transform);
      const scale = matrix.a;

      // 计算缩放导致的中心偏移
      const offsetX = (div2Rect.width * (1 - scale)) / 2;
      const offsetY = (div2Rect.height * (1 - scale)) / 2;

      let starObj = {};
      starObj.width = div2Rect.width * scale;
      starObj.height = div2Rect.height * scale;
      starObj.offsetX = div2Rect.left - div1Rect.left + offsetX;
      starObj.offsetY = div2Rect.top - div1Rect.top + offsetY;

      // 播放星星动画
      starFun(starObj, itemData,$(this).index() + 1);
      // 播放正确音效
      let audio = $(".correct-audio")[0];
      SDK.playRudio({
        index: audio,
        syncName: $(".correct-audio").attr("data-syncaudio"),
      });
    } else {
      // 处理错误物品的点击
      // 播放错误音效
      // $(this).css("animation", "shake 0.5s");
      // let audio = $(".error-audio")[0];
      // SDK.playRudio({
      //   index: audio,
      //   syncName: $(".error-audio").attr("data-syncaudio"),
      // });
      // $(".error-audio").one("ended", () => {
      //   console.log("错误音频结束");
      //   $(this).css("animation", "");
      //   moduleAuthorizationFn("teaOnly", "12");
      //   soundClick = true;
      //   SDK.setEventLock();
      // });
    }
  });
  //星星动画
  async function starFun(starObj, itemData,clickIndex) {
    $("#modal-item-img").attr("src", "");
    $(".star").css({
      width: starObj.width,
      height: starObj.height,
      left: starObj.offsetX,
      top: starObj.offsetY,
      display: "block",
    });
    starJson = await lottieAnimations.init(
      starJson,
      "./image/fruit-ribbon.json",
      "#star",
      false
    );
    lottieAnimations.play(starJson);
    starJson.addEventListener("complete", function microphoneAnimation() {
      // 显示模态窗口
      showModal(itemData,clickIndex);
      console.log("星星动画结束");
      lottieAnimations.stop(starJson);
      lottieAnimations.destroy(starJson);
      $("#star").hide();
      soundClick = true;
      SDK.setEventLock();
    });
  }
  let correctClick = true; //正确按钮是否可点击
  // 点击正确按钮
  $("#correct").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();

    if (correctClick) {
      correctClick = false;
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_SPOKEBUTTON",
          teaData: {
            roundid: $("#modal-content").data("itemIndex"),
          },
        },
        USER_TYPE.TEA
      );
      if (!isSync) {
        $(this).trigger("syncCorrectClick");
        return;
      }

      SDK.bindSyncEvt({
        sendUser: "",
        receiveUser: "",
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "syncCorrectClick",
        funcType: "audio",
      });
    }
  });
  // 模态窗口确认按钮处理程序
  $("#correct").on("syncCorrectClick", async function (e, message) {
    const itemId = $("#modal-content").data("itemId");

    // 播放教师确认音效
    let audio = $(".correct-audio")[0];
    SDK.playRudio({
      index: audio,
      syncName: $(".correct-audio").attr("data-syncaudio"),
    });
    correctFun(itemId);
  });
  //正确动画
  async function correctFun(itemId) {
    correctJson = await lottieAnimations.init(
      correctJson,
      "./image/fruit-ribbon.json",
      "#correct-animation",
      false
    );
    lottieAnimations.play(correctJson);
    correctJson.addEventListener("complete", function correctAnimation() {
      console.log("正确动画结束");
      lottieAnimations.stop(correctJson);
      lottieAnimations.destroy(correctJson);
      // $(".correct-animation").hide();
      // 更新目标物品栏
      const targetCell = $('.target-cell[data-id="' + itemId + '"]');
      const itemData = options.find((item) => item.id == itemId);

      targetCell.find("img").attr("src", itemData.clickImg);
      targetCell.addClass("found");

      // 根据afterClicking决定是否隐藏物品
      const itemElement = $('.item[data-id="' + itemId + '"]');
      if (itemData.afterClicking == "0") {
        // 隐藏物品
        itemElement.css("visibility", "hidden");
        // 销毁场景中该物品的动画对象
        if (isJsonAnimation(itemData.clickImg)) {
          // 销毁原动画
          const animKey = "anim-" + itemId;
          if (animationsCache[animKey]) {
            animationsCache[animKey].destroy();
            delete animationsCache[animKey]; // 从缓存中移除
            console.log("销毁动画:", animKey);
          }
        }
      } else if (itemData.afterClicking == "1") {
        // 显示物品并更新层级
        itemElement.css("visibility", "visible");
        if (itemData.afterClickingZIndex) {
          itemElement.css("zIndex", itemData.afterClickingZIndex);
        }
        if (
          itemData.afterClickingPostion &&
          itemData.afterClickingPostion == 1
        ) {
          itemElement.css({
            left: itemData.afterClickingPostionX / 100 + "rem",
            top: itemData.afterClickingPostionY / 100 + "rem",
            zIndex: itemData.afterClickingZIndex,
          });
        }
      }
      itemElement.find("img").attr("src", itemData.clickImg);

      // 关闭模态窗口
      closeModal();
      correctClick = true;
      // 检查所有物品是否都已找到
      if (foundItems.length == options.length) {
        SDK.reportTrackData(
          {
            action: "CK_FT_INTERACTION_COMPLETE",
            teaData: {
              result: "success",
            },
          },
          USER_TYPE.TEA
        );
        feedback();
      } else {
        moduleAuthorizationFn("teaOnly", "12");
        SDK.setEventLock();
      }
    });
  }
  //麦克风动画
  microphoneFun();
  async function microphoneFun() {
    microphoneJson = await lottieAnimations.init(
      microphoneJson,
      "./image/microphone.json",
      "#microphone",
      true
    );
    lottieAnimations.play(microphoneJson);
    microphoneJson.addEventListener("complete", function microphoneAnimation() {
      console.log("麦克风动画结束");
      lottieAnimations.stop(microphoneJson);
      microphone.hide();
    });
  }

  // 显示带有物品详情的模态窗口
  function showModal(itemData,clickIndex) {
    // 根据modelImg字段决定显示哪张图片
    $("#modal-item-img").attr("src", itemData.modelImg);
    $("#modal-content").data("itemId", itemData.id);
    $("#modal-content").data("itemIndex", clickIndex);
    $("#modal-overlay").css("display", "flex");
    if (!isSync) {
      $(".correct").show();
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU) {
        $(".correct").show();
      }
      if (userType == USER_TYPE.TEA) {
        $(".correct").show();
      }
    }
  }

  // 关闭模态窗口
  function closeModal() {
    $("#modal-overlay").css("display", "none");
  }

  // 启动提示计时器
  function startHintTimer() {
    lastClickTime = new Date().getTime();

    clearInterval(hintInterval);
    hintInterval = setInterval(function () {
      const currentTime = new Date().getTime();
      if (currentTime - lastClickTime > 10000) {
        // 10秒
        showHint();
      }
    }, 1000); // 每秒检查一次
  }

  // 显示提示
  function showHint() {
    // 找到第一个未找到的物品
    const unfoundItems = options.filter(
      (item) => !foundItems.includes(item.id)
    );
    if (unfoundItems.length > 0) {
      const nextItem = unfoundItems[0];
      const itemElement = $('.item[data-id="' + nextItem.id + '"]');

      // 获取原始scale值
      const scale = nextItem.scale / 100 || 1;
      currentHintItem = itemElement;

      // 使用JavaScript实现抖动，保持原始scale
      let step = 0;
      const shakeInterval = setInterval(() => {
        step++;
        let xOffset = 0;

        // 根据步骤计算水平偏移
        if (step == 1) xOffset = -8;
        else if (step == 2) xOffset = 8;
        else if (step == 3) xOffset = -8;
        else if (step == 4) {
          xOffset = 0;
          clearInterval(shakeInterval);
          if (currentHintItem == itemElement) {
            itemElement.css("transform", `scale(${scale})`);
            currentHintItem = null;
          }
          return;
        }

        // 应用变换，同时保持scale
        itemElement.css(
          "transform",
          `translateX(${xOffset}px) scale(${scale})`
        );
      }, 125); // 总共抖动0.5秒，分4步
    }
  }

  // 重置提示计时器
  function resetHintTimer() {
    lastClickTime = new Date().getTime();
    if (currentHintItem) {
      // 恢复原始scale
      const itemId = $(currentHintItem).data("id");
      const itemData = options.find((item) => item.id == itemId);
      if (itemData) {
        $(currentHintItem).css(
          "transform",
          `scale(${itemData.scale / 100 || 1})`
        );
      }
      currentHintItem = null;
    }
  }

  /**
   * 授权模式方法
   * @param {*} type: 方法
   * @param {*} value：权限
   * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
   */
  function moduleAuthorizationFn(type, value) {
    if (!isSync) {
      return;
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      console.log(isSync, classStatus, userType, "292");
      if (classStatus == CLASS_STATUS.NOT) {
        return;
      } else {
        SDK.bindSyncCtrl({
          type: type,
          tplAuthorization: "tpl",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [
              {
                key: "classStatus",
                value: value,
                ownerUID: SDK.getClassConf().user.id,
              },
            ],
          },
        });
      }
    }
  }

  //正反馈动画
  async function feedback() {
    console.log("全部正确结束，执行反馈动画");
    await feedbackAnimation("feedKey1");
    SDK.setEventLock();
  }
});
