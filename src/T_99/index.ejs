<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="DCL0001FT_好眼力FT" ; %>
    <%include ./src/common/template/index_head %>
</head>

<body>
  <div class="container" id="container" data-syncresult="1">
    <!-- 反馈动效 -->
    <%include ./src/common/template/feedbackAnimation/index.ejs %>
      <section class="commom">
        <div class="desc"></div>
        <div class="title">
          <h3></h3>
        </div>
      </section>
      <div class="content-main">
        <!-- 开屏效果 -->
        <div class="start-screen">
          <div class="search">
            <ul class="start-fruit"></ul>
          </div>

          <div class="start-button" data-syncactions="startSyncactions"></div>
        </div>
        <div id="game-container">
          <ul id="main-scene">
            <!-- 物品将在这里动态添加 -->
          </ul>
          <div id="target-word">
            <div id="target-bar">
              <!-- 目标物品栏将在这里动态添加 -->
            </div>
          </div>
        </div>

        <div id="modal-overlay">
          <div id="modal-content">
            <img id="modal-item-img" src="" alt="找到的物品">
            <div class="microphone" id="microphone"></div>
            <div class="correct" id="correct" data-syncactions="correctSyncactions"></div>
          </div>
          <div class="correct-animation" id="correct-animation"></div>
        </div>
        <!-- 星星动画 -->
        <div class="star" id="star"></div>
        <!-- 错误音频 -->
        <audio class="error-audio" src="./audio/wrong.mp3" data-syncaudio="error-audio"></audio>
        <!-- 正确音频 -->
        <audio class="correct-audio" src="./audio/correct.mp3" data-syncaudio="correct-audio"></audio>
      </div>

      <script type="text/javascript">
        document.documentElement.addEventListener(
          "touchstart",
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault();
            }
          },
          false
        );
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener(
          "touchend",
          function (event) {
            var now = Date.now();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
      </script>
  </div>
  <%include ./src/common/template/index_bottom %>
    <%include ./src/common/template/lottie %>
</body>

</html>
