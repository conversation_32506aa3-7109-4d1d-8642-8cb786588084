<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>DCL0001FT_好眼力FT</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>" />
  <script src="./form/js/jquery-2.1.1.min.js"></script>
  <script src="./form/js/vue.min.js"></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">DCL0001FT_好眼力FT</h3>

      <% include ./src/common/template/common_head %>
        <!-- IP对话组件 -->
        <% include ./src/common/template/multyDialog/form %>

          <!-- 内容设置 -->
          <div class="c-group">
            <div class="c-title">设置点击内容</div>
            <div class="c-area upload img-upload">
              <ul>
                <li v-for="(item,index) in configData.source.options">
                  <div class="c-well">
                    <!-- 内容图片 -->
                    <div class="field-wrap">
                      <div class="add-field-content">
                        <label class="field-label" for="">点击元素{{ index + 1 }}</label>
                        <span class="dele-tg-btn" v-on:click="delOption(item)"
                          v-show="configData.source.options.length>=2"></span>
                      </div>
                      <!-- <div class="field-wrap">
                        <label class="field-label">遮罩图</label>
                        <label :for="'content-pic-one-mask'+index" class="btn btn-show upload"
                          v-if="!item.maskClickImg">上传图片</label>
                        <label :for="'content-pic-one-mask'+index" class="btn upload re-upload"
                          v-if="item.maskClickImg!=''?true:false">重新上传</label>
                        <div class="audio-tips">
                          <label>
                            <span><em>JPG、PNG、json格式，大小小于等于80KB</em></span>
                          </label>
                        </div>
                        <input type="file" v-bind:key="Date.now()" class="btn-file" size=""
                          accept=".jpg,.png,.jpeg,.json" isKey="1" :id="'content-pic-one-mask'+index"
                          @change="imageUpload($event,item,'maskClickImg',80)" />
                        <div class="img-preview" v-if="item.maskClickImg">
                          <img v-bind:src="item.maskClickImg" alt="" />
                          <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="item.maskClickImg=''">删除</span>
                          </div>
                        </div>
                      </div>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">显示位置</label>
                        <label class="rules-field-label">X：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                          v-model="item.maskPositionX" placeholder="请输入" />
                        <label class="rules-field-label">Y：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                          v-model="item.maskPositionY" placeholder="请输入" />
                        <label class="rules-field-label">范围：1920——1080</label>
                      </div>
                      <br>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">层级</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 66px !important;
                          display: inline-block;" oninput="if(value>100)value=100;if(value<1)value=1"
                          v-model="item.maskZIndex" placeholder="请输入" />
                        <label class="rules-field-label">1——100，数字越大，显示越靠上</label>
                      </div>
                      <br> -->

                      <div class="field-wrap">
                        <label class="field-label">点击元素{{ index + 1 }}<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <label :for="'content-pic-one-'+index" class="btn btn-show upload"
                          v-if="!item.clickImg">上传图片</label>
                        <label :for="'content-pic-one-'+index" class="btn upload re-upload"
                          v-if="item.clickImg!=''?true:false">重新上传</label>
                        <div class="audio-tips">
                          <label>
                            <span><em>JPG、PNG、json格式，大小小于等于240KB</em></span>
                          </label>
                        </div>
                        <input type="file" v-bind:key="Date.now()" class="btn-file" size=""
                          accept=".jpg,.png,.jpeg,.json" isKey="1" :id="'content-pic-one-'+index"
                          @change="imageUpload($event,item,'clickImg',240)" />
                        <div class="img-preview" v-if="item.clickImg">
                          <img v-bind:src="item.clickImg" alt="" />
                          <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="item.clickImg=''">删除</span>
                          </div>
                        </div>
                      </div>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label>
                        <label class="rules-field-label">X：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                          v-model="item.positionX" placeholder="请输入" />
                        <label class="rules-field-label">Y：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                          v-model="item.positionY" placeholder="请输入" />
                        <label class="rules-field-label">范围：1920——1080</label>
                      </div>
                      <br>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">缩放<em>*</em></label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 120px !important;
                          display: inline-block;" oninput="if(value>200)value=200;if(value<1)value=1"
                          v-model="item.scale" placeholder="请输入" />
                        <label class="rules-field-label">默认100%，范围1-200</label>
                      </div>
                      <br>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">层级<em>*</em></label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 66px !important;
                          display: inline-block;" oninput="if(value>100)value=100;if(value<1)value=1"
                          v-model="item.zIndex" placeholder="请输入" />
                        <label class="rules-field-label">1——100，数字越大，显示越靠上</label>
                      </div>
                      <br>
                      <div class="field-wrap">
                        <label class="rules-field-label">点击后<em>*</em></label>
                        <select class="select-c" id="teachTime" v-model="item.afterClicking" @change="afterClickingChange(item)">
                          <option name="optive" value="1">显示</option>
                          <option name="optive" value="0">隐藏</option>
                        </select>
                      </div>
                      <br>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">点击后显示层级<em>*</em></label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 66px !important;
                          display: inline-block;" oninput="if(value>100)value=100;if(value<1)value=1"
                          v-model="item.afterClickingZIndex" placeholder="请输入" />
                        <label class="rules-field-label">1——100，数字越大，显示越靠上</label>
                      </div>
                      <br>
                      <div class="field-wrap" v-if="item.afterClicking == 1">
                        <label class="field-label">点击后位置<em>*</em></label>
                        <label for="twomodule" class="inline-label">
                          <input type="radio" :name="'afterClickingPostion' + index" value="0"
                            v-model="item.afterClickingPostion" @change="afterClickingPostionAChange(item)">位置不变
                        </label>
                        <label for="twomodule" class="inline-label">
                          <input type="radio" :name="'afterClickingPostion' + index" value="1"
                            v-model="item.afterClickingPostion" @change="afterClickingPostionBChange(item)">自定义位置
                        </label>
                      </div>
                      <br>
                      <div class="rules-content" v-if="item.afterClickingPostion == 1 && item.afterClicking == 1">
                        <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label>
                        <label class="rules-field-label">X：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                          v-model="item.afterClickingPostionX" placeholder="请输入" />
                        <label class="rules-field-label">Y：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                          v-model="item.afterClickingPostionY" placeholder="请输入" />
                        <label class="rules-field-label">范围：1920——1080</label>
                      </div>
                      <br v-if="item.afterClickingPostion == 1">
                      <div class="field-wrap">
                        <label class="field-label">弹窗图片<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <label :for="'content-pic-two-'+index" class="btn btn-show upload"
                          v-if="!item.modelImg">上传图片</label>
                        <label :for="'content-pic-two-'+index" class="btn upload re-upload"
                          v-if="item.modelImg!=''?true:false">重新上传</label>
                        <div class="audio-tips">
                          <label>
                            <span><em>JPG、PNG格式，大小小于等于240KB</em></span>
                          </label>
                        </div>
                        <input type="file" v-bind:key="Date.now()" class="btn-file" size=""
                          accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-two-'+index"
                          @change="imageUpload($event,item,'modelImg',240)" />

                        <div class="img-preview" v-if="item.modelImg">
                          <img v-bind:src="item.modelImg" alt="" />
                          <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="item.modelImg=''">删除</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <br>
                  </div>
                </li>
              </ul>
                         <!-- maskClickImg: '',
                maskPositionX:'',
                maskPositionY:'',
                maskZIndex: '', -->
              <button type="button" class="add-btn" v-on:click="addOption({
            clickImg: '',
            positionX: '',
            positionY: '',
            scale: '100',
            zIndex: '',
            afterClicking: '1',
            afterClickingZIndex: '',
            afterClickingPostion: '0',
            afterClickingPostionX: '',
            afterClickingPostionY: '',
            modelImg: '',
          })">
                添加点击元素
              </button>
            </div>
          </div>

          <!-- 收集框 -->
          <div class="c-group">
            <div class="c-title">收集框</div>
            <div class="c-area upload img-upload">
              <div class="field-wrap">
                <span class="fixed-width">是否显示<em>*</em></span>
                <select class="select-c" id="teachTime" v-model="configData.source.collectionDisplay">
                  <option name="optive" value="1">显示</option>
                  <option name="optive" value="0">不显示</option>
                </select>
              </div>
              <br>
              <div class="field-wrap">
                <span class="fixed-width">显示位置<em>*</em></span>
                <select class="select-c" id="teachTime" v-model="configData.source.collectionPosition">
                  <option name="optive" value="0">右边</option>
                  <option name="optive" value="1">上边</option>
                  <option name="optive" value="2">下边</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 上传干扰元素 -->
          <div class="c-group">
            <div class="c-title">上传干扰元素</div>
            <div class="c-area upload img-upload">
              <ul>
                <li v-for="(item,index) in configData.source.interferenceOptions">
                  <div class="c-well">
                    <!-- 干扰元素 -->
                    <div class="field-wrap">
                      <div class="add-field-content">
                        <label class="field-label" for="">干扰元素{{ index + 1 }}</label>
                        <span class="dele-tg-btn" v-on:click="interferenceDelOption(item)"
                          v-show="configData.source.interferenceOptions.length>=2"></span>
                      </div>
                      <label class="field-label">干扰元素{{ index + 1 }}<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                      <label :for="'content-pic-three-'+index" class="btn btn-show upload"
                        v-if="!item.clickImg">上传图片</label>
                      <label :for="'content-pic-three-'+index" class="btn upload re-upload"
                        v-if="item.clickImg!=''?true:false">重新上传</label>
                      <div class="audio-tips">
                        <label>
                          <span><em>JPG、PNG、json格式，大小小于等于240KB</em></span>
                        </label>
                      </div>
                      <input type="file" v-bind:key="Date.now()" class="btn-file" size="" accept=".jpg,.png,.jpeg,.json"
                        isKey="1" :id="'content-pic-three-'+index" @change="imageUpload($event,item,'clickImg',240)" />

                      <div class="img-preview" v-if="item.clickImg">
                        <img v-bind:src="item.clickImg" alt="" />
                        <div class="img-tools">
                          <span class="btn btn-delete" v-on:click="item.clickImg=''">删除</span>
                        </div>
                      </div>

                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label>
                        <label class="rules-field-label">X：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                          v-model="item.positionX" placeholder="请输入" />
                        <label class="rules-field-label">Y：</label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                          v-model="item.positionY" placeholder="请输入" />
                        <label class="rules-field-label">范围：1920——1080</label>
                      </div>
                      <br>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">缩放<em>*</em></label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 120px !important;
                          display: inline-block;" oninput="if(value>200)value=200;if(value<1)value=1"
                          v-model="item.scale" placeholder="请输入" />
                        <label class="rules-field-label">默认100%，范围1-200</label>
                      </div>
                      <br>
                      <div class="rules-content">
                        <label class="rules-field-label" style="margin-right: 10px;">层级<em>*</em></label>
                        <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 66px !important;
                          display: inline-block;" oninput="if(value>100)value=100;if(value<1)value=1"
                          v-model="item.zIndex" placeholder="请输入" />
                        <label class="rules-field-label">1——100，数字越大，显示越靠上</label>
                      </div>
                    </div>
                    <br>
                  </div>
                </li>
              </ul>
              <button type="button" class="add-btn" v-on:click="interferenceAddOption({
                clickImg: '',
                positionX: '',
                positionY: '',
                scale: '',
                zIndex: '',
          })">
                添加干扰元素
              </button>
            </div>
          </div>

          <!-- 正反馈 -->
          <% include ./src/common/template/feedbackAnimation/form %>

            <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="" />
        </div>
        <ul class="show-txt">
          <li>图片格式：<em></em>JPG/PNG</li>
          <li>声音格式：<em></em>MP3</li>
          <li>视频格式：<em></em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src="./form/js/form.js?_=<%= Date.now() %>"></script>

</html>
