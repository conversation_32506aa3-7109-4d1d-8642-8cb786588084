var configData = {
  bg: "./assets/images/bg.png",
  desc: "",
  title: "",
  tImg: "./image/b164806a1b826b1b386f5496478573fe.png",
  tImgX: 1340,
  tImgY: 15,
  tg: [
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "1111111111111111111",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
  ],
  level: {
    high: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
    low: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
  },
  source: {
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "assets/images/e41570807174c4a7617e2a75455838d1.png",
          audio: "./assets/audios/03d7e77310bba5b8ad38811964ce9c9a.mp3",
        },
        {
          text: "assets/images/4dfeec15c2cd5d502bf53095ec38b9ea.png",
          audio: "assets/audios/4a5ec638c60f9afcc25e797e006919ab.mp3",
        },
      ],
      messageLocationX: "550", // 消息内容位置x
      messageLocationY: "100", // 消息内容位置y
      roleLocationX: "100", // 角色位置x
      roleLocationY: "100", // 角色位置y
      roleImg: "assets/images/Doll_turn1.json", // 角色图片
      prevBtn: "assets/images/cd28b2d464fd49af8fba357129e92873.png",
      nextBtn: "assets/images/9aba8b13d42b1e7c51f03ffc923e6e05.png",
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框 1不自动播放  2自动播放
      hiddenStatus: "3", // 播放完是否应藏的状态 1不隐藏  2气泡隐藏  3IP和气泡都隐藏
    },
    options: [
      {
        id: "mb7qg3djoeo19",
        // maskClickImg: "./assets/images/Doll_turn1.png", //遮罩图片
        // maskPositionX: "500", //遮罩显示位置x
        // maskPositionY: "600", //遮罩显示位置y
        // maskZIndex: "4", //遮罩层级
        clickImg: "./assets/images/Doll_turn1.json", //点击图片
        positionX: "500", //显示位置x
        positionY: "600", //显示位置y
        scale: "150", //缩放
        zIndex: "4", //层级
        afterClicking: "1", //点击后
        afterClickingZIndex: "5", //点击后显示层级
        afterClickingPostion: "0", //点击后显示位置
        afterClickingPostionX: "500", //点击后显示位置x
        afterClickingPostionY: "600", //点击后显示位置y
        modelImg: "./assets/images/fruit-box1.png", //弹窗图片
      },
      {
        id: "mb7qg55mr9hvu",
        // maskClickImg: "./assets/images/Doll_turn1.png", //遮罩图片
        // maskPositionX: "500", //遮罩显示位置x
        // maskPositionY: "600", //遮罩显示位置y
        // maskZIndex: "4", //遮罩层级
        clickImg: "./assets/images/fruit2.png", //点击图片
        positionX: "500", //显示位置x
        positionY: "200", //显示位置y
        scale: "100", //缩放
        zIndex: "4", //层级
        afterClicking: "0", //点击后
        afterClickingZIndex: "5", //点击后显示层级
        afterClickingPostion: "0", //点击后显示位置
        afterClickingPostionX: "500", //点击后显示位置x
        afterClickingPostionY: "200", //点击后显示位置y
        modelImg: "./assets/images/fruit-box2.png", //弹窗图片
      },
      {
        id: "mb7qgriqt4mc6",
        // maskClickImg: "./assets/images/Doll_turn1.png", //遮罩图片
        // maskPositionX: "500", //遮罩显示位置x
        // maskPositionY: "600", //遮罩显示位置y
        // maskZIndex: "4", //遮罩层级
        clickImg: "./assets/images/fruit3.png", //点击图片
        positionX: "1200", //显示位置x
        positionY: "200", //显示位置y
        scale: "100", //缩放
        zIndex: "4", //层级
        afterClicking: "0", //点击后
        afterClickingZIndex: "5", //点击后显示层级
        afterClickingPostion: "0", //点击后显示位置
        afterClickingPostionX: "1200", //点击后显示位置x
        afterClickingPostionY: "200", //点击后显示位置y
        modelImg: "./assets/images/fruit-box3.png", //弹窗图片
      },
    ],
    collectionDisplay: "1",//侧边栏是否显示 1显示  0不显示
    collectionPosition: "1",//侧边栏位置 0右边 1上边 2下边
    interferenceOptions: [
      {
        id: "mb7qh0bstor1s",
        clickImg: "./assets/images/fruit4.png", //点击图片
        positionX: "500", //显示位置x
        positionY: "200", //显示位置y
        scale: "100", //缩放
        zIndex: "4", //层级
      },
      {
        id: "mb7qh46gbdcpe",
        clickImg: "./assets/images/fruit5.png", //点击图片
        positionX: "1000", //显示位置x
        positionY: "600", //显示位置y
        scale: "100", //缩放
        zIndex: "4", //层级
      },
    ],
  },
  feedbackLists: [
    {
      positiveFeedback: "-1",
      feedbackList: [
        { id: "-1", json: "", mp3: "" },
        { id: "0", json: "./image/prefect.json", mp3: "./audio/prefect.mp3" },
        { id: "1", json: "./image/goldCoin.json", mp3: "./audio/goldCoin.mp3" },
        { id: "2", json: "./image/FKJB.json", mp3: "./audio/resultWin.mp3" },
        { id: "9", json: "./image/guang.json", mp3: "" },
        {
          id: "10",
          json: "./image/chest-change.json",
          mp3: "./audio/chest-change.mp3",
        },
      ],
      feedbackObj: { id: "9", json: "./image/guang.json", mp3: "" },
      feedback: "./image/prefect.json",
      feedbackAudio: "./audio/prefect.mp3",
      feedbackName: "整体反馈",
      background: "0",
      loop: false,
      key: "feedKey1",
    },
  ],
};
(function (pageNo) {
  configData.page = pageNo;
})(0);
