//var domain = 'http://172.16.0.107:9011/pages/159/';
import {
  feedbackAnimationSend,
  feedbackData,
  delWheelGamePrew,
  initializeFeedback,
  feedBackChange,
} from "../../../common/template/feedbackAnimation/form.js";
import {
  addInstruction,
  validateInstructions,
  removeInstruction,
  getDynamicInstructions,
} from "../../../common/template/dynamicInstruction/form.js";
var domain = "";
var feedback1Obj = feedbackData({
  key: "feedback1",
  name: "第1轮反馈",
});

// todo 初始化掉落元素
const now = new Date().getTime();
const droppedEles = [
  {
    id: now + 1,
    img: "",
  },
  {
    id: now + 2,
    img: "",
  },
  {
    id: now + 3,
    img: "",
  },
  // {
  //   id: 1,
  //   img: "",
  // },
  // {
  //   id: 2,
  //   img: "",
  // },
  // {
  //   id: 3,
  //   img: "",
  // },
  // {
  //   id: 4,
  //   img: "",
  // },
  // {
  //   id: 5,
  //   img: "",
  // },
  // {
  //   id: 6,
  //   img: "",
  // },
];
console.log(droppedEles, "---droppedEles");
// todo 初始化轮次
const rounds = [
  // {
  //   subRounds:[
  //     {
  //       target: [3,5],
  //       audio: "./assets/audios/rotate.mp3",
  //     },
  //     {
  //       target: [1],
  //       audio: "./assets/audios/pipe.mp3",
  //     }
  //   ],
  //   distractors: [2, 4],
  //   speedMode:1,
  //   audio: "./assets/audios/rotate.mp3",
  //   count:6,
  // },
  {
    subRounds:[
      {
        target: [],
        audio: "",
      },
    ],
    distractors: [],
    speedMode: 1,
    audio: "",
    count: 3,
  },
];

var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    instructions: [
      {
        commandId: "-1",
      },
    ],
    tg: [
      {
        title: "",
        content: "",
      },
    ],
    level: {
      high: [
        {
          title: "",
          content: "",
        },
      ],
      low: [
        {
          title: "",
          content: "",
        },
      ],
    },
    source: {
      // dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
      droppedEles: JSON.parse(JSON.stringify(droppedEles)),
      rounds: JSON.parse(JSON.stringify(rounds)),
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
    },
    //
    feedbackLists: JSON.parse(JSON.stringify([feedback1Obj])),
    // feedbackLists:[],
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [],
};

$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }

      if (
        !Data.configData.source.rounds ||
        !Data.configData.source.rounds.length
      ) {
        Data.configData.source.rounds = JSON.parse(JSON.stringify(rounds));
      }

      if (
        !Data.configData.source.droppedEles ||
        !Data.configData.source.droppedEles.length
      ) {
        Data.configData.source.rounds = JSON.parse(JSON.stringify(droppedEles));
      }

      if (!Data.configData.instructions) {
        Data.configData.instructions = [
          {
            commandId: "-1",
          },
        ];
      }

      // todo 升级后兼容历史版本
      if(!Data.configData.source.rounds[0].subRounds) {
        // todo 历史版本
        Data.configData.source.rounds = JSON.parse(JSON.stringify(rounds));
        Data.configData.feedbackLists = JSON.parse(JSON.stringify([feedback1Obj]))
      }

      // todo 反馈组件
      initializeFeedback(Data);
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
  el: "#container",
  data: Data,
  mounted: function() {
    this.getDynamicInstructions();
  },
  methods: {
    getDynamicInstructions: function() {
      var that = this;
      getDynamicInstructions(function(res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function () {
      addInstruction(this.configData);
    },
    removeInstruction: function (index) {
      removeInstruction(index, this.configData);
    },
    validateInstructions: function () {
      return validateInstructions(this.configData);
    },

    // todo 反馈动画组件相关
    delWheelGamePrew(item, key) {
      delWheelGamePrew(item, key);
    },
    feedBackChange(item) {
      feedBackChange(item);
    },
    feedbackUpload: function (e, item, attr, fileSize) {
      console.log(e, item, attr, fileSize);
      const file = e.target.files[0];
      if (file.type === "image/png") {
        this.imageUpload(e, item, attr, fileSize);
      } else {
        this.lottieUpload(e, item, attr, fileSize);
      }
    },
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e);
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && Number(height) + 1 > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
      }
      return checkSize;
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(",");
      var max = dom.attr("max");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (max) {
          if (width >= data.width && height >= data.height) {
            return true;
          }
        } else {
          if (width == data.width && height == data.height) {
            return true;
          }
        }

        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
        input.value = "";
      }
      return checkSize;
    },
    validate: function () {
      // todo 验证droppedEles
      const { droppedEles } = this.configData.source;
      if (droppedEles.length < 3 || droppedEles.length > 12) {
        return alert("掉落元素，最少3个，最多12个");
      }
      for (let index = 0; index < droppedEles.length; index++) {
        const element = droppedEles[index];
        if (!element.img) {
          return alert("请上传掉落元素图片");
        }
      }

      // todo 验证轮次
      const { rounds } = this.configData.source;
      if (rounds.length < 1 || rounds.length > 6) {
        return alert("轮次，最少1轮，最多6轮");
      }
      for (let index = 0; index < rounds.length; index++) {
        const element = rounds[index];
        const { subRounds, distractors, speedMode, speed, count } = element;
        const checkAudio = subRounds.some(item => !item.audio)
        const checkTarget = subRounds.some(item => !item.target.length)
        if (checkAudio) {
          return alert("请上传读题音频");
        }
        if (checkTarget) {
          return alert("请填写正确选项");
        }
        if (!distractors.length) {
          return alert("请填写干扰选项");
        }
        if (speedMode == -1 && !speed) {
          return alert("请填写自定义速度");
        }
        if (count < 3 || count > 12) {
          return alert("请填写结束次数，最少3次，最多12次");
        }
        if (!Number.isInteger(count)) {
          return alert("结束次数，只能是整数");
        }
      }

      // todo 验证 反馈动画
      const { feedbackLists } = this.configData;
      for (let index = 0; index < feedbackLists.length; index++) {
        const element = feedbackLists[index];
        const { feedbackObj } = element;
        if (feedbackObj.id == "9") {
          if (!element.feedback || !element.feedbackAudio) {
            return alert("请选择反馈动画");
          }
        } else {
          if (!feedbackObj.mp3 || !feedbackObj.json) {
            return alert("请选择反馈动画");
          }
        }
      }
      return true;
    },
    onSend: function () {
      var data = this.configData;
      // todo 反馈组件
      let feedbackStatus = feedbackAnimationSend(data);
      if(!feedbackStatus){
        return;
      }
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == "-2") {
        //其他
        data.log.teachTime =
          data.log_editor.TeachTimeOtherM * 60 +
          data.log_editor.TeachTimeOtherS +
          "";
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长");
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther;
      }
      console.log(data);
      // debugger
      var val = this.validate();
      if (val && this.validateInstructions()) {
        var _data = JSON.stringify(data);
        $.ajax({
          url: domain + "content?_method=put",
          type: "POST",
          data: {
            content: _data,
          },
          success: function (res) {
            window.parent.postMessage("close", "*");
          },
          error: function (err) {
            console.log(err);
          },
        });
      } else {
        // alert("带有“*”号为必填项！");
      }
    },
    postData: function (file, item, attr) {
      var FILE = "file";
      var bg = arguments.length > 2 ? arguments[2] : null;
      var oldImg = item[attr];
      var data = new FormData();
      var _this = this;
      data.append("file", file);
      if (oldImg != "") {
        data.append("key", oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: "post",
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          console.log(res.data.key);
          var options = _this.configData.source.options;

          item[attr] = domain + res.data.key;
          console.log(Data.configData);
        },
        error: function (err) {
          console.log(err);
        },
      });
    },
    studyAudioUpload: function (e, item, index, attr, fileSize) {
      //校验规则
      //var _type = this.rules.audio.sources.type;

      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      // if (!_type.test(type)) {
      //     alert("您上传的文件类型错误，请检查后再上传！");
      //     return;
      // }

      this.checkTotalAudio(index, (size / 1024).toFixed(2)); //存储所有音频的文件大小

      if ((size / 1024).toFixed(2) > 500) {
        console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      } else {
        console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      }
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error(
          "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
        );
        alert(
          "您上传的声音大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      this.postData(file, item, attr);
    },
    checkTotalAudio: function (index, size) {
      var options = this.configData.source.options;
      for (var i = 0; i < options.length; i++) {
        if (i == index) {
          options[i].audioSize = size;
        }
      }
    },
    addSele: function () {
      this.configData.source.options.push({
        audio: "",
        // pos: "",
        x: 0,
        y: 0,
        audioSize: 0,
      });
    },
    delSele: function (item) {
      this.configData.source.cardList.remove(item);
    },
    addTg: function (item) {
      this.configData.tg.push({ title: "", content: "" });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    delOption: function (item) {
      this.configData.source.options.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({ title: "", content: "" });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: "", content: "" });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    delPrew: function (item) {
      item.image = "";
    },
    // 添加对话框
    addDialog: function () {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: "",
      });
    },
    // 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },
    // 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },
    // 添加动效
    // addAnimation: function() {
    //   this.configData.source.multyAnimation.push({
    //     roleLocationX: '', // 角色位置x
    //     roleLocationY: '', // 角色位置y
    //     roleImg: "", // 角色图片,
    //     scale: 100, //缩放比例  1-500
    //   });
    // },
    // 删除动效
    delAnimation: function (item) {
      this.configData.source.multyAnimation.remove(item);
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          // img = null;
        } else {
          // img = null;
        }
      };
      reader.readAsText(file);
    },
    //ip组件音频上传
    audioUpload: function (e, item, attr, fileSize = 500) {
      console.log("audioUpload", item);
      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      var that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error(
          "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
        );
        alert(
          "您上传的音频大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      var url = URL.createObjectURL(file); //获取录音时长
      var audioElement = new Audio(url);
      var duration;
      audioElement.addEventListener("loadedmetadata", function (_event) {
        duration = audioElement.duration ? audioElement.duration : "";
        var check = that.sourceAudioCheck(e.target, {
          duration: duration,
        });
        if (check) {
          item[attr] = "./form/img/loading.jpg";
          that.postData(file, item, attr);
          audioElement = null;
        } else {
          audioElement = null;
        }
      });
    },
    //音频长度校检
    sourceAudioCheck: function (input, data) {
      let dom = $(input),
        time = dom.attr("time");
      if (time == "" || time == undefined || data.duration == "") return true;
      let checkSize = false;
      if (data.duration <= time) {
        checkSize = true;
      } else {
        alert(
          `您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`
        );
      }
      return checkSize;
    },

    // todo 元素相关的方法
    // 删除已上传的图片
    delTargetsPrew: function (item, key) {
      item[key] = "";
    },

    delDropEle: function (data) {
      this.configData.source.droppedEles.remove(data);
      const { id } = data;

      // todo 删除正确选项和干扰项
      this.configData.source.rounds.forEach(function (item) {
        if (item.target.includes(id)) {
          item.target = item.target.filter(function (sub) {
            return sub !== id;
          });
        }
        if (item.distractors.includes(id)) {
          item.distractors = item.distractors.filter(function (sub) {
            return sub !== id;
          });
        }
      });
    },

    // todo 计算正确答案选项
    calculateRightAnswer: function (item, index, subIndex) {
      const { id } = item;
      const round = this.configData.source.rounds[index]
      const distractorsData = round['distractors']
      const otherTargetData = round.subRounds.reduce((acc,cur,i) => {
        if(i !== subIndex) {
          return [...acc,...cur.target]
        } else {
          return [...acc]
        }
      },[]);
      const result = [...distractorsData,...otherTargetData].some((sub) => sub == id);
      return result;
    },

    calculateDistractorsAnswer: function (item, index) {
      const { id } = item;
      const round = this.configData.source.rounds[index]
      const targetData = round.subRounds.reduce((acc,cur,i) => {
        return [...acc,...cur.target]
      },[]);
      const result = targetData.some((sub) => sub == id);
      return result;
    },

    // 添加跟读内容图片
    addDropEle: function () {
      if (this.configData.source.droppedEles.length >= 12) return;
      this.configData.source.droppedEles.push({
        img: "",
        id: new Date().getTime(),
      });
    },

    delTargetsLocation: function (item, index) {
      this.configData.targets[index].locations.remove(item);
    },

    addTargetsLocation: function (index) {
      this.configData.targets[index].locations.push({
        x: 0,
        y: 0,
      });
    },

    // todo 添加轮次
    addRounds: function () {
      if (this.configData.source.rounds.length >= 6) return;
      this.configData.source.rounds.push({
        subRounds: [
          {
            target: [],
            audio: "",
          },
        ],
        distractors: [],
        // speed: u,
        speedMode: 1,
        audio: "",
        count: 3,
      });
      this.configData.feedbackLists.push(
        feedbackData({
          key: `feedback${this.configData.feedbackLists.length + 1}`,
          name: `第${this.configData.feedbackLists.length + 1}轮反馈`,
        })
      );
    },

    // todo 添加子轮次
    addSubRounds: function (item) {
      if(!item.subRounds || item.subRounds.length >= 6) return;
      item.subRounds.push(
        {
          target: [],
          audio: "",
        },
      )
    },

    // todo 删除子轮次
    delSubRounds: function(item, index) {
      this.configData.source.rounds[index].subRounds.remove(item);
    },

    delDestination: function (item, index) {
      this.configData.source.rounds.remove(item);

      this.configData.feedbackLists.remove(
        this.configData.feedbackLists[index]
      );

      // todo rename
      this.configData.feedbackLists.forEach((item, index) => {
        (item.key = `feedback${index + 1}`),
          (item.feedbackName = `第${index + 1}轮反馈`);
      });
      console.log(this.configData.feedbackLists);
    },

    delDestinationPrew: function (item, key) {
      item[key] = "";
    },
    halfFeedBackOnInput(value) {
      console.log("halfFeedBackOnInput", Number(value));
      if (value >= this.effectiveTragetLocationsLength - 1) {
        this.configData.halfFeedbackCount =
          this.effectiveTragetLocationsLength - 1;
      }
      if (value < 0) {
        this.configData.halfFeedbackCount = 0;
      }
    },
    delAudio(item) {
      item.audio = "";
    },
  },
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
