var configData = {
  bg: "",
  feedbackLists: [
    {
      positiveFeedback: "9",
      feedbackList: [
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./image/prefect.json', mp3:'./audio/prefect.mp3' },
        { id:'1', json:'./image/goldCoin.json', mp3:'./audio/goldCoin.mp3' },
        { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
        { id:'9', json: './image/guang.json', mp3: '' },
      ],
      feedbackObj: {
        id: "9",
        json: "./image/guang.json",
        mp3: "",
      },
      feedback: "./image/prefect.json",
      feedbackAudio: "./audio/prefect.mp3",
      feedbackName: "第一轮反馈",
      key: "feedback1",
    },
    {
      positiveFeedback: "2",
      feedbackList: [
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./image/prefect.json', mp3:'./audio/prefect.mp3' },
        { id:'1', json:'./image/goldCoin.json', mp3:'./audio/goldCoin.mp3' },
        { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
        { id:'9', json: './image/guang.json', mp3: '' },
      ],
      feedbackObj: {
        id: "2",
        json: "./image/FKJB.json",
        mp3: "./audio/resultWin.mp3",
      },
      feedback: "",
      feedbackAudio: "",
      feedbackName: "第2轮反馈",
      key: "feedback2",
    },
  ],
  tg: [
    {
      content:
        "AHI0002_句子霓虹灯eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
  ],
  level: {
    high: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
    low: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
  },
  source: {
    droppedEles: [
      {
        id: 1,
        // img: "./assets/images/apple.svg",
        img: "./assets/images/01.png",
      },
      {
        id: 2,
        // img: "./assets/images/banana.svg",
        img: "./assets/images/02.png",

      },
      {
        id: 3,
        // img: "./assets/images/grape.svg",
        img: "./assets/images/03.png",

      },
      {
        id: 4,
        // img: "./assets/images/orange.svg",
        img: "./assets/images/04.png",

      },
      {
        id: 5,
        img: "./assets/images/pear.svg",
      },
      {
        id: 6,
        img: "./assets/images/strawberry.svg",
      },
    ],
    rounds: [
      {
        subRounds:[
          {
            target: [3,5],
            audio: "./assets/audios/rotate.mp3",
          },
          {
            target: [4],
            audio: "./assets/audios/pipe.mp3",
          }
        ],
        distractors: [2, 1],
        speed: 1,
        speedMode:'-1',
        count:3,
      },
      {
        subRounds:[
          {
            target: [1],
            audio: "./assets/audios/rotate.mp3",
          },
          {
            target: [4,3],
            audio: "./assets/audios/pipe.mp3",
          }
        ],
        speed: 1,
        // speedMode:2,
        speedMode:-1,
        distractors: [2, 6, 5],
        count:5,
      },
      // {
      //   target: [2,6],
      //   distractors: [1, 3, 5],
      //   speed: 20000,
      //   audio: "./assets/audios/rotate.mp3",
      //   count:6,
      // },
      // {
      //   target: [3],
      //   distractors: [6, 1, 4],
      //   speed: 20000,
      //   audio: "./assets/audios/rotate.mp3",
      //   count:6,
      // },
      // {
      //   target: [6],
      //   distractors: [3, 5, 2],
      //   speed: 20000,
      //   audio: "./assets/audios/rotate.mp3",
      //   count:6,
      // },
      // {
      //   target: [5],
      //   distractors: [4, 1, 6],
      //   speed: 20000,
      //   audio: "./assets/audios/rotate.mp3",
      //   count:6,
      // },
    ],
  },
  tImg: "",
  tImgX: 1340,
  tImgY: 15,
};
