<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>HBY0001_红包雨</title>
    <link rel="stylesheet" href="form/css/style.css" />
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
  </head>
  <body>
    <div id="container">
      <div class="edit-form">
        <div class="h-title">HBY0001_红包雨</div>
        <% include ./src/common/template/common_head %>
        <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <div class="c-group">
          <div class="c-title">掉落的元素（最少3个，最多12个）</div>

          <div class="c-area upload img-upload">
            <div
              class="c-well"
              v-for="(item, index) in configData.source.droppedEles"
              v-bind:key="index"
            >
              <div>
                <span
                  class="dele-tg-btn"
                  style="position: relative; z-index: 10"
                  @click="delDropEle(item)"
                  v-show="configData.source.droppedEles.length>3"
                ></span>
                <div class="field-wrap">
                  <label
                    class="field-label"
                    for=""
                    >{{`元素${index+1}* `}}</label
                  >
                  <span class="txt-info"
                    ><em
                      >尺寸：宽度最大280
                      ，高度最大280,文件大小≤40KB,格式.jpg.png *</em
                    ></span
                  >
                  <input
                    type="file"
                    class="btn-file"
                    v-bind:key="Date.now()"
                    :id="`target-${index}`"
                    size="280*280"
                    :max="true"
                    accept=".jpg,.jpeg,.png"
                    @change="imageUpload($event,item,'img',40)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    :for="`target-${index}`"
                    class="btn btn-show upload"
                    v-if="!item.img"
                    >上传</label
                  >
                  <label
                    :for="`target-${index}`"
                    class="btn upload re-upload"
                    v-if="item.img"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="item.img">
                  <img :src="item.img" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delTargetsPrew(item, 'img')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
            <button
              v-if="configData.source.droppedEles.length<12"
              type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog"
              @click="addDropEle"
            >
              添加元素
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">轮次设置（最少1轮，最多6轮）</div>

          <div class="c-area upload img-upload">
            <div
              class="c-well"
              v-for="(item, index) in configData.source.rounds"
              v-bind:key="index"
            >
              <div>
                <span
                  class="dele-tg-btn"
                  style="position: relative; z-index: 10"
                  @click="delDestination(item,index)"
                  v-show="configData.source.rounds.length>1"
                ></span>
                <div>{{`第${index+1}轮设置`}}</div>
                <div v-for="(subItem, subIndex) in item.subRounds" :key="subIndex" style="margin: 15px 0;">
                  <span
                    class="dele-tg-btn"
                    style="position: relative; z-index: 10;margin-top: 15px;"
                    @click="delSubRounds(subItem,index)"
                    v-show="item.subRounds.length>1"
                  ></span>
                  <div class="field-wrap">
                    <label class="field-label" for="">读题音频{{subIndex + 1}}</label>
                    <span class="txt-info"><em>音频大小≤30KB * </em></span>
                    <input
                      type="file"
                      v-bind:key="Date.now()"
                      class="btn-file"
                      :id="`tarets-audio-${index}-${subIndex}`"
                      accept=".mp3"
                      @change="audioUpload($event,subItem,'audio',30)"
                    />
                  </div>
  
                  <div class="field-wrap">
                    <label
                      :for="`tarets-audio-${index}-${subIndex}`"
                      class="btn btn-show upload"
                      v-if="!subItem.audio"
                      >上传</label
                    >
                    <label
                      :for="`tarets-audio-${index}-${subIndex}`"
                      class="btn upload re-upload"
                      v-if="subItem.audio"
                      >重新上传</label
                    >
                    <label
                      class="btn upload btn-audio-dele"
                      v-if="subItem.audio"
                      @click="delAudio(subItem)"
                      >删除</label
                    >
                  </div>
                  <div class="audio-preview" v-show="subItem.audio">
                    <div class="audio-tools">
                      <p v-show="subItem.audio">{{ subItem.audio }}</p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="subItem.audio"></audio>
                    </span>
                  </div>
  
                  <div class="field-wrap answer-wrap">
                    <label class="field-label" for="">正确选项*</label>
                    <label
                      :class="{'cursor-not-allowed': calculateRightAnswer(option,index,subIndex)}"
                      v-for="(option,optionIndex) in configData.source.droppedEles"
                      :key="option.id"
                    >
                      <input
                        type="checkbox"
                        v-model="subItem.target"
                        :value="option.id"
                        :disabled="calculateRightAnswer(option,index,subIndex)"
                      />
                      {{ `下落元素${optionIndex + 1}` }}
                    </label>
                  </div>
                </div>

                <button
                    v-if="item.subRounds.length<6"
                    type="button"
                    class="sub-round-add-btn add-tg-btn add-tg-btn-dialog"
                    @click="addSubRounds(item)"
                  >
                    添加读题音频
                </button>

                <div class="field-wrap answer-wrap">
                  <label class="field-label" for="">干扰项*</label>
                  <label
                    :class="{'cursor-not-allowed': calculateDistractorsAnswer(option,index)}"
                    v-for="(option,optionIndex) in configData.source.droppedEles"
                    :key="option.id"
                  >
                    <input
                      type="checkbox"
                      v-model="item.distractors"
                      :value="option.id"
                      :disabled="calculateDistractorsAnswer(option,index)"
                    />
                    {{ `下落元素${optionIndex + 1}` }}
                  </label>
                </div>

                <div class="field-wrap">
                  <label class="field-label" for="">掉落速度</label>
                  <select v-model="item.speedMode" style="width: 170px">
                    <option name="speedMode1" value="1">慢速（0.2）</option>
                    <option name="speedMode2" value="2">中速（0.5）</option>
                    <option name="speedMode3" value="3">高速（0.8）</option>
                    <option name="speedMode4" value="-1">自定义</option>
                  </select>
                </div>

                <div class="field-wrap" v-if="item.speedMode == -1">
                  <label class="field-label" for="">自定义速度*</label>
                  <input
                    step="0.1"
                    min="0.1"
                    max="1"
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1)value=1;if(value<0.1)value=0.1"
                    v-model="item.speed"
                  /><span class="txt-info"><em>数字,范围0.1-1,单位0.1</em></span>
                </div>

                <div class="field-wrap">
                  <label class="field-label" for="">选对多少次结束*</label>
                  <input
                    step="1"
                    min="3"
                    max="12"
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>12)value=12;if(value<3)value=3"
                    v-model="item.count"
                  /><span class="txt-info"><em>整数数字,范围3-12,单位1</em></span>
                </div>
              </div>
            </div>
            <button
              v-if="configData.source.rounds.length<6"
              type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog"
              @click="addRounds"
            >
              添加轮次
            </button>
          </div>
        </div>

        <% include ./src/common/template/feedbackAnimation/form %>
        <button class="send-btn" v-on:click="onSend">提交</button>
      </div>
      <div class="edit-show">
        <div class="show-fixed">
          <div class="show-img">
            <img
              src="form/img/preview.jpg?v=<%=new Date().getTime()%>"
              alt=""
            />
          </div>
          <ul class="show-txt">
            <li><em>图片格式：</em>JPG/PNG/GIF</li>
            <li><em>声音格式：</em>MP3/WAV</li>
            <li><em>视频格式：</em>MP4</li>
            <li>带有" * "号为必填项</li>
          </ul>
        </div>
      </div>
    </div>
  </body>
  <script src="form/js/form.js?v=<%=new Date().getTime()%>"></script>
</html>
