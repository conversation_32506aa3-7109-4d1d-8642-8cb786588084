"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, CLASS_STATUS, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";
import { createTeaToast } from "../../common/template/disconnectRecover/index.js";
$(function () {

  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "3", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };

  let h5SyncActions = parent.window.h5SyncActions;
  const isSync = h5SyncActions && h5SyncActions.isSync;
  let source = configData.source;
  const { rounds = [], droppedEles = [] } = source;
  const droppedElesObj = {};
  droppedEles.forEach((item) => {
    droppedElesObj[item.id] = {
      img: item.img,
    };
  });

  // 兼容历史版本
  rounds.forEach(item => {
    if(!item.subRounds) {
      item.subRounds = [
        {
          target:[...item.target],
          audio:item.audio
        }
      ]
    }
  })

  let classStatus = "0"; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }

  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/bg.png)" });
  }
  let isFirst = true;

  const everyround_correctcount = rounds.map((item,index) => `${index + 1}-${item.count}`).join(',')
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      roundCount:configData.source.rounds.length || 0 ,
      everyround_correctcount:everyround_correctcount,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.PRACTICE_INPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.LISTEN
    },
  })

  const page = {
    musicConfig: {
      right: "./audio/right-change.mp3",
      wrong: "./audio/wrong.mp3",
    },

    defaultConfig: {
      horn: "./image/laba.json",
      spawnInterval: 3000, //800,
      audioInterval: 3000,
      maxFruitsOnScreen: 50,
      maxRandomCount: 50,
      maxSpeed: 3000,
      minSpeed: 10000,
      levelOneInterval: 2000,
      levelTwoInterval: 1000,
      levelThreeInterval: 700,
      originStateLoc:[
        {
          x: 333,
          y:136
        },
        {
          x: 936,
          y:130
        },
        {
          x: 1343,
          y:100
        },
        {
          x: 443,
          y:400
        },
        {
          x: 823,
          y:466
        },
        {
          x: 1233,
          y:505
        }
      ]
    },

    // todo 游戏状态
    gameState: {
      currentRound: 0,
      collectedFruits: 0,
      isPlaying: false,
      spawnTimer: null,
      // audioTimer: null,
      fruitsOnScreen: 0,
      droppedCount: 0,
      everyCountForClear:40,
      everyCounterForCount:0,
      everyClearCount:5
    },

    // todo 随机数据
    randomFruitsArr: [],
    // todo 随机数据索引
    indexInRandomFruitsArr: 0,

    // todo 随机数据字符串类型
    randomFruitsString:'',

    hornAnimation: null,

    targetsAllowClick: false,

    init: function () {
      this.initOriginState()
      this.isShowMask();
      this.initAnimation();
      this.initPage();
      this.initRule();
    },

    dealConfigData: function () {
      const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        const classStatus =
          SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.IN_CLASS && userType == USER_TYPE.STU) {
          // todo 学生端显示start按钮
          // $('.startBtn').hide();
        }


      let i = 0;
      const {
        maxSpeed,
        minSpeed,
        levelOneInterval,
        levelTwoInterval,
        levelThreeInterval,
      } = page.defaultConfig;
      rounds.forEach((item) => {
        if (item.speedMode != -1) {
          if (Number(item.speedMode) <= 1) {
            item.interval = levelOneInterval;
          } else if (Number(item.speedMode) <= 2) {
            item.interval = levelTwoInterval;
          } else {
            item.interval = levelThreeInterval;
          }
          switch (Number(item.speedMode)) {
            case 1:
              item.speed = minSpeed - (0.2 - 0.1) * (minSpeed - maxSpeed);
              break;
            case 2:
              item.speed = minSpeed - (0.5 - 0.1) * (minSpeed - maxSpeed);
              break;
            case 3:
              item.speed = minSpeed - (0.8 - 0.1) * (minSpeed - maxSpeed);
              break;
            default:
              break;
          }
        } else {
          if (item.speed <= 0.4) {
            item.interval = levelOneInterval;
          } else if (item.speed <= 0.8) {
            item.interval = levelTwoInterval;
          } else {
            item.interval = levelThreeInterval;
          }
          item.speed = minSpeed - (item.speed - 0.1) * (minSpeed - maxSpeed);
        }
      });
      for (let index = 0; index < droppedEles.length; index++) {
        const element = droppedEles[index];
        getImageSize(element.img, (width, height) => {
          i++;
          droppedElesObj[element.id].width = width / 100;
          droppedElesObj[element.id].height = height / 100;
          if (i === droppedEles.length) {
            page.init();
          }
        });
      }
    },

    initOriginState:function() {

      for(let i = 0; i < page.defaultConfig.originStateLoc.length; i++) {
        const ele = droppedEles[i % droppedEles.length]
        const { id,img } = ele
        const width = droppedElesObj[id].width + 'rem'
        const height = droppedElesObj[id].height + 'rem'
        let {x,y} = page.defaultConfig.originStateLoc[i]
        const $img = $(`<img src="${img}" class="origin-item">`)
        $img.css({
          width,
          height,
          left:x / 100+ 'rem',
          top:y / 100 + 'rem'
        })
        $('.origin-state').append($img)
      }      
    },

    isShowMask: function () {
      // if (isSync) {
      //   const userType = window.frameElement
      //     ? window.frameElement.getAttribute("user_type")
      //     : "";
      //   const classStatus = SDK.getClassConf().h5Course.classStatus;
      //   if (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU) {
      //     // not in class && student
      //     $(".funcMask").show();
      //   }
      // } else {
      //   $(".funcMask").show();
      // }
      $(".funcMask").show()
    },

    initAnimation: function () {
      this.createHornAnimation();
    },

    initPage: function () {
      $(".game-container").on("syncInitRule", function (e) {
        console.log("recover ganme-container sdk");
        // if(typeof SDK.syncData.randomFruitsArr === 'string') {
        //   try {
        //     page.randomFruitsArr = JSON.parse(SDK.syncData.randomFruitsArr)
        //   } catch (error) {
        //     console.log('jf syncInitRule error',error)
        //   }
        // } else if (Array.isArray(SDK.syncData.randomFruitsArr)){
        //   page.randomFruitsArr = [...SDK.syncData.randomFruitsArr];
        // } 
        // page.randomFruitsArr = [...SDK.syncData.randomFruitsArr];
        page.randomFruitsString = SDK.syncData.randomFruitsString
        SDK.setEventLock();
      });

      $(".experience-bar-fill").on(
        "transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd",
        function (e) {
          console.log("经验值动画结束", e.originalEvent.propertyName);
          const { currentRound, collectedFruits } = page.gameState;
          if (currentRound >= rounds.length - 1) {
            if (collectedFruits >= rounds[currentRound].count) {
              // todo 播放最后轮动画
              SDK.reportTrackData({
                action: 'CK_FT_INTERACTION_COMPLETE',
                data: {
                  result:'success',
                }
              },USER_TYPE.TEA)
              page.execFeedBack();
              page.gameOver();
            }
          } else {
            if (collectedFruits >= rounds[currentRound].count) {
              // todo 播放单轮动画
              // finalFeedback && page.execFinalFeedback();
              page.execFeedBack();
              page.beforeCompleteRound();
              setTimeout(() => {
                page.completeRound();
              }, 2000);
            }
          }
        }
      );

      $(".startBtn").on("click touchstart", function (e) {
        console.log("jf startBtn click");
        if (e.type == "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation();
        if (!isSync) {
          $(this).trigger("sycnStartBtnClick");
          return;
        }
        const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        const classStatus =
          SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.IN_CLASS && userType == USER_TYPE.STU) {
          return;
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "sycnStartBtnClick",
          recoveryMode: "1",
        });
      });

      $(".startBtn").on("sycnStartBtnClick", function () {
        console.log("jf sycnStartBtnClick");
        // $('.startBox').hide()
        SDK.reportTrackData({
          action: 'CK_FT_INTERACTION_STARTBUTTON',
          data: {}
        },USER_TYPE.TEA)
        $('.origin-state').hide()
        const classStatus =
          SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;

          if(classStatus == CLASS_STATUS.IN_CLASS) {
            console.log('上课状态')  
            moduleAuthorizationFn("stuOnly", "12")
          }
        page.initCommonData();
        page.threeTwoOne();
        SDK.setEventLock();
      });
    },

    initRule: function () {
      const userType = window.frameElement
        ? window.frameElement.getAttribute("user_type")
        : "";
      const classStatus =
        SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
      if (
        !isSync ||
        (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU)
      ) {
        // page.initAudio();
        page.computeResult();
      } else {
        // if (userType === USER_TYPE.TEA) {
        //   // todo tea coming computed result
        //   if (
        //     page.randomFruitsArr.length === 0 &&
        //     (!SDK.syncData.randomFruitsArr ||
        //       SDK.syncData.randomFruitsArr.length === 0)
        //   ) {
        //     // page.initAudio();
        //     page.computeResult();
        //     // SDK.syncData.randomFruitsArr = [...page.randomFruitsArr];
        //     SDK.syncData.randomFruitsArr = page.randomFruitsArr;
        //     SDK.bindSyncEvt({
        //       index: $(".game-container").data("syncactions"),
        //       eventType: "sendmessage",
        //       method: "event",
        //       syncName: "syncInitRule",
        //     });
        //   }
        // }

        if(userType === USER_TYPE.TEA) {
          if(page.randomFruitsString.length === 0 && (!SDK.syncData.randomFruitsString || SDK.syncData.randomFruitsString.length === 0)) {
            page.computeResult();
            SDK.syncData.randomFruitsString = page.randomFruitsString
            SDK.bindSyncEvt({
              index: $(".game-container").data("syncactions"),
              eventType: "sendmessage",
              method: "event",
              syncName: "syncInitRule",
            });
          }
        }
      }
    },

    computeResult: function () {
      const tmpArr = []
      const gameContainerWidth = $(".game-container").width();
      // const gap = gameContainerWidth * 0.05;
      const gap = gameContainerWidth * 0.135;
      const remInPx = parseFloat(
        getComputedStyle(document.documentElement).fontSize
      );
      for (let i = 0; i < rounds.length; i++) {
        const round = rounds[i];
        const { subRounds } = round
        let allFruits = []
        let allTargetFruit = []
        let allSingleFruit = []
        for(let j=0;j<subRounds.length;j++) {
          allTargetFruit = [...allTargetFruit,...subRounds[j].target]
        }
        allSingleFruit = [...allTargetFruit,...round.distractors]
        allFruits = [...allTargetFruit,...round.distractors,...allTargetFruit]
        let ids = Array.from({ length: page.defaultConfig.maxRandomCount - allSingleFruit.length }, () => allFruits[Math.floor(Math.random() * allFruits.length)]).concat([...allSingleFruit]);
        ids = shuffleArray([...ids])
        console.log(ids)
        console.log(allFruits,'---')
        for (
          let index = 0;
          index < page.defaultConfig.maxRandomCount;
          index++
        ) {
          // const id = allFruits[Math.floor(Math.random() * allFruits.length)];
          const id = ids[index]
          // const randomLeft = Math.floor(
          //   Math.random() * (gameContainerWidth - 300) + 150
          // );
          const randomLeft = Math.floor(
            Math.random() * (gameContainerWidth - gap * 2) + gap
          );
          // const randomLeft = gameContainerWidth - 150
          const eleWidthPX = Math.floor(droppedElesObj[id].width * remInPx);
          let totalLeft = randomLeft + eleWidthPX;
          let resultLeft = randomLeft;
          if (totalLeft >= gameContainerWidth - gap) {
            // resultLeft = gameContainerWidth - gap - eleWidthPX - 20;
            resultLeft = (30 + Math.random() * 40) / 100 * gameContainerWidth // 30%-70%
            console.log('---resultLeft',resultLeft,gameContainerWidth * 0.4 ,gameContainerWidth * 0.6)
          }

          const obj = {
            // randomFruit: droppedElesObj[id].img,
            // left: resultLeft + "px",  转换成百分比
            left: Math.floor((resultLeft / gameContainerWidth) * 100) + "%",
            // top: "-1rem",
            // top: 0,
            id,
            // width: droppedElesObj[id].width + "rem",
            // height: droppedElesObj[id].height + "rem",
          };
          // page.randomFruitsArr[i]
          //   ? page.randomFruitsArr[i].push(obj)
          //   : (page.randomFruitsArr[i] = [obj]);

          tmpArr[i] ? tmpArr[i].push(obj) : (tmpArr[i] = [obj]);
        }
      }
      // const round = rounds[page.gameState.currentRound];
      // const allFruits = [...round.target, ...round.distractors];

      // for (let index = 0; index < page.defaultConfig.maxRandomCount; index++) {
      //   const id = allFruits[Math.floor(Math.random() * allFruits.length)];
      //   const obj = {
      //     randomFruit: droppedElesObj[id],
      //     left: Math.random() * ($(window).width() - 400) + "px",
      //     top: "-400px",
      //     id,
      //   };
      //   page.randomFruitsArr.push(obj);
      // }
      // page.randomFruitsArr = JSON.stringify(page.randomFruitsArr)
      page.randomFruitsString = JSON.stringify(tmpArr)
      console.log("jf result:", page.randomFruitsArr,page.randomFruitsString,tmpArr);
    },

    initCommonData: function () {
      // if (
      //   page.randomFruitsArr.length > 0 &&
      //   (!SDK.syncData.randomFruitsArr ||
      //     SDK.syncData.randomFruitsArr.length === 0)
      // ) {
      //   SDK.syncData.randomFruitsArr = [...page.randomFruitsArr];
      // } else if (
      //   SDK.syncData.randomFruitsArr &&
      //   SDK.syncData.randomFruitsArr.length > 0 &&
      //   page.randomFruitsArr.length === 0
      // ) {
      //   page.randomFruitsArr = [...SDK.syncData.randomFruitsArr];
      // }

      if(page.randomFruitsString.length > 0 && ( !SDK.syncData.randomFruitsString || !SDK.syncData.randomFruitsString.length)) {
        SDK.syncData.randomFruitsString = page.randomFruitsString
      } else if ((SDK.syncData.randomFruitsString && SDK.syncData.randomFruitsString.length > 0) && !page.randomFruitsString.length) {
        page.randomFruitsString = SDK.syncData.randomFruitsString
      }
      
      //todo 给randomFruitsArr赋值
     try {
      page.randomFruitsArr = JSON.parse(SDK.syncData.randomFruitsString)
     } catch (error) {
      console.log('jf initCommonData error',error)
     }
    },

    gameBegin: function () {
      console.log("gameBegin");
      page.startRound();
    },

    // todo 开始轮次
    startRound: function () {
      console.log("jf  round start");
      page.clearExperienceValue();
      page.targetsAllowClick = true;
      this.gameState.collectedFruits = 0;
      page.playAudio();
      this.indexInRandomFruitsArr = 0;
      this.gameState.isPlaying = true;
      this.gameState.fruitsOnScreen = 0;
      this.gameState.droppedCount = 0;
      this.gameState.everyCounterForCount = 0;
      if (rounds[page.gameState.currentRound].interval) {
        page.defaultConfig.spawnInterval =
          rounds[page.gameState.currentRound].interval;
      }
      this.startSpawningFruits();
    },

    startSpawningFruits: function () {
      if (this.gameState.spawnTimer) {
        clearInterval(this.gameState.spawnTimer);
      }
      const self = this;
      self.spawnFruit();
      this.gameState.spawnTimer = setInterval(
        () => self.spawnFruit(),
        this.defaultConfig.spawnInterval
      );
    },

    spawnFruit: function () {
      const { everyCountForClear, everyClearCount } = page.gameState
      if (
        this.gameState.fruitsOnScreen >= this.defaultConfig.maxFruitsOnScreen
      ) {
        return;
      }

      const round = rounds[this.gameState.currentRound];
      if (
        this.indexInRandomFruitsArr ===
        this.randomFruitsArr[this.gameState.currentRound].length
      ) {
        this.indexInRandomFruitsArr = 0;
      }
      this.gameState.droppedCount++;
      const obj =
        this.randomFruitsArr[this.gameState.currentRound][
          this.indexInRandomFruitsArr++
        ];
      const $fruit = $(
        `<img draggable="false"  data-syncactions="sycnFruitClick-${this.gameState.currentRound}-${this.gameState.droppedCount}">`
      )
        .addClass("fruit")
        // .attr("src", obj.randomFruit)
        .attr("src",droppedElesObj[obj.id].img)
        .attr("data-target", obj.id)
        .css({
          left: obj.left,
          // top: obj.top,
          // width: obj.width,
          // height: obj.height,
          top:0,
          width:droppedElesObj[obj.id].width + 'rem',
          height:droppedElesObj[obj.id].height + 'rem',
        });

      $(".game-container").append($fruit);
      this.gameState.fruitsOnScreen++;
      
      this.gameState.everyCounterForCount++;
      if(this.gameState.everyCounterForCount % (everyCountForClear + 1) === everyCountForClear  ) {
        // console.log('30---',$('.fruit').length)
        if($('.fruit').length >= everyCountForClear - 1){
          $('.fruit').slice(0,everyClearCount).remove()
          this.gameState.everyCounterForCount = everyCountForClear - everyClearCount
          // console.log('移除',$('.fruit').length)
        }
      }

      const self = this;
      $fruit.animate(
        {
          top: $(".game-container").height() + "px",
        },
        round.speed,
        "linear",
        function () {
          self.gameState.fruitsOnScreen--;
        }
      );

      const debouncedHandleFruitClick = debounce(page.handleFruitClick, 500,true); 

      $fruit.on("click touchstart", debouncedHandleFruitClick);

      $fruit.on("sycnFruitClick", function () {
        console.log('jf sycnFruitClick')
        const clickedFruit = $(this).data("target");
        const length = round.subRounds.length
        // const targetFruit = round.target;
        const targetFruit = round.subRounds[self.gameState.collectedFruits % length].target
        if (targetFruit.indexOf(clickedFruit) !== -1) {
          console.log($(this).attr("data-clicked"),'jf data-clicked')
          if ($(this).attr("data-clicked") === "true") {
            return SDK.setEventLock();
          }
          
          // self.audio.correct.play();
          page.playClickAudio(true);
          self.gameState.collectedFruits++;
          // self.updateProgress();
          page.dealExperienceValue();
          if (
            self.gameState.collectedFruits >=
            rounds[self.gameState.currentRound]
          ) {
            // self.completeRound();
          }
          self.gameState.fruitsOnScreen--;
          // $(this).remove();
          $(this).attr("data-clicked", "true");
          $(this)
            .addClass("explode")
            .one(
              "animationend webkitAnimationEnd oAnimationEnd MSAnimationEnd",
              function () {
                // $(this).remove();
              }
            );
            SDK.reportTrackData({
              action: 'CK_FT_INTERACTION_ITEM',
              data: {
                roundid: page.gameState.currentRound + 1,
                result:'right'
              }
            },USER_TYPE.STU)
        } else {
          // self.audio.wrong.play();
          SDK.reportTrackData({
            action: 'CK_FT_INTERACTION_ITEM',
            data: {
              roundid: page.gameState.currentRound + 1,
              result:'wrong'
            }
          },USER_TYPE.STU)
          page.playClickAudio(false);
          // 添加错误动画
          $(this).addClass("wrong-animation");
          // 动画结束后移除动画类
          setTimeout(() => {
            $(this).removeClass("wrong-animation");
          }, 500);
        }
        SDK.setEventLock();
      });
    },

    handleFruitClick: function (e) {
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation();

      if (!isSync) {
        $(this).trigger("sycnFruitClick");
        return;
      }

      SDK.bindSyncEvt({
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "sycnFruitClick",
        recoveryMode: "1",
      });
    },

    // todo 处理经验值
    dealExperienceValue: function () {
      const { collectedFruits, currentRound } = page.gameState;
      const percent = (collectedFruits / rounds[currentRound].count) * 100;

      const width = `${percent > 100 ? 100 : percent}%`;
      $(".experience-bar-fill").css({
        width,
      });
    },

    // todo 清空经验值
    clearExperienceValue: function () {
      $(".experience-bar-fill").css({
        transition: "none",
        width: 0,
      });

      setTimeout(() => {
        $(".experience-bar-fill").css({
          transition: " width 1s ease",
          width: 0,
        });
      }, 500);
    },

    beforeCompleteRound: function () {
      // 清除所有现有的水果
      page.gameState.isPlaying = false;
      page.stopSpawningFruits();
      page.pauseTargetMusic();
      $(".fruit").remove();
      page.gameState.fruitsOnScreen = 0;
    },

    // todo 结束本轮
    completeRound: function () {
      setTimeout(() => {
        // $("#reward").hide();
        page.pauseTargetMusic();
        page.gameState.currentRound++;

        if (page.gameState.currentRound < rounds.length) {
          page.startRound();
        } else {
          // self.showGameComplete();
        }
      }, 3000);
    },

    // todo 游戏结束
    gameOver: function () {
      console.log("游戏结束！");
      // page.gameState.isPlaying = false;
      // page.stopSpawningFruits();
      // page.pauseTargetMusic();
      // $(".fruit").remove();
      page.beforeCompleteRound();
    },

    // todo 停止产生水果
    stopSpawningFruits: function () {
      if (page.gameState.spawnTimer) {
        clearInterval(page.gameState.spawnTimer);
        page.gameState.spawnTimer = null;
      }
    },

    playAudio: function () {
      const { currentRound, collectedFruits } = page.gameState;
      const count =  rounds[currentRound].subRounds.length
      console.log(collectedFruits,count,'-----')
      const audio = $("#effect-audio").get(0);
      audio.currentTime = 0;
      if (page.hornAnimation) {
        lottieAnimations.stop(page.hornAnimation);
        lottieAnimations.play(page.hornAnimation);
      }
      $("#effect-audio").attr("src", rounds[currentRound].subRounds[collectedFruits % count].audio);
      SDK.playRudio({
        index: audio,
        syncName: $("#effect-audio").attr("data-syncaudio"),
      });
    },

    pauseTargetMusic: function () {
      const audio = $("#effect-audio").get(0);
      // todo pause target music
      SDK.pauseRudio({
        index: audio,
        syncName: $("#effect-audio").attr("data-syncaudio"),
      });
      audio.currentTime = 0;
      console.log('jf pauseTargetMusic')
    },

    playClickAudio: function (isRight) {
      console.log('jf playClickAudio',isRight)
      try {
        page.pauseTargetMusic();
        setTimeout(() => {
          const audio = $("#click-audio").get(0);
          // audio.currentTime = 0;
          $("#click-audio").attr(
            "src",
            isRight ? page.musicConfig.right : page.musicConfig.wrong
          );
          SDK.playRudio({
            index: audio,
            syncName: $("#click-audio").attr("data-syncaudio"),
          });
          console.log('jf playClickAudio---2',)
          $("#click-audio")
            .off("ended")
            .one("ended", function () {
              // todo go on playing target music
              console.log(page.gameState.collectedFruits,'-909090')
              if(page.gameState.collectedFruits >= rounds[page.gameState.currentRound].count) {
                console.log('jieshu 9090')
                return 
              }
              console.log("click-audio ended");
              const audio = $("#effect-audio").get(0);
              audio.currentTime = 0;
              // SDK.playRudio({
              //   index: audio,
              //   syncName: $("#effect-audio").attr("data-syncaudio"),
              // });
              page.playAudio()
            });
        }, 300);

      } catch (error) {
        console.log(error)
      }
    },
    threeTwoOne: function () {
      let q = 1;
      $(".funcMask").show();
      // $('.startBtn').hide()
      $(".startBox")
        .hide()
        .siblings(".timeChangeBox")
        .show()
        .find(".numberList");
      SDK.playRudio({
        index: $(".timeLowAudio_" + q).get(0),
        syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
      });
      let audioPlay = setInterval(function () {
        q++;
        if (q > 4) {
          clearInterval(audioPlay);
          SDK.setEventLock();
          $(".funcMask").hide();
          $(".timeChangeBox").hide();
          setTimeout(() => {
            page.gameBegin();
          }, 500);
        } else {
          SDK.playRudio({
            index: $(".timeLowAudio_" + q).get(0),
            syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
          });
          $(".numberList").css({
            "background-position-x": -(1.5 * (q - 1)) + "rem",
          });
        }
      }, 1000); // @WARNING
    },

    createHornAnimation: function () {
      const { horn } = this.defaultConfig;
      getImageSizeFromJSON(horn, async (width, height) => {
        this.hornAnimation = await lottieAnimations.init(
          this.hornAnimation,
          horn,
          ".horn",
          true
        );
      });
    },

    execFeedBack: async function () {
      page.pauseTargetMusic();
      setTimeout(async () => {
        if (page.hornAnimation) {
          lottieAnimations.stop(page.hornAnimation);
        }
        console.log(`执行动第${this.gameState.currentRound + 1}阶段画！`);
        await feedbackAnimation(`feedback${this.gameState.currentRound + 1}`);
      }, 300);
    },
  };
  // page.init();
  page.dealConfigData();

  //断线重连页面恢复
  SDK.recover = function (data) {
  };

  let gamePauseFlag = false;
  let stuStatus, teaStatus; //检测老师或学生在教室的状态

  SDK.actAuthorize = function (message) {
    console.log("jf actAuthorize", message, isSync);
    if (isSync) {
      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        if (userType == USER_TYPE.TEA) {
          SDK.setEventLock();
          $(".startBtn").trigger("click");
          SDK.setEventLock();
        }
      }
    }
  };

  SDK.memberChange = function (message) {
    if (message.role === USER_TYPE.STU && message.state === 'enter') {
      let disconnectText = "The student  has been disconnected on this page.<br/> You need to click the 'Refresh' button to start over."
      // 学生重连
      createTeaToast(disconnectText)
      SDK.reportTrackData({
        action: 'PG_FT_INTERACTION_RECONNECTION',
        data: {},
      },USER_TYPE.TEA)
    }
  }

  // 从JSON文件中获取图片尺寸
  function getImageSizeFromJSON(jsonUrl, callback) {
    $.getJSON(jsonUrl, function (data) {
      const width = data.width || data.w;
      const height = data.height || data.h;
      callback(width, height);
    }).fail(function () {
      console.error("JSON 文件加载失败");
    });
  }

  // 显示图片信息，播放音频
  function getImageSize(url, callback) {
    const img = new Image();
    img.src = url;
    // 确保图片加载完成后获取宽高
    img.onload = function () {
      const width = img.width;
      const height = img.height;
      callback(width, height);
    };
    // 处理加载错误
    img.onerror = function () {
      console.error("图片加载失败");
    };
  }

  /**
   * 授权模式方法
   * @param {*} type: 方法
   * @param {*} value：权限
   * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
   */

  function moduleAuthorizationFn(type, value) {
    if (!isSync) {
      return;
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      // console.log(isSync, classStatus, userType, "292");
      if (classStatus == CLASS_STATUS.NOT) {
        return;
      } else {
        SDK.bindSyncCtrl({
          type: type,
          tplAuthorization: "tpl",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [
              {
                key: "classStatus",
                value: value,
                ownerUID: SDK.getClassConf().user.id,
              },
            ],
          },
        });
      }
    }
  }
  
});

function debounce(func, wait, immediate) {
  let timeout;
  return function(...args) {
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    }, wait);
    if (callNow) func.apply(this, args);
  };
}

// todo 数组乱序
function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}
