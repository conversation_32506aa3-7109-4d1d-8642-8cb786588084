//var domain = 'http://172.16.0.107:9011/pages/159/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
var domain = "";
var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: '',
    tImgX: '',
    tImgY: '',
    instructions: [{
      commandId: '-1'
    }],
    tg: [
      {
        title: "",
        content: "",
      },
    ],
    level: {
      high: [
        {
          title: "",
          content: "",
        },
      ],
      low: [
        {
          title: "",
          content: "",
        },
      ],
    },
    source: {
      pics: [{ pic: "" }, { pic: "" }],
      role: "",
      pursuitAudio: "", // 追击音频
      firstRole: "",
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
    },
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [], //交互提示标签
};

$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if(!Data.configData.tImg){
        Data.configData.tImg = '';
      }
      if(!Data.configData.tImgX){
        Data.configData.tImgX = 1340
      }
      if(!Data.configData.tImgY){
        Data.configData.tImgY = 15
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }
      if(!Data.configData.instructions){
        Data.configData.instructions = [{
          commandId: '-1'
        }]
      }
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
  el: "#container",
  data: Data,
  mounted: function () {
    this.getDynamicInstructions();
  },
  methods: {
    getDynamicInstructions: function() {
      var that = this;
      getDynamicInstructions(function(res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function() {
      addInstruction(this.configData);
    },
    removeInstruction: function(index) {
      removeInstruction(index, this.configData);
    },
    validateInstructions: function() {
      return validateInstructions(this.configData);
    },

    //辅助提示图片上传
    tImageUpload: function(e, attr, fileSize) {
      console.log("tImageUpload",e)
      var file = e.target.files[0],
          size = file.size,
          naturalWidth = -1,
          naturalHeight = -1,
          that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function() {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(e.target, {
          height: naturalHeight,
          width: naturalWidth
        }, item, attr);
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      }
      var reader = new FileReader();
      reader.onload = function(evt) {
        img.src = evt.target.result;
      }
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function(input, data, item, attr) {
      let dom = $(input),
          size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function(item, idx) {
        let _size = item.split("*"),
            width = _size[0],
            height = _size[1];
        if (width == data.width && (height+1) > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width+"*"+data.height);
      }
      return checkSize;
    },

    //音频上传
    audioUpload: function(e, item, attr, fileSize) {
      console.log("audioUpload",item)
        //获取到的内容数据
        var file = e.target.files[0],
            type = file.type,
            size = file.size,
            name = file.name,
            path = e.target.value,
        that = this;
        if ((size / 1024).toFixed(2) > fileSize) {
            console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
            alert("您上传的音频大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
            return;
        }
        var url = URL.createObjectURL(file); //获取录音时长
        var audioElement = new Audio(url);
        var duration;
        audioElement.addEventListener("loadedmetadata", function(_event) {
            duration = audioElement.duration ? audioElement.duration : '';
            var check = that.sourceAudioCheck(e.target, {
                duration: duration,
            });
            if (check) {
                item[attr] = "./form/img/loading.jpg";
                that.postData(file, item, attr);
                audioElement = null;
            } else {
                audioElement = null;
            }
        });
    },
    //音频长度校检
    sourceAudioCheck: function(input, data) {
        let dom = $(input),
            time = dom.attr("time");
        if (time == "" || time == undefined || data.duration == '') return true;
        let checkSize = false;
        if (data.duration <= time) {
            checkSize = true;
        } else {
            alert(`您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`);
        }
        return checkSize;
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      reader.readAsText(file);
    },
    sourceImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height == data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(`应上传图片大小为：${size.join("或")}, 但上传图片尺寸为：${data.width}*${data.height}`);
      }
      return checkSize;
    },
    validate: function () {
      var isPass = this.configData.source.pics.some(function (item) {
        return item.pic == "";
      });
      if (isPass == true) {
        alert("请上传编辑内容");
        return false;
      }
      if (!this.configData.source.role || !this.configData.source.firstRole) {
        alert("请上传角色");
        return false;
      }
      if (!this.configData.source.role) {
        alert("请上传追击音频");
        return false;
      }
      return true;
    },
    onSend: function () {
      var data = this.configData;
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == "-2") {
        //其他
        data.log.teachTime =
          data.log_editor.TeachTimeOtherM * 60 +
          data.log_editor.TeachTimeOtherS +
          "";
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长");
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther;
      }
      var val = this.validate();
      if (val && this.validateInstructions()) {
        var _data = JSON.stringify(data);
        $.ajax({
          url: domain + "content?_method=put",
          type: "POST",
          data: {
            content: _data,
          },
          success: function (res) {
            window.parent.postMessage("close", "*");
          },
          error: function (err) {
            console.log(err);
          },
        });
      }
    },
    postData: function (file, item, attr) {
      var FILE = "file";
      var oldImg = item[attr];
      var data = new FormData();
      const that = this;
      data.append("file", file);
      if (oldImg != "") {
        data.append("key", oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: "post",
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          console.log(res,item, attr, that.configData,  999999999);

          item[attr] = domain + res.data.key;
        },
        error: function (err) {
          item[attr] = "";
        },
      });
    },
    addCards: function () {
      if (this.configData.source.pics.length >= 6) {
        return;
      }
      this.configData.source.pics.push({
        pic: "",
      });
    },
    delCards: function (item) {
      this.configData.source.pics.remove(item);
    },
    delPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.pic = "";
      }
    },
    addTg: function (item) {
      this.configData.tg.push({ title: "", content: "" });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({ title: "", content: "" });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: "", content: "" });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
  },
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
