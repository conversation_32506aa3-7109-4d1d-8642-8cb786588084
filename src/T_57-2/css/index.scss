@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/resultPage/style.scss';

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.hide {
  display: none;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.container {
  background-size: auto 100%;
  position: relative;
  // font-family:"ARLRDBD";

}

.pics {
  position: absolute;
  top: 1.3rem;
  left: 1.46rem;
  vertical-align: middle;

  .pic {
    border-radius: .1rem;
    margin-left: .56rem;
    width: 15.16rem;
    height: 4.72rem;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .page {
    position: absolute;
    width: 1rem;
    height: .5rem;
    right: 0;
    bottom: -0.6rem;
    font-size: .3rem;
    line-height: .5rem;
    background: #eeeeee;
    text-align: center;
    border-radius: .4rem;
  }

  .lrbtn-bg {
    width: 1.04rem;
    height: 1.30rem;
    position: absolute;
    top: 1.71rem;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    display: none;
  }

  .left {
    left: 0rem;
    background: url(../image/left.png) no-repeat;
    background-size: auto 100%;

  }

  .disable-left {
    background: url(../image/left-d.png) no-repeat;
    background-size: auto 100%;
    cursor: not-allowed !important;
  }

  .right {
    right: -0.56rem;
    background: url(../image/right.png) no-repeat;
    background-size: auto 100%;
  }

  .disable-right {
    background: url(../image/right-d.png) no-repeat;
    background-size: auto 100%;
    cursor: not-allowed !important;
  }
}

#pagenext {
  position: absolute;
  margin-left: 0rem;
  left: 8.5rem;
  width: 2.25rem;
  height: 1rem;
  line-height: .8rem;
  bottom: 2.25rem;
  font-size: .5rem;
  z-index: 501;
  border: none;
  background: url(../image/btn.png) no-repeat;
  background-size: 100% auto;

}

#pageclose {
  position: absolute;
  margin-left: 0rem;
  left: 8.5rem;
  width: 2.25rem;
  height: 1rem;
  line-height: .8rem;
  bottom: 2.25rem;
  font-size: .5rem;
  z-index: 501;
  border: none;
  background: url(../image/btn.png) no-repeat;
  background-size: 100% auto;
}

.start-page {
  position: absolute;
  width: 7.2rem;
  height: 4.8rem;
  background: rgba($color: #000000, $alpha: 0.8);
  top: 2.5rem;
  left: 6rem;
  border-radius: .4rem;
  z-index: 400;
  display: none;

  .shade {
    position: absolute;
    width: 4.1rem;
    height: 3.94rem;
    top: .43rem;
    left: 1.55rem;
    z-index: 500;
    background: url(../image/bg_backnum.png) no-repeat;
    background-size: auto 100%;
    line-height: 3.94rem;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;

    img {
      height: 2rem;
      vertical-align: middle;
    }
  }

  .result-container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    height: 100%;
  }
}

.firstRole {
  position: absolute;
  left: 0.4rem;
  bottom: 1.56rem;
  width: 3.70rem;
  height: 3.69rem;
  z-index: 400;
}

.role {
  position: absolute;
  left: 3.8rem;
  bottom: 1.56rem;
  width: 3.84rem;
  height: 3.69rem;
}

#btngogo {
  display: none;
}

.btn {
  position: absolute;
  bottom: 1.2rem;
  color: #fff;
  background: #F1A91E;
  border: #ffffff 3px solid;
  display: block;
  font-size: .26rem;
  width: 1.3rem;
  height: .6rem;
  line-height: .5rem;
  border-radius: .4rem;
  text-align: center;
  margin-left: 9rem;
  cursor: pointer;
  z-index: 500;
  display: none;
}
.btn-item{
  position: absolute;
  bottom: 1.2rem;
  width: 4.36rem;
  height: 1.9rem;
  margin-left: 7.42rem;
  cursor: pointer;
  z-index: 500;
  display: none;
}
.start{
  background: url("../image/start.png") no-repeat;
  background-size: 100%;
}
.gogo{
  background: url("../image/gogo.png") no-repeat;
  background-size: 100%;
}
