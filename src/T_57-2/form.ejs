<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>TCE0001FT_逃脱游戏FT</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="module-title">TCE0001FT_逃脱游戏FT</div>
      <% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
			<div class="c-group">
				<div class="c-title">编辑内容</div>
				<div class="c-areAtation img-upload">
					<font>注:图片数量2～6个</font>
				</div>
				<div class="c-area upload img-upload" >
					<div class="c-well" v-for="(item, index) in configData.source.pics">
						<span class="dele-tg-btn" @click="delCards(item)" v-show="configData.source.pics.length>2"></span>
						<div class="field-wrap">
							<label class="field-label"  for="">图片</label>
							<span class='txt-info'><em>文件大小≤100KB,尺寸1516x472 * </em></span>
							<input type="file"  v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size="1516*472" accept=".gif,.jpg,.jpeg,.png" @change="imageUpload($event,item,'pic',100)">
						</div>

						<div class="field-wrap">
							<label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.pic">上传</label>
							<label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.pic">重新上传</label>
						</div>
						<div class="img-preview" v-if="item.pic">
							<img :src="item.pic" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(item)">删除</span>
							</div>
						</div>
					</div>
					<button v-if="configData.source.pics.length<6" type="button" class="add-tg-btn" @click="addCards" >+</button>
				</div>
			</div>
			<div class="c-group">
				<div class="c-title">上传角色</div>
				<div class="c-area upload img-upload" >
          <div class="c-well">
						<div class="field-wrap">
							<label class="field-label"  for="">上传追击的角色（侧面的动画）</label>
							<span class='txt-info'><em>文件大小≤5000KB,尺寸370x369 * </em></span>
							<input type="file"  v-bind:key="Date.now()" class="btn-file" id="content-first-role" size="370*369" accept=".json" @change="lottieUpload($event,configData.source,'firstRole',5000)">
						</div>
						<div class="field-wrap">
							<label for="content-first-role" class="btn btn-show upload" v-if="!configData.source.firstRole">上传</label>
							<label for="content-first-role" class="btn upload re-upload" v-if="configData.source.firstRole">重新上传</label>
						</div>
						<div class="img-preview" v-if="configData.source.firstRole">
							<!-- <img src="//cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
							<img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(configData.source,'firstRole')">删除</span>
							</div>
						</div>
					</div>
					<div class="c-well">
						<div class="field-wrap">
							<label class="field-label"  for="">上传被追的角色（侧面的动画）</label>
							<span class='txt-info'><em>文件大小≤5000KB,尺寸384x369 * </em></span>
							<input type="file"  v-bind:key="Date.now()" class="btn-file" id="content-role" size="384*369" accept=".json" @change="lottieUpload($event,configData.source,'role',5000)">
						</div>
						<div class="field-wrap">
							<label for="content-role" class="btn btn-show upload" v-if="!configData.source.role">上传</label>
							<label for="content-role" class="btn upload re-upload" v-if="configData.source.role">重新上传</label>
						</div>
						<div class="img-preview" v-if="configData.source.role">
							<!-- <img src="//cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
							<img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
              <div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(configData.source,'role')">删除</span>
							</div>
						</div>
					</div>
          <div class="c-well">
						<div class="field-wrap">
              <label class="field-label"  for="">追击音频</label>
              <em>音频大小≤200KB </em>
              <input type="file"  v-bind:key="Date.now()" class="btn-file" id="pursuitAudio" accept=".mp3" @change="audioUpload($event,configData.source,'pursuitAudio',200)">
            </div>

            <div class="field-wrap">
              <label for="pursuitAudio" class="btn btn-show upload" v-if="!configData.source.pursuitAudio">上传</label>
              <label for="pursuitAudio" class="btn upload re-upload" v-if="configData.source.pursuitAudio">重新上传</label>
              <label class="btn upload btn-audio-dele" v-if="configData.source.pursuitAudio" @click="configData.source.pursuitAudio=''">删除</label>
            </div>
            <div class="audio-preview" v-show="configData.source.pursuitAudio">
              <div class="audio-tools">
                <p v-show="configData.source.pursuitAudio">{{configData.source.pursuitAudio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="configData.source.pursuitAudio"></audio>
              </span>
            </div>
					</div>
				</div>
			</div>

			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
