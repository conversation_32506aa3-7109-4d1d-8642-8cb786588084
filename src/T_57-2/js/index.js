"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import {
  resultWin,
  resultLose,
  resultHide,
} from "../../common/template/resultPage/index.js";
import "../../common/js/teleprompter.js";
import {
  USER_TYPE,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js";

$(function () {
  window.h5Template = {
    hasPractice: "0",
  };
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  let classStatus = "0"; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }
  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/defaultBg.jpg)" });
  }
  // 角色动画图
  let roleAnimations = 0;
  let firstRoleAnimations = 0;
  //业务逻辑
  let source = configData.source;
  let pics = source.pics || [];

  let gameLimit = 30; //游戏限定时长,单位是秒
  let roleStepLimit = 5; //点击次数，五次走完
  let biteLen = 1.6; //咬到的判定距离，单位rem
  let endX = 16.4; //终点x坐标，单位是rem
  let firstRoleLen = endX - 3.81; //3.81是css里面鱼的位置+宽度
  let firstRoleUnitLin = firstRoleLen / gameLimit; //鱼的单位移动
  let roleLen = endX - 7.84; //7.84是css里面人物的位置+宽度
  let roleUnitLine = roleLen / roleStepLimit; //角色的单位移动
  let gameInterval; //游戏执行的线程

  console.log(pics.length, "----");

  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      roundcount: pics.length || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.TEACHING_INPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.SPEAK,
    },
  });

  //填充内容
  const page = {
    showPics: async function () {
      let html = "";
      for (let i = 0; i < pics.length; i++) {
        let cls = "hide";
        if (i === 0) {
          cls = "";
        }
        html += `<img id="pic-${i}" class="${cls}" src="${pics[i].pic}" />`;
      }
      $(".pic").append(html);
      //配置的替换图片
      if (source.role) {
        roleAnimations = await lottieAnimations.init(
          roleAnimations,
          source.role,
          ".role"
        );
      }
      if (source.firstRole) {
        firstRoleAnimations = await lottieAnimations.init(
          firstRoleAnimations,
          source.firstRole,
          ".firstRole"
        );
      }
    },
    initPage: function () {
      $("#picpage").html("1/" + pics.length);
      if (!isSync || window.frameElement.getAttribute("user_type") == "tea") {
        $("#btnstart").show();
        $(".lrbtn-bg").css("display", "flex");
      }
      $("#soundwater").attr("src", source.pursuitAudio);
    },
    initCurrent: function () {
      SDK.syncData.currentPicIndex = 0; //当前页
      SDK.syncData.rolestep = 0; //角色前进步数
      SDK.syncData.successArr = []; // 成功次数
      SDK.syncData.hasFinishSuccessRequest = false; // 是否发送成功埋点
    },
    //重置游戏界面
    resetGame: function () {
      // $("#soundwater")[0].pause();
      SDK.pauseRudio({
        index: $("#soundwater").get("0"),
        syncName: $("#soundwater").attr("data-syncaudio"),
      });
      //角色和鲨鱼归位
      $(".firstRole").css({ left: "0.4rem" });
      $(".role").css({ left: "3.8rem" });
      lottieAnimations.stop(roleAnimations);
      lottieAnimations.stop(firstRoleAnimations);
      //隐藏遮罩
      $(".start-page").hide();
      //隐藏输赢的画面
      $("#result-role").hide();
      $(".win-animation").hide();
      $(".lose-animation").hide();

      //显示左右切换按钮
      if (!isSync || window.frameElement.getAttribute("user_type") == "tea") {
        //显示开始按钮
        $("#btnstart").show();
        $(".lrbtn-bg").css("display", "flex");
      }
      //重置缓存信息
      window.localStorage.removeItem("firstRolePosition");
      //重置必要的切面信息
      SDK.syncData.rolestep = 0; //角色前进步数
      SDK.syncData.gameStart = null; //游戏是否开始
      SDK.syncData.gameOver = null; //游戏判定结果
    },
    //设置左右图片切换按钮状态样式
    setLRBtn: function () {
      let currentPic = SDK.syncData.currentPicIndex;
      if (currentPic === pics.length - 1) {
        $(".right").addClass("disable-right");
      } else {
        $(".right").removeClass("disable-right");
      }
      if (currentPic === 0) {
        $(".left").addClass("disable-left");
      } else {
        $(".left").removeClass("disable-left");
      }
    },
    gamePause: function () {
      // $("#soundwater")[0].pause();
      SDK.pauseRudio({
        index: $("#soundwater").get("0"),
        syncName: $("#soundwater").attr("data-syncaudio"),
      });
      lottieAnimations.stop(roleAnimations);
      lottieAnimations.stop(firstRoleAnimations);
    },
    //玩游戏
    playGame: function () {
      let that = this;
      lottieAnimations.play(roleAnimations);
      lottieAnimations.play(firstRoleAnimations);
      // $("#soundwater")[0].play();
      SDK.playRudio({
        index: $("#soundwater")[0],
        syncName: $("#soundwater").attr("data-syncaudio"),
      });
      gameInterval = setInterval(function () {
        //游戏暂停
        if (gamePauseFlag) {
          that.gamePause();
          return;
        } else {
          lottieAnimations.play(roleAnimations);
          lottieAnimations.play(firstRoleAnimations);
        }

        let left =
          $(".firstRole").position().left / window.base + firstRoleUnitLin;
        //老师控制判定结果
        if (!isSync || window.frameElement.getAttribute("user_type") == "tea") {
          $(".firstRole").animate({ left: left + "rem" }, 1000, "linear");
          let isOver = that.isGameOver();
          if (isOver !== "doing") {
            SDK.syncData.gameOver = isOver;
            if (!isSync) {
              $("#btngogo").trigger("gameOver");
            } else {
              SDK.bindSyncEvt({
                index: "btn-gogo",
                eventType: "mygameevent",
                method: "event",
                syncName: "gameOver",
              });
            }
          }
          window.localStorage.setItem("firstRolePosition", left);
        } else {
          let isOver = that.isGameOver();
          if (
            isOver === "lose" &&
            SDK.syncData.gameOver !== "win" &&
            SDK.syncData.gameOver !== "lose"
          ) {
            //说明老师端没有判定游戏结束,或者结束事件延迟,不再移动
            window.localStorage.setItem(
              "firstRolePosition",
              $(".firstRole").position().left / window.base
            );
          } else {
            $(".firstRole").animate({ left: left + "rem" }, 1000, "linear");
            window.localStorage.setItem("firstRolePosition", left);
          }
        }
      }, 1000);
    },
    //判断游戏是否执行完毕
    isGameOver: function () {
      let res = "doing";
      //说明角色到达终点，游戏结束
      if (SDK.syncData.rolestep === roleStepLimit) {
        res = "win";
      }
      if (
        $(".role").position().left - $(".firstRole").position().left <
        biteLen * window.base
      ) {
        res = "lose";
      }
      return res;
    },
    gameOver: function () {
      lottieAnimations.stop(roleAnimations);
      lottieAnimations.stop(firstRoleAnimations);
      clearInterval(gameInterval);
      $("#btngogo").hide();
      // $("#soundwater")[0].pause();
      SDK.pauseRudio({
        index: $("#soundwater").get("0"),
        syncName: $("#soundwater").attr("data-syncaudio"),
      });
      const round = SDK.syncData.currentPicIndex || 0;
      console.log(round + 1,"----round");
      if (SDK.syncData.gameOver === "win") {
        SDK.syncData.successArr[round] = 1;

        SDK.reportTrackData(
          {
            action: "CK_FT_EVERYROUNDANSWER_RESULT",
            data: {
              roundid: round + 1,
              result: "pass",
            },
          },
          USER_TYPE.TEA
        );
        if (
          SDK.syncData.successArr.filter((item) => item === 1).length ===
            pics.length &&
          !SDK.syncData.hasFinishSuccessRequest
        ) {
          console.log("wacheng", SDK.syncData.successArr);
          SDK.reportTrackData(
            {
              action: "CK_FT_INTERACTION_COMPLETE",
              data: {
                result: "success"
              },
            },
            USER_TYPE.TEA
          );
          SDK.syncData.hasFinishSuccessRequest = true;
        }
        resultWin({
          WinIsLoop: false, // 答对声音是否重复播放 true/false
          Mask_Z_Index: "500", // 遮罩层z_index
        });
      } else {
        SDK.reportTrackData(
          {
            action: "CK_FT_EVERYROUNDANSWER_RESULT",
            data: {
              roundid: round + 1,
              result: "unpass",
            },
          },
          USER_TYPE.TEA
        );
        resultLose({
          src: "./image/resultSnow.png", // 雪图片
          loseIsLoop: false, // 答错声音是否重复播放 true/false
          Mask_Z_Index: "500", // 遮罩层z_index
          Snow_Z_Index: "1", // 雪z_index
        });
      }
      setTimeout(function () {
        if (!isSync || window.frameElement.getAttribute("user_type") == "tea") {
          let currentPic = SDK.syncData.currentPicIndex;
          if (currentPic < pics.length - 1) {
            $("#pagenext").fadeIn();
          } else {
            $("#pageclose").fadeIn();
          }
          SDK.setEventLock();
        }
      }, 1000);
      SDK.setEventLock();
    },
    init: function () {
      this.showPics(); //渲染卡片
      this.initPage(); //初始化页面其他数据
      this.initCurrent(); //初始化切面状态信息
    },
  };
  page.init();

  //断线重连页面恢复
  SDK.recover = function (data) {
    //恢复页签
    let currentPicIndex = data.currentPicIndex;
    $("#pic-0").hide();
    $("#pic-" + currentPicIndex).show();
    $("#picpage").html(currentPicIndex + 1 + "/" + pics.length);
    page.setLRBtn();

    //恢复游戏
    $(".shade").hide(); //倒计时不恢复
    $("#btnstart").hide();
    if (!data.gameStart) {
      //游戏未开始
      $("#btnstart").show();
    } else {
      //游戏开始，禁止切换卡片
      $(".left").hide();
      $(".right").hide();
      //恢复角色位置
      let rolestep = data.rolestep;
      let left =
        $(".role").position().left / window.base + rolestep * roleUnitLine;
      $(".role").css({ left: left + "rem" });
      //恢复鲨鱼位置
      let firstRolePosition = window.localStorage.getItem("firstRolePosition");
      $(".firstRole").css({ left: firstRolePosition + "rem" });
      if (data.gameOver !== "win" && data.gameOver !== "lose") {
        //游戏未结束
        if (!isSync || window.frameElement.getAttribute("user_type") == "tea") {
          $("#btngogo").show();
        }
        page.playGame();
      } else {
        page.gameOver();
      }
    }
    SDK.setEventLock();
  };

  let stuStatus, teaStatus; //检测老师或学生在教室的状态
  let gamePauseFlag = false; //游戏进程是否暂停标识
  SDK.memberChange = function (message) {
    if (message.role === "stu") {
      stuStatus = message.state;
    }
    if (message.role === "tea") {
      teaStatus = message.state;
    }
    //只要老师没在教室，游戏暂停
    if (teaStatus == "out") {
      gamePauseFlag = true;
    }
    //上课状态中，学生退出了，游戏暂停
    else if (classStatus == "1" && stuStatus == "out") {
      gamePauseFlag = true;
    } else {
      gamePauseFlag = false;
    }
  };

  $("#btngogo").on("gameOver", function () {
    page.gameOver();
  });

  let leftBtnClick = false;
  $(".left").syncbind(
    "click touchstart",
    function (dom, next) {
      if (leftBtnClick) {
        return;
      }
      if (dom.is(".disable-left")) {
        return;
      }
      leftBtnClick = true;
      //修改切面信息
      SDK.syncData.currentPicIndex--;
      if (!isSync) {
        next(false);
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        next();
      }
    },
    function () {
      let currentPic = SDK.syncData.currentPicIndex;
      page.setLRBtn(); //设置左右翻页按钮
      $(`#pic-${currentPic + 1}`).hide();
      $(`#pic-${currentPic}`).show();
      $("#picpage").html(currentPic + 1 + "/" + pics.length);
      page.resetGame();

      leftBtnClick = false;
      SDK.setEventLock();
    }
  );

  let rightBtnClick = false;
  $(".right").syncbind(
    "click touchstart",
    function (dom, next) {
      if (rightBtnClick) {
        return;
      }
      if (dom.is(".disable-right")) {
        return;
      }
      rightBtnClick = true;
      //修改切面信息
      SDK.syncData.currentPicIndex++;
      if (!isSync) {
        next(false);
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        next();
      }
    },
    function () {
      let currentPic = SDK.syncData.currentPicIndex;
      page.setLRBtn(); //设置左右翻页按钮
      $(`#pic-${currentPic - 1}`).hide();
      $(`#pic-${currentPic}`).show();
      $("#picpage").html(currentPic + 1 + "/" + pics.length);
      page.resetGame();

      rightBtnClick = false;
      SDK.setEventLock();
    }
  );

  let gameStartBtn = false;
  $("#btnstart").syncbind(
    "click touchstart",
    function (dom, next) {
      if (gameStartBtn) {
        return;
      }
      if (gamePauseFlag) {
        page.gamePause();
        //游戏暂停状态（学生端掉线了）
        return;
      }
      gameStartBtn = true;
      //游戏开始，禁止切换卡片
      $(".left").hide();
      $(".right").hide();
      //存储切面信息，游戏开始
      SDK.syncData.gameStart = true;
      if (!isSync) {
        next(false);
        // return
      }
      const round = SDK.syncData.currentPicIndex || 0;
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_STARTBUTTON",
          data: {
            roundid: round + 1,
          },
        },
        USER_TYPE.TEA
      );
      if (window.frameElement.getAttribute("user_type") == "tea") {
        next();
      }
    },
    function () {
      $(this).hide();
      $(".start-page").show();
      countDown(function () {
        $(".start-page").hide();
        if (!isSync || window.frameElement.getAttribute("user_type") == "tea") {
          $("#btngogo").show();
        }
        page.playGame();
      });
      gameStartBtn = false;
      SDK.setEventLock();
    }
  );

  let gogoBtn = false;
  let clickGogo = 0;
  $("#btngogo").syncbind(
    "click touchstart",
    function (dom, next) {
      if (gogoBtn) {
        return;
      }
      if (gamePauseFlag) {
        page.gamePause();
        //游戏暂停状态（学生端掉线了）
        return;
      }
      gogoBtn = true;
      //存储切面信息，角色前进步数
      SDK.syncData.rolestep++;
      //存储切面信息，游戏是否结束

      SDK.syncData.gameOver = page.isGameOver();
      if (!isSync) {
        next(false);
        return;
      }
      const round = SDK.syncData.currentPicIndex || 0;
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_SPOKEBUTTON",
          data: {
            roundid: round,
          },
        },
        USER_TYPE.TEA
      );
      if (window.frameElement.getAttribute("user_type") == "tea") {
        next();
      }
    },
    function () {
      clickGogo++;
      let left = $(".role").position().left / window.base + roleUnitLine;
      $(".role").animate({ left: left + "rem" }, 200, "linear", function () {
        if (
          SDK.syncData.gameOver === "win" ||
          SDK.syncData.gameOver === "lose"
        ) {
          page.gameOver();
        }
        gogoBtn = false;
        SDK.setEventLock();
      });
    }
  );

  let nextBtnClick = false;
  $("#pagenext").syncbind(
    "click touchstart",
    function (dom, next) {
      if (nextBtnClick) {
        return;
      }
      nextBtnClick = true;
      //存储切面信息，卡片索引
      SDK.syncData.currentPicIndex++;
      if (!isSync) {
        next(false);
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        next();
      }
    },
    function () {
      $(this).hide();
      resultHide();
      let currentPic = SDK.syncData.currentPicIndex;
      page.setLRBtn(); //设置左右翻页按钮
      $(`#pic-${currentPic - 1}`).hide();
      $(`#pic-${currentPic}`).show();
      $("#picpage").html(currentPic + 1 + "/" + pics.length);
      page.resetGame();

      nextBtnClick = false;
      SDK.setEventLock();
    }
  );

  let closeBtnClick = false;
  $("#pageclose").syncbind(
    "click touchstart",
    function (dom, next) {
      if (closeBtnClick) {
        return;
      }
      closeBtnClick = true;
      if (!isSync) {
        next(false);
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        next();
      }
    },
    function () {
      $(this).hide();
      resultHide();
      page.resetGame();

      closeBtnClick = false;
      SDK.setEventLock();
    }
  );

  function countDown(callback) {
    $(".shade").show();
    let num = 3;
    console.log("num", num);
    $(".shade img").attr("src", `image/num${num}.png`);
    function numAnimate() {
      $("#soundone").load();
      if (num === 0) {
        // $("#soundone")[0].play();
        SDK.playRudio({
          index: $("#soundone")[0],
          syncName: $("#soundwater").attr("data-syncaudio"),
        });
      } else {
        // $("#soundtwo")[0].play();
        SDK.playRudio({
          index: $("#soundone")[0],
          syncName: $("#soundwater").attr("data-syncaudio"),
        });
      }
      $(".shade img").css({ height: "2rem", opacity: 1 });
      $(".shade img").animate({ height: "0rem", opacity: 0 }, 900, function () {
        num--;
        if (num > -1) {
          $(".shade img").attr("src", `image/num${num}.png`);
        }
        if (num === -1) {
          $(".shade").hide();
          callback && callback();
        } else {
          setTimeout(function () {
            numAnimate();
          }, 100);
        }
      });
    }
    numAnimate();
  }
});
