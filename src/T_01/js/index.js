"use strict"
import '../../common/js/common_1v1.js'
import "../../common/js/teleprompter.js"
$(function () {
    window.h5Template = {
    	hasPractice: '1'
    }
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync

    if (configData.bg == '') {
        $(".container").css({ 'background-image': 'url(./image/defaultBg.png)' })
    }
    let itemClick = true;
    $('.main_box').prepend('<img src="'+ configData.source.img+'" class="catch_doll">')
    $('.rod').on('click touchstart', function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (itemClick) {
            itemClick = false;
            if (!isSync) {
                $(this).trigger('rodClick')
                return;
            }
            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                funcType: 'audio',
                syncName: 'rodClick'
            });
        }
    })
    $('.rod').on('rodClick', function () {
        $('.one_mp3').get(0).currentTime=0;
        // $('.one_mp3').get(0).play();
        SDK.pauseRudio({
            index: $('.one_mp3').get('0'),
            syncName: $('.one_mp3').attr("data-syncaudio")
        })
        SDK.playRudio({
            index: $('.one_mp3').get('0'),
            syncName: $('.one_mp3').attr("data-syncaudio")
        })
        $('.hand').removeClass('handAnimate').hide();
        $('.rod').addClass('rodDown');
        $('.billot').addClass('billotAn').on('animationend webkitAnimationEnd', function () {
            setTimeout(function () {
                // $('.one_mp3').get(0).pause();
                // $('.two_mp3').get(0).play();
                SDK.pauseRudio({
                    index: $('.one_mp3').get('0'),
                    syncName: $('.one_mp3').attr("data-syncaudio")
                })
                SDK.playRudio({
                    index: $('.two_mp3').get('0'),
                    syncName: $('.two_mp3').attr("data-syncaudio")
                })
                $('.billot').off('animationend webkitAnimationEnd');
                $('.rod').addClass('rodLeft').removeClass('rodDown');
                $('.moveItem').addClass('moveItemAn').on('animationend webkitAnimationEnd',function () {
                    $('.button').addClass('buttonAn');
                    $('.moveItem').off('animationend webkitAnimationEnd');
                    setTimeout(function () {
                        // $('.two_mp3').get(0).pause();
                        // $('.three_mp3').get(0).play();
                        SDK.pauseRudio({
                            index: $('.two_mp3').get('0'),
                            syncName: $('.two_mp3').attr("data-syncaudio")
                        })
                        SDK.playRudio({
                            index: $('.three_mp3').get('0'),
                            syncName: $('.three_mp3').attr("data-syncaudio")
                        })
                        setTimeout(function () {
                            $('.behind_claw').hide()
                        },900)
                        $('.line').addClass('lineAn').on('animationend webkitAnimationEnd',function () {
                            $('.line').off('animationend webkitAnimationEnd');
                            setTimeout(function () {
                                // $('.three_mp3').get(0).pause();
                                // $('.four_mp3').get(0).play();
                                SDK.pauseRudio({
                                    index: $('.three_mp3').get('0'),
                                    syncName: $('.three_mp3').attr("data-syncaudio")
                                })
                                SDK.playRudio({
                                    index: $('.four_mp3').get('0'),
                                    syncName: $('.four_mp3').attr("data-syncaudio")
                                })
                                $('.left_claw').addClass('catchLe');
                                $('.right_claw').addClass('catchRi');
                                setTimeout(function () {
                                    $('.catch_doll').addClass('upDoll');
                                    $('.line').removeClass('lineAn').addClass('lineAn_2').on('animationend webkitAnimationEnd',function () {
                                        setTimeout(function () {
                                            // $('.four_mp3').get(0).pause();
                                            SDK.pauseRudio({
                                                index: $('.four_mp3').get('0'),
                                                syncName: $('.four_mp3').attr("data-syncaudio")
                                            })
                                            $('.moveItem').off('animationend webkitAnimationEnd');
                                            $('.moveItem1').addClass('moveItemAn_2');
                                            $('.catch_doll').addClass('leftDoll');
                                            // $('.five_mp3').get(0).play();
                                            SDK.playRudio({
                                                index: $('.five_mp3').get('0'),
                                                syncName: $('.five_mp3').attr("data-syncaudio")
                                            })
                                            setTimeout(function () {
                                                $('.left_claw').removeClass('catchLe');
                                                $('.right_claw').removeClass('catchRi');
                                                $('.catch_doll').addClass('downDoll');
                                                $('#behind_claw').show();
                                                //娃娃机结束 上报
                                                if(isSync){
                                                  console.log("over")
                                                  SDK.bindSyncEvt({
                                                    method: 'ai_event',
                                                    syncName: 'over'
                                                  });
                                                }

                                            }, 1000)
                                        },1000)
                                    });
                                },1000)
                            },1000)
                        })
                    },1000)
                })
            },1000)
        })
        // itemClick = true;
        SDK.setEventLock();
    })
})
