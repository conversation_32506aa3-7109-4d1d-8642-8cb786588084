"use strict"
import '../../common/js/common_1v1.js'
import '../../common/js/drag.js'

$(function() {
  window.h5Template = {
    hasPractice: '0'
  }
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  if(configData.bg==''){
    $(".container").css({'background-image': 'url(./image/defaultBg.jpg)'})
  }
  //多语言
  console.log("configData.lang",configData.lang)
  const lang1 = configData.lang == 1 ?'開始':'开始' ;
  const lang2 = configData.lang == 1 ?'誰贏了?':'谁赢了?'
  const lang_teacher_chose = configData.lang == 1 ?'老師':'老师'
  const lang_student_chose = configData.lang == 1 ?'學生':'学生'
  const lang5 = configData.lang == 1 ?'teacher-replace-2':'teacher-replace-22'
  const lang6 = configData.lang == 1 ?'student-replace-2':'student-replace-22'
  // const lang7 = configData.lang == 1 ?'wincup':'wincup2'
  const lang7 = 'wincup';
  $("#lang1").text(lang1)
  $("#lang2").text(lang2)
  $("#lang3").text(lang_teacher_chose)
  $("#lang4").text(lang_student_chose)
  $("#lang5").addClass(lang5)
  $("#lang6").addClass(lang6)
  //业务逻辑
  let source = configData.source;
  let picTemp = source.pics;
  let pics = [];
  let times = source.time; 
  //兼容第一版模板（第一版没有time字段）
  if(!times){
    times = 2;
  }
  while(times>0){
    pics = pics.concat(picTemp); //第一版需求上要重复两遍，第二版是可配置重复次数
    times--;
  }
  
  //填充内容
  const page = {
    showPics: function() { 
      let html = '<img id="pic-start" data-syncactions="pic-start" src="image/source_question.png" />';
      for (let i = 0; i < pics.length; i++) {
        html += `<img class="pic-${i} hide" data-syncactions="pic-${i}" src="${pics[i].pic}" />`;
      }  
      html += `<div id="pic-end" class="hide pic-end" data-syncactions="pic-end">
        <div class="light light-run"></div>
        <div class="${lang7}"></div>
      </div>`;
      $(".pic").html(html);
      $(".text").html("1/" + pics.length);
    },
    init: function() {
      this.showPics(); //渲染卡片
    }
  }
  page.init();

  //老师操作的拖拽条
  $(".alertBox").drag();

  //倒计时动画
  function countDown(callback) { 
    callback&&callback();
    // $(".shade").show().addClass("shade-run").on('animationend webkitAnimationEnd',function(){
    //   $(this).hide();
    //   callback&&callback();
    // }); 
    // SDK.playRudio({
    //   index: $('#soundtwo')[0],
    //   syncName: $('#soundtwo').attr("data-syncaudio")
    // })
    // setTimeout(function(){
    //   $("#soundtwo")[0].currentTime = 0;
    //   // $("#soundtwo")[0].play();
    //   SDK.playRudio({
    //     index: $('#soundtwo')[0],
    //     syncName: $('#soundtwo').attr("data-syncaudio")
    //   })
    // },800);
    // setTimeout(function(){
    //   // $("#soundone")[0].play();
    //   SDK.playRudio({
    //     index: $('#soundone')[0],
    //     syncName: $('#soundone').attr("data-syncaudio")
    //   })
    // },1600);
  } 

  //老师勾选时的动画
  function rightAnimate(state,callback){
    // $("#soundthree")[0].play();
    SDK.playRudio({
      index: $('#soundthree')[0],
      syncName: $('#soundthree').attr("data-syncaudio")
    })
    let obj = { width: ".66rem", height:".494rem", left: "-3.4rem",top:"2rem",opacity:0 };
    if(state==="right"){
      obj = { width: ".66rem", height:".494rem", left: "10rem",top:"2rem",opacity:0 };
    }
    $(".pic-"+cardNum).animate(obj, 600, function() {
      callback&&callback();
    }); 
  } 

  //断线重连恢复结果切面信息 
  SDK.recover = function(data) { 
    cardNum = data.cardNum;
    teaScore = data.teaScore;
    stuScore = data.stuScore;
    let hNum = Math.floor(teaScore/10);
    let lNum = Math.floor(teaScore%10); 
    $(".teacher .score_1").css("background-position",getScoreClass(hNum));
    $(".teacher .score_2").css("background-position",getScoreClass(lNum)); 
    hNum = Math.floor(stuScore/10);
    lNum = Math.floor(stuScore%10); 
    $(".student .score_1").css("background-position",getScoreClass(hNum));
    $(".student .score_2").css("background-position",getScoreClass(lNum)); 

    if(teaScore){
      if(configData.lang == 1){
        $(".teacher-folder").removeClass("teacher-replace-2").addClass("teacher-replace-1");
      }else{
        $(".teacher-folder").removeClass("teacher-replace-22").addClass("teacher-replace-11");
      }
      
    }
    if(stuScore){
      if(configData.lang == 1){
        $(".student-folder").removeClass("student-replace-2").addClass("student-replace-1");
      }else{
        $(".student-folder").removeClass("student-replace-22").addClass("student-replace-11");
      }
      
    }
    $(".text").html((cardNum+1)+"/" + pics.length);
    //表示正在进行
    if(data.isRun){
      $("#pic-start").hide();
      $(".start-btn").hide();
      $(".pic-"+cardNum).show();
      if (!isSync||window.frameElement.getAttribute('user_type') == 'tea') {  
        if(pics[cardNum].text){
          $(".alertBox .keystr").html("答案:"+pics[cardNum].text);
          $(".alertBox .chosespan").css({"float":"right","margin-right":".8rem"});
        }else{
          $(".alertBox .keystr").html("");
          $(".alertBox .chosespan").css({"float":"none","margin-right":"0rem"});
        }
        $(".alertBox").show();
      }
    }else{
      //最终状态（都答完了）
      if(cardNum===pics.length-1){
        // $("#winaudio")[0].play();
        SDK.playRudio({
          index: $('#winaudio')[0],
          syncName: $('#winaudio').attr("data-syncaudio")
        })
        $("#pic-start").hide();
        $(".start-btn").hide(); 
        $("#pic-end").show(); 
      }
    } 

    SDK.setEventLock();

  }

  //初始化切面信息
  SDK.syncData = {
    teaScore:0,  //老师得分
    stuScore:0,  //学生得分
    cardNum:-1,  //进行到卡片第几页
    isRun:false //是否在进行中
  };


  let startBtnClick = false;
  let cardNum = -1; //进行到卡片第几页
  $(".start-btn").syncbind("click touchstart",function(dom,next){
    if(startBtnClick){
      return;
    }
    startBtnClick = true;
    cardNum++;
    if (!isSync) {
      next(false);
      return
    }
    if (window.frameElement.getAttribute('user_type') == 'tea') { 
      //存储结果切面信息
      SDK.syncData.cardNum = cardNum;
      SDK.syncData.isRun = true;
      next();
    }
  },function(){
    if(isSync){
      cardNum = SDK.syncData.cardNum;
    }
    // $("#pic-start").hide();
    $(".start-btn").hide(); 
    countDown(function(){ 
      $(".pic-"+cardNum).show();
      if (!isSync||window.frameElement.getAttribute('user_type') == 'tea') { 
        if(pics[cardNum].text){
          $(".alertBox .keystr").html("答案:"+pics[cardNum].text);
          $(".alertBox .chosespan").css({"float":"right","margin-right":".8rem"});
        }else{
          $(".alertBox .keystr").html("");
          $(".alertBox .chosespan").css({"float":"none","margin-right":"0rem"});
        }
        $(".alertBox").fadeIn();
      } 
      $(".text").html((cardNum+1)+"/" + pics.length);
      startBtnClick = false;
      SDK.setEventLock();
    });
  }); 


  function getScoreClass(num){
    if(num==0){
      return "-9.9rem 0";
    }else if(num==1){
      return "0 0";
    }else{
      return -((num-1)*1.1)+"rem 0";
    }
  }
 

  let teaChose = false; 
  let teaScore = 0;
  $("#teacher_chose").syncbind("click touchstart",function(dom,next){
    if(teaChose){
      return;
    }
    teaChose = true; 
    teaScore++;
    if (!isSync) {
      next(false);
      return
    }
    if (window.frameElement.getAttribute('user_type') == 'tea') { 
      //存储结果切面信息 
      SDK.syncData.isRun = false;
      SDK.syncData.teaScore = teaScore;
      next();
    }
  },function(){
    if(isSync){
      teaScore = SDK.syncData.teaScore;
    }
    let _this = $(this);
    _this.find("img").attr("src","image/chose.png");
    rightAnimate("left",function(){
      $(".alertBox").fadeOut(function(){
        _this.find("img").attr("src","image/unchose.png");
      }); 
      let hNum = Math.floor(teaScore/10);
      let lNum = Math.floor(teaScore%10); 
      $(".teacher .score_1").css("background-position",getScoreClass(hNum));
      $(".teacher .score_2").css("background-position",getScoreClass(lNum));
      if(configData.lang == 1){
        $(".teacher-folder").removeClass("teacher-replace-2").addClass("teacher-replace-1");
      }else{
        $(".teacher-folder").removeClass("teacher-replace-22").addClass("teacher-replace-11");
      }
      if(cardNum<pics.length-1){
        $("#pic-start").fadeIn();
        $(".start-btn").fadeIn();
      }else{
        //所有图片都答完了
        // $("#winaudio")[0].play();
        SDK.playRudio({
          index: $('#winaudio')[0],
          syncName: $('#winaudio').attr("data-syncaudio")
        })
        $("#pic-end").fadeIn();
        $(".score_1").addClass("tobig");
        $(".score_2").addClass("tobig");
      } 
      SDK.setEventLock();
      teaChose = false;
    });
  });
 
  let stuScore = 0;
  $("#student_chose").syncbind("click touchstart",function(dom,next){
    if(teaChose){
      return;
    }
    teaChose = true; 
    stuScore++;
    if (!isSync) {
      next(false);
      return
    }
    if (window.frameElement.getAttribute('user_type') == 'tea') { 
      //存储结果切面信息 
      SDK.syncData.isRun = false;
      SDK.syncData.stuScore = stuScore;
      next();
    }
  },function(){
    if(isSync){
      stuScore = SDK.syncData.stuScore;
    } 
    let _this = $(this);
    _this.find("img").attr("src","image/chose.png");
    rightAnimate("right",function(){
      $(".alertBox").fadeOut(function(){
        _this.find("img").attr("src","image/unchose.png");
      });
      let hNum = Math.floor(stuScore/10);
      let lNum = Math.floor(stuScore%10);
      $(".student .score_1").css("background-position",getScoreClass(hNum));
      $(".student .score_2").css("background-position",getScoreClass(lNum));
      
      if(configData.lang == 1){
        $(".student-folder").removeClass("student-replace-2").addClass("student-replace-1");
      }else{
        $(".student-folder").removeClass("student-replace-22").addClass("student-replace-11");
      }
      if(cardNum<pics.length-1){
        $("#pic-start").fadeIn();
        $(".start-btn").fadeIn();
      }else{
        //所有图片都答完了
        // $("#winaudio")[0].play();
        SDK.playRudio({
          index: $('#winaudio')[0],
          syncName: $('#winaudio').attr("data-syncaudio")
        })
        $("#pic-end").fadeIn();
        $(".score_1").addClass("tobig");
        $(".score_2").addClass("tobig");
      } 
      SDK.setEventLock();
      teaChose = false;
    });
  });

})