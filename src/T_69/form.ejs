<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>PRS0001_呈现翻转</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src='./form/js/vue.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">PRS0001_呈现翻转</h3>

      <% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
      <!-- 翻转设置 -->
      <div class="c-group">
        <div class="c-title">翻转设置</div>
          <div class="c-area upload img-upload">
            <div class="field-wrap">
              <div class="c-well">
                <div class="title">音效&nbsp;&nbsp;<span>伴随翻转动画出现</span></div>
                <div class="audio-list">
                  <label class="field-label" for="">音频<em>*</em>&nbsp;&nbsp;&nbsp;</label>
                  <input type="file" accept=".mp3" id="content-audio" volume="120" v-bind:key="Date.now()"
                    class="btn-file" v-on:change="audioUpload($event,configData.source,'audio')">
                  <label for="content-audio" class="btn btn-show upload" v-if="!configData.source.audio">上传</label>
                  <div class="audio-preview" v-show="configData.source.audio">
                    <div class="audio-tools">
                      <p v-show="configData.source.audio">{{configData.source.audio}}</p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="configData.source.audio"></audio>
                    </span>
                  </div>
                  <label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.audio"
                    @click="configData.source.audio=''">删除</label>
                  <label for="content-audio" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
                  <label><em>大小：≤120KB</em></label>
                </div>
                <div class="details">
                  <label>
                    <span>播放时长</span>
                    <em>*</em>
                    <input type="number" class="c-input-txt input70"
                      oninput="if(value>6000)value=6000;if(value<0)value=0" v-model="configData.source.audioPlayTime">
                    数字，1000～6000 毫秒
                  </label>
                </div>
              </div>
              <div class="c-well">
                <div class="title">教学音频设置</div>
                <div class="details">
                  <span>组合数量</span>
                  <label class="inline-label" for="isAudioShow">
                    <input type="radio" name="isAudioShow" value="1" v-model="configData.source.isAudioShow">
                    含教学音频
                  </label>
                  <label class="inline-label" for="isAudioShow">
                    <input type="radio" name="isAudioShow" value="2" v-model="configData.source.isAudioShow">
                    不含教学音频
                  </label>
                </div>
              </div>
              <div class="c-well">
                <div class="title">老师面板</div>
                <div class="details">
                  <span>序号显示</span>
                  <label class="inline-label" for="number">
                    <input type="radio" name="number" value="1" v-model="configData.source.number">
                    显示序号
                  </label>
                  <label class="inline-label" for="number">
                    <input type="radio" name="number" value="2" v-model="configData.source.number">
                    不显示序号
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 模块组合 -->
        <div class="c-group">
          <div class="c-title">翻转模块</div>
          <div class="c-area upload img-upload">
            <div>-每个模块中共用相同的音效。</div><br>
            <div class="field-wrap" v-for="(item,index) in configData.source.options">
              <div class="field-title">翻转模块{{index+1}}</div>
              <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>1"></span>
              <div class="c-well">
                <!-- 触发按钮 -->
                <div class="title">触发按钮</div>
                <div class="details">
                  <label class="title-label">
                    <span class="title-t">触发按钮<em>*</em><br>文案</span>
                    <input type="text" class="tiggerCls" placeholder="请在此输入描述" v-model="item.title">
                    <span class="title-s">
                      <=12个字符。<br>
                        参考文案：show 。
                    </span>
                  </label>
                </div>
                <!-- 老师面板 -->
                <div class="details">
                  <label class="title-label">
                    <span class="title-t">老师面板<em>*</em><br>位置</span>&nbsp;&nbsp;
                    X:<input type="number" class="tiggerCls distance" v-model="item.moveX"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" class="tiggerCls distance" v-model="item.moveY"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>
                <!-- 触发按钮序号 -->
                <div class="details" v-if="configData.source.number == 1">
                  <label class="title-label">
                    <span class="title-t">触发按钮<em>*</em><br>序号</span>&nbsp;&nbsp;
                    <select class="select-class" v-model="item.number">
                      <option v-for="conPos in configData.source.options.length" name="optive" :value="conPos">
                        {{conPos}}</option>
                    </select>
                  </label>
                </div>
                <!-- 备注文案 -->
                <div class="details">
                  <label class="title-label">
                    <span class="title-t">备注文案</span>
                    <input type="text" class="tiggerCls" placeholder="请在此输入描述" v-model="item.subTitle">
                    <span class="title-s"></span>
                  </label>
                </div>
              </div>
              <!-- 翻转动画 -->
              <div class="c-well">
                <div class="title">翻转动画</div>
                <!-- <div class="details"> -->
                <!-- 默认面 -->
                <div class="details">
                  <span class='txt-info'>默认面<em>*</em>
                    <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.positiveImg">上传</label>
                    <label :for="'content-pic-'+index" class="btn upload re-upload"
                      v-if="item.positiveImg!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤60KB </em></span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1"
                      :id="'content-pic-'+index" @change="imageUpload($event,item,'positiveImg',60)">
                  </span>
                </div>
                <div class="img-preview" v-if="item.positiveImg">
                  <img v-bind:src="item.positiveImg" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.positiveImg=''">删除</span>
                  </div>
                </div>
                <!-- 答案面 -->
                <div class="details">
                  <span class='txt-info'>答案面<em>*</em>
                    <label :for="'content-reverse-'+index" class="btn btn-show upload"
                      v-if="!item.reverseImg">上传</label>
                    <label :for="'content-reverse-'+index" class="btn upload re-upload"
                      v-if="item.reverseImg!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤60KB </em></span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1"
                      :id="'content-reverse-'+index" @change="imageUpload($event,item,'reverseImg',60)">
                  </span>
                </div>
                <div class="img-preview" v-if="item.reverseImg">
                  <img v-bind:src="item.reverseImg" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.reverseImg=''">删除</span>
                  </div>
                </div>
                <!-- 位置 -->
                <div class="details">
                  <label class="title-label">
                    <span class="title-t">位置<em>*</em></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    X:<input type="number" v-model="item.positionX" class="tiggerCls distance"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" v-model="item.positionY" class="tiggerCls distance"
                      oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>

                <!-- </div> -->
              </div>
              <!-- 教学音频 -->
              <div class="c-well" v-if="configData.source.isAudioShow == 1">
                <div class="title">教学音频</div>
                <div class="audio-list">
                  <label class="field-label" for="">音频<em>*</em>&nbsp;&nbsp;&nbsp;</label>
                  <input type="file" accept=".mp3" :id="'content-audio-'+index" volume="120" v-bind:key="Date.now()"
                    class="btn-file" v-on:change="audioUpload($event,item,'teachingAudio')">
                  <label :for="'content-audio-'+index" class="btn btn-show upload" v-if="!item.teachingAudio">上传</label>
                  <div class="audio-preview" v-show="item.teachingAudio">
                    <div class="audio-tools">
                      <p v-show="item.teachingAudio">{{item.teachingAudio}}</p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="item.teachingAudio"></audio>
                    </span>
                  </div>
                  <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.teachingAudio"
                    @click="item.teachingAudio=''">删除</label>
                  <label :for="'content-audio-'+index" class="btn upload re-upload"
                    v-if="item.teachingAudio">重新上传</label>
                  <label><em>大小：≤120KB</em></label>
                </div>
                <div class="details">
                  <label>
                    <span>播放时长</span>
                    <em>*</em>
                    <input type="number" class="c-input-txt input70"
                      oninput="if(value>6000)value=6000;if(value<0)value=0" v-model="item.teachingAudioTime">
                    数字，1000～6000 毫秒
                  </label>
                </div>
                <div class="details">
                  <label>
                    <span>播放时机</span>
                    <em>*</em>
                  </label>
                  <label class="inline-label" >
                    <input type="radio"  value="1" v-model="item.teachingAudioTiming">
                    自动播放
                  </label>
                  <label class="inline-label" >
                    <input type="radio"  value="2" v-model="item.teachingAudioTiming">
                    手动播放
                  </label>
                </div>
                <!-- 位置 -->
                <div class="details" v-if="item.teachingAudioTiming == 2">
                  <label class="title-label">
                    <span class="title-t">按钮位置<em>*</em></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    X:<input type="number" v-model="item.teachingTimingX" class="tiggerCls distance"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" v-model="item.teachingTimingY" class="tiggerCls distance"
                      oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>

                <!-- <div class="details">
                  <label><span>播放时机<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;翻转动画、音效结束后，播放教学音频</span></label>
                </div>
                <div class="details">
                  <label><span>教学音频图标&nbsp;&nbsp;&nbsp;&nbsp;不显示</span></label>
                </div> -->
              </div>
            </div>
          </div>

          <button type="button" class="add-tg-btn" v-show="configData.source.options.length<4" v-on:click="addOption({
          title: '',
          subTitle: '',
          moveX: '',
          moveY: '',
          number: '',
          positiveImg: '',
          reverseImg: '',
          positionX:'',
          positionY:'',
          teachingAudio:'',
          teachingAudioTime:'',
          isRight:0
        })">添加一轮</button>
          <br>
        </div>

        <button class="send-btn" v-on:click="onSend">提交</button>

    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>

</html>
