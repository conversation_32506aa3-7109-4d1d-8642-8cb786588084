"use strict"
import '../../common/js/common_1v1.js'
// import '../../common/js/commonFunctions.js'
import "../../common/js/teleprompter.js"

const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function() {

    // 埋点：进入页面
    var h5SyncActions = parent.window.h5SyncActions;
    /**
     * 控制器调用的变量
     */
    window.h5Template = {
        hasPractice: '0' // 是否有授权按钮 1：是 0：否
    }

    if (configData.bg == '') {
        $(".container").css({
            'background-image': 'url(./image/bj.jpg)'
        })
    }
    let audio = configData.source.audio, //音频
        audioPlayTime = configData.source.audioPlayTime, //音频播放时长
        isAudioShow = configData.source.isAudioShow, //是否显示教学音频  1：显示  2:不显示
        options = configData.source.options, //上次素材
        isCheck = true, //是否可点击
        isPlaySound = true, //是否播放声音
        index = '', //当前的index值
        isKeys = []; //选中后的key集合（用于断线重连）

    /**
     *初始化动画组合列表
     */
    optionsList()

    function optionsList() {
        console.log("初始化状态")
        // 音频音效赋值
        $(".soundAudio").attr("src", audio)
        options.forEach(function(item, index) {
            console.log("item----",item)
            let teachingAudioDom = (item.teachingAudio)?`<audio src=${item.teachingAudio} data-syncaudio="soundAudio-${index}"></audio>`:'<span></span>'
            console.log(teachingAudioDom)
            let liEle = '';
            isKeys = [];
            // 触发按钮和图片的位置
            let moveLeft = item.moveX / 100 + 'rem',
                moveTop = item.moveY / 100 + 'rem',
                imgLeft = item.positionX / 100 + 'rem',
                imgTop = item.positionY / 100 + 'rem',
                audioLeft = item.teachingTimingX / 100 + 'rem',
                audioTop = item.teachingTimingY / 100 + 'rem';
            liEle = $(`<li data-audio=${item.teachingAudio} data-audio-time=${item.teachingAudioTime} data-syncactions="onList-${index}">` +
                `<div class="list-position triggerList" data-syncactions="onList-${index}" data-timing=${item.teachingAudioTiming}>` +
                `<span class="number">${item.number}</span>` +
                `<div class="content-tit"><span class="tit">${item.title}</span>` +
                `<span class="sub-title">${item.subTitle}</span></div>` +
                '</div>' +
                '<div class="list-position options-list">' +
                `<img class="positive-img img" src=${item.positiveImg}>` +
                `<img class="reverse-img img hide" src=${item.reverseImg}>` +
                '</div>' +
                `<div class="example-audio sound  example-audio-${index}" data-syncactions="audio-${index}">` +
                '<img src="./image/sound.gif" alt="" class="gif small">' +
                '<img src="./image/sound.png" alt="" class="png small">' +
                teachingAudioDom +
                '</div>' +
                '</li>')
            // 触发按钮初始化样式
            liEle.find(".triggerList").css({
                left: moveLeft,
                top: moveTop
            });
            // 图片初始化样式
            liEle.find(".options-list").css({
                left: imgLeft,
                top: imgTop,
                // display: 'flex'
            })
            // 音频样式
            liEle.find(".example-audio").css({
                left: audioLeft,
                top: audioTop
            });
            // 加载图片 获取宽高
            var imgList = [{
                    image: item.positiveImg
                },
                {
                    image: item.reverseImg
                }
            ]
            var preImgs = [];
            $.when(preloadImg(imgList, preImgs)).done(function() { //图片加载完成后 才能获得宽高
                liEle.find(".positive-img").css({
                    backgroundSize: '100% 100%',
                    width: preImgs[0].width / 100 + 'rem',
                    height: preImgs[0].height / 100 + 'rem'
                });
                liEle.find(".reverse-img").css({
                    backgroundSize: '100% 100%',
                    width: preImgs[1].width / 100 + 'rem',
                    height: preImgs[1].height / 100 + 'rem'
                });
            });


            $(".dinosaur-list ul").append(liEle);
            //是否显示教学音频
            if (isAudioShow == 2 || (isAudioShow == 1 && item.teachingAudioTiming == 1)) {
                // $('.example-audio').hide();
                $(`.example-audio-${index}`).hide();

            }
            // 学生不显示
            if (isSync && window.frameElement.getAttribute('user_type') == 'stu') {
                $(".triggerList").hide();
            }
            // 是否显示序号
            if (configData.source.number == 2) $(".dinosaur-list ul li").find(".number").hide();
            if (item.number.length == 0) $(".dinosaur-list ul li").eq(index).find(".number").hide();
            // 是否显示副标题
            if (item.subTitle.length == 0) $(".dinosaur-list ul li").eq(index).find(".sub-title").hide();
        })
    }

    /**
     * 触发按钮列表事件
     */
    $(".dinosaur-list ul").on("click touchstart", "li .triggerList", function(e) {
        if (isPlaySound) {
            if ($(this).parent().find(".reverse-img").is(":hidden")) {
                isKeys.push($(this).parent().index())
            }

            if (e.type == "touchstart") {
                e.preventDefault()
            }
            e.stopPropagation();
            if (!isSync) {
                $(this).trigger("synResultClick");
                return;
            }

            if (window.frameElement.getAttribute('user_type') == 'tea') {
                SDK.bindSyncEvt({
                    sendUser: '',
                    receiveUser: '',
                    index: $(e.currentTarget).data("syncactions"),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'synTriggerClick',
                    otherInfor: {
                        index: isKeys
                    },
                    recoveryMode: '1'
                });
            }
        }
    })

    $(".dinosaur-list ul").on("click synTriggerClick", "li .triggerList", function(e, message) {
        index = $(this).parent().index();
        let dinosaurLi = $(".dinosaur-list ul li").eq(index);

        // 断线重连
        if (isSync && message && message.operate == 5) {
            let obj = message.data[0].value.syncAction.otherInfor;
            isKeys = obj.index
            if (obj.index.length > 0) {
                for (let i = 0; i < obj.index.length; i++) {
                    let isSuycLi = $(".dinosaur-list ul li").eq(obj.index[i]);
                    // 显示正面图片
                    isSuycLi.find(".positive-img").hide();
                    isSuycLi.find(".reverse-img").show();
                    // 触发按钮置灰
                    isSuycLi.find(".triggerList").addClass("gray");
                    // 为当前翻转添加动画效果
                    dinosaurLi.find(".img").css({
                        transition: 'all 1s',
                        transform: 'rotateY(180deg)'
                    });
                }
            }
            SDK.setEventLock();
        }

        if ($(this).parent().find(".reverse-img").is(":hidden") && isPlaySound) {
            // 赋值教学音频
            $(".teacherAudio").attr("src", options[index].teachingAudio)
            // 播放音频
            audioFn($(".soundAudio")).soundAudio();
            // 为当前翻转添加动画效果
            dinosaurLi.find(".img").css({
                transition: 'all 1s',
                transform: 'rotateY(180deg)'
            });

            $(this).delay(300).queue(function() {
                // 显示正面图片
                dinosaurLi.find(".positive-img").hide();
                dinosaurLi.find(".reverse-img").show();
                // 触发按钮置灰
                dinosaurLi.find(".triggerList").addClass("gray");
                $(this).dequeue();
            }).delay(1000).queue(function() {
                // 播放教学音频
                if (isAudioShow == 1 && $(this).attr('data-timing') == 1) audioFn($(".teacherAudio")).soundAudio();
                $(this).dequeue();
            })
        }

        SDK.setEventLock();

    });

    /**
     *
     * 点击播放音频
     */
    let soundClick = true;
    // isPlaySound = true;
    $(".dinosaur-list ul").on("click touchstart", "li .sound", function(e) {
        if (e.type == 'touchstart') {
            e.preventDefault()
        }
        e.stopPropagation();

        if (soundClick) {
            soundClick = false;
            if (!isSync) {
                $(this).trigger('syncSoundClick');
                return;
            }
            if (window.frameElement.getAttribute('user_type') == 'tea') {
                SDK.bindSyncEvt({
                    sendUser: '',
                    receiveUser: '',
                    index: $(e.currentTarget).data('syncactions'),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'syncSoundClick',
                    funcType: 'audio'
                });
            } else {
                $(this).trigger('syncSoundClick');
                return;
            }
        }
    });

    $(".dinosaur-list ul").on("syncSoundClick", "li .sound", function(e, message) {
        let gif = $(this).find('.gif');
        let png = $(this).find('.png');
        let audio = $(this).find('audio')[0];
        if (isPlaySound) {
            // audio.play();
            SDK.playRudio({
                index: audio,
                syncName: $(this).find('audio').attr("data-syncaudio")
            })
            gif.show();
            png.hide();
        } else {
            // audio.pause();
            SDK.pauseRudio({
                index: audio,
                syncName: $(this).find('audio').attr("data-syncaudio")
            })
            gif.hide();
            png.show();
        }
        audio.onended = function() {
            gif.hide();
            png.show();
            isPlaySound = true;
        }.bind(this);

        isPlaySound = !isPlaySound;

        SDK.setEventLock();
        soundClick = true;
    });

    /**
     *音频方法
     */
    function audioFn(audio) {
        return {
            "soundAudio": function() {
                // 开始播放
                if (isPlaySound) {
                    console.log("开始播放")
                    isPlaySound = false;
                    SDK.playRudio({
                        index: audio[0],
                        syncName: audio.attr("data-syncaudio")
                    })
                    // 播放结束
                    audio[0].onended = function() {
                        console.log("播放结束")
                        isPlaySound = true;
                    }.bind(this);
                    SDK.setEventLock();
                }
            }
        }
    }

})

/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
    var def = $.Deferred(),
        len = list.length;
    $(list).each(function(i, e) {
        var img = new Image();
        img.src = e.image;
        if (img.complete) {
            imgs[i] = img;
            len--;
            if (len == 0) {
                def.resolve();
            }
        } else {
            img.onload = (function(j) {
                return function() {
                    imgs[j] = img
                    len--;
                    if (len == 0) {
                        def.resolve();
                    }
                };
            })(i);
            img.onerror = function() {
                len--;
            };
        }
    });
    return def.promise();
};
