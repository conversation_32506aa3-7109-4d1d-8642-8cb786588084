<!DOCTYPE html>
<html lang="en">
<head>
    <% var title="PRS0001_呈现翻转"; %>
    <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">

    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>

    <!-- <div class="example-audio sound" data-syncactions="audio-1">
      <img src="./image/sound.gif" alt="" class="gif small">
      <img src="./image/sound.png" alt="" class="png small">
      <audio src="" class="soundAudio" data-syncaudio="soundAudio"></audio>
    </div> -->

    <!-- 题干音频 -->
    <audio src="" class="soundAudio" data-syncaudio="soundAudio"></audio>
    <!-- 教学音频 -->
    <audio src="" class="teacherAudio" data-syncaudio="teacherAudio"></audio>

    <div class="content-main">
      <!-- 动画组合 -->
      <div class="dinosaur-list">
        <ul></ul>
      </div>
    </div>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
