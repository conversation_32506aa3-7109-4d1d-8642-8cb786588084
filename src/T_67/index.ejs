<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="LCH0003_听音找图"; %>
  <%include ./src/common/template/index_head %>
  <!-- 提示模式 -->
  <% include ./src/common/template/hint/index.ejs %>
</head>

<body>
  <div class="container" id="container" data-syncresult="1">
    <section class="commom">
      <div class="desc">123132</div>
    </section>

    <section class="main">
      <!-- 黑色遮罩 -->
      <div class="blackShadow"></div>
      <!-- 正确错误音效 -->
      <audio src="./audio/right.mp3" class="rightAudio" data-syncaudio="rightAudio"></audio>
      <audio src="./audio/wrong.mp3" class="wrongAudio" data-syncaudio="wrongAudio"></audio>
      <!-- 正确特效 -->
      <div class="rightBox"></div>
      <!-- 选项 -->
      <ul class="optionUl"></ul>
      <!-- 像素格 -->
      <div class="boxList">
        <ul class="boxUl"></ul>
      </div>
      <!-- 标志物 -->
      <img class="landMarkImg hide" src="" alt="">
      <!-- 喇叭 -->
      <!-- 左下角区域 -->
      <div class="leftLowerBox">
        <!-- 进度条 -->
        <div class="proprogressBarBox hide">
          <div class="proprogressBar">
            <div class="circle c1 hide"></div>
            <div class="circle c2 hide"></div>
            <div class="circle c3 hide"></div>
            <div class="circle c4 hide"></div>
            <div class="circle c5 hide"></div>
            <div class="circle c6 hide"></div>
          </div>
          <div class="yellowBar">
            <div class="yellow y1 "></div>
            <div class="yellow y2 "></div>
            <div class="yellow y3 "></div>
            <div class="yellow y4 "></div>
            <div class="yellow y5 "></div>
            <div class="yellow y6 "></div>
          </div>
        </div>
        <!-- 放大镜 -->
        <div class="magnifier hide"></div>
        <!-- 喇叭 -->
        <div class="horn"></div>
        <audio class="frequencyAudio" data-syncaudio="frequencyAudio" ></audio>
      </div>
      <!-- 功能遮罩层 -->
      <div class="funcMask">
        <!-- 预习模式操作区 -->
        <div class="startBox">
          <div class="demoTextBox">
            <!-- <img src="https://cdn.51talk.com/apollo/images/4dfeec15c2cd5d502bf53095ec38b9ea.png" class="startMsg" /> -->
            <img src="./image/4dfeec15c2cd5d502bf53095ec38b9ea.png" class="startMsg" />
          </div>
          <div class="demoBtnBox">
            <!-- <img src="https://cdn.51talk.com/apollo/images/57f60d04ee91d3178a98cc81d7b08001.png"
              class="demo-btnStu" /><img src="https://cdn.51talk.com/apollo/images/0250983386ad8a2c2226cac7b83be49e.png"
              class="startBtn"> -->
            <img src="./image/57f60d04ee91d3178a98cc81d7b08001.png"
              class="demo-btnStu" /><img src="./image/0250983386ad8a2c2226cac7b83be49e.png"
              class="startBtn">
          </div>

        </div>
        <!-- 倒计时 -->
        <div class="timeChangeBox hide">
          <div class="timeBg">
            <div class="numberList"></div>
            <audio src="./audio/timeLow.mp3" class="timeLowAudio_1" data-syncaudio="timeLowAudio_1"></audio>
            <audio src="./audio/timeLow.mp3" class="timeLowAudio_2" data-syncaudio="timeLowAudio_2"></audio>
            <audio src="./audio/timeLow.mp3" class="timeLowAudio_3" data-syncaudio="timeLowAudio_3"></audio>
            <audio src="./audio/timehigh.mp3" class="timeLowAudio_4" data-syncaudio="timeLowAudio_4"></audio>
          </div>
        </div>
      </div>
      <!-- 终局页 -->
      <div class="tea-stu-not-in"></div>
      <div class="overBtn" data-syncactions="overBtn"></div>
      <div class="outBtn" data-syncactions="outBtn"></div>
      <div class="runing" data-syncactions="runing"></div>
    </section>
    <div class="doneTip hide">
      <p>It's S's turn, you can't play now.</p>
    </div>
    <script type="text/javascript">
      document.documentElement.addEventListener('touchstart', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, false);
      // 禁用手指双击缩放：

      var lastTouchEnd = 0;
      document.documentElement.addEventListener('touchend', function (event) {
        var now = Date.now();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
    </script>
  </div>
  <!-- 图片预加载 -->
  <div id="preload-01"></div>
  <div id="preload-02"></div>
  <div id="preload-03"></div>
  <% include ./src/common/template/ribbonWin/index.ejs %>
  <%include ./src/common/template/index_bottom %>
</body>

</html>
