@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import '../../common/template/ribbonWin/style.scss';
@import '../../common/template/hint/style.scss';

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
    position: absolute;
    left: $l;
    top: $t;
    width: $w;
    height: $h;
}

body {
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: fixed;
}

.commom {
    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 2.2rem;
    position: absolute;
    right: 0px;

    .desc {
        top: 0.6rem;
    }

    .title-first {
        width: 100%;
        height: 0.8rem;
        padding: 0 1.4rem;
        box-sizing: border-box;
        text-align: center;
        margin: 0.45rem auto 0.2rem;
        font-size: 0.8rem;
        font-weight: bold;
        color: #333;
    }
}

.container {
    background-size: auto 100%;
    position: relative;

    .main {
        width: 100%;
        height: 100%;
        position: relative;

        //  黑色遮罩
        .blackShadow {
            display: none;
            position: absolute;
            right: 0;
            top: 0;
            width: 1.95rem;
            height: 0.67rem;
            background: rgb(0, 0, 0);
            opacity: 0.6;
            border-bottom-left-radius: 0.22rem;
            color: #fff;
            line-height: 0.67rem;
            font-size: 0.43rem;
            width: 100%;
            height: 100%;

            img {
                width: 0.51rem;
                height: 0.53rem;
                margin-left: 0.22rem;
            }
        }

        //标志物
        .landMarkImg {
            position: absolute;
            background-position-x: 0rem;
            background-repeat: no-repeat;
            z-index: 21;
        }

        //选项
        .optionUl {
            position: relative;
            width: 100%;
            height: 100%;

            //图片选项
            .optionLi {
                position: absolute;
                cursor: pointer;
                background-size: 100% 100%;
                background-repeat: no-repeat;
            }

            //提示模式放大
            .optionScale {
                animation: optionScale 1s infinite
            }

            //图片放大缩小
            @keyframes optionScale {
                0% {
                    transform: scale(1.0)
                }

                25% {
                    transform: scale(1.15)
                }

                50% {
                    transform: scale(1.0)
                }

                75% {
                    transform: scale(1.0)
                }

                100% {
                    transform: scale(1.0)
                }
            }
        }

        // 左下角区域
        .leftLowerBox {
            position: absolute;
            left: 1.3rem;
            bottom: 0.71rem;
            height: 1.2rem;
            width: 5.5rem;
            z-index: 31;

            // 进度条
            .proprogressBarBox {
                position: relative;
                left: 0.4rem;
                bottom: 0.42rem;
                height: 1rem;
                padding-left: 0.29rem;
                margin: 0 auto;
                width: 4.65rem;
                height: 1rem;
                background: url("../image/progress.png");
                background-repeat: repeat-x;
                background-size: 100% 100%;
                float: left;

                // 进度条末端半圆
                &:after {
                    position: absolute;
                    content: '';
                    background: url("../image/progressRight.png") no-repeat;
                    background-size: 100% 100%;
                    width: 0.5rem;
                    height: 1rem;
                    right: -0.5rem;
                    top: 0;
                }

                //绿色线
                .proprogressBar {
                    height: 0.13rem;
                    width: 4rem;
                    background-color: #177C6C;
                    position: absolute;
                    top: 0.45rem;
                    left: 0.9rem;

                    .circle {
                        width: 0.35rem;
                        height: 0.35rem;
                        background-color: #177C6C;
                        border-radius: 50%;
                        position: absolute;
                        top: -0.12rem;
                    }

                    .c2 {
                        left: 0.76rem; //0.35 + 0.41
                    }

                    .c3 {
                        left: 1.52rem; //0.76*2
                    }

                    .c4 {
                        left: 2.28rem; //0.76*3
                    }

                    .c5 {
                        left: 3.04rem;
                    }

                    .c6 {
                        left: 3.8rem;
                    }

                    .yellow {
                        width: 0.45rem;
                        height: 0.45rem;
                        background-image: url("../image/dot.png");
                        background-size: 0.45rem 0.45rem;
                        border-radius: 50%;
                        position: absolute;
                        top: -0.16rem;
                        opacity: 0;
                    }

                    .y1 {
                        left: -0.06rem;
                    }

                    .y2 {
                        left: 0.7rem;
                    }

                    .y3 {
                        left: 1.46rem;
                    }

                    .y4 {
                        left: 2.22rem;
                    }

                    .y5 {
                        left: 2.98rem;
                    }

                    .y6 {
                        left: 3.74rem;
                    }
                }

                //黄条
                .yellowBar {
                    height: 0.13rem;
                    max-width: 4rem;
                    background: url("../image/yellowBar.png");
                    background-repeat: repeat-x;
                    background-size: 100% 100%;
                    position: absolute;
                    top: 0.45rem;
                    left: 1rem;

                    .yellow {
                        width: 0.45rem;
                        height: 0.45rem;
                        background-image: url("../image/dot.png");
                        background-size: 0.45rem 0.45rem;
                        border-radius: 50%;
                        position: absolute;
                        top: -0.16rem;
                        opacity: 0;
                    }

                    .y1 {
                        left: -0.16rem;
                    }

                    .y2 {
                        left: 0.6rem;
                    }

                    .y3 {
                        left: 1.365rem;
                    }

                    .y4 {
                        left: 2.12rem;
                    }

                    .y5 {
                        left: 2.88rem;
                    }

                    .y6 {
                        left: 3.65rem;
                    }
                }
            }

            // 放大镜
            .magnifier {
                z-index: 31;
                position: absolute;
                left: -0.3rem;
                bottom: 0rem;
                width: 1.78rem;
                height: 2.18rem;
                background-image: url("../image/magnifier.png");
                background-size: 1.78rem 2.18rem;
            }

            // 喇叭
            .horn {
                z-index: 31;
                position: absolute;
                left: -0.1rem;
                // top: -0.1rem;
                bottom: 2.21rem;
                width: 2.32rem;
                height: 1.56rem;
                background-image: url("../image/horn1.png");
                background-size: 18.69rem 1.56rem;
            }
        }


    }


    //记分台
    .intSatge {
        position: absolute;
        bottom: 2.4rem;
        left: 1.7rem;
        width: 1.7rem;
        height: 0.6rem;
        border-radius: 0.3rem;



        .wrong {
            background-position-x: -10.75rem;
        }

        .score {
            width: 100%;
            height: 100%;
            border-radius: 0.3rem;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 0.46rem;
            color: #f9bc2e;
            box-sizing: border-box;
            padding-left: 0.7rem;
            line-height: 0.6rem;
            font-weight: 400;
            color: rgba(255, 100, 14, 1);
        }
    }



    //喇叭动效
    .hornAnimation {
        animation: hornPlay 1s steps(3) infinite;
        -webkit-animation: hornPlay 1s steps(3) infinite;
    }

    @keyframes hornPlay {
        0% {
            background-position: 0 0;
        }

        100% {
            background-position: 18.69rem 0;
        }
    }

    // 功能遮罩层
    .funcMask,
    .tea-stu-not-in {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 50;
        display: none;

        .startBox {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 7.02rem;
            width: 10.5rem;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 0.5rem;

            .demoTextBox {
                width: 9.26rem;
                height: 3.9rem;
                position: absolute;
                left: 50%;
                top: 0.64rem;
                transform: translate(-50%);
                border-radius: 0.3rem;
                background: rgba(0, 0, 0, 0.2);
                text-align: center;

                .startMsg {
                    width: 6.75rem;
                    height: 2.88rem;
                    margin-top: 0.4rem;
                }
            }

            .demoBtnBox {
                width: 5.72rem;
                height: 1.14rem;
                transform: translate(-50%);
                // background: rgba(0, 0, 0, 0.2);
                text-align: center;
                position: absolute;
                left: 50%;
                bottom: 0.75rem;

                .demo-btnStu {
                    width: 2.14rem;
                    height: auto;
                    cursor: pointer;
                    display: inline-block;
                }

                .startBtn {
                    width: 2.03rem;
                    height: auto;
                    cursor: pointer;
                    display: inline-block;
                    margin-right: 0;
                    margin-left: 1.55rem;
                }
            }
        }

        .timeChangeBox {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 4.8rem;
            width: 7.2rem;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 0.5rem;

            .timeBg {
                width: 3.79rem;
                height: 3.84rem;
                position: absolute;
                top: 1rem;
                background: url(../image/timeBg.png) no-repeat;
                background-size: 100% 100%;
                left: 50%;
                margin-left: -1.9rem;
                top: 50%;
                margin-top: -1.92rem;

                .numberList {
                    width: 1.5rem;
                    height: 1.5rem;
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    top: 0;
                    margin: auto;
                    background: url(../image/number1.png) no-repeat;
                    background-size: 6rem 100%;
                    background-position-x: 0.1rem;
                }
            }
        }
    }

    //终局页
    .endGame {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 20;
        background: rgba(0, 0, 0, 0.5);
        display: none;


        .hiddenContain {
            overflow: hidden;
            width: 85%;
            position: absolute;
            top: 50%;
            left: 50%;
            text-align: center;
            transform: translate(-50%, -50%);
            height: 7.1rem;
            margin-left: 0.42rem;

            ul {
                height: 7.1rem;
                overflow-x: auto;
                overflow-y: hidden;
                white-space: nowrap;
                display: flex;
                flex-flow: row nowrap;
                justify-content: center;
                align-items: center;

                li {
                    width: 4.05rem;
                    height: 5.8rem;
                    background: rgba(255, 255, 255, 1);
                    border-radius: 0.76rem;
                    display: inline-block;
                    margin-right: 0.9rem;
                    // background-size: 90%;
                    background-origin: content;
                    background-repeat: no-repeat;
                    background-position: center center;
                    opacity: 0;
                    position: relative;
                }
            }

            // .scroll-2{
            //   justify-content: left!important;
            // }

            // 音频播放
            .example-audio {
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                width: 1.45rem;
                height: 1.34rem;
                background: url(../image/sound_bg.png) no-repeat center;
                background-size: cover;
                cursor: pointer;
                position: absolute;
                bottom: .13rem;
                left: .17rem;

                .small {
                    width: 0.9rem;
                    margin-top: 1px;
                }

                img {
                    width: 1.45rem;
                }

                .gif {
                    display: none;
                }
            }
        }

        .scrollBtn {
            position: absolute;
            bottom: 0.5rem;
            left: 50%;
            transform: translate(-50%);

            span {
                display: inline-block;
                width: 1.36rem;
                height: 1.36rem;
                cursor: pointer;
                left: 0.92rem;
            }

            .leftScroll {
                background: url(../image/left.png) no-repeat;
                background-size: contain;
                opacity: 0.5
            }

            .rightScroll {
                background: url(../image/right.png) no-repeat;
                background-size: contain;
            }
        }
    }
}

.big {
    transform: scale(1.05);
}




//终局特效
.perfectBox {
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 7.34rem;
    height: 7.33rem;
    transform: translateX(-50%) translateY(-50%);
    // background-color: red;

    .light {
        position: absolute;
        z-index: 100;
        width: 6.87rem;
        height: 6.87rem;
        animation: rotation 6s linear infinite;
    }

    @keyframes rotation {
        from {
            -webkit-transform: rotate(0deg);
        }

        to {
            -webkit-transform: rotate(360deg);
        }
    }

    .perfect {
        position: absolute;
        top: 1.2rem;
        z-index: 101;
        width: 7.34rem;
        height: 4.53rem;
    }

    .numBox {
        position: absolute;
        bottom: 0;
        width: 3.7rem;
        height: 1.38rem;
        background-color: #fff;
        border-radius: .68rem;
        z-index: 102;
        transform: translateX(-50%);
        left: 50%;

        img {
            width: 1.11rem;
            height: 1.17rem;
            position: absolute;
            left: 0.46rem;
            top: 50%;
            transform: translateY(-50%);
        }

        label {
            display: inline-block;
            width: 0.03rem;
            height: 0.7rem;
            background: rgba(147, 147, 147, 1);
            position: absolute;
            left: 1.93rem;
            top: 50%;
            transform: translateY(-50%);
        }

        .score {
            position: absolute;
            left: 2.27rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 108, 15, 1);
            font-size: 0.91rem;
        }
    }
}


//正确特效
.rightBox {

    .rightAniBox {
        overflow: hidden;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 8.85rem;
        height: 8.85rem;
        transform: translateX(-50%) translateY(-50%);
        z-index: 40;

        // 大光圈
        .rightLight {
            position: absolute;
            width: 8.85rem;
            height: 8.85rem;
            animation: rotation 6s linear infinite;
        }

        @keyframes rotation {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(360deg);
            }
        }

        //内圈元素
        .optionInlight {
            position: absolute;
            max-width: 7.34rem;
            max-height: 4.53rem;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    // 亮点
    .smallLight {
        z-index: 40;
        position: absolute;
        width: 2rem;
        height: 2rem;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}


.doneTip {
    width: 7.91rem;
    height: 1rem;
    border-radius: 0.2rem 0.2rem 0 0;
    position: absolute;
    margin-left: -3.5rem;
    left: 50%;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    font-size: 0.3rem;
    z-index: 21;

    p {
        height: 100%;
        width: 100%;
        line-height: 1rem;
        text-align: center;
    }

    .btn {
        position: absolute;
        top: 0.2rem;
        height: 0.23rem;
        padding: 0.17rem 0.26rem;
        color: #fff;
        text-align: center;

        line-height: 0.23rem;
        border-radius: 0.3rem;
        cursor: pointer;
    }

    .demo-btn {
        background: #f1a91e;
        left: 5.6rem;
    }
}

.boxList {
    position: absolute;
    left: 1.08rem;
    top: 0.3rem;
    width: 17rem;
    height: 9.8rem;
    z-index: -1;

    .boxUl {
        position: absolute;
        left: 0.2rem;
        top: 0.7rem;
        width: 16rem;
        height: 8.4rem;
        display: flex;
        flex-wrap: wrap;

        li {
            // background: red;
            margin-left: 0.01rem;
            width: 0.39rem;
            height: 0.39rem;
        }
    }
}

//小手点击动画
// .hand {
//     position: absolute;
//     left: 1.4rem;
//     top: 1.3rem;
//     margin-left: -0.35rem;
//     background: url("../image/hands.png");
//     background-size: 7.2rem 1.8rem;
//     cursor: pointer;
//     opacity: 0;
//     z-index: 99;
//     width: 0px;
//     height: 0px;
// }

// .handAnimation {
//     opacity: 1;
//     width: 1.8rem;
//     height: 1.8rem;
//     left: 5.5rem;
//     top: 6rem;
//     animation-name: handLclick, handHide;
//     animation-duration: 0.8s, 1s;
//     animation-timing-function: steps(4, end), linear;
//     animation-delay: 0s, 2s;
//     animation-iteration-count: 1, 1;
//     animation-fill-mode: forwards, forwards;
//     animation-direction: normal, normal;
// }

// @keyframes handLclick {
//     0% {
//         background-position: 0 0;
//     }

//     100% {
//         background-position: 133% 0;
//     }
// }

// @keyframes handHide {
//     0% {
//         opacity: 0;
//         width: 0px;
//     }

//     100% {
//         opacity: 0;
//         width: 0px;
//     }
// }



//选项上下抖动
.optionUDAni {
    animation: optionUD linear 1s 2s infinite alternate;
    -webkit-animation: optionUD linear 1s 2s infinite alternate;
}

@keyframes optionUD {
    0% {
        transform: translateY(0px);
    }

    100% {
        transform: translateY(10px);
    }
}

//其他选项
.optionOther {
    animation: imTypeOther 0.3s 1 forwards;
}


//错误反馈动画
.shake {
    animation: shakeUp 0.4s both ease-in;
}

@keyframes shakeUp {
    0% {
        transform: translateX(10px);
    }

    20% {
        transform: translateX(-10px);
    }

    40% {
        transform: translateX(10px);
    }

    60% {
        transform: translateX(-10px);
    }

    80% {
        transform: translateX(10px);
    }

    100% {
        transform: translateX(0px);
    }
}

//终局卡片底部切换特效(卡片大于3时)
@keyframes card1 {
    0% {
        background-position: 0 0;
        transform: scale(1, 1);
    }

    100% {
        // opacity: 0;
        // width: 0;
        background-position: 150% 0;
        transform: scale(0, 1);
    }
}

* {
    touch-action: pan-y;
}



#preload-01 {
    background: url(../image/light.png) no-repeat -9999px -9999px;
}

#preload-02 {
    background: url(../image/prefect.png) no-repeat -9999px -9999px;
}

#preload-03 {
    background: url(../image/light2.png) no-repeat -9999px -9999px;
}
