"use strict"
import '../../common/js/common_1v1.js'
import '../../common/js/drag.js'
import {
  resultWin,
  resultHide
} from '../../common/template/ribbonWin/index.js';

import {
  templateScale,
  handClick,
  handDrag,
  handHide,
} from '../../common/template/hint/index.js';

const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  //是否在控制器显示功能按钮
  window.h5Template = {
    hasDemo: '0', //0 默认值，无提示功能  1 有提示功能
    hasPractice: '3' //0 无授权功能  1  默认值，普通授权模式  2 start授权模式  3 新授权模式
}
  let classStatus = 1; //教室状态
  let userType = window.frameElement && window.frameElement.getAttribute('user_type'); //用户身份学生还是老师
  var h5SyncActions = parent.window.h5SyncActions;
  let staticData = configData.source;
  let isFirst = true;
  // 是否有音频
  let isVideoVaule = {
      isVideo: staticData.isVideo, //是否有题干音频（无：1  有：2）
      videoUrl: staticData.videoUrl, //音频文件
      videoX: staticData.videoX, //音频横向位置
      videoY: staticData.videoY, //音频纵向位置
      videoDuration: staticData.videoDuration, //题干声音长度
  }
  let openWay = staticData.openWay; //是否是默认起点位置（是：1  否：2）
  let isEnding = staticData.isEnding; //是否出现终局页（是：1 否：2）
  // 结局页
  let endList = {
      endWidth: staticData.endWidth,
      endHeight: staticData.endHeight,
      endFigure: staticData.endFigure,
      endVideo: staticData.endVideo,
      endDuration: staticData.endDuration,
  }
  //数据列表
  let optionsList = staticData.options;
  let optionsNewArr = []; //重组后的数组
  let isDragFinish = true; //是否拖拽结束
  let isDrag = false; //拖拽还是点击
  let childOptionsArr = []; //选中后的数组集合
  let timeInit = '';
  let isClick = true; //是否可点击

  let time = 3000, //消失终局特效的时间
      isDemo = false, //是否是demo模式
      isAnyOneOut = false,//是否断线  true 已断线
      isHint = false, //是否是hint模式 true:hint模式
      timeInt = null; // 定时器

  // 终局雪碧图
  let spriteWidth = '1920',
      spriteHeight = '640';

  /**
   *
   * 新授权模式  仅老师有权限 s/t
  */
  teacherStAction()
  //S/T 老师有权限
  function teacherStAction () {
    if (!isSync) {
      return;
    }
    // iframe是否是预加载
    var frame_id = $(window.frameElement).attr('id');
    if(SDK.getClassConf().h5Course.classStatus == 1 && frame_id != 'h5_course_cache_frame' ) {
      moduleAuthorizationFn('teaRoot', '13')
    }
    SDK.setEventLock();
  }

  /**
     * 授权模式方法
     * @param {*} type: 方法
     * @param {*} value：权限
     * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
    */
  function moduleAuthorizationFn(type, value) {
    if (!isSync) {
      return;
    }
    SDK.bindSyncCtrl({
      'type': type,
      'tplAuthorization':"tpl",
      'data': {
          CID: SDK.getClassConf().course.id + '', //教室id 字符串
          operate: '1',
          data: [{
              key: 'classStatus',
              value: value,
              ownerUID: SDK.getClassConf().user.id
          }]
      }
    });
  }

  //判断用户角色，显示不同功能
  isShowBtn();
  //判断用户角色，显示不同功能(如老师的底部文字提示，学生的预习模式等)
  function isShowBtn() {
    if (isSync) { //同步模式
      console.log('issync 同步模式---')
      $('.funcMask').show(); //预习模式和倒计时
        // classStatus = SDK.getClassConf().h5Course.classStatus;
        // if (classStatus == 0 && userType == 'stu') {
        //     $('.funcMask').show(); //预习模式和倒计时
        // }
    } else { //非同步模式
      console.log('issync 非同步模式------')
        var hrefParam = parseURL('http://www.example.com');
        if (top.frames[0] && top.frames[0].frameElement) {
            hrefParam = parseURL(top.frames[0].frameElement.src)
        }
        var role_num = hrefParam.params['role']

        function parseURL(url) {
            var a = document.createElement('a')
            a.href = url
            return {
                source: url,
                protocol: a.protocol.replace(':', ''),
                host: a.hostname,
                port: a.port,
                query: a.search,
                params: (function() {
                    var ret = {},
                        seg = a.search.replace(/^\?/, '').split('&'),
                        len = seg.length,
                        i = 0,
                        s
                    for (; i < len; i++) {
                        if (!seg[i]) {
                            continue;
                        }
                        s = seg[i].split('=')
                        ret[s[0]] = s[1]
                    }
                    return ret
                })(),
                file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
                hash: a.hash.replace('#', ''),
                path: a.pathname.replace(/^([^\/])/, '/$1'),
                relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
                segments: a.pathname.replace(/^\//, '').split('/')
            }
        }
        // $('.funcMask').show();
        if (role_num == '1' || role_num == '2' || role_num == undefined) {
            $('.funcMask').show();
        }
    }
  }

  /**
   *  开始游戏
   *  2种途径可开启游戏，1.老师通过控制器点击开始 2.非课中模式，如预习，学生点击开启
   */
  let startBtnStatus = true; //开始按钮是否可点击(预习模式)
  $('.startBtn').on('click touchstart', function(e) {
    if (e.type == 'touchstart') {
      e.preventDefault()
    }
    e.stopPropagation(); //阻止事件进一步传播

    if (!isSync) {
      $(this).trigger('syncStartClick');
      // 恢复数据
      defaultListAction();
      threeTwoOne(); //321倒计时
      return;
    }
    // if (window.frameElement.getAttribute('user_type') == 'tea') {
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'syncStartClick',
        otherInfor: '',
        recoveryMode: '1'
      });
    // }
  });
  $('.startBtn').on('click syncStartClick', function(e) {
    if (isSync) {
      $('.img-list').addClass('img-list-cursor');
      if (startBtnStatus) {
        startBtnStatus = false;
        // 恢复数据
        defaultListAction();
        threeTwoOne(); //321倒计时
      }
    }
    SDK.setEventLock();
  });

  /**
   *  demo模式
   *  2种途径可开启demo模式，1.老师通过控制器点击提示 2.非课中模式，如预习，学生点击开启
   */

  $('.demo-btnStu').on('click touchstart', function(e) {
      if (e.type == 'touchstart') {
          e.preventDefault()
      }
      e.stopPropagation(); //阻止事件进一步传播

      if (!isSync) {
        demoFun(true); //demo模式
        $(this).trigger('syncDemoClick');
        return;
      }
      // demoFun(true); //demo模式
      // if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'syncDemoClick',
          otherInfor: '',
          recoveryMode: '1'
        });
      // }
  });
  $('.demo-btnStu').on('click syncDemoClick', function(e) {
      if (isSync) {
        demoFun(true); //demo模式
      }
      SDK.setEventLock();
  });


  // 3 2 1倒计时
  function threeTwoOne() {
      let q = 1;
      $('.startBox').hide().siblings('.timeChangeBox').show().find('.numberList');
      SDK.playRudio({
          index: $('.timeLowAudio_' + q).get(0),
          syncName: $('.timeLowAudio_' + q).attr('data-syncaudio')
      })
      let audioPlay = setInterval(function() {
          q++;
          if (q > 4) {
              clearInterval(audioPlay);
              //3210倒计时结束
              SDK.setEventLock();
              $('.funcMask').hide();
              $('.timeChangeBox').hide();
              //学生有权限 s/t 模式
              moduleAuthorizationFn('teaRoot', '14')
              SDK.setEventLock();
              // optionsInit(timeIndex); //本轮游戏选项初始化
              // addEvent(); //泡泡选项添加事件
              // sorceTimeInit(); //进度条初始化
              timeStartFn(); //计时器倒计时
              // gameProcessBidSync(); //上报游戏数据
          } else {
              // 播放倒计时声音
              SDK.playRudio({
                  index: $('.timeLowAudio_' + q).get(0),
                  syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
              })
              $('.numberList').css({
                  'background-position-x': -(1.5 * (q - 1)) + 'rem'
              })
          }
      }, 1000)
  }

  //计时器倒计时
  function timeStartFn() {
    window.localStorage.setItem('countDownTime', time); //将本轮倒计时存储在缓存中
    if (timeInt) {
        clearInterval(timeInt);
        timeInt = null;
    }
    timeInt = setInterval(function() {
        if (isAnyOneOut) { //如断线则停止游戏
            clearInterval(timeInt);
            soundControl().pause(); //停止声音
        }
        time--; //本轮游戏时间
        // isEndGameFun(time); //本轮游戏是否结束
        window.localStorage.setItem('countDownTime', time); //每秒更新一次时间
        // moveItemW = moveItemW - everLen; //每秒更新一次进度条长度    新进度条长度 = 原进度条长度 - 每秒移动距离
        // $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);
    }, 1000);

    // 重置数据
    // resetFn()
  };

  /**
   * demo hint 模式
   * showfuncMask 是否是学生
   */
  // demoFun()
  function demoFun(showfuncMask) {
    // 重置数据
    // resetFn()
    isDemo = true;
    $('.hand').show();

   // 添加模版缩放模式
   templateScale({
     show: true,
     className: $('.container')
   })
   if (showfuncMask) { //是否显示学生用的预习模式弹窗
     $('.funcMask').hide();
   }
   // 是否为hint模式
   isHint = true;

    // 执行队列触发事件
    let optionsEqLi = $('.options-list ul li').eq(0);
    let areaListLi = $('.area-list ul').eq(0).find('li').eq(0);
    // 恢复数据
    // optionsEqLi.css({
    //   top: (Number(areaListLi.attr('data-start-areay')) + Number(optionsEqLi.attr('data-areax')))/100 + 'rem',
    //   left: (Number(areaListLi.attr('data-start-areax')) + Number(optionsEqLi.attr('data-areay')))/100 + 'rem',
    // })
    optionsEqLi.css({
      left: (Number(optionsEqLi.attr('data-img-startx')))/100 + 'rem',
      top: (Number(optionsEqLi.attr('data-img-starty')))/100 + 'rem',
    })
    // 恢复手样样式
    $('.hand').css({
      top: 100 + '%',
      left: 100 + '%',
    })


    // 点击第一个模块的位置
    hintMoveFn()
    optionsEqLi.delay(1300).queue(function() {
      optionsEqLi.css({
        top: areaListLi.attr('data-end-areay')/100 + 'rem',
        left: areaListLi.attr('data-end-areax')/100 + 'rem'
      })
      $(this).dequeue();
    }).delay(1000).queue(function() { //结束后恢复数据
      $('.hand').hide();

      if(showfuncMask || (classStatus == 0 && userType == 'stu') || !userType) {//是否显示学生用的预习模式弹窗
        $('.funcMask').show();
      }
      //退出demo模式
      isDemo = true;
      // 开场方式是否从终点飞到起点
      // openWayAction();

      $(this).dequeue();
    })

 }

 /**
  *
  * @param {*} 恢复列表默认数据
  */
 function defaultListAction() {
  let optionsEqLi = $('.options-list ul li').eq(0),
      areaListLi = $('.area-list ul').eq(0).find('li').eq(0);
    // optionsEqLi.css({
    //   top: (Number(areaListLi.attr('data-start-areay')) + Number(optionsEqLi.attr('data-areax')))/100 + 'rem',
    //   left: (Number(areaListLi.attr('data-start-areax')) + Number(optionsEqLi.attr('data-areay')))/100 + 'rem',
    // })

    optionsEqLi.css({
      left: (Number(optionsEqLi.attr('data-img-startx')))/100 + 'rem',
      top: (Number(optionsEqLi.attr('data-img-starty')))/100 + 'rem',
    })
    templateScale({
      className: $('.container'),
      show: false,
    })
 }

 /**
   * hint移动位置
  */
  function hintMoveFn() {
    let postionTop = $('.options-list ul li').eq(0).offset().top,
        postionLeft = $('.options-list ul li').eq(0).offset().left,
        endTop = $('.default-end-box').offset().top,
        endLeft = $('.default-end-box').offset().left;
        // endTop = $('.success-list ul').eq(0).offset().top,
        // endLeft = $('.success-list ul').eq(0).offset().left;
        handDrag(postionTop-30, postionLeft-30, endTop-20, endLeft-20);
  }

  //是否有音频
  if(isVideoVaule.isVideo == 2) {
    $('.example-audio audio').attr('src', isVideoVaule.videoUrl);
    $('.example-audio ').css({
      left: isVideoVaule.videoX/100 + 'rem',
      top: isVideoVaule.videoY/100 + 'rem',
    })
  } else {
    $('.example-audio').hide()
  }

  /**
   * 重组数组
   */
  optionsArrFn()
  function optionsArrFn() {
    let teamArr = []
    let listData = optionsList
    // 为题干音频赋值
    $('.topicAudio').attr('src', isVideoVaule.videoUrl)
    for (let i = 0; i < listData.length; i++) {
      if (!teamArr.includes(listData[i]['areaX']) || !teamArr.includes(listData[i]['areaY'])) {
        optionsNewArr.push({
          isRightAnswers: listData[i]['isRightAnswers'],
          areaX: listData[i]['areaX'],
          areaY: listData[i]['areaY'],
          areaWidth: listData[i]['areaWidth'],
          areaHeight: listData[i]['areaHeight'],
          data: [listData[i]]
        })
        teamArr.push(listData[i]['areaX'])
      } else {
        for (let j = 0; j < optionsNewArr.length; j++) {
          if (optionsNewArr[j]['areaX'] == listData[i]['areaX'] && optionsNewArr[j]['areaY'] == listData[i]['areaY']) {
            optionsNewArr[j].data.push(listData[i])
            break
          }
        }
      }
    }
    if(optionsNewArr[0].isRightAnswers == 2) optionsNewArr = optionsNewArr.reverse()
    areaListFn(optionsNewArr)
  }

  /**
   * 正确区域列表展示
  */
  function areaListFn(optionsNewArr) {
    let areaStr = '', //终点区域
        successStr = '', //正确区域
        areaListStr = '', //正确区域列表渲染
        optionStr = '', //选项区域
        demoEndStr = '',//demo默认
        liIndex = 0;
    optionsNewArr.forEach(function(msg, key){
      // if(msg.isRightAnswers == 1) {
        // 正确区域
        successStr = $(`<ul data-areaX=${msg.areaX} data-areaY=${msg.areaY} data-key=${key} >`
                     +'</ul>')
        $('.success-list').append(successStr);

        // 终点区域位置渲染
        areaStr = $(`<ul data-areaX=${msg.areaX} data-areaY=${msg.areaY} data-key=${key} >`
                    +'</ul>')
        $('.area-list').append(areaStr);
        //正确区域位置
        successStr.css({
          width: msg.areaWidth / 100 + 'rem',
          height: msg.areaHeight / 100 + 'rem',
          top: msg.areaY/ 100 + 'rem',
          left: msg.areaX / 100 + 'rem',
        })
      // }



      msg.data.forEach(function(item, index) {
        // if(msg.isRightAnswers == 1) {
          // 终点区域列表渲染
          areaListStr = $(`<li data-index=${index} data-end-areaX=${item.imgEndX} data-end-areaY=${item.imgEndY} data-start-areaX=${item.imgStartX-msg.areaX} data-start-areaY=${item.imgStartY-msg.areaY}>`
                      + `<img src=${item.img}>`
                      +'</li>')
          $('.area-list ul').eq(key).append(areaListStr)
        // }

        // 选项区域列表渲染
        optionStr = $(`<li data-isRightAnswers=${msg.isRightAnswers} data-img-startx=${item.imgStartX} data-img-starty=${item.imgStartY} data-areaX=${msg.areaX} data-areaY=${msg.areaY} data-key=${key} data-index=${index} data-syncactions='list${liIndex}' data-liIndex=${liIndex}>`
                    + `<img src=${item.img}>`
                    +'</li>')
        liIndex++
        $('.options-list ul').append(optionStr)

        // 列表起点位置
        optionStr.css({
          width: item.imgStartWidth / 100 + 'rem',
          height: item.imgStartHeight / 100 + 'rem',
          top: item.imgStartY / 100 + 'rem',
          left: item.imgStartX / 100 + 'rem',
        })
        // 列表终点位置
        areaListStr.css({
          width: item.imgEndWidth / 100 + 'rem',
          height: item.imgEndHeight / 100 + 'rem',
          top: item.imgEndY / 100 + 'rem',
          left: item.imgEndX / 100 + 'rem',
        })


      })
    });

    // demo 终点默认元素
    let demoEndData = optionsNewArr[0].data[0];
    $('.default-end-box').css({
      top: demoEndData.imgEndY / 100 + 'rem',
      left: demoEndData.imgEndX / 100 + 'rem',
    })
    // 开场方式是否从终点飞到起点
    openWayAction()
  }

  /**
   * 开场方式
  */
  function openWayAction() {
    if(openWay == 2 && isDemo) {
      console.log('开场方式')
      $('.area-list ul li').show();
      $('.options-list ul li').hide();
      for(let i = 0; i < $('.area-list ul li').length; i++) {
        let areaListLi = $('.area-list ul li').eq(i),
            startAreaX = areaListLi.attr('data-start-areaX'),
            startAreaY = areaListLi.attr('data-start-areaY'),
            endAreaX = areaListLi.attr('data-end-areaX'),
            endAreaY = areaListLi.attr('data-end-areaY');
            setTimeout(function(){
              // 移动到起初始位置
              areaListLi.css({
                left: (startAreaX)/100 + 'rem',
                top: startAreaY/100 + 'rem'
              })
              //恢复到终点位置
              areaListLi.css({
                left: endAreaX/100 + 'rem',
                top: endAreaY/100 + 'rem'
              })
              $('.area-list ul li').hide();
              $('.options-list ul li').show();

            },1500)

      }
    }
  }


  /**
   * 拖拽事件
   */
  let dragXy = {
    x: 0,
    y: 0,
  }

  $('.options-list ul li').drag({
    before: function(e) {
      dragXy.x = e.pageX / window.base;
      dragXy.y = e.pageY / window.base;
      isDrag = false;
      isDragFinish = false;
      console.log('drag is before')
    },
    process: function(e) {
      isDrag = true;
      // console.log('drag is proces')
      dragAction(e, $(this))
    },
    end: function(e) {
      console.log('drag is end')
      dragAction(e, $(this),'isEnd')
      if (!isSync) {
        $(this).trigger('syncDragEnd', {
          childOptionsArr: childOptionsArr,
          isDrag: isDrag
        })
        return
      }
      SDK.bindSyncEvt({
        index: $(this).data('syncactions'),
        eventType: 'dragEnd',
        method: 'drag',
        pageX: '',
        pageY: '',
        syncName: 'syncDragEnd',
        otherInfor: {
          childOptionsArr: childOptionsArr,
          isDrag: isDrag
        },
        recoveryMode: 1
      })
    }
  })


  /**
   *
   * 拖拽回调
   */
  $('.options-list ul li').on('syncDragEnd', function(e, message) {
    // dragAction(e, $(this),'isEnd')
    console.log('message----', message)

    let that = $(this),
        childOptionsArrS = [],
        isDrag = '';

    if (isSync) {
      childOptionsArrS = message.otherInfor.childOptionsArr
      isDrag = message.otherInfor.isDrag
      // 断线重连只展示页面结果
      // 是否隐藏demo模式
      if(childOptionsArrS.length) {
        $('.funcMask').hide();
        //学生有权限 s/t 模式
        // 老师是否有权限
        // if(SDK.getClassConf().h5Course.classStatus == 13) {
        //   moduleAuthorizationFn('teaRoot', '14')
        // }
      }
      let dataLIndex = '',
          dataKey = '',
          dataIndex = '';
      childOptionsArrS.forEach(function(item, key){
            dataLIndex = Number(item.dataLiIndex),
            dataKey = Number(item.dataKey),
            dataIndex = Number(item.dataIndex);
        $('.options-list ul li').eq(dataLIndex).hide();
        $('.area-list ul').eq(dataKey).find('li').eq(dataIndex).show();
      })
      childOptionsArr = childOptionsArrS
       // 是否游戏结束
       isEndAction(that, childOptionsArr);
      //  if($('.options-list ul li').eq(dataLIndex).attr('data-isrightanswers') == 1) {
      //   isEndAction(childOptionsArr);
      //  }

    }


    SDK.setEventLock();
  })


  /**
   * 拖拽函数
  */
  function dragAction(e, $this, type) {
    let that = $this;
    let isRightAnswers = that.attr('data-isRightAnswers'); //是否是干扰项
    if(isRightAnswers == 2) {
      return
    }
    let dataKey = that.attr('data-key'), //对应正确区域的数组
        dataIndex = that.attr('data-index'), //当前数组的key值
        pageX = e.pageX / window.base,
        pageY = e.pageY / window.base,
        successS = $('.success-list ul').eq(dataKey),
        areaS = $('.area-list ul').eq(dataKey);
        // 修正错误,不要根据eq读取,根据预埋的key去读取.
        successS = $(`.success-list ul[data-key=${dataKey}]`);
        areaS = $(`.area-list ul[data-key=${dataKey}]`);
    let t2 = successS.offset().top / window.base,
        l2 = successS.offset().left / window.base,
        r2 = (successS.offset().left + successS.width()) / window.base,
        b2 = (successS.offset().top + successS.height()) / window.base;
    if ((pageY > t2 && pageY < b2) && (pageX > l2 && pageX < r2)) { //表示碰上
      console.log('已进入正确的区域3')
      if(type) {
        childOptionsArr.push({
          dataKey:  dataKey,
          dataIndex: dataIndex,
          dataLiIndex: that.attr('data-liIndex')
        })
        // 当前正确区域显示
        areaS.find('li').eq(dataIndex).show()
        // 隐藏拖拽的素材
        that.hide();
        //动效音效播放
        isAudioEvent().right();
        // 是否游戏结束
        isEndAction(that, childOptionsArr);
      }
    }
  }

  /**
   * 是否终局页
   */
  function isEndAction($that, arr) {
    let isRightAnswers = $that.attr('data-isRightAnswers'); //是否是干扰项
    if(isRightAnswers == 2) {
      return
    }
    console.log('终局页')
    if(arr.length == $('.area-list ul li').length) {
      console.log('拖拽游戏已结束')
      // 是否出现终局页
      if(isEnding == 1) {
        // 为音频赋值
        $('.endAudio').attr('src', endList.endVideo)
        setTimeout(function(){
          $('.areaEndBox').show();
          $('.optionLight').css({
            width: spriteWidth/300 + 'rem',
            height: spriteHeight/100 + 'rem',
            background: 'url(' + endList.endFigure + ')',
            backgroundSize: '300% ' + '100%',
            // backgroundSize: spriteWidth + "rem" + spriteHeight/100 + "rem",
          })
          // 动画效果
          figureAnimationFn()
          // 音频播放
          isAudioEvent().endAudio()
        },1500)
      }
    }
  }

  /**
   * 雪碧图动画
   */
  function figureAnimationFn() {
    let imgIndex = 0;
    timeInit = setInterval(function() {
      console.log('雪碧图效果--')
      imgIndex++;
      $('.optionLight').css({
        backgroundPosition: '-'+100*imgIndex + '%' + ' ' + '100%'
      })

    },300)
  }

  /**
   *
   * 点击播放音频
  */
   let soundClick = true,
       isPlaySound = true;
    $('.sound').on('click touchstart', function (e) {
        if (e.type == 'touchstart') {
          e.preventDefault()
        }
        e.stopPropagation();

        if (soundClick) {
          soundClick = false;
          if (!isSync) {
            $(this).trigger('syncSoundClick');
            return;
          }
          if (window.frameElement.getAttribute('user_type') == 'tea') {
            SDK.bindSyncEvt({
              sendUser: '',
              receiveUser: '',
              index: $(e.currentTarget).data('syncactions'),
              eventType: 'click',
              method: 'event',
              syncName: 'syncSoundClick',
              funcType: 'audio'
            });
          } else {
            $(this).trigger('syncSoundClick');
            return;
          }
        }
    })

    $('.sound').on('syncSoundClick', function (e, message) {
      let gif = $(this).find('.gif');
      let png = $(this).find('.png');
      let audio = $(this).find('audio')[0];
      if (isPlaySound) {
        // audio.play();
        SDK.playRudio({
          index: audio,
          syncName: $(this).find('audio').attr("data-syncaudio")
        })
        gif.show();
        png.hide();
      } else {
        // audio.pause();
        SDK.pauseRudio({
          index: audio,
          syncName: $(this).find('audio').attr("data-syncaudio")
        })
        gif.hide();
        png.show();
      }
      audio.onended = function () {
        gif.hide();
        png.show();
        isPlaySound = true;
      }.bind(this);

      isPlaySound = !isPlaySound;

      SDK.setEventLock();
      soundClick = true;

    });

  /**
   * 音频播放
   */
  function isAudioEvent() {
    return {
      // 终局音频播放
      "endAudio": function() {
        isClick = false;
        SDK.playRudio({
          index: $(".endAudio")[0],
          syncName: $(".endAudio").attr("data-syncaudio")
        })
        $(".endAudio")[0].onended = function() {
          console.log('终局音频播放结束')
          clearInterval(timeInit);
          setTimeout(function(){
            $('.areaEndBox').hide();
          },1500)
          isClick = true;
        }
      },

      // 动效音频
      "right": function() {
        isClick = false;
        SDK.playRudio({
          index: $(".rightAudio")[0],
          syncName: $(".rightAudio").attr("data-syncaudio")
        })
        $(".rightAudio")[0].onended = function() {
          console.log("动效音效已结束")
          isClick = true;
        }
      },

      // 题干音频
      "topicAudio": function() {
        isClick = false;
        SDK.playRudio({
          index: $(".topicAudio")[0],
          syncName: $(".topicAudio").attr("data-syncaudio")
        })
        $(".topicAudio")[0].onended = function() {
          console.log("题干音效已结束")
          isClick = true;
        }
      },

    }
  }

});
