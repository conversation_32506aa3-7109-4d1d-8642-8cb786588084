@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/ribbonWin/style.scss';
@import '../../common/template/hint/style.scss';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
body{
	overflow: hidden;
}
ul,li{
  list-style: none;
}
img{
  width: 100%;
}
.container {
	background-size: auto 100%;
	position: relative;
  .default-end-box{
    position: absolute;
  }
  .area-list{
    ul{
      li{
        position: absolute;
        display: none;
        img{
          vertical-align: top;
        }
      }
    }
  }
  .success-list{
    ul{
      // background:red;
      position: absolute;
      li{
        position: absolute;
        // display: none;
        img{
          vertical-align: top;
        }
      }
    }
  }
  .options-list{
    ul > li{
      position: absolute;
      img{
        vertical-align: top;
      }
    }
  }
  .areaEndBox{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: none;
    .area-mask{
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 20;
      background: rgba($color: #000000, $alpha: 0.5);
    }
    .content{
      overflow: hidden;
      position: absolute;
      top: 50%;
      left: 50%;
      width: 10rem;
      height: 10rem;
      transform: translateX(-50%) translateY(-50%);
      z-index: 40;
      // 大光圈
      .rightLight {
        position: absolute;
        width: 10rem;
        height: 10rem;
        animation: rotation 6s linear infinite;
      }
      .optionLight{
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6.4rem;
        height: 6.4rem;
        transform: translateX(-50%) translateY(-50%);
        background: url('../image/sprite.png');
        // background-size: 6.7rem 3.65rem;
        background-size: 300% 100%;
        // animation: handClick 1s steps(3) 1;
        // animation-iteration-count: infinite；
        animation-fill-mode: 'forwards';
      }
    }
    .stars-list{
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      ul{
        li{
          position: absolute;
          width: .76rem;
          height: .76rem;
        }
      }
    }

  }

   // 音频播放
   .example-audio{
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    width: 1.45rem;
    height: 1.34rem;
    background: url(../image/sound_bg.png) no-repeat center;
    background-size: cover;
    cursor: pointer;
    z-index: 100;
    .small {
      width: 0.9rem;
      margin-top: 1px;
    }
    img {
      width: 1.45rem;
    }
    .gif {
      display: none;
    }
  }


}
// 功能遮罩层
.funcMask,
.tea-stu-not-in {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 50;
    display: none;

    .startBox {
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        height: 2.16rem;
        width: 11.79rem;
        // background: rgba(0, 0, 0, 0.6);
        border-radius: 0.5rem;

        .demoTextBox {
            width: 11.79rem;
            height: 2.16rem;
            position: absolute;
            left: 50%;
            // top: 0.64rem;
            transform: translateX(-50%);
            border-radius: 0.3rem;
            // background: rgba(0, 0, 0, 0.2);
            text-align: center;

            .startMsg {
                // width: 6.75rem;
                // height: 2.88rem;
                // margin-top: 0.4rem;
            }
        }

        .demoBtnBox {
            width: 7.4rem;
            // height: 1.14rem;
            transform: translate(-50%);
            // background: rgba(0, 0, 0, 0.2);
            text-align: center;
            position: absolute;
            left: 50%;
            // bottom: 0.75rem;
            top: .6rem;

            .demo-btnStu {
                width: 3.49rem;
                height: auto;
                cursor: pointer;
                display: inline-block;
            }

            .startBtn {
                width: 2.03rem;
                height: auto;
                cursor: pointer;
                display: inline-block;
                margin-right: 0;
                margin-left: 1.49rem;
            }
        }
    }

    .timeChangeBox {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        height: 4.8rem;
        width: 7.2rem;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 0.5rem;

        .timeBg {
            width: 3.79rem;
            height: 3.84rem;
            position: absolute;
            top: 1rem;
            background: url(../image/timeBg.png) no-repeat;
            background-size: 100% 100%;
            left: 50%;
            margin-left: -1.9rem;
            top: 50%;
            margin-top: -1.92rem;

            .numberList {
                width: 1.5rem;
                height: 1.5rem;
                position: absolute;
                left: 0;
                bottom: 0;
                right: 0;
                top: 0;
                margin: auto;
                background: url(../image/number1.png) no-repeat;
                background-size: 6rem 100%;
                background-position-x: 0.1rem;
            }
        }
    }
}

.stars-list > ul > li{
  &:nth-child(1) {
    animation: moveInOne 1s linear forwards;
  }
  &:nth-child(2) {
    animation: moveInTwo 1s linear forwards;
  }
  &:nth-child(3) {
    animation: moveInT 1s linear forwards;
  }
  &:nth-child(4) {
    animation: moveInF 1s linear forwards;
  }
  &:nth-child(5) {
    animation: moveInS 1s linear forwards;
  }
  &:nth-child(6) {
    animation: moveInw 1s linear forwards;
  }
  &:nth-child(7) {
    animation: moveInu 1s linear forwards;
  }
  &:nth-child(8) {
    animation: moveInn 1s linear forwards;
  }
}
@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes moveInOne {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    left: -5rem;
    top: -3rem;
  }
  100%{
    opacity: 0;
  }
}
@keyframes moveInTwo {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    left: -4.2rem;
    top: -1rem;
  }
  100%{
    opacity: 0;
  }
}

@keyframes moveInT {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    left: -1rem;
    top: -5.1rem;
  }
  100%{
    opacity: 0;
  }
}
@keyframes moveInF {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    left: -4.6rem;
    top: 1rem;
  }
  100%{
    opacity: 0;
  }
}
@keyframes moveInS {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    right: -5rem;
    top: -.5rem;
  }
  100%{
    opacity: 0;
  }
}
@keyframes moveInw {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    right: -4.5rem;
    top: -3rem;
  }
  100%{
    opacity: 0;
  }
}
@keyframes moveInu {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    right: -4rem;
    top: 2rem;
  }
  100%{
    opacity: 0;
  }
}
@keyframes moveInn {
  0%{
    top: 0;
    left: 0;
  }
  99%{
    right: -2rem;
    top: -3rem;
  }
  100%{
    opacity: 0;
  }
}

