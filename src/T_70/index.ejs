<!DOCTYPE html>
<html lang="en">
<head>
  <% var title="TDR0007_目标拖拽"; %>
  <%include ./src/common/template/index_head %>
  <!-- 提示模式 -->
  <% include ./src/common/template/hint/index.ejs %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">

  <!-- 选中正确的音效 -->
  <audio src="./audio/right.mp3" class="rightAudio" data-syncaudio="rightAudio"></audio>
  <!-- 终局音频 -->
  <audio src="" class="endAudio" data-syncaudio="endAudio"></audio>
  <!-- 题干音频 -->
  <div class="example-audio sound" data-syncactions="audio-1">
    <img src="./image/sound.gif" alt="" class="gif small">
    <img src="./image/sound.png" alt="" class="png small">
    <audio src="" class="topicAudio" data-syncaudio="topicAudio"></audio>
  </div>

  <!-- 正确区域 -->
  <div class="success-list">
  </div>

  <!-- 终点位置 -->
  <div class="area-list"></div>

  <!-- 选项区域 -->
  <div class="options-list">
    <ul></ul>
  </div>

  <!-- 终局页 -->
  <div class="areaEndBox">
    <div class="area-mask"></div>
    <div class="content">
      <img src="./image/sucess_bj.png" alt="" class="rightLight">
      <div class="optionLight"></div>
      <div class="stars-list">
        <ul>
          <li><img src="./image/star01.png" alt=""></li>
          <li><img src="./image/star02.png" alt=""></li>
          <li><img src="./image/star03.png" alt=""></li>
          <li><img src="./image/star04.png" alt=""></li>
          <li><img src="./image/star01.png" alt=""></li>
          <li><img src="./image/star02.png" alt=""></li>
          <li><img src="./image/star03.png" alt=""></li>
          <li><img src="./image/star04.png" alt=""></li>
        </ul>
      </div>
    </div>

    <div class="st"></div>
    <!-- <img class="optionLight" src="./image//sketch01-3.png" alt=""> -->
  </div>

  <!-- demo 第一个元素终点默认值 默认为1个像素点（为了demo获取left top值） -->
  <div class="default-end-box"></div>

  <!-- 功能遮罩层 -->
  <div class="funcMask">
    <!-- 预习模式操作区 -->
    <div class="startBox">
      <div class="demoTextBox">
        <!-- <img src="//cdn.51talk.com/apollo/images/04fa2ea2eeb4a63897be5f06721a7816.png" class="startMsg" /> -->
        <img src="./image/04fa2ea2eeb4a63897be5f06721a7816.png" class="startMsg" />
      </div>
      <div class="demoBtnBox">
        <!-- <img src="//cdn.51talk.com/apollo/images/70d9c19b1a7ec17fc84af9569b2e0618.png"
          class="demo-btnStu" data-syncactions='demoStuBox'/>
        <img src="//cdn.51talk.com/apollo/images/********************************.png"
          class="startBtn" data-syncactions='demoStartBtn'> -->
        <img src="./image/70d9c19b1a7ec17fc84af9569b2e0618.png"
          class="demo-btnStu" data-syncactions='demoStuBox'/>
        <img src="./image/********************************.png"
          class="startBtn" data-syncactions='demoStartBtn'>
      </div>

    </div>
    <!-- 倒计时 -->
    <div class="timeChangeBox hide">
      <div class="timeBg">
        <div class="numberList"></div>
        <audio src="./audio/timeLow.mp3" class="timeLowAudio_1" data-syncaudio="timeLowAudio_1"></audio>
        <audio src="./audio/timeLow.mp3" class="timeLowAudio_2" data-syncaudio="timeLowAudio_2"></audio>
        <audio src="./audio/timeLow.mp3" class="timeLowAudio_3" data-syncaudio="timeLowAudio_3"></audio>
        <audio src="./audio/timehigh.mp3" class="timeLowAudio_4" data-syncaudio="timeLowAudio_4"></audio>
      </div>
    </div>
  </div>

  <script type="text/javascript">
      document.documentElement.addEventListener('touchstart', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, false);
      // 禁用手指双击缩放：

      var lastTouchEnd = 0;
      document.documentElement.addEventListener('touchend', function (event) {
        var now = Date.now();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
  </script>
<% include ./src/common/template/ribbonWin/index.ejs %>
</div>
<script inline type="text/javascript" src="js/funParabola.js"></script>
<!-- 遮罩层 -->
<%include ./src/common/template/index_bottom %>
</body>
</html>
