var configData = {
	bg:'',
      desc:'Look,listen and choose!',
      title:'',
	tg:[
            {
                  content:"T103--11111111",
                  title:"weqwf appy family. My father i"
            },
            {
                  content:"T103--11111111",
                  title:"weqwf appy family. My father i"
            },
            {
                  content:"T103--11111111",
                  title:"weqwf appy family. My father i"
            }
      ],
      level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
				content: "eegeg appy family. My father i appy family. My father i",
				title: "weqwf appy family. My father i"
			}]
	},
	source:{
            // texttitle:'',
            textImage:'assets/images/textImage1_02.png',
            text:'<p>I have a happy family. My father is a police officer. He is tall. He likes to play basketball. My mother is a teacher. She is thin and short. She likes to watch TV and read books. I am a student. I am a boy. I am very handsome. We like to eat meat. We are a happy family. </p>',
            audio:'./assets/audios/02.mp3',
            isBig:false,
            isSmall:false,
            textQues:'I have a happy family. My father is a police officer. He is tall.',
            options:['I have a happy family. I have a happy family.I have a happy family.I have a happy family.I have a happy family.My father is a police officer.I have a happy family. My father is a police officer.','sdsaf<br/>sfsad dgrdhg <br/>sdfdg<br/>wewd sadwq weqwe<br/>ewqe jeie','He likes to play basketball. My mother is a teacher. She is thin and short. She likes to watch TV and read books. I am a student','dsad fere'],
            right:1 
	}
};
(function (pageNo) { configData.page = pageNo })(0)