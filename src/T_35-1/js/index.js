"use strict"
import '../../common/js/common_1v1.js'
import './drag.js'
import '../record/js/record.js'

$(function () {
  window.h5Template = {
    hasPractice: '0'
  }
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  if (configData.bg == '') {
    $(".container").css({ 'background-image': 'url(./image/defaultBg.jpg)' })
  }


  //多语言 简体中文
  if (configData.lang == 2) {
    $("#next_pic").attr("src", './image/nextBtn2.png');
    $("#last_pic").attr("src", './image/lastBtn2.png');
  }

  let options = configData.source.options;
  let staticData = configData.source;
  let title = staticData.title;
  let currentPage = 1; //当前页码
  let topicType = configData.source.topicType;
  let optionsData = configData.source.optionsData;

  /**
   *
   * @param 题干类型，文字 or 图片
   * @returns
   */
  if (topicType == 2) {
    $('.fontColor').hide();
  } else {
    $('.fontColor').show();
  }


  //获取卡片的个数
  function getCardNum(index) {
    let num = 0;
    for (let i = 0; i < options[index].wordsList.letter.length; i++) {
      options[index].wordsList.letter[i].w != '' ? num++ : num;
    }
    return num
  }
  //判断红色标记的位置和对应的单词
  function wordData(str) {
    var wordCard = [];
    // 图片 or 文字
    if (topicType == 2) {
      for (let i = 0; i < optionsData.length; i++) {
        wordCard.push(optionsData[i].picsList[str])
      }
    } else {
      for (let i = 0; i < options.length; i++) {
        wordCard.push(options[i].wordsList[str])
      }
    }
    return wordCard
  }

  function isRed(index) {
    var html = '';
    // 图片 or 文字
    if (topicType == 2) {
      for (let i = 0; i < wordData('letter')[index].length; i++) {
        html += `<li class="card"><img src="${wordData('letter')[index][i].w}" alt="" class="pic-img"></li>`
      }
    } else {
      for (let i = 0; i < wordData('letter')[index].length; i++) {
        if (wordData('color')[index][i].isRed == true) {
          html += `<li class="card isRed">${wordData('letter')[index][i].w}</li>`
        } else {
          if (!wordData('letter')[index][i].w) {
            html += `<li class="card">${wordData('letter')[index][i].w}</li>`
          } else {
            html += `<li class="card">${wordData('letter')[index][i].w}</li>`
          }
        }
      }
    }

    return `<ul class="index${index}">` + html + `</ul>`
  }
  //当前显示第几页内容
  function showCurrentPage(currentPage) {
    let isOptionsData = []; // 选项数据数组
    // 判断是图片还是文字
    if (topicType == 2) {
      isOptionsData = optionsData; // 使用optionsData
    } else {
      isOptionsData = options; // 使用options
    }
    for (let i = 0; i < isOptionsData.length; i++) {
      // 判断当前页是否等于当前页码
      if (i == currentPage - 1) {
        $('.cardPos ul').eq(i).css({
          'display': 'block' // 显示当前页的选项
        })
      } else {
        $('.cardPos ul').eq(i).css({
          'display': 'none' // 隐藏非当前页的选项
        })
      }
    }
    $(".page").html(currentPage + "/" + isOptionsData.length); // 显示当前页和总页数
  }
  //单词翻页效果
  let timer = null;

  function scrollWords(index, operate) {
    let isOptionsData = [];
    // 图片 or 文字
    if (topicType == 2) {
      isOptionsData = optionsData;
    } else {
      isOptionsData = options;
    }
    //音效
    var audio = $(".runRandom")[0];
    audio.currentTime = 0;
    // audio.play();
    SDK.playRudio({
      index: audio,
      syncName: $('.runRandom').attr("data-syncaudio")
    })
    let lis = $(".cardPos ul").eq(index - 1).find('li')
    let standard = 0.3
    let timeStatus = 0.05
    let standardTime = 800
    if (isSync && operate == 5) {
      standard = 0 //掉线
      standardTime = 100
    }
    let num = 0;
    for (let i = 0; i < lis.length; i++) {
      let time = 0.8 + standard * i
      if (isSync && operate == 5) {
        time = 0
      }
      num++;
      // $(lis[i]).css({
      //   'animation': 'goTop ' + timeStatus + 's ease-out ' + time + 's forwards',
      //   '-webkit-animation': 'goTop ' + timeStatus + 's s ease-out ' + time + 's forwards',
      // })
    }
    timer = setTimeout(function () {
      lastLock = true
      nextLock = true;
      if (currentPage < isOptionsData.length) {
        $('.nextBtn').addClass('btns')
      }

      if (currentPage > 1) {
        $('.lastBtn').addClass('btns')
      }

      $(".lampUp").addClass('upActive')
      $(".lampDown").addClass('downActive')
      // 此内容只为文字所属
      if (topicType == 1) {
        $(".fontColor").text(isOptionsData[currentPage - 1].words)
      }
      clearTimeout(timer)
      // audio.pause();
      SDK.pauseRudio({
        index: audio,
        syncName: $('.runRandom').attr("data-syncaudio")
      })
    }, standardTime + standard * num * 1000)
  }

  function scrollGif() {
    let i = 0
    let lis = $(".cardTransform ul").find('li')
    $(lis).addClass('gif')
    $(".cardTransform ul").delay(500).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(300).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(300).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(300).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();

    })
  }

  // 填充内容
  const page = {
    addCard: function () {
      let isOptionsData = [];
      // 图片 or 文字
      if (topicType == 2) {
        isOptionsData = optionsData;
      } else {
        isOptionsData = options;
      }
      for (let i = 0; i < isOptionsData.length; i++) {
        $(".cardPos").append(isRed(i))
      }
      showCurrentPage(currentPage) //默认显示第一页
      //是否添加录音组件
      // recordEvents.createRcordBtn(isOptionsData)

      let lis = $(".cardPos ul").eq(0).find('li')
      let standard = 0
      let timeStatus = 0
      for (let i = 0; i < lis.length; i++) {
        let time = standard * i
        // $(lis[i]).css({
        //   'animation': 'goTop 0.3s ease-in-out forwards',
        //   '-webkit-animation': 'goTop 0.1s ease-in forwards'
        // })
      }
      $(".fontColor").text(isOptionsData[currentPage - 1].words)
    },
    btnInit: function () {
      if (!isSync) {
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        $(".fontColor").addClass("hide");
        $(".nextBtn").removeClass("hide");
        $(".lastBtn").removeClass("hide");
      } else {
        $(".fontColor").removeClass("hide");
        $(".nextBtn").addClass("hide");
        $(".lastBtn").addClass("hide");
      }
    },
    ifAddEmptyCard: function (index) {

      for (let i = 0; i < options[index].wordsList.letter.length; i++) {
        if (options[index].wordsList.letter[i].w == '') {
          $('.index' + index).find("li").eq(i).addClass('emptyCard')
        }
      }
    },
    init: function () {
      this.addCard();
      this.btnInit();
      this.ifAddEmptyCard(0)
    }
  }
  page.init()
  //点击上一个按钮
  let lastLock = false;
  $(".lastBtn").on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    //录音状态不可点击
    if (recordEvents.reStart == false) {
      recordEvents.stopClick()
      return
    }
    if (currentPage <= 1) {
      return
    }
    if (lastLock) {
      lastLock = false;
      if (!isSync) {
        $(this).trigger('lastClickSync')
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'lastClickSync',
          recoveryMode: 1,
          otherInfor: {
            currentPage: currentPage - 1
          }
        });
      }
    }
  })

  $(".lastBtn").on('lastClickSync', function (e, message) {

    $(".fontColor").text("")
    $(".lampUp").removeClass('upActive')
    $(".lampDown").removeClass('downActive')
    $(".nextBtn").removeClass('btns')
    $(".nextBtn img").removeClass("hide")
    $(this).removeClass('btns')
    if (currentPage == 2) {
      $(this).find("img").addClass("hide")
    }

    nextLock = false

    currentPage--

    if (topicType == 1) {
      page.ifAddEmptyCard(currentPage)
    }
    //options[currentPage - 1].recordStatus == true ? recordEvents.isShowRecord(true) : recordEvents.isShowRecord(false);
    recordEvents.getRecordIndex(currentPage);


    let obj = '';
    if (!isSync) {
      currentPage = currentPage
      scrollGif()
      scrollWords(currentPage - 1)

    } else {
      obj = message.data[0].value.syncAction.otherInfor;
      if (message == undefined || message.operate == '1') {
        currentPage = obj.currentPage
        if (currentPage > 1) {
          $(".lastBtn").removeClass('btns')
          $(".lastBtn img").removeClass("hide")
        }
        scrollWords(currentPage - 1)
        scrollGif()
      } else {
        if (obj.currentPage > 1) {
          $(".lastBtn").removeClass('btns')
          $(".lastBtn img").removeClass("hide")
        }
        //直接恢复页面
        rebackPage(obj, message.operate)
      }
    }
    if (isSync && message.operate != '1') {
      currentPage = obj.currentPage
    }
    showCurrentPage(currentPage)
    SDK.setEventLock()
  })

  //点击下一个按钮
  let nextLock = true;
  $(".nextBtn").on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }

    //录音状态不可点击
    if (recordEvents.reStart == false) {
      recordEvents.stopClick()
      return
    }
    var optionsNum;
    if (topicType == 2) { //图
      optionsNum = optionsData.length - 1;
    } else { //文字
      optionsNum = options.length - 1;
    }
    if (currentPage > optionsNum) {
      return
    }
    if (nextLock) {
      nextLock = false;
      if (!isSync) {
        $(this).trigger('nextClickSync')
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'nextClickSync',
          recoveryMode: 1,
          otherInfor: {
            currentPage: currentPage + 1
          }
        });
      }
    }
  })

  $(".nextBtn").on('nextClickSync', function (e, message) {

    $(".fontColor").text("")
    $(".lampUp").removeClass('upActive')
    $(".lampDown").removeClass('downActive')
    if (currentPage == 1) {
      scrollWords(1)
    }
    $(".lastBtn").removeClass('btns')
    $(".lastBtn img").removeClass("hide")
    $(this).removeClass('btns')

    // 区分是否是文字or图片

    let isOptionsData = [];
    // 图片 or 文字
    if (topicType == 2) {
      isOptionsData = optionsData;
    } else {
      isOptionsData = options;
    }

    if (currentPage == isOptionsData.length - 1) {
      $(this).find("img").addClass("hide")
    }

    lastLock = false
    if (topicType == 1) {
      page.ifAddEmptyCard(currentPage)
    }
    currentPage++
    //options[currentPage - 1].recordStatus == true ? recordEvents.isShowRecord(true) : recordEvents.isShowRecord(false);
    recordEvents.getRecordIndex(currentPage)
    if (!isSync) {
      currentPage = currentPage
      scrollGif()
      scrollWords(currentPage) //字母动效
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      currentPage = obj.currentPage
      if (message == undefined || message.operate == 1) {
        scrollWords(currentPage) //字母动效
        scrollGif()
      } else {
        //直接恢复页面
        rebackPage(obj, message.operate)
      }
    }
    showCurrentPage(currentPage)
    SDK.setEventLock()
  })

  //恢复页面
  function rebackPage(page, operate) {
    let pageIndex = page.currentPage
    showCurrentPage(pageIndex)
    scrollWords(pageIndex, operate)
  }
});
