@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../record/css/record.css';
@mixin setEle($l:0rem, $t:0rem, $w:0rem, $h:0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.hide {
  display: none;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  .cardArea {
    width: 100%;
    height: 8.3rem;
    margin-top: 2.5rem;
    background: url('../image/cardBgs.png') no-repeat center bottom;
    background-size: 16.84rem 8.08rem;
    position: relative;
    .fontColor {
      font-size: 0.96rem;
      color: #fff;
      position: absolute;
      bottom: 1.28rem;
      left: 50%;
      transform: translateX(-50%);
    }
    .cardTransform {
      z-index: 100;
    }
    .cardPos,
    .cardTransform {
      position: absolute;
      height: 4.2rem;
      width: 14.6rem;
      top: 1.02rem;
      left: 50%;
      transform: translateX(-50%);
      padding: 0.35rem 0.58rem;
      box-sizing: border-box;
    }
    ul {
      position: absolute;
      height: 3.52rem;
      width: 100%;
      top: 0.35rem;
      left: 0.58rem;
      overflow: hidden;
      .card,
      .cardGif {
        width: 2.96rem;
        height: 3.52rem;
        line-height: 3.52rem;
        text-align: center;
        float: left;
        font-size: 1.52rem;
        position: absolute;
      }
      .cardGif {
        height: 3.52rem;
      }
      .card {
        top: -3.52rem;
      }
      li:nth-child(1) {
        left: 0;
      }
      li:nth-child(2) {
        left: 3.5rem
      }
      li:nth-child(3) {
        left: 7rem;
      }
      li:nth-child(4) {
        left: 10.5rem;
      }
      .pic-img{
        width: 2.96rem;
        height: 3.52rem;
      }
      li{
        animation: goTop 0.3s ease-in-out forwards;
        -webkit-animation: goTop 0.1s ease-in forwards;
      }
      // &:nth-child(1) {
      //   li{
      //     animation: goTop 0.3s ease-in-out forwards;
      //     -webkit-animation: goTop 0.1s ease-in forwards;
      //   }
      // }
    }
  }
}

.emptyCard {
	background: #AEB4B4;
	border-radius: .2rem;
}

.container {
  background-size: auto 100%;
  position: relative;
  // font-family:"ARLRDBD";
}

.lookTitle{
	width:100%;
	height:1.5rem;
	font-size:0.8rem;
	text-align:center;
	font-weight:700;
	color:#333333;
	span{
		display:inline-block;
		margin-top:0.45rem;
	}
}
.isRed{
  color: red;
}
.gif{
	background: url('../image/scroll.gif') no-repeat center -0.02rem;
	background-size: 2.62rem 3.52rem;
}
.btn{
	width: 3.42rem;
	height: 1.42rem;
	position: absolute;
	top: 5.85rem;
	background: url('../image/btnBg.png') no-repeat;
	background-size: contain;
	img{
		width: 100%;
		height:1.42rem;
		position: absolute;
	}
}
.btns:active{
	transform: scale(0.9,0.9);
}
.lastBtn{
	left: 4.25rem;
}
.nextBtn{
	left: 11.55rem;
}
.lamp{
	width: 1.6rem;
	height: 3.09rem;
	position: absolute;
	bottom: 0rem;
}
.lampUp{
	left: 1.84rem;
	/*background:url('../image/lampUp.jpg') no-repeat;*/
	background-size: contain;
}
.lampDown{
	right: 1.84rem;
	/*background:url('../image/lampDown.jpg') no-repeat;*/
	background-size: contain;
	transform: scaleX(-1);
}
.upActive{
	animation: changeBgUp 0.8s ease both infinite;
}
.downActive{
	animation: changeBgDown 0.8s ease both infinite;
}


.page{
	background: #FF7200;
	width: 1.6rem;
	position: absolute;
	bottom: 2.67rem;
	left: 50%;
	margin-left: -0.8rem;
	font-size: 0.5rem;
	color: #fff;
	text-align: center;
	border-radius: .1rem;
}


@-webkit-keyframes changeBgUp{
	0%{
		background:url('../image/lampDown.jpg') no-repeat;
		background-size: contain;
	}
	100%{
		background:url('../image/lampUp.jpg') no-repeat;
		background-size: contain;
	}
}
@keyframes changeBgUp{
	0%{
		background:url('../image/lampDown.jpg') no-repeat;
		background-size: contain;
	}
	50%{
		background:url('../image/lampUp.jpg') no-repeat;
		background-size: contain;
	}
	100%{
		background:url('../image/lampDown.jpg') no-repeat;
		background-size: contain;
	}
}

@keyframes changeBgDown{
	0%{
		background:url('../image/lampUp.jpg') no-repeat;
		background-size: contain;
	}
	50%{
		background:url('../image/lampDown.jpg') no-repeat;
		background-size: contain;
	}
	100%{
		background:url('../image/lampUp.jpg') no-repeat;
		background-size: contain;
	}
}
@-webkit-keyframes changeBgDown{
	0%{
		background:url('../image/lampUp.jpg') no-repeat;
		background-size: contain;
	}
	50%{
		background:url('../image/lampDown.jpg') no-repeat;
		background-size: contain;
	}
	100%{
		background:url('../image/lampUp.jpg') no-repeat;
		background-size: contain;
	}
}

@keyframes hide{
	100%{
		display: none;
	}
}
@keyframes goTop{
	0%{
		top: -3.52rem
	}
	30%{
		top: 1rem;
	}
	100%{
		top: 0;
	}
}
@-webkit-keyframes goTop{
	0%{
		top: -3.52rem
	}
	30%{
		top: 1rem;
	}
	100%{
		top: 0;
	}
}
