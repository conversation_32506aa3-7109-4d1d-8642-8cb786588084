<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>TRA0001_随机音素_中文版</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TRA0001_随机音素_中文版</div>

			<% include ./src/common/template/common_head_cn %>

			<!-- 添加选项 -->
			<div class="c-group">
				<div class="c-title">添加选项</div>
				<div class="c-area upload img-upload">
          <div class="word-title" v-if="configData.source.topicType == 1 || typeof(configData.source.topicType) == 'undefined'">
            <label>卡片单词 （显示位置3）<em>&nbsp;&nbsp; * 每个文本框字符:≤3</em></label>
            需要标记为红色的字母，在下方勾选即可。
          </div>
          <div class="pic-title" v-else>
            <p class="title">-每组可设置1-4张图片</p>
            <p class="title">-可设置2-4组</p>
          </div>
          <label v-if="typeof(configData.source.topicType) != 'undefined'">
            <div class="c-well content-list">
              <span>题干类型<em>*</em></span>
              <label class="inline-label" for="topicType"><input type="radio" name="topicType" value="1" v-model="configData.source.topicType"> 文字</label>
              <label class="inline-label" for="topicType"><input type="radio" name="topicType" value="2" v-model="configData.source.topicType"> 图片</label>
            </div>
          </label>
          <!-- 文字类型 -->
          <div class="words-list" v-if="configData.source.topicType == 1 || typeof(configData.source.topicType) == 'undefined'">
            <div class="c-well" v-for="(item, index) in configData.source.options">{{index+1}}、
              <span class="dele-btn" @click="delOption(item)" v-show="configData.source.options.length>2"></span>
              <ul class="c-input-List">
              <li v-for="(arr, i) in item.wordsList.letter">
                <input type="text" class='c-input-words' placeholder="" v-model="arr.w" maxlength="3"  :class="{ red: item.wordsList.color[i].isRed ,borderColor:item.color[i]==i}">&nbsp;<em>{{i>=item.wordsList.letter.length-1?'=':'+'}}</em>

                <input type="checkbox" class="c-input-color" name="" v-model="item.wordsList.color[i].isRed">标红
              </li>
                <span class="words">{{spellWords(index)}}</span>
              </ul>
            <!-- 语音评测 -->
              <!-- <div class="field-wrap record"> -->
              <!--item.text为空或者全为空格时  禁用语音测评 -->
                <!-- <input type="checkbox" :id="'record-upload-'+index" class="c-input-record" name="" v-model="item.recordStatus" :disabled='item.words== ""||new RegExp("^[ ]+$").test(item.words)'>启用语音评测 -->
                <!-- <div class="timeBox"> -->
                  <!-- 录音时长&nbsp;<input type="number" class='c-input-time' placeholder="" v-model="item.timeLong" min="3" max="20"  :disabled="item.recordStatus!=true">&nbsp;秒							 -->
                <!-- </div> -->
              <!-- </div> -->
            </div>
            <button v-if="configData.source.options.length<10" type="button" class="add-btn" @click="addOption" >+</button>
          </div>
          <!-- 图片类型 -->
          <div class="pic-list" v-else>
            <div class="c-well" v-for="(item,index) in configData.source.optionsData">
              <div class="field-wrap">
                <div class="add-field-content">
                  <label class="field-label"  for="">第{{index+1}}组</label>
                  <span class="dele-tg-btn" v-on:click="delOptionImg(item)" v-show="configData.source.optionsData.length>2"></span>
                </div>

                <div class="img-list" v-for="(arr, i) in item.picsList.letter">
                  <span class='txt-info'>题干图片{{i+1}}</em>
                  <label :for="'content-pic-'+index+i" class="btn btn-show upload" v-if="!arr.w">上传</label>
                  <label :for="'content-pic-'+index+i" class="btn upload re-upload" v-if="arr.w!=''?true:false">重新上传</label>
                  <span class='txt-info'><em>&nbsp;&nbsp;尺寸：296*352 大小:≤30KB </em></span>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1" :id="'content-pic-'+index+i" @change="imageUpload($event,arr,'w',30)">


                  <div class="img-preview" v-if="arr.w">
                    <img v-bind:src="arr.w" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="arr.w=''">删除</span>
                    </div>
                  </div>
                </div>

              </div>
            </div>
            <button type="button" class="add-tg-btn" v-show="configData.source.options.length<10" v-on:click="addOptionImg">+</button>
          </div>
        </div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/bg.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
