@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import "../../common/template/disconnectRecover/style.scss";

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;

  .desc {
    top: 0.6rem;
  }

  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.container {
  background-size: auto 100%;
  position: relative;

  .content-main {
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;

    .speaker {
      position: absolute;
      width: 0.6rem;
      height: 0.6rem;
      top: 0.4rem;
      left: 6.27rem;
      background-image: url('../image/laba.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
    }

    .speaker-json {
      position: absolute;
      width: 0.6rem;
      height: 0.6rem;
      top: 0.1rem;
      left: 6.27rem;
      display: none;
    }

    .progress-container {
      margin: 0 auto;
      width: 5.2rem;
      height: 0.36rem;
      position: absolute;
      top: 0.53rem;
      left: 7.17rem;
      background-image: url('../image/progressa.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 6;

      .progress-img {
        position: absolute;
        width: 0.74rem;
        height: 0.69rem;
        top: -0.26rem;
        right: -0.35rem;
        background-image: url('../image/progressb.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 6;
      }

      .progress-content {
        position: absolute;
        width: 5.08rem;
        height: 0.24rem;
        left: 0.06rem;
        top: 0.06rem;
        border-radius: 0.12rem;
        overflow: hidden;

        .progress-bar {
          height: 100%;
          width: 0%;
          background-color: #FFD02F;
          transition: width 0.3s;
          border-radius: 0.12rem;
        }
      }
    }

    .lanes {
      box-sizing: border-box;
      position: relative;
      height: calc(100% - 2.02rem);
      margin-top: 1.6rem;
      margin-bottom: 0.42rem;
    }

    .lane {
      height: calc(100% / 3);
      position: relative;
    }

    .lane-flash {
      animation: lane-flash-animation 0.5s;
    }

    @keyframes lane-flash-animation {

      0%,
      100% {
        // background-color: transparent;
        background: rgba(255, 0, 0, 0.08);
      }

      50% {
        background: rgba(255, 0, 0, 0.25);
        /* 红色透明背景 */
      }
    }

    .dog {
      position: absolute;
      width: 2.6rem;
      height: 2.9rem;
      left: 1.5rem;
      /* 位置将在JS中动态设置 */
      z-index: 10;
      transition: top 0.5s ease;
    }

    .dog.running {
      animation: running 0.5s infinite alternate;
    }

    @keyframes running {
      0% {
        transform: translateY(0);
      }

      100% {
        transform: translateY(-0.1rem);
      }
    }

    .dog.shake {
      animation: shake 0.5s;
    }

    @keyframes shake {

      0%,
      100% {
        transform: translateX(0);
      }

      10%,
      30%,
      50%,
      70%,
      90% {
        transform: translateX(-0.1rem);
      }

      20%,
      40%,
      60%,
      80% {
        transform: translateX(1rem);
      }
    }

    .fruit {
      width: 3.8rem;
      height: 2.5rem;
      position: absolute;
      right: -3.8rem;
      background-repeat: no-repeat;
      background-position: center;
      // background-attachment: fixed;  /* 防止滚动抖动 */
      background-size: cover;
      will-change: transform;
      cursor: pointer;
      transition: transform 0.3s;
      z-index: 9;
    }

    .fruit-A {
      width: 3.8rem;
      height: 2.5rem;
      display: none;
    }

    .fruit:hover {
      transform: scale(1.1);
    }

    .fruit.wrong {
      animation: wobble 0.5s;
    }

    @keyframes wobble {

      0%,
      100% {
        transform: translateX(0);
      }

      20% {
        transform: translateX(-0.2rem);
      }

      40% {
        transform: translateX(0.2rem);
      }

      60% {
        transform: translateX(-0.1rem);
      }

      80% {
        transform: translateX(0.1rem);
      }
    }

    .right-mask {
      position: absolute;
      width: 2rem;
      height: 100%;
      top: 0rem;
      right: 0rem;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 10;
    }

    .explosion {
      position: absolute;
      width: 3.8rem;
      height: 2.5rem;
      z-index: 20;
    }

    @keyframes explode {
      0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
      }

      100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
      }
    }

    .game-over {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 36px;
      z-index: 30;
      display: none;
    }

    .restart-btn {
      margin-top: 0.2rem;
      padding: 0.1rem 0.3rem;
      font-size: 20px;
      background-color: #FF5722;
      color: white;
      border: none;
      border-radius: 0.5rem;
      cursor: pointer;
    }
  }
}

.funcMask {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 21;
  display: none;

  .startBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 7.02rem;
    width: 10.5rem;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 0.5rem;

    .demoTextBox {
      width: 9.26rem;
      height: 3.9rem;
      position: absolute;
      left: 50%;
      top: 0.64rem;
      transform: translate(-50%);
      border-radius: 0.3rem;
      background: rgba(0, 0, 0, 0.2);
      text-align: center;

      .startMsg {
        width: 6.75rem;
        height: 2.88rem;
        margin-top: 0.4rem;
      }
    }

    .demoBtnBox {
      width: 5.72rem;
      height: 1.14rem;
      transform: translate(-50%);
      // background: rgba(0, 0, 0, 0.2);
      text-align: center;
      position: absolute;
      left: 50%;
      bottom: 0.75rem;

      .demo-btnStu {
        width: 2.14rem;
        height: auto;
        cursor: pointer;
        display: inline-block;
      }

      .startBtn {
        width: 2.03rem;
        height: auto;
        cursor: pointer;
        display: inline-block;
        margin-right: 0;
        //  margin-left: 1.55rem;
      }
    }
  }

  .timeChangeBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 4.8rem;
    width: 7.2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.5rem;

    .timeBg {
      width: 3.79rem;
      height: 3.84rem;
      position: absolute;
      top: 1rem;
      background: url(../image/timeBg.png) no-repeat;
      background-size: 100% 100%;
      left: 50%;
      margin-left: -1.9rem;
      top: 50%;
      margin-top: -1.92rem;

      .numberList {
        width: 1.5rem;
        height: 1.5rem;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        top: 0;
        margin: auto;
        background: url(../image/number1.png) no-repeat;
        background-size: 6rem 100%;
        background-position-x: 0.1rem;
      }
    }
  }
}
