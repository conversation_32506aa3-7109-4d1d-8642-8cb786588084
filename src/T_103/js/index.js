"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, CLASS_STATUS } from "../../common/js/constants.js";
import { createTeaToast } from "../../common/template/disconnectRecover/index.js";

const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "2", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };
  let classStatus = 0; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }

  let options = configData.source.options, //卡牌内容
    fruitMoveSpeed = Number(configData.source.fruitMoveSpeed), //卡牌速度
    parkourCharacter = configData.source.parkourCharacter, //跑步人物和右侧遮罩图
    retreatArr = configData.source.retreatArr, //后退元素
    userType =
      window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师

  let audioJson = null; //音频动画
  let dogJson = null; //小狗动画
  let dogXiaoJson = null; //小狗笑动画
  let dogKuJson = null; //小狗哭动画
  let explosionJson = null; //爆炸动画
  let retreatAnimationId; //后退元素动画id

  // 计算元素在跑道中垂直居中的位置
  function calculateCenteredPosition(lane, elementHeight) {
    const lanesHeight = $(".lanes").height();
    const laneHeight = Math.floor(lanesHeight / 3);
    return lane * laneHeight + (laneHeight - elementHeight) / 2;
  }

  // 游戏变量
  let currentRound = 0;
  let correctCount = 0;
  let userSelected = false;
  let userStatus = false;
  let audio = new Audio();
  let currentRoundCorrect = false; // 新增：标记当前回合是否已选择正确

  // 游戏速度参数（毫秒），控制水果从右向左移动的速度，数值越小速度越快
  // let fruitMoveSpeed = 8000;
  isShowMask();
  function isShowMask() {
    if (isSync) {
      const userType = window.frameElement
        ? window.frameElement.getAttribute("user_type")
        : "";
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == 0 && userType == "stu") {
        // not in class && student
        $(".funcMask").show();
      }
    } else {
      $(".funcMask").show();
    }
    //右侧遮罩图
    if (parkourCharacter[0].modalImg) {
      $(".right-mask").css({
        "background-image": `url(${parkourCharacter[0].modalImg})`,
      });
    } else {
      $(".right-mask").css({
        display: "none",
      });
    }
    // 遍历 retreatArr 数组
    $.each(retreatArr, function (index, item) {
      // 创建 <img> 元素（暂不设置 src，避免因加载过快错过事件）
      var $img = $("<img>", {
        alt: "",
        class: "retreat",
        id: "retreat" + index,
      });

      // 监听图片加载完成事件
      $img.on("load", function () {
        // 获取图片原始宽高
        let naturalWidth = this.naturalWidth; // 原始宽度（像素）
        let naturalHeight = this.naturalHeight; // 原始高度（像素）

        // 将宽高转换为 1/100 rem（例如：100px → 1rem）
        $img.css({
          width: naturalWidth / 100 + "rem",
          height: naturalHeight / 100 + "rem",
        });
      });

      // 设置图片路径（必须在绑定 load 事件后设置 src，确保事件生效）
      $img.attr("src", item.img);

      // 设置绝对定位坐标（单位默认像素 px）
      $img.css({
        position: "absolute",
        left: item.x ? item.x / 100 + "rem" : "0rem",
        top: item.y ? item.y / 100 + "rem" : "0rem",
        display: "block",
      });
      $(".container").append($img);
    });
  }
  // 开始游戏
  $(".startBtn").on("click touchstart", function (e) {
    console.log("startBtnclick");
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("sycnStartBtnClick");
      return;
    }
    SDK.bindSyncEvt({
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "sycnStartBtnClick",
      recoveryMode: "1",
    });
  });

  $(".startBtn").on("sycnStartBtnClick", function () {
    console.log("sycnStartBtnClick");
    threeTwoOne();
    SDK.setEventLock();
  });
  //通过控制器点击start
  SDK.actAuthorize = function (message) {
    console.log("jf actAuthorize", message, isSync);
    if (isSync) {
      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        if (userType == USER_TYPE.TEA) {
          console.log("jf 老师开始");
          SDK.setEventLock();
          $(".startBtn").trigger("click");
          SDK.setEventLock();
        }
      }
    }
  };
  //倒计时
  function threeTwoOne() {
    console.log("jf threeTwoOne");
    let q = 1;
    $(".funcMask").show();
    $(".startBox").hide().siblings(".timeChangeBox").show().find(".numberList");
    SDK.playRudio({
      index: $(".timeLowAudio_" + q).get(0),
      syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
    });
    let audioPlay = setInterval(function () {
      q++;
      if (q > 4) {
        //开始播放小狗动画
        lottieAnimations.play(dogJson);
        clearInterval(audioPlay);
        SDK.setEventLock();
        $(".funcMask").hide();
        $(".timeChangeBox").hide();
        setTimeout(() => {
          startRound();
          retreatAnimation();
        }, 500);
      } else {
        SDK.playRudio({
          index: $(".timeLowAudio_" + q).get(0),
          syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
        });
        $(".numberList").css({
          "background-position-x": -(1.5 * (q - 1)) + "rem",
        });
      }
    }, 1000); // @WARNING
  }

  //音频动画
  audioFun();
  async function audioFun() {
    audioJson = await lottieAnimations.init(
      audioJson,
      "./image/laba.json",
      "#speaker-json",
      true
    );
    lottieAnimations.play(audioJson);
    audioJson.addEventListener("complete", function microphoneAnimation() {
      console.log("麦克风动画结束");
      lottieAnimations.stop(audioJson);
    });
  }
  //小狗动画
  dogFun(1);
  async function dogFun(status) {
    if (parkourCharacter[0].parkourImg) {
      dogJson = await lottieAnimations.init(
        dogJson,
        parkourCharacter[0].parkourImg,
        "#dog",
        true
      );
    } else {
      dogJson = await lottieAnimations.init(
        dogJson,
        "./image/dog.json",
        "#dog",
        true
      );
    }
    if (status == 1) {
      lottieAnimations.stop(dogJson);
    } else {
      lottieAnimations.play(dogJson);
    }

    dogJson.addEventListener("complete", function microphoneAnimation() {
      console.log("小狗动画结束");
      lottieAnimations.stop(dogJson);
    });
  }
  //小狗动画笑
  // dogXiaoFun();
  async function dogXiaoFun() {
    if (parkourCharacter[0].parkourImgXiao) {
      dogXiaoJson = await lottieAnimations.init(
        dogXiaoJson,
        parkourCharacter[0].parkourImgXiao,
        "#dog",
        false
      );
    } else {
      dogXiaoJson = await lottieAnimations.init(
        dogXiaoJson,
        "./image/dog-xiao.json",
        "#dog",
        false
      );
    }
    lottieAnimations.play(dogXiaoJson);
    dogXiaoJson.addEventListener("complete", function microphoneAnimation() {
      console.log("小狗笑动画结束");
      lottieAnimations.stop(dogXiaoJson);
      lottieAnimations.destroy(dogXiaoJson);
    });
  }
  //小狗动画哭
  // dogKuFun();
  async function dogKuFun() {
    if (parkourCharacter[0].parkourImgKu) {
      dogKuJson = await lottieAnimations.init(
        dogKuJson,
        parkourCharacter[0].parkourImgKu,
        "#dog",
        true
      );
    } else {
      dogKuJson = await lottieAnimations.init(
        dogKuJson,
        "./image/dog-ku.json",
        "#dog",
        true
      );
    }
    lottieAnimations.play(dogKuJson);
    dogKuJson.addEventListener("complete", function microphoneAnimation() {
      console.log("小狗哭动画结束");
      lottieAnimations.stop(dogKuJson);
      lottieAnimations.destroy(dogKuJson);
    });
  }
  //爆炸动画
  async function explosionFun() {
    explosionJson = await lottieAnimations.init(
      explosionJson,
      "./image/fruit-ribbon.json",
      "#explosion",
      false
    );
    lottieAnimations.play(explosionJson);
    explosionJson.addEventListener("complete", function microphoneAnimation() {
      console.log("爆炸动画结束");
      lottieAnimations.stop(explosionJson);
      lottieAnimations.destroy(explosionJson); //销毁动画
    });
  }
  // 播放当前回合的音频
  async function playCurrentAudio() {
    $(".speaker-json").css("display", "block");
    $(".speaker").css({
      "background-image": "url('./image/laba.png')",
      display: "none",
    });
    $(".speaker-audio").attr("src", options[currentRound].audio);
    SDK.playRudio({
      index: $(".speaker-audio")[0],
      syncName: $("speaker-audio").attr("data-syncaudio"),
    });
    // audio.src = options[currentRound].audio;
    // audio.loop = true; // 设置音频循环播放
    // audio.play();
  }

  // 停止音频播放
  function stopAudio() {
    $(".speaker-audio").attr("loop", "");
    $(".speaker-audio")[0].pause();
    $(".speaker-audio")[0].currentTime = 0;
    // audio.loop = false;
    // audio.pause();
    // audio.currentTime = 0;
    $(".speaker").css({
      "background-image": "url('./image/laba.png')",
      display: "block",
    });
    $(".speaker-json").css("display", "none");
  }

  // 开始回合
  function startRound() {
    let fruitAHeight = $(".fruit-A").height();
    let fruitAWidth = $(".fruit-A").width();
    if (currentRound >= options.length) {
      // 如果到达数组末尾但能量条未满，重新开始循环数据
      currentRound = 0;
    }

    userSelected = false;
    userStatus = false;
    currentRoundCorrect = false;

    // 播放音频
    playCurrentAudio();

    // 显示水果
    const currentData = options[currentRound];
    const fruitKeys = ["fruitA", "fruitB", "fruitC"];
    for (let i = 0; i < 3; i++) {
      const fruitKey = fruitKeys[i];
      const fruit = currentData[fruitKey];
      const $fruit = $("<div>")
        .addClass("fruit")
        .css({
          "background-image": `url(${fruit})`,
          top: calculateCenteredPosition(i, fruitAHeight) + "px", // fruitAHeight为水果高度
          right: -fruitAWidth + "px", // 与CSS保持一致
        })
        .attr("data-syncactions", "syncfruit" + i)
        .data("lane", i)
        .data("correct", fruitKey == currentData.correctAnswer); // fruitA 是正确选项

      $(".lanes").append($fruit);

      // 水果移动动画
      $fruit.animate(
        { right: $(".lanes").width() + "px" },
        fruitMoveSpeed,
        "linear",
        function () {
          $(this).remove();
          if (!userSelected) {
            moveDog(1); // 默认回到中间跑道
            stopAudio(); // 停止当前回合的音频
            // nextRound();
            if ($(".fruit").length === 0) {
              // 如果所有水果都移出屏幕且未选择正确，重新开始当前回合
              setTimeout(function () {
                startRound();
              }, 300);
            }
          }
        }
      );

      // 如果是错误图片，计算何时会经过小狗位置并触发抖动
      if (!$fruit.data("correct")) {
        const dogPosition = fruitAWidth / 1.4; // 小狗的left位置
        const windowWidth = $(".lanes").width();
        const fruitLane = i; // 水果所在跑道

        // 只有当水果与小狗在同一跑道时才计算
        $fruit.data("dogShakePending", true); // 标记为等待触发抖动

        const fruitWidth = $fruit.width();
        // 计算水果到达小狗位置的时间
        // 考虑水果的宽度，确保水果的左侧到达小狗位置时触发
        const timeToReachDog =
          ((windowWidth - dogPosition - fruitWidth) / windowWidth) *
          fruitMoveSpeed;
          //提前一秒禁止点击
          setTimeout(() => {
            userSelected = true;
            userStatus = true;
          }, timeToReachDog - 1000);
        // 设置定时器触发小狗抖动
        setTimeout(function () {
          // 检查水果是否仍在页面上，以及是否在小狗所在跑道
          if (
            $fruit.parent().length &&
            $fruit.data("dogShakePending") &&
            $fruit.data("lane") == $(".dog").data("currentLane")
          ) {
            // 小狗动画哭
            lottieAnimations.destroy(dogJson);
            dogKuFun();
            // 停止音频播放
            stopAudio();
            // 播放撞击音频
            SDK.playRudio({
              index: $(".shake-audio")[0],
              syncName: $(".shake-audio").attr("data-syncaudio"),
            });
            const $dog = $(".dog");
            $dog.removeClass("running").addClass("shake");
            setTimeout(function () {
              $dog.removeClass("shake").addClass("running");
              // 小狗动画哭结束，恢复跑酷动画
              lottieAnimations.destroy(dogKuJson);
              dogFun(2);
              // 小狗抖动后，设置userSelected为true，禁止继续点击
              userSelected = true;

              // 等待所有水果移出屏幕后进入下一轮
              const checkInterval = setInterval(function () {
                if ($(".fruit").length === 0) {
                  clearInterval(checkInterval);
                  // nextRound();
                  setTimeout(function () {
                    startRound();
                  }, 300);
                }
              }, 500);
            }, 500);
            $fruit.data("dogShakePending", false); // 标记为已触发抖动
          }
        }, timeToReachDog);
      }

      // 水果点击事件
      $fruit.on("click touchstart", function (e) {
        if (userSelected || userStatus) return;

        if (e.type == "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation();
        if (!isSync) {
          $(this).trigger("syncFruitClick");
          return;
        }

        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "syncFruitClick",
          funcType: "audio",
        });
      });

      $fruit.on("syncFruitClick", function () {
        if (userSelected || userStatus) return;
        userSelected = true;

        const lane = $(this).data("lane");
        const isCorrect = $(this).data("correct");

        moveDog(lane);

        if (isCorrect) {
          // 正确选择
          correctCount++;
          currentRoundCorrect = true;
          updateProgress();

          // 获取正确水果的位置
          const $fruit = $(this);
          const fruitPosition = {
            left: $fruit.position().left,
            top: $fruit.position().top,
          };
          // 小狗动画笑
          setTimeout(() => {
            lottieAnimations.destroy(dogJson);
            dogXiaoFun();
          }, 300);
          // 停止音频播放
          stopAudio();
          // 创建爆炸效果在水果当前位置
          explosionFun();
          //播放正确音频
          SDK.playRudio({
            index: $(".explosion-audio")[0],
            syncName: $(".explosion-audio").attr("data-syncaudio"),
          });
          const $explosion = $(".explosion");
          $explosion.css({
            left: fruitPosition.left + "px",
            top: fruitPosition.top + "px",
          });

          // 小狗动画笑结束，恢复跑酷动画
          setTimeout(function () {
            lottieAnimations.destroy(dogXiaoJson);
            dogFun(2);
          }, 800);
          // 停止当前动画，原地爆炸
          $fruit.data("dogShakePending", false); // 取消触发抖动的标记
          $fruit.stop().remove();

          // 添加标记表示已选择正确
          // userSelected = true;

          // 让其他水果继续移动出屏幕
          const $otherFruits = $(".fruit");

          // 检查是否所有非正确水果都已离开屏幕
          const checkInterval = setInterval(function () {
            if ($(".fruit").length == 0) {
              clearInterval(checkInterval);
              setTimeout(function () {
                nextRound(); // 即使是最后一轮正确也会先调用nextRound
                SDK.setEventLock();
              }, 200);
            }
          }, 300);
        } else {
          // 错误选择
          const $wrongFruit = $(this);
          $wrongFruit.addClass("wrong");


          // 小狗移动到对应跑道
          const $dog = $(".dog");

          // 获取小狗所在跑道并添加闪烁效果
          const dogLane = $dog.data("currentLane");
          const $lane = $(`#lane${dogLane}`);
          $lane.addClass("lane-flash");
          // 停止音频播放
          stopAudio();
          //播放错误音频
          SDK.playRudio({
            index: $(".lane-flash-audio")[0],
            syncName: $(".lane-flash-audio").attr("data-syncaudio"),
          });
          $(".lane-flash-audio").one("ended", () => {
            console.log("错误音频结束");
            // 开始音频播放
            playCurrentAudio();
          });
          setTimeout(function () {
            $lane.removeClass("lane-flash");
            // 重置userSelected状态，允许继续点击其他水果
            userSelected = false;
            SDK.setEventLock();
          }, 500);

          // // 设置检查水果是否全部离开屏幕的定时器
          // const checkInterval = setInterval(function () {
          //   if ($(".fruit").length == 0) {
          //     clearInterval(checkInterval);
          //     stopAudio(); // 停止当前回合的音频
          //     nextRound();
          //     SDK.setEventLock();
          //   }
          // }, 500);
        }
      });
    }
  }

  // 移动小狗到指定跑道
  function moveDog(lane) {
    // 将小狗垂直居中于对应跑道
    const dogHeight = $(".dog").height(); // 小狗图片高度
    const topPosition = calculateCenteredPosition(lane, dogHeight);
    $(".dog").data("currentLane", lane); // 记录当前小狗所在跑道
    $(".dog").css("top", topPosition + "px");
  }

  // 更新进度条
  function updateProgress() {
    const progress = (correctCount / options.length) * 100;
    $(".progress-bar").css("width", progress + "%");

    // 如果达到3次正确，不立即结束游戏，等待所有水果移出屏幕
    // 最后一轮游戏结束的逻辑将在nextRound函数中处理
  }

  // 下一回合
  function nextRound() {
    // 只有在当前回合选择正确时才增加回合数
    if (currentRoundCorrect) {
      currentRound++;
    }

    // 移除剩余水果
    $(".fruit").stop().remove();

    setTimeout(function () {
      // 检查是否已经获得3次正确
      if (correctCount >= options.length) {
        // 能量条已满，游戏结束
        endGame();
      } else {
        // 能量条未满，继续下一回合
        // moveDog(1); // 回到中间跑道
        startRound();
      }
    }, 20);
  }

  // 游戏结束
  function endGame() {
    $(".fruit").stop().remove();
    stopAudio(); // 确保音频停止
    $(".dog").removeClass("running");
    $(".dog").css("left", "calc(50% - 1.3rem)");
    moveDog(1); // 移动到中间跑道(索引1)
    cancelAnimationFrame(retreatAnimationId);
    feedback();
  }

  SDK.memberChange = function (message) {
    if (message.role === USER_TYPE.STU && message.state === 'enter') {
      let disconnectText = "The student  has been disconnected on this page.<br/> You need to click the 'Refresh' button to start over."
      // 学生重连
      createTeaToast(disconnectText)
      SDK.reportTrackData({
        action: 'PG_FT_INTERACTION_RECONNECTION',
        data: {},
      },USER_TYPE.TEA)
    }
  }

  //正反馈动画
  async function feedback() {
    console.log("全部正确结束，执行反馈动画");
    await feedbackAnimation("feedKey1");
  }
  // // 重置游戏
  // function resetGame() {
  //   currentRound = 0;
  //   correctCount = 0;
  //   userSelected = false;
  //   $(".progress-bar").css("width", "0%");

  //   // 将小狗放到中间跑道
  //   $(".dog").addClass("running");
  //   moveDog(1); // 移动到中间跑道(索引1)
  //   $(".fruit").remove();
  // }

  // 页面加载完成后，将小狗放在中间跑道
  moveDog(1); // 移动到中间跑道(索引1)

  // 初始化小狗数据
  $(".dog").data("currentLane", 1);
  function retreatAnimation() {
    // container容器 在这个元素中播放动画
    const $container = $(".container");

    // 获取容器宽度（以rem为单位）
    const containerWidth = $container.width() / window.base;

    // 定义1像素对应的rem值，用于消除轮播间的缝隙
    const onePixelInRem = 1 / window.base;

    // 存储每个后退元素的轮播容器和速度信息
    const retreatElements = [];

    // 为每个后退元素创建轮播容器
    retreatArr.forEach((item, index) => {
      const $element = $("#retreat" + index);

      // 从DOM中移除原始元素，后续会添加到新创建的轮播容器中
      $element.detach();

      // 获取后退速度，先取item的值，再取全局值，最后使用默认值1
      // 速度系数降低，使移动更平滑
      const speed = (item.retreatSpeed || 2) * 0.02;

      // 创建两个相同的轮播容器（一个在视图内，一个在右侧待命）
      for (let i = 0; i < 2; i++) {
        // 创建与container同样大小的div容器
        const $carouselDiv = $("<div>", {
          class: `retreat-carousel retreat-${index}-${i}`,
          css: {
            position: "absolute",
            left: i === 0 ? "0rem" : containerWidth - onePixelInRem + "rem", // 第二个容器减去1像素宽度避免缝隙
            top: "0",
            width: containerWidth + "rem",
            height: "100%",
          },
        });

        // 复制元素并调整位置
        const $clonedElement = $element.clone();
        // 保持元素在容器中的相对位置
        $clonedElement.css({
          left: (item.x ? item.x / 100 : 0) + "rem",
          top: (item.y ? item.y / 100 : 0) + "rem",
        });

        // 将复制的元素添加到轮播容器中
        $carouselDiv.append($clonedElement);

        // 将轮播容器添加到主容器中
        $container.append($carouselDiv);

        // 记录元素信息
        retreatElements.push({
          $carousel: $carouselDiv,
          left: i === 0 ? 0 : containerWidth - onePixelInRem,
          speed: speed,
          index: index,
          carouselId: i,
        });
      }
    });

    // 记录上一帧的时间戳
    let lastTimestamp = null;

    // 动画函数
    function animate(timestamp) {
      // 请求下一帧动画
      retreatAnimationId = requestAnimationFrame(animate);

      // 计算时间增量
      if (!lastTimestamp) {
        lastTimestamp = timestamp;
        return;
      }

      const deltaTime = timestamp - lastTimestamp;
      lastTimestamp = timestamp;

      // 确保时间增量在合理范围内，避免页面不活跃时的大跳变
      if (deltaTime > 100) {
        return;
      }

      // 按照元素索引分组，确保同一元素的两个轮播容器一起处理
      const elementGroups = {};
      retreatElements.forEach((element) => {
        if (!elementGroups[element.index]) {
          elementGroups[element.index] = [];
        }
        elementGroups[element.index].push(element);
      });

      // 分组处理每个元素的轮播容器
      Object.values(elementGroups).forEach((group) => {
        // 获取同一元素的两个轮播容器
        const container1 = group[0];
        const container2 = group[1];

        // 计算新位置
        container1.left -= container1.speed * (deltaTime / 16);
        container2.left -= container2.speed * (deltaTime / 16);

        // 更新DOM位置
        container1.$carousel.css("left", container1.left + "rem");
        container2.$carousel.css("left", container2.left + "rem");

        // 无缝衔接处理：当一个容器完全离开视口时，将其重置到另一个容器的右侧
        if (container1.left + containerWidth <= 0) {
          container1.left = container2.left + containerWidth - onePixelInRem; // 减去1像素避免缝隙
          container1.$carousel.css("left", container1.left + "rem");
        }

        if (container2.left + containerWidth <= 0) {
          container2.left = container1.left + containerWidth - onePixelInRem; // 减去1像素避免缝隙
          container2.$carousel.css("left", container2.left + "rem");
        }
      });
    }

    // 启动动画
    retreatAnimationId = requestAnimationFrame(animate);
  }
});
