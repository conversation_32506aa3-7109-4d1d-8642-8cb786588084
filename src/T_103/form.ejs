<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>TYPK0001FT_听音跑酷FT</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>" />
  <script src="./form/js/jquery-2.1.1.min.js"></script>
  <script src="./form/js/vue.min.js"></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">TYPK0001FT_听音跑酷FT</h3>

      <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>

          <!-- 上传后退元素 -->
          <div class="c-group">
            <div class="c-title">上传后退元素</div>
            <div class="c-area upload img-upload">
              <div class="c-well">
                <div v-for="(item,index) in configData.source.retreatArr" :key="index" style="margin-top: 20px">
                  <div class="field-wrap" style="display: flex">
                    <div>
                      <label class="field-label" style="width: 80px;margin: 0" for="">后退元素{{index +
                        1}}<em>*</em></label>
                    </div>
                    <div style="flex: 1">
                      <div class="field-wrap">
                        <label :for="'retreat'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
                        <label :for="'retreat'+index" class="btn upload re-upload" v-if="item.img">重新上传</label>
                        <span class="dele-tg-btn" v-on:click="deleRetreat(item)" v-show="index>0"></span>
                      </div>
                      <div style="margin-left: 10px">
                        <span class='txt-info'><em>JPG、PNG格式，小于等于60Kb</em></span>
                        <input type="file" v-bind:key="Date.now()+index" class="btn-file" :id="'retreat'+index" size=""
                          accept=".jpg,.png" @change="imageUpload($event,item,'img',60)">
                        <div style="margin-top: 10px">
                          <span>初始位置&nbsp;&nbsp;</span>
                          X:<input type="number" class="c-input-txt "
                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                            oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.x">
                          Y:<input type="number" class="c-input-txt "
                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                            oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.y">
                          <div><em>x:0-1920. y:0-1080</em></div>
                        </div>
                        <div style="margin-top: 10px">
                          <span>移动速度&nbsp;&nbsp;</span><input type="number" class="c-input-txt"
                            style="margin: 0 10px; width: 80px!important; display: inline-block;" min="1" max="10"
                            step="1" oninput="if(value>10)value=10;if(value<1)value=1"
                            v-model.number="item.retreatSpeed">
                          <div><em>速度范围: 1-10，数值越大速度越快</em></div>
                        </div>
                      </div>


                    </div>
                  </div>
                  <div class="img-preview" v-if="item.img">
                    <img :src="item.img" alt="" />
                    <div class="img-tools">
                      <span class="btn btn-delete" @click="delPrew(item,'img')">删除</span>
                    </div>
                  </div>
                </div>
                <button type="button" class="add-btn" style="margin: 20px auto 0;" v-on:click="addRetreat"
                  v-show="configData.source.retreatArr.length < 6">添加后退元素
                </button>
              </div>
            </div>
          </div>

          <!-- 轮次设置 -->
          <div class="c-group">
            <div class="c-title">轮次设置</div>
            <div class="c-area upload img-upload">
              <ul>
                <li v-for="(item,index) in configData.source.options">
                  <div class="c-well">
                    <!-- 内容图片 -->
                    <div class="field-wrap">
                      <div class="add-field-content">
                        <label class="field-label" for="">第{{ index + 1 }}轮</label>
                        <span class="dele-tg-btn" v-on:click="delOption(item)"
                          v-show="configData.source.options.length>=4"></span>
                      </div>

                      <div class="field-wrap">
                        <label class="field-label" style="width: 80px">上传音频<em>*</em></label>
                        <input type="file" accept=".mp3,.wav" :id="'content-audio-'+index" volume="50"
                          v-bind:key="Date.now()" class="btn-file" v-on:change="audioUpload($event,item,'audio')">
                        <label :for="'content-audio-'+index" class="btn btn-show upload" v-if="!item.audio">上传音频</label>
                        <div class="audio-preview" v-show="item.audio">
                          <div class="audio-tools">
                            <p v-show="item.audio">{{item.audio}}</p>
                          </div>
                          <span class="play-btn" v-on:click="play($event)">
                            <audio v-bind:src="item.audio"></audio>
                          </span>
                        </div>
                        <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.audio"
                          @click="item.audio=''">删除</label>
                        <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
                        <div class="audio-tips">
                          <label>
                            <span><em>支持mp3格式，小于等于50Kb</em></span>
                          </label>
                        </div>
                      </div>

                      <div class="field-wrap">
                        <label class="field-label" style="width: 80px">第一道<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <label :for="'content-pic-one-'+index" class="btn btn-show upload"
                          v-if="!item.fruitA">上传图片</label>
                        <label :for="'content-pic-one-'+index" class="btn upload re-upload"
                          v-if="item.fruitA!=''?true:false">重新上传</label>
                        <label for="twomodule" class="inline-label">
                          正确答案<input type="radio" :name="'correctAnswer' + index" value="fruitA" v-model="item.correctAnswer"
                            style="margin-left: 10px;">
                        </label>
                        <div class="audio-tips">
                          <label>
                            <span><em>JPG、PNG格式，图片尺寸420x340，小于等于50KB</em></span>
                          </label>
                        </div>
                        <input type="file" v-bind:key="Date.now()" class="btn-file" size="420*340"
                          accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-one-'+index"
                          @change="imageUpload($event,item,'fruitA',50)" />

                        <div class="img-preview" v-if="item.fruitA">
                          <img v-bind:src="item.fruitA" alt="" />
                          <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="item.fruitA=''">删除</span>
                          </div>
                        </div>
                      </div>

                      <div class="field-wrap">
                        <label class="field-label" style="width: 80px">第二道<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <label :for="'content-pic-two-'+index" class="btn btn-show upload"
                          v-if="!item.fruitB">上传图片</label>
                        <label :for="'content-pic-two-'+index" class="btn upload re-upload"
                          v-if="item.fruitB!=''?true:false">重新上传</label>
                        <label for="twomodule" class="inline-label">
                          正确答案<input type="radio" :name="'correctAnswer' + index" value="fruitB" v-model="item.correctAnswer"
                            style="margin-left: 10px;">
                        </label>
                        <div class="audio-tips">
                          <label>
                            <span><em>JPG、PNG格式，图片尺寸420x340，小于等于50KB</em></span>
                          </label>
                        </div>
                        <input type="file" v-bind:key="Date.now()" class="btn-file" size="420*340"
                          accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-two-'+index"
                          @change="imageUpload($event,item,'fruitB',50)" />

                        <div class="img-preview" v-if="item.fruitB">
                          <img v-bind:src="item.fruitB" alt="" />
                          <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="item.fruitB=''">删除</span>
                          </div>
                        </div>
                      </div>

                      <div class="field-wrap">
                        <label class="field-label" style="width: 80px">第三道<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                        <label :for="'content-pic-three-'+index" class="btn btn-show upload"
                          v-if="!item.fruitC">上传图片</label>
                        <label :for="'content-pic-three-'+index" class="btn upload re-upload"
                          v-if="item.fruitC!=''?true:false">重新上传</label>
                        <label for="twomodule" class="inline-label">
                          正确答案<input type="radio" :name="'correctAnswer' + index" value="fruitC" v-model="item.correctAnswer"
                            style="margin-left: 10px;">
                        </label>
                        <div class="audio-tips">
                          <label>
                            <span><em>JPG、PNG格式，图片尺寸420x340，小于等于50KB</em></span>
                          </label>
                        </div>
                        <input type="file" v-bind:key="Date.now()" class="btn-file" size="420*340"
                          accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-three-'+index"
                          @change="imageUpload($event,item,'fruitC',50)" />

                        <div class="img-preview" v-if="item.fruitC">
                          <img v-bind:src="item.fruitC" alt="" />
                          <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="item.fruitC=''">删除</span>
                          </div>
                        </div>
                      </div>

                    </div>
                    <br>
                  </div>
                </li>
              </ul>
              <button type="button" class="add-btn"
                v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            audio: '',
            fruitA: '',
            fruitB: '',
            fruitC: '',
            correctAnswer: 'fruitA'
          })">
                添加轮次（最多12轮）
              </button>
            </div>
          </div>

          <!-- 速度设置 -->
          <div class="c-group">
            <div class="c-title">移动速度<em>*</em></div>
            <div class="c-area upload img-upload">
              <div class="field-wrap">
                <div class="field-wrap">
                  <label class="rules-field-label">点击后<em>*</em></label>
                  <select class="select-c" id="teachTime" v-model="configData.source.fruitMoveSpeed">
                    <option name="optive" value="12000">1</option>
                    <option name="optive" value="11000">2</option>
                    <option name="optive" value="10000">3</option>
                    <option name="optive" value="9000">4</option>
                    <option name="optive" value="8000">5</option>
                    <option name="optive" value="7000">6</option>
                    <option name="optive" value="6000">7</option>
                    <option name="optive" value="5000">8</option>
                    <option name="optive" value="4000">9</option>
                    <option name="optive" value="3000">10</option>
                  </select>
                  <div class="audio-tips">
                    <label>
                      <span><em>数字越大移动越快</em></span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 跑酷人物 -->
          <div class="c-group">
            <div class="c-title">跑酷人物</div>
            <div class="c-area upload img-upload">
              <ul>
                <li v-for="(item,index) in configData.source.parkourCharacter">
                  <div class="c-well">
                    <!-- 跑酷人物 -->
                    <div class="field-wrap">
                      <label class="field-label">跑酷人物&nbsp;&nbsp;&nbsp;&nbsp;</label>
                      <label :for="'content-pic-parkourImg'+index" class="btn btn-show upload"
                        v-if="!item.parkourImg">上传图片</label>
                      <label :for="'content-pic-parkourImg'+index" class="btn upload re-upload"
                        v-if="item.parkourImg!=''?true:false">重新上传</label>

                      <input type="file" v-bind:key="Date.now()" class="btn-file" size="520*580" accept=".json"
                        isKey="1" :id="'content-pic-parkourImg'+index"
                        @change="imageUpload($event,item,'parkourImg',240)" />

                      <div class="img-preview" v-if="item.parkourImg">
                        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
                        <div class="img-tools">
                          <span class="btn btn-delete" v-on:click="item.parkourImg=''">删除</span>
                        </div>
                      </div>
                      <div class="audio-tips">
                        <label>
                          <span><em>不上传展示默认形象，json格式</em></span>
                          <span><em>尺寸不超过520x580，小于等于240KB</em></span>
                        </label>
                      </div>
                    </div>
                    <!-- 跑酷人物 笑 -->
                    <div class="field-wrap">
                      <label class="field-label">跑酷人物笑</label>
                      <label :for="'content-pic-parkourImg-xiao'+index" class="btn btn-show upload"
                        v-if="!item.parkourImgXiao">上传图片</label>
                      <label :for="'content-pic-parkourImg-xiao'+index" class="btn upload re-upload"
                        v-if="item.parkourImgXiao!=''?true:false">重新上传</label>

                      <input type="file" v-bind:key="Date.now()" class="btn-file" size="520*580" accept=".json"
                        isKey="1" :id="'content-pic-parkourImg-xiao'+index"
                        @change="imageUpload($event,item,'parkourImgXiao',240)" />

                      <div class="img-preview" v-if="item.parkourImgXiao">
                        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
                        <div class="img-tools">
                          <span class="btn btn-delete" v-on:click="item.parkourImgXiao=''">删除</span>
                        </div>
                      </div>
                      <div class="audio-tips">
                        <label>
                          <span><em>不上传展示默认形象，json格式</em></span>
                          <span><em>尺寸不超过520x580，小于等于240KB</em></span>
                        </label>
                      </div>
                    </div>

                    <!-- 跑酷人物 哭 -->
                    <div class="field-wrap">
                      <label class="field-label">跑酷人物哭</label>
                      <label :for="'content-pic-parkourImg-ku'+index" class="btn btn-show upload"
                        v-if="!item.parkourImgKu">上传图片</label>
                      <label :for="'content-pic-parkourImg-ku'+index" class="btn upload re-upload"
                        v-if="item.parkourImgKu!=''?true:false">重新上传</label>

                      <input type="file" v-bind:key="Date.now()" class="btn-file" size="520*580" accept=".json"
                        isKey="1" :id="'content-pic-parkourImg-ku'+index"
                        @change="imageUpload($event,item,'parkourImgKu',240)" />

                      <div class="img-preview" v-if="item.parkourImgKu">
                        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
                        <div class="img-tools">
                          <span class="btn btn-delete" v-on:click="item.parkourImgKu=''">删除</span>
                        </div>
                      </div>
                      <div class="audio-tips">
                        <label>
                          <span><em>不上传展示默认形象，json格式</em></span>
                          <span><em>尺寸不超过520x580，小于等于240KB</em></span>
                        </label>
                      </div>
                    </div>

                    <!-- 右侧遮罩图 -->
                    <div class="field-wrap">
                      <label class="field-label">右侧遮罩图</label>
                      <label :for="'content-pic-modalImg'+index" class="btn btn-show upload"
                        v-if="!item.modalImg">上传图片</label>
                      <label :for="'content-pic-modalImg'+index" class="btn upload re-upload"
                        v-if="item.modalImg!=''?true:false">重新上传</label>

                      <input type="file" v-bind:key="Date.now()" class="btn-file" size="1920*1080"
                        accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-modalImg'+index"
                        @change="imageUpload($event,item,'modalImg',120)" />

                      <div class="img-preview" v-if="item.modalImg">
                        <img v-bind:src="item.modalImg" alt="" />
                        <div class="img-tools">
                          <span class="btn btn-delete" v-on:click="item.modalImg=''">删除</span>
                        </div>
                      </div>
                      <div class="audio-tips">
                        <label>
                          <span><em>jpg、PNG格式，尺寸小于等于1920x1080，小于等于120KB</em></span>
                        </label>
                      </div>
                    </div>

                  </div>
                </li>
              </ul>
            </div>
          </div>

          <!-- 正反馈 -->
          <% include ./src/common/template/feedbackAnimation/form %>

            <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="" />
        </div>
        <ul class="show-txt">
          <li>图片格式：<em></em>JPG/PNG</li>
          <li>声音格式：<em></em>MP3</li>
          <li>视频格式：<em></em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src="./form/js/form.js?_=<%= Date.now() %>"></script>

</html>
