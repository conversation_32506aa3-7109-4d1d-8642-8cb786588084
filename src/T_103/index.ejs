<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="TYPK0001FT_听音跑酷FT"; %>
    <%include ./src/common/template/index_head %>
</head>

<body>
  <div class="container" id="container" data-syncresult="1">
    <!-- 学生重连老师弹窗 -->
    <%include ./src/common/template/disconnectRecover/index.ejs %>

    <!-- 反馈动效 -->
    <%include ./src/common/template/feedbackAnimation/index.ejs %>
      <section class="commom">
        <div class="desc"></div>
        <div class="title">
          <h3></h3>
        </div>
      </section>
      <div class="content-main">
        <div class="speaker"></div>
        <div class="speaker-json" id="speaker-json">
          <audio class="speaker-audio" loop="loop" src="" data-syncaudio="speaker-audio"></audio>
        </div>
        <!-- 进度条 -->
        <div class="progress-container">
          <div class="progress-img"></div>
          <div class="progress-content">
            <div class="progress-bar"></div>
          </div>
        </div>
        <!-- 进度条音频 -->
        <!-- <audio class="progress-audio" src="./audio/energyBar.wav" data-syncaudio="progress-audio"></audio> -->

        <div class="lanes">
          <div class="lane" id="lane0"></div>
          <div class="lane" id="lane1"></div>
          <div class="lane" id="lane2"></div>
          <div class="dog running" id="dog"></div>
          <div class="explosion" id="explosion"></div>
          <audio class="explosion-audio" src="./audio/explosion-audio.mp3" data-syncaudio="explosion-audio"></audio>
          <audio class="lane-flash-audio" src="./audio/lane-flash-audio.mp3" data-syncaudio="lane-flash-audio"></audio>
          <audio class="shake-audio" src="./audio/shake-audio.mp3" data-syncaudio="shake-audio"></audio>
        </div>

        <div class="fruit-A"></div>
        <div class="right-mask"></div>
      </div>

      <!-- 功能遮罩层 -->
      <div class="funcMask">
        <!-- 预习模式操作区 -->
        <div class="startBox">
          <div class="demoTextBox">
            <img
              src="./image/4dfeec15c2cd5d502bf53095ec38b9ea.png"
              class="startMsg"
            />
          </div>
          <div class="demoBtnBox">
            <img
              src="./image/0250983386ad8a2c2226cac7b83be49e.png"
              class="startBtn"
              data-syncactions="sycnStartBtnClick"
            />
          </div>
        </div>
        <!-- 倒计时 -->
        <div class="timeChangeBox hide">
          <div class="timeBg">
            <div class="numberList"></div>
            <audio
              src="./audio/timeLow.mp3"
              class="timeLowAudio_1"
              data-syncaudio="timeLowAudio_1"
            ></audio>
            <audio
              src="./audio/timeLow.mp3"
              class="timeLowAudio_2"
              data-syncaudio="timeLowAudio_2"
            ></audio>
            <audio
              src="./audio/timeLow.mp3"
              class="timeLowAudio_3"
              data-syncaudio="timeLowAudio_3"
            ></audio>
            <audio
              src="./audio/timehigh.mp3"
              class="timeLowAudio_4"
              data-syncaudio="timeLowAudio_4"
            ></audio>
          </div>
        </div>
      </div>

      <script type="text/javascript">
        document.documentElement.addEventListener(
          "touchstart",
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault();
            }
          },
          false
        );
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener(
          "touchend",
          function (event) {
            var now = Date.now();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
      </script>
  </div>

  <%include ./src/common/template/index_bottom %>
    <%include ./src/common/template/lottie %>
</body>

</html>
