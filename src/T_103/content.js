var configData = {
  bg: "./assets/images/bg.png",
  desc: "",
  title: "",
  tImg: "./image/b164806a1b826b1b386f5496478573fe.png",
  tImgX: 1340,
  tImgY: 15,
  tg: [
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "1111111111111111111",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
  ],
  level: {
    high: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
    low: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
  },
  source: {
    retreatArr: [
      {
        img: './assets/images/fruit1.png',
        x: '1400',
        y: '300',
        retreatSpeed: 1 //后退元素速度
      },
      {
        img: './assets/images/fruit2.png',
        x: '600',
        y: '500',
        retreatSpeed: 1 //后退元素速度
      },
      {
        img: './assets/images/fruit3.png',
        x: '150',
        y: '500',
        retreatSpeed: 2 //后退元素速度
      }
    ],
    optionLength: 12, //选项上限
    options: [
      {
        audio: "./audio/correct.mp3",
        fruitA: "./assets/images/fruit1.png",
        fruitB: "./assets/images/fruit2.png",
        fruitC: "./assets/images/fruit3.png",
        correctAnswer: "fruitA",
      },
      {
        audio: "./audio/fruit.mp3",
        fruitA: "./assets/images/fruit3.png",
        fruitB: "./assets/images/fruit2.png",
        fruitC: "./assets/images/fruit1.png",
        correctAnswer: "fruitB",
      },
    ],
    fruitMoveSpeed: 8000, //水果移动速度
    parkourCharacter: [
      {
        parkourImg: "./image/dog.json", //跑酷人物
        parkourImgXiao: "./image/dog-xiao.json", //跑酷人物 笑
        parkourImgKu: "./image/dog-ku.json", //跑酷人物 哭
        modalImg: "./assets/images/right-mask.png", //右侧遮罩图
      },
    ],
  },
  feedbackLists: [
    {
      positiveFeedback: "-1",
      feedbackList: [
        { id: "-1", json: "", mp3: "" },
        { id: "0", json: "./image/prefect.json", mp3: "./audio/prefect.mp3" },
        { id: "1", json: "./image/goldCoin.json", mp3: "./audio/goldCoin.mp3" },
        { id: "2", json: "./image/FKJB.json", mp3: "./audio/resultWin.mp3" },
        { id: "9", json: "./image/guang.json", mp3: "" },
        {
          id: "10",
          json: "./image/chest-change.json",
          mp3: "./audio/chest-change.mp3",
        },
      ],
      feedbackObj: { id: "9", json: "./image/guang.json", mp3: "" },
      feedback: "./image/prefect.json",
      feedbackAudio: "./audio/prefect.mp3",
      feedbackName: "整体反馈",
      background: "0",
      loop: false,
      key: "feedKey1",
    },
  ],
};
(function (pageNo) {
  configData.page = pageNo;
})(0);
