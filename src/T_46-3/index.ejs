<!DOCTYPE html>
<html lang="en">

<head>
        <% var title="THV0001FT_大小声FT"; %>
        <%include ./src/common/template/index_head %>
</head>

<body>
    <div class="container" id="container" data-syncresult='show-result-1'>
        <section class="commom">
            <div class="desc"></div>
            <div class="title">
                <h3></h3>
            </div>
        </section>

        <section class="main">
            <audio src="./audio/high.mp3" class="audioMp3_0" data-syncaudio="audioHigh"></audio>
            <audio src="./audio/middle.mp3" class="audioMp3_1" data-syncaudio="audioMiddle"></audio>
            <audio src="./audio/low.mp3" class="audioMp3_2" data-syncaudio="audioLow"></audio>
            <!-- 显示器 -->
            <div class="tv">
                <div class="tv-child"></div>
                <audio src="" class="tv-audio" data-syncaudio="tv-audio"></audio>
                <div class="list"></div>
                <div class="pageNum">
                    <span class="page">1</span>
                    <span>/</span>
                    <span class="num"></span>
                </div>
            </div>
            <!-- 声音区 -->
            <div class="volumn">
                <div class="lineup"></div>
                <div class="linedown"></div>
                <div class="control">
                    <span class="high" data-syncactions='high'><em class="highChoose"></em></span>
                    <span class="middle" data-syncactions='middle'><em class="middleChoose"></em></span>
                    <span class="low" data-syncactions='low'><em class="lowChoose"></em></span>
                </div>
            </div>
            <ul class="role">
                <li class="low hide"></li>
            </ul>
            <!-- 选择按钮 -->
            <div class="chooseBtns">
                <div class="choose initPre" data-class="choosePre" data-syncactions="pre"></div>
                <div class="choose initGreat" data-class="chooseGreat" data-syncactions="great"></div>
                <div class="choose initGood" data-class="chooseGood" data-syncactions="good"></div>
                <!-- <div class="monkey"></div> -->
            </div>
            <div class="leftBtn leftBtnDisable" data-syncactions="leftBtn"></div>
            <div class="rightBtn" data-syncactions="rightBtn"></div>
            <!-- <div class="voice"></div> -->
        </section>

    </div>
    <%include ./src/common/template/index_bottom %>
</body>

</html>
