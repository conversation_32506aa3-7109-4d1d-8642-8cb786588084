@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';


// #__vconsole .vc-switch {
//   right: auto !important; /* 覆盖默认的右侧定位 */
//   left: 0;                 /* 设置左侧位置为0 */
//   top: auto !important;                  /* 设置顶部位置为0 */
// }
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.container{
	position: relative;
}
audio{
	width: 0;
	height: 0;
	opacity: 0;
}
.main {
    width: 100%;
    height: 100%;
    position: relative;
}
.hide{
    opacity:0;
   
}
.mainArea{
    position: relative;
    height: 10.8rem;
    .ans{
        position: absolute;
        top:9.8rem;
        left: 50%;
        width: 9.36rem;
        height: 1rem;
        line-height: 1rem;
        margin-left: -4.68rem;
        background: rgba(255, 255, 255, 0.80);
        border-radius: .15rem;
        font-size: .3rem;
        text-align: center;
        font-weight: bold;
    }
    .handShake{
        position: absolute;
        width: 3.8rem;
        height: 3.2rem;
        left: 2.57rem;
        top: 4.38rem;
        // background:url('../image/handImg.png');
        background-size: 7.6rem 3.2rem;
    }
    .btnArea{
        position: absolute;
        top: 6.85rem;
        left: 6.38rem;
        width: 1.98rem;
        height: 1.07rem;
        .hands{
            position: absolute;
            width:1.8rem;
            height:1.8rem;
            left: 50%;
            top: 0.3rem;
            margin-left: -0.25rem;
            background: url('../image/hands.png');
            background-size: 7.2rem 1.8rem;
        }
    }
    .showStartPos{
        position: absolute;
        top: 4.25rem;
        left:6.46rem ;
        width: 1.82rem;
        height: 1.82rem;
        ul{
            .listPos{
                position: absolute;
                top: 0;
                left: -.19rem;
                width: 2.2rem;
                height: 2.2rem;
                padding: .11rem;
                box-sizing: border-box;
                overflow: hidden;
                img{
                    position: absolute;
                    // top:.78rem;
                    top:2.2rem;
                    left: 0;
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
    ul{
        .pic{
            position: absolute;
            width:2.2rem;
            height: 2.2rem;
        }
        .pic0{
            top:2.30rem;
            left: 13.15rem;
        }
        .pic1{
            top: 5.6rem;
            left: 9.9rem;
        }
        .pic2{
            top: 4.52rem;
            left: 12.1rem;
        }
        .pic3{
          top: 5.32rem; 
          left: 14.62rem; 
        }
        .pic4{
           top: 6.86rem;
           left: 12.42rem;
        }
    }

}


@keyframes hand {
    0%{
        background-position: 0 0;
    }
    100%{
        background-position:133% 0;
    }
}
@-webkit-keyframes hand {
    0%{
        background-position: 0 0;
    }
    100%{
        background-position:133% 0;
    }
}
@keyframes handShakes {
    0%{
        background-position: 0 0;
    }
    100%{
        background-position:200% 0;
    }
}
@-webkit-keyframes handShakes {
    0%{
        background-position: 0 0;
    }
    100%{
        background-position:200% 0;
    }
}
 //开始按钮
 .startBtn {
  display: none;
  position: absolute;
  bottom: 0.1rem;
  left: 50%;
  width: 2.7rem;
  height: 0.76rem;
  background: rgba(255, 255, 255, 1);
  border-radius: 0.38rem;
  transform: translate(-50%, 0px);
  cursor: pointer;

  .startContent {
      width: 2.54rem;
      height: 0.6rem;
      background: rgba(229, 171, 66, 1);
      border-radius: 0.3rem;
      margin: 0 auto;
      position: relative;
      top: 50%;
      transform: translate(0, -50%);
      font-size: 0.3rem;
      font-family: Century Gothic;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      line-height: 0.46rem;
      line-height: .6rem;
      text-align: center;
  }
}
// 麦克风动画
.animation-con {
position: absolute;
bottom: 0px;
width: 100%;

.record-con-tea {
    display: none;
    text-align: center;
    position: absolute;
    bottom: 2.51rem;
    width: 4rem;
    height: 0.88rem;
    left: 50%;
    transform: translate(-50%, 0px);
    background: rgba(0, 0, 0, 0.6);
    border-radius: 16px;
    font-size: 0.3rem;
    font-weight: 400;
    line-height: 0.88rem;
    color: #fff;
}

.record-con {
    display: none;
    position: absolute;
    bottom: 0;
    width: 4rem;
    height: 2.33rem;
    left: 50%;
    transform: translate(-50%, 0px);
    background: rgba(0, 0, 0, 0.6);
    border-radius: 40px 40px 0px 0px;
}

.processAni {
    background: transparent;
}

.waitAni {
    width: 3.48rem;
    height: 1.74rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0px);
    bottom: 0.3rem;
}

.playAni {
    width: 3.48rem;
    height: 1.74rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0px);
    bottom: 0.3rem;
}
}
// 评价动画
.finialAni {
  position: absolute;
  width: 100%;
  height: 100%;
  display: none;
  top: 0;
  left:0;
  z-index: 9999;
}

// 评价
.evaluateTip {
display: none;
width: 9.1rem;
height: 1rem;
border-radius: 0.16rem;
position: absolute;
left: 50%;
transform: translate(-50%, 0px);
bottom: 0;
background: rgba(255, 255, 255, 0.6);
font-size: .3rem;

.tip {
    height: 100%;
    width: 4.6rem;
    line-height: 1rem;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.evaluateBtn {
    position: absolute;
    top: .12rem;
    color: #fff;
    text-align: center;
    line-height: 0.45rem;
    border-radius: .38rem;
    border: #fff solid 0.08rem;
    height: 0.6rem;
    cursor: pointer;
}



.great-btn {
    background: #ED6E22;
    right: 2.68rem;
    width: 1.78rem;
    
}

.goodjob-btn {
    background: #f1a91e;
    right: 0.46rem;
    width: 1.84rem;
}
.disableBtn {
  background:#DDDDDD;
  cursor: not-allowed;
}
}

#playSyncbtn,
#stopPlaySyncbtn,
#showEvaluateSyncbtn {
  display: none;
}


