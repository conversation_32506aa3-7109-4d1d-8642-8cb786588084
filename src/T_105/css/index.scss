@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.container {
    background: url(../image/bg.png) no-repeat center;
    background-size: contain;

    .main {
        width: 100%;
        height: 100%;
        position: relative;
        .toys {
            position: absolute;
            left: 1.5rem;
            top: 1.7rem;
            width: 16.2rem;
            height: 5.4rem;

            .toys-list {
                width: 100%;
                height: 100%;

                li {
                    float: left;
                    width: 4rem;
                    height:2.5rem;
                    display: flex;
                    justify-content: center;
                    align-items: flex-end;
                    transition: .5s;
                    // background: red;
                    cursor: pointer;
                    img {
                        height:2rem;
                        width: 2.3rem;
                    }
                }
            }
        }

        .scale {
            transition: 1s cubic-bezier(.6, -1.2, .3, 1);
            transform: translateY(-.15rem) scale(1.3);
        }

        // .transition {
        //     transition: .5s;
        // }

        .car-scale {
            transform: scale(.7);
        }

        .cars {
            position: absolute;
            width: 100%;
            height: 2.6rem;
            bottom: 1rem;
            
            .road {
                position: relative;
                width: 100%;
                height: 50%;
                

                .car {
                    transition: left .5s;
                    width: 1.8rem;
                    height: 1.35rem;
                    position: absolute;
                    left: 0;
                    margin-top: -.3rem;
                    margin-left: 1.4rem;
                    cursor: pointer;
                }
                .car-animate {
                    animation: moveCar 1s;
                }
                @keyframes moveCar {
                    0% {
                        transform: scale(1);
                    }
                    50% {
                        transform: scale(.6);
                    }
                    100% {
                        transform: scale(1);
                    }
                }
            }

            .flag {
                position: absolute;
                right: 1.5rem;
                top: -.35rem;
                width: 1.37rem;
                height: 1.78rem;
                cursor: pointer;

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}
.mask {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: rgba(0,0,0,.8);
    display: none;
    .winImg {
        width: 5.94rem;
        height: 3.66rem;
        position: absolute;;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        background: url('../image/win.png') no-repeat;
        background-size: contain;
        transform: scale(0);
    }
    .max{
        animation: toMax .8s forwards;
    }
    @keyframes toMax {
        0% {
            transform: scale(0);
        }
        100% {
            transform: scale(1);
        }
    }
}