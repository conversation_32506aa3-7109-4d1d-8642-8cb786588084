@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/ribbonWin/style.scss';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
body{
	overflow: hidden;
}

.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	width: 11.86rem;
	height: 2.2rem;
	position: absolute;
    right: 0px;

	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
}
.container {
	background-size: auto 100%;
	position: relative;
}

.big{
	transform: scale(1.05);
}

.content {
	width: 16.4rem;
	height: 4.5rem;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
	margin-left: 1.4rem;
	position: absolute;
	bottom: .9rem;
}
.main {
	width: 100%;
	height: 100%;
	.stage{
		width: 100%;
		height: 100%;
		position: relative;
		left: 0;
		top: 0;
		.dis-area{
			position: absolute;
			top:0;
			right: 0;
			width: 11.3rem;
			height: 100%;
			background:rgba(255,255,255,.75);
			overflow: hidden;
			.dis-area-img{
				width: 100%;
				height: 100%;
				overflow: hidden;
				img{
					width: 100%;
					height: 100%;
				}
			}
			.title-text{
				position: relative;
				font-size: .38rem;
				height: 1rem;
				width: 6.2rem;
				color:#333;
				font-weight: bolder;
				margin: .45rem;
				overflow: hidden;
				p{
					width: 100%;
					position: absolute;
					left: 0;
					top: 0;
					font-size: .38rem;
					line-height: .48rem;
				}

			}
			.btns{
				display: none;
				width: .6rem;
				height: 1.69rem;
				position: absolute;
				right: .34rem;
				bottom: 0.16rem;
				border-radius: .3rem;
				// z-index: 21;
				overflow: hidden;
				box-shadow: 0 0 0.12rem rgba(0,0,0,0.25);
				background:#ffc600;
				em{
					display: block;
					float: left;
					height: 2px;
					width: 100%;
					background:#ffba00;
				}
				span{
					display: block;
					float: left;
					width: 100%;
					height: .83rem;
					background: #ffc600;
					position: relative;
					cursor: pointer;
					img{
						width: .6rem;
						position: absolute;
						left: .024rem;
					}
				}
				.upBtn{
					background:#fdd132;
					img{
						top:.22rem;
						opacity: .8;
					}
				}
				.downBtn{
					border-radius: 0 0 .3rem .3rem;
					img{
						bottom:.14rem;
					}
				}
			}
		}
		.ans-area{
			@include setEle(0.5,0.5rem,2.9rem,4.3rem);
			line-height: 2rem;
			border-radius: 0.3rem;
			overflow: hidden;
      cursor: pointer;
      text-align: center;
			img{
				position: absolute;
				left: 0;
				top: 0;
				right: 0;
				bottom: 0;
				margin: auto;
			}
		}
		.btn-area{
			width: 7.9rem;
			height: 100%;
		}
		.scale:hover img{
			transform: scale(1.05,1.05);
		}
		.ans-area:hover {
			border: 1px #fff dashed;
			box-shadow: 0 0 0.5rem rgba(253,253,30,0.9);
		}

		.ansBot-area{
			@include setEle(0.5rem,0.5rem,3.06rem,4.44rem);
			border-radius: 0.6rem;
			border: .02rem #fff dashed;
			display: none;
			// box-shadow: 0 0 0.14rem rgba(0,0,0,0.25);
    }
    .ans-revise{
      position: absolute;
      top: 0;
      left: 0;
      transform: translate(-50%,-50%);
    }
	}
	.photoMask{
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		display: none;
		.showMask{
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			// background: rgba(8, 8, 8, 0.7);
			z-index: 199;
		}
		.dis-area{
			overflow: hidden;
		}
	}

}
.resultMask {
	position: absolute;
	.winBook {
		overflow: hidden;
		position: absolute;
		top: 50%;
		left: 50%;
		width: 13.32rem;
		height: 8.42rem;
		transform: translate(-50%, -50%);
		background: url('../image/result_win.png');
		background-size: 100% 100%;
		.perfect{
			position: absolute;
			left: 0.45rem;
			bottom: 0.33rem;
			z-index: 101;
			width: 4.12rem;
			height: 2.72rem;
		}
		.result_real{
			position: absolute;
			top: 50%;
			left: 50%;
			width: 8.32rem;
			height: 6.59rem;
			transform: translate(-50%, -50%);
		}
	}
}
.doneTip{
    width: 7rem;
    height: 1rem;
	border-radius: 0.2rem .2rem 0 0;
	position: absolute;
	margin-left: -3.5rem;
	left:50%;
	bottom: 0;
	background: rgba(255,255,255,.7);
	display: none;
	z-index: 9;
	p{
		height: 100%;
		width: 2.9rem;
		line-height: 1rem;
		text-align: center;
		font-size: .24rem;
	}
	.btn {
		position: absolute;
		top: .2rem;
		height: .23rem;
		padding: 0.17rem 0.26rem;
		color:#fff;
		text-align: center;
		font-size: .24rem;
		line-height: 0.23rem;
		border-radius: .3rem;
		cursor: pointer;
	}
	.done-btn{
		left: 2.87rem;
		background: #ed6e21;
	}
	.hint-btn{
		background: #f1a91e;
		left: 4.9rem;
	}
	.middle-line{
		display: inline-block;
		position: absolute;
		top: .2rem;
		left: 4.40rem;
		width: 0.02rem;
		height: 0.64rem;
		background: #939393;
	}
}
.boxList {
	position: absolute;
	left: 1.08rem;
	top: .3rem;
	width: 17rem;
	height: 9.8rem;
	z-index: -1;
	.boxUl {
		position: absolute;
		left: .5rem;
		top: .7rem;
		width: 16rem;
		height: 8.4rem;
		display: flex;
		flex-wrap: wrap;
		li {
			// background: red;
			margin-left: .01rem;
			width: .39rem;
			height: .39rem;
		}
	}
}
.moveImg {
	position: absolute;
    left: 0;
    top: 0;
    width: .39rem;
	height: .39rem;
	z-index: 100;
}
@keyframes camStyle{
	0%{
		background: rgba(51, 51, 51, 1);
	}
	20%{
		background: rgba(255, 255, 255, 1);
	}
	40%{
		background: rgba(255, 255, 255, 0.8);
	}
	100%{
		background:rgba(51, 51, 51, 0.51);
	}
}
@-webkit-keyframes camStyle{
	0%{
		background: rgba(51, 51, 51, 1);
	}
	20%{
		background: rgba(255, 255, 255, 1);
	}
	40%{
		background: rgba(255, 255, 255, 0.8);
	}
	100%{
		background:rgba(51, 51, 51, 0.51);
	}
}
//2帧和3帧动画   添加边框和黄色阴影以及执行雪碧图动画（PC端有滑过效果 移动端没有）
.sprite-border-shadow {
	border: 1px #fff dashed;
	box-shadow: 0 0 0.5rem rgba(253,253,30,0.9);
}

.stage .sprite3Ani .ans-revise{
	animation: sprite3 1s steps(3) infinite;
}
.stage .sprite3:hover {
	animation: sprite3 1s steps(3) infinite;
	border: 1px #fff dashed;
	box-shadow: 0 0 0.5rem rgba(253,253,30,0.9);
}
@keyframes sprite3 {
	0%{
		background-position: 0% 0;
	}
	100%{
		background-position:150% 0;
	}
}
@-webkit-keyframes sprite3 {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:150% 0;
	}
}
.stage .sprite2Ani .ans-revise{
	animation: sprite2 1s steps(2) infinite;
}
.stage .sprite2:hover {
	animation: sprite2 1s steps(2) infinite;
	border: 1px #fff dashed;
	box-shadow: 0 0 0.5rem rgba(253,253,30,0.9);
}
@keyframes sprite2 {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:200% 0;
	}
}
@-webkit-keyframes sprite2 {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:200% 0;
	}
}
//小手点击动画
.hand{
	width:1.8rem;
	height:1.8rem;
	background: url('../image/hands.png');
	background-size: 7.2rem 1.8rem;
	position: absolute;
	animation: handClick 1s steps(4) infinite;
	opacity: 1;
	cursor: pointer;
	z-index: 20;
	display: none;
}
@keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}
@-webkit-keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}
* {
    touch-action: pan-y;
}
