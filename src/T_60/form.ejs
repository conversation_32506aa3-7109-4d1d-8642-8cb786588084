<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>TDR0005_拖拽_左右分屏</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src='./form/js/vue.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">TDR0005_拖拽_左右分屏</h3>

      <!-- 背景/描述 -->
      <div class="c-group">
        <div class="c-title">背景/标题</div>
        <!-- 上传图片 -->
        <div class="c-area upload img-upload radio-group">
          <div class="field-wrap">
            <label class="field-label" for="">上传左侧背景</label>
            <span class='txt-info'><em>*尺寸：789X1080。文件大小≤50KB</em></span>
            <input type="file" v-bind:key="Date.now()" class="btn-file" id="leftImg" size="789*1080"
              v-on:change="imageUpload($event,configData,'leftImg',50,-1)">
          </div>
          <div class="field-wrap">
            <label for="leftImg" class="btn btn-show upload" v-if="configData.leftImg==''?true:false">上传</label>
            <label for="leftImg" class="btn upload re-upload" v-if="configData.leftImg!=''?true:false">重新上传</label>
          </div>
          <div class="img-preview" v-if="configData.leftImg!=''?true:false">
            <img v-bind:src="configData.leftImg" alt="" />
            <div class="img-tools">
              <span class="btn btn-delete" v-on:click="configData.leftImg=''">删除</span>
            </div>
          </div>
        </div>
        <div class="c-area upload img-upload radio-group">
          <div class="field-wrap">
            <label class="field-label" for="">上传右侧背景</label>
            <span class='txt-info'><em>*尺寸：1134X1080。文件大小≤50KB</em></span>
            <input type="file" v-bind:key="Date.now()" class="btn-file" id="rightImg" size="1134*1080"
              v-on:change="imageUpload($event,configData,'rightImg',50,-1)">
          </div>
          <div class="field-wrap">
            <label for="rightImg" class="btn btn-show upload" v-if="configData.rightImg==''?true:false">上传</label>
            <label for="rightImg" class="btn upload re-upload" v-if="configData.rightImg!=''?true:false">重新上传</label>
          </div>
          <div class="img-preview" v-if="configData.rightImg!=''?true:false">
            <img v-bind:src="configData.rightImg" alt="" />
            <div class="img-tools">
              <span class="btn btn-delete" v-on:click="configData.rightImg=''">删除</span>
            </div>
          </div>
        </div>
        <!-- 描述 -->
        <div class="c-area">
          <label>大标题（显示位置1） 字符：≤20</label>
          <input type="text" class='c-input-txt' placeholder="请在此输入描述" v-model="configData.desc" maxlength='40'>
        </div>
      </div>

      <!-- 基础埋点 -->
      <div class="c-group">
        <div class="c-title">基础埋点</div>
        <div class="c-area ">
          <div class="field-wrap" style="display: none;">
            <label class="field-label" style="width: 100px;">教学环节</label>
            <select id="teachPart" v-model="configData.log.teachPart" style="width: 150px;">
              <option name="optive" value="-1">请选择</option>
              <option v-for="item in teachInfo" name="optive" :value="item.id">{{item.process_name}}</option>
            </select>
          </div>
          <div class="field-wrap">
            <label class="field-label" style="width: 100px;">建议教学时长</label>
            <select id="teachTime" v-model="configData.log_editor.isTeachTimeOther" style="width: 150px;">
              <option name="optive" value="-1">请选择</option>
              <option name="optive" value="30">0.5分钟</option>
              <option name="optive" value="60">1分钟</option>
              <option name="optive" value="90">1.5分钟</option>
              <option name="optive" value="120">2分钟</option>
              <option name="optive" value="150">2.5分钟</option>
              <option name="optive" value="180">3分钟</option>
              <option name="optive" value="-2">其他</option>
            </select>
            <br>
            <div class="field-wrap" v-show="configData.log_editor.isTeachTimeOther=='-2'">
              <label class="field-label" style="width: 100px;"></label>
              <input type="number" v-model="configData.log_editor.TeachTimeOtherM" style="width: 50px;"> 分
              <input type="number" v-model="configData.log_editor.TeachTimeOtherS" style="width: 50px;"> 秒(整数)
            </div>
          </div>
          <div class="field-wrap" style="display: none;">
            <label class="field-label" style="width: 100px;">题目属性</label>
            <label class="field-label" style="width: 150px;" v-if="configData.log.tplQuestionType=='2'">客观判断正误</label>
            <select style="width: 150px;" v-model="configData.log.tplQuestionType" v-else>
              <option name="optive" value="-1">请选择</option>
              <option name="optive" value="0">无题目</option>
              <option name="optive" value="1">主观判断正误</option>
            </select>
          </div>
        </div>
      </div>

      <!-- TG -->
      <div class="c-group">
        <div class="c-title">添加TG</div>
        <div class="c-area">
          <div class="c-well" v-for="(item,index) in configData.tg">
            <div class="well-title">
              <p>TG {{index+1}}</p>
              <span class="dele-tg-btn" v-on:click="deleTg(item)" v-show="configData.tg.length>1"></span>
            </div>
            <div class="well-con">
              <label>标题</label>
              <input type="text" class='c-input-txt' placeholder="请在此输入TG标题" v-model="item.title">
              <label>内容 <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em></label>
              <textarea name="" cols="56" rows="2" placeholder="请在此输入TG内容" v-model="item.content"></textarea>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-on:click="addTg">+</button>
        </div>
      </div>
      <!-- level -->
      <div class="c-group">
        <div class="c-title">添加分层 H 教学</div>
        <div class="c-area">
          <div class="c-well" v-for="(item,index) in configData.level.high">
            <div class="well-title">
              <p>H {{index+1}}</p>
              <span class="dele-tg-btn" v-on:click="deleH(item)" v-show="configData.level.high.length>1"></span>
            </div>
            <div class="well-con">
              <label>标题</label>
              <input type="text" class='c-input-txt' placeholder="请在此输入 H 标题" v-model="item.title">
              <label>内容
                <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
              </label>
              <textarea name="" cols="56" rows="2" placeholder="请在此输入 H 内容" v-model="item.content"></textarea>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-on:click="addH">+</button>
        </div>
      </div>

      <div class="c-group">
        <div class="c-title">添加分层 L 教学</div>
        <div class="c-area">
          <div class="c-well" v-for="(item,index) in configData.level.low">
            <div class="well-title">
              <p>L {{index+1}}</p>
              <span class="dele-tg-btn" v-on:click="deleL(item)" v-show="configData.level.low.length>1"></span>
            </div>
            <div class="well-con">
              <label>标题</label>
              <input type="text" class='c-input-txt' placeholder="请在此输入 L 标题" v-model="item.title">
              <label>内容
                <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
              </label>
              <textarea name="" cols="56" rows="2" placeholder="请在此输入 L 内容" v-model="item.content"></textarea>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-on:click="addL">+</button>
        </div>
      </div>

      <!-- 选项元素 -->
      <div class="c-group">
        <div class="c-title">选项元素</div>
        <div class="c-area">
          <div class="c-well">
            <span>选项个数</span>
            <label class="inline-label" for="optionLength"><input type="radio" name="optionLength" value="4"
                v-model="configData.source.optionLength"> 2~4项 </label>
            <label class="inline-label" for="optionLength"><input type="radio" name="optionLength" value="6"
                v-model="configData.source.optionLength"> 5~6项</label>
          </div>
          <div class="c-well">
            <span>图片类型</span>
            <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="1"
                v-model="configData.source.imgType"> 静态图片</label>
            <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="2"
                v-model="configData.source.imgType"> 2帧雪碧图</label>
            <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="3"
                v-model="configData.source.imgType"> 3帧雪碧图</label>
          </div>
          <div class="c-well">
            <span>多帧图片播放次数</span>
            <input type="text" class="play-time" maxlength="2" v-model="configData.source.palyTime"> 次 (1～20,"#" 无限重复)
          </div>
        </div>
        <div class="c-area upload img-upload uploadImg">
          <div v-for="(item,index) in configData.source.options">
            <div class="c-well">
              <div class="field-wrap">
                <label class="field-label" for="">选项{{index+1}}</label>
                <span class="dele-tg-btn" v-on:click="delOption(item)"
                  v-show="configData.source.options.length>2"></span><br>
                <span class='txt-info'>上传图片
                  <label :for="'content-pic-'+index" class="btn btn-show upload"
                    v-if="item.img==''?true:false">上传</label>大小≤50KB</span>
                <input type="file" v-bind:key="Date.now()" class="btn-file" size="" :id="'content-pic-'+index"
                  @change="imageUpload($event,item,'img',50, index)">
                <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img">重新上传</label>
              </div>
              <div class="img-preview" v-if="item.img">
                <img v-bind:src="item.img" alt="" />
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                </div>
              </div>
            </div>
          </div>
          <br>
        </div>
        <button type="button" class="add-tg-btn"
          v-show="configData.source.options.length<configData.source.optionLength"
          v-on:click="addOption({img: ''})">+</button>
      </div>

      <!--提示功能  -->
      <div class="c-group">
        <div class="c-title">提示功能</div>
        <div class="c-area">
          <p>- 课中老师点击提示后，界面显示小手从“起始点”拖拽至“终点”。</p>
          <div class="c-well">
            <span>是否启用提示功能</span>
            <label class="inline-label" for="openTip"><input type="radio" name="openTip" value="1"
                v-model="configData.source.openTip"> 使用 </label>
            <label class="inline-label" for="openTip"><input type="radio" name="openTip" value="0"
                v-model="configData.source.openTip"> 不使用</label>
            <label class="dviding-line"></label>
            <div v-show="configData.source.openTip == 1">
              <p>根据标准网格，填写坐标点</p>
              <label>拖拽：起始点<em> * </em><input type="number" v-model="configData.source.start"> 1-800</label>
              <label>拖拽：终点<em> * </em><input type="number" v-model="configData.source.end"> 1-800</label>
              <label class="dviding-line"></label>
            </div>
          </div>
          <div class="field-wrap">
            <label class="field-label" for="">提示声音</label>
            <span class='txt-info'>大小：≤50KB</span>
            <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-audio"
              v-on:change="audioUpload($event,configData.source,'audio')">
            <label for="content-audio" class="btn btn-show upload" v-if="!configData.source.audio">上传</label>
            <div class="audio-preview" v-show="configData.source.audio">
              <div class="audio-tools">
                <p v-show="configData.source.audio">{{configData.source.audio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="configData.source.audio"></audio>
              </span>
            </div>
            <label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.audio"
              @click="configData.source.audio=''">删除</label>
            <label for="content-audio" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
          </div>
        </div>
      </div>


      <button class="send-btn" v-on:click="onSend">提交</button>

    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>

</html>
