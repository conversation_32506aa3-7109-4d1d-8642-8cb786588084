<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TDR0005_拖拽_左右分屏"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">
    <section class="commom">
        <div class="desc">123132</div>
    </section>

    <section class="main">
            <audio src="audio/drag.mp3" class="audioDrag" data-syncaudio="audioDrag"></audio>
            <audio src="audio/photo.mp3" class="audioPhoto" data-syncaudio="audioPhoto"></audio>
            <audio src="" class="audioMove" data-syncaudio="audioMove"></audio>
            <div class="stage">
                <div class="btn-area"></div>
                <div class="dis-area">
                    <div class='dis-area-img'></div>
                </div>

            </div>
            <div class="photoMask">
                <div class="showMask"></div>
            </div>
            <div class="boxList">
                <ul class="boxUl"></ul>
            </div>

            <div class="moveImg">
                <div class="hand"></div>
            </div>
    </section>
    <div class="doneTip" id="haveTip">
      <p>Student`s work: </p>
      <span class="btn done-btn" data-syncactions="doneBtn">Done</span>
      <span class="middle-line"></span>
      <span class="btn hint-btn" data-syncactions="hintBtn">Stu`s Hint</span>
    </div>
    <div class="doneTip" id="noTip">
      <p>Student`s work: </p>
      <span class="btn done-btn" data-syncactions="doneBtn">Done</span>
    </div>
    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
<% include ./src/common/template/ribbonWin/index.ejs %>
</div>
<script inline type="text/javascript" src="js/funParabola.js"></script>
<!-- 遮罩层 -->
<%include ./src/common/template/index_bottom %>
</body>
</html>
