// var domain = 'http://172.16.0.107:9011/pages/1152/';
var domain = '';

var Data = {
    configData: {
        bg: '',
        desc: '',
        leftImg: '',
        rightImg: '',
        sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
        tg: [{
            title: '',
            content: ''
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
            optionLength: 4, //选项上限
            imgType: 1, //1  静态图  2 2帧雪碧图  3  2帧雪碧图
            palyTime: 1, //播放次数
            openTip: 0, //1 启用  0 不启用
            start: 1, //起点
            end: 1, //终点
            audio: "", //提示声音
            options: [{
                    img: "",
                    natWidth: "",
                    natHeight: "",
                },
                {
                    img: "",
                    natWidth: "",
                    natHeight: "",
                }
            ]
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
};
//从CMS系统获取教学环节
function getTeachInfo() {
    $.ajax({
        type: "get",
        // url: "http://172.16.0.45:7300/mock/5bf11fb71d2cb328eddca64f/SmartCc/grabLeads",
        url: "https://studybag.51talkjr.com/api/cms/get_teach_process",
        async: false,
        success: function(res) {
            var res = JSON.parse(res)
            if (res.error_code == 0) {
                Data.teachInfo = res.ret.data;
            }
        },
        error: function(res) {
            console.log(res)
        }
    });
}
getTeachInfo();
$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    watch: {
        'configData.source.imgType': function() {
            this.configData.source.options = [{ img: "" }, { img: "" }];
        }
    },
    methods: {

        imageUpload: function(e, item, attr, fileSize, index) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }

            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                if (index != -1) {
                    var data = Data.configData.source
                    data.options[index].natWidth = naturalWidth
                    data.options[index].natHeight = naturalHeight
                }
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        sourceImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                alert("应上传图片大小为：" + size.join("或") + ", 上传图片尺寸为：" + data.width + "*" + data.height);
                item[attr] = "";
                // alert("图片尺寸不符合要求！");
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source;
            var checkMsg = false;
            var option6Arr = [5, 6];
            var palyTime = data.palyTime;
            if (!this.configData.leftImg) {
                checkMsg = "请上传左侧背景图";
            }
            if (!this.configData.rightImg) {
                checkMsg = "请上传右侧背景图";
            }
            if (!data.optionLength) {
                checkMsg = "未选择选项个数";
            }
            if (!data.imgType) {
                checkMsg = "未选择图片类型"
            }
            if (data.openTip == 1 && (!data.start || !data.end || data.start <= 0 || data.end <= 0 || data.end > 800)) {
                checkMsg = "请填写正确坐标";
            }
            if (data.optionLength < data.options.length) {
                checkMsg = "实际选项个数大于选择选项个数";
            }
            if (data.optionLength == 6 && option6Arr.indexOf(data.options.length) == -1) {
                checkMsg = "实际选项个数与所选选项个数不符";
            }
            for (let i of data.options) {
                if (!i.img) {
                    checkMsg = "有未上传图片的选项";
                    break;
                }
            }
            if (palyTime == "#") {

            } else {
                if (parseFloat(palyTime).toString() == "NaN") {
                    checkMsg = "请填写正确的播放次数";
                } else {
                    if (!palyTime || palyTime > 20 || palyTime < 0 || palyTime == 0) {
                        checkMsg = "请填写正确的播放次数";
                    }
                }

            }
            return checkMsg
        },
        onSend: function() {
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            var val = this.validate();
            if (!val) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                alert(val);
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            bg = arguments.length > 2 ? arguments[2] : null;
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    console.log(res.data.key);
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },
        audioUpload: function(e, item, attr) {
            //校验规则
            //var _type = this.rules.audio.sources.type;

            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            // if (!_type.test(type)) {
            //     alert("您上传的文件类型错误，请检查后再上传！");
            //     return;
            // }
            if ((size / 1024).toFixed(2) > 500) {
                console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            } else {
                console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            }
            if ((size / 1024).toFixed(2) > 50) {
                console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
                alert("您上传的声音大小为：" + (size / 1024).toFixed(2) + "KB, 超过50KB上限，请检查后上传！");
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            this.postData(file, item, attr);
        },
        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addOption: function(item) {
            if (item) {
                this.configData.source.options.push(item)
            } else {
                this.configData.source.options.push('')
            }
        },
        delOption: function(item) {
            this.configData.source.options.remove(item)
        },
        setAnswer: function(item) {
            this.configData.source.right = item;
        },
        inputPlayNum: function() {
            var reg = new RegExp("^([1-9][0-9]{0,1}|[2][0]{0,1})$");
            var regResult = reg.test(Data.configData.source.palyTime);
            if (!regResult && Data.configData.source.palyTime != "#") {
                Data.configData.source.palyTime = "";
            }
        }
    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
