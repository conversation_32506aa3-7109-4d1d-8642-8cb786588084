"use strict"
import '../../common/js/common_1v1.js'
import '../../common/js/drag.js'
import {
  resultWin,
  resultHide
} from '../../common/template/ribbonWin/index.js';
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  var h5SyncActions = parent.window.h5SyncActions;
  let staticData = configData.source;
  let seleList = staticData.options;
  let seleListLength = seleList.length;
  let childPosArr = []; //存储所有被选中的元素相对于拖拽目标场景的位置坐标
  let startPos = 1,
    endPos = 1;
  let palyTime = staticData.palyTime;
  if (palyTime === "#") {
    palyTime = "infinite"
  }
  var baseWidth = 3.31,baseHeight = 4.40;
  var reviseW = '', reviseH = '' ;
  if (seleListLength > 4) {
    baseWidth = 3.31
    baseHeight = 2.87;
  }
  //判断是否使用默认背景图片
  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/defaultBg.jpg)'
    })
  }
  //生成像素表格
  renderPx();

  function renderPx() {
    let liHtml = '';
    for (let i = 1; i < 801; i++) {
      liHtml += `
				<li class="pos_${i}"></li>	`
    }
    $('.boxUl').html(liHtml);
  }
  //添加提示语音
  tipsAudio();

  function tipsAudio() {
    $(".audioMove").attr("src", staticData.audio);
  }
  //定义小手初始位置
  handPosition();

  function handPosition() {
    if (configData.source.start && configData.source.end) {
      startPos = configData.source.start;
      endPos = configData.source.end;
      let left = ($('.pos_' + startPos).offset().left - $('.container').offset().left) / window.base + 'rem';
      let top = ($('.pos_' + startPos).offset().top - $('.container').offset().top) / window.base + 'rem';
      $('.moveImg').css({
        left: left,
        top: top
      });
    }
  }
  //根据imgType(静态还是雪碧) 和 optionLength 选项长度 初始化一些变量(雪碧图展示方式)

  function imgTypeInit(that) {
    if (staticData.imgType == 1) {
      that.find(".ans-revise").css({
        backgroundSize:  that.attr("bgsize"),
        backgroundPosition: '50% 50%',
        backgroundSize:'100% 100%',

      })
    }
    if (staticData.imgType == 2) {
      that.find(".ans-revise").css({
        backgroundSize: '200% 100%'
      })
    }
    if (staticData.imgType == 3) {
      that.find(".ans-revise").css({
        backgroundSize: '300% 100%'
      })
      // that.addClass("sprite3AniEnd")
    }
  }

  //根据imgType 添加样式
  function imgTypeStyle(that) {
    if (staticData.imgType == 2) {
      that.addClass("sprite2Ani").addClass("sprite-border-shadow");
    }
    if (staticData.imgType == 3) {
      that.addClass("sprite3Ani").addClass("sprite-border-shadow");
    }
    if (staticData.imgType == 1) {
      // return;
    }
  }
  //渲染左左侧背景图
  renderLeftBg();

  function renderLeftBg() {
    let leftImg = new Image();
    leftImg.src = configData.leftImg;
    leftImg.style.width = "100%";
    leftImg.style.height = "100%";
    $(".btn-area").append(leftImg);
  }
  //渲染右侧背景图
  renderRightBg();

  function renderRightBg() {
    let showImg = new Image();
    showImg.src = configData.rightImg;
    $('.dis-area-img').append(showImg);
  }
  //渲染选项区域
  renderOption();

  function renderOption() {
    let ansH = 4.3,
      ansBotH = 4.44;
    let pos = {
      l: 0.55,
      t: 0.4
    };
    let pos1 = {
      l: 0.55,
      t: 0.4
    };
    let ele = '',
      x, y, x1, y1, l_val = 3.8,
      t_val = 5;
    if (seleListLength > 4) {
      ansH = 2.74;
      ansBotH = 3;
      pos = {
        l: 0.5,
        t: 0.4
      };
      pos1 = {
        l: 0.5,
        t: 0.4
      };
      t_val = 3.5;
    }
    if(seleListLength <= 2) {
      pos = {
        l: 0.5,
        t: 3
      };
      pos1 = {
        l: 0.5,
        t: 3
      };
    }
    for (let i = 0, length = seleList.length; i < length; i++) {
      childPosArr.push({
        t: -1,
        l: -1
      })
      x = pos.l + (i % 2) * l_val;
      y = pos.t + (parseInt(i / 2)) * t_val;
      x1 = pos1.l + (i % 2) * l_val;
      y1 = pos1.t + (parseInt(i / 2)) * t_val;
      let scaleWh = seleList[i].natWidth/staticData.imgType/seleList[i].natHeight;
      let scaleW = '', scaleH = '';
      if(seleList[i].natWidth/staticData.imgType > seleList[i].natHeight) { //图片横屏
        reviseW = baseWidth;
        reviseH = baseWidth/scaleWh;
      } else { //竖屏
        reviseW = baseHeight*scaleWh;
        reviseH = baseHeight;
      }
      ele += '<div class="ansBot-area" data-index="item-' + i + '" style="left:' + x + 'rem;top:' + y + 'rem;  height:' + ansBotH + 'rem"></div>'+
      '<div class="ans-area scale" data-width="'+ reviseW +'" data-height="'+ reviseH +'" data_index= "' + i + '"data-syncactions=item-' + i + ' style="left:' + x1 + 'rem;top:' + y1 + 'rem;height:' + ansH + 'rem;"><div class="ans-revise" style="height:' + reviseH + 'rem;'+ 'width:'+ reviseW + 'rem;'  + 'margin-left:' + baseWidth/2 + 'rem;' + 'margin-top:' + reviseH/2 + 'rem;"></div><img src="' + seleList[i].img + '"/></div>'
    }
    $('.stage').append(ele);
    initSprite();
  }
  //是否提示
  function isTip(tea) {
    if (tea) {
      if (staticData.openTip == 1) {
        $("#haveTip").show();
      } else {
        $("#noTip").show().css({
          width: '4.6rem',
          marginLeft: '-2.3rem'
        });
      }
    } else {
      $(".doneTip").hide();
    }

  }
  //判断是学生还是老师显示底部按钮区域
  isShowBtn();

  function isShowBtn() {
    if (isSync) {
      if (staticData.openTip == 1) {
        btnStatus = true;
      }
      if (window.frameElement && window.frameElement.getAttribute('user_type') == 'tea') {
        isTip('tea');
      } else {
        isTip();
      }
    } else {
      // $(".doneTip").show();
      var hrefParam = parseURL("http://www.example.com");
      if (top.frames[0] && top.frames[0].frameElement) {
        hrefParam = parseURL(top.frames[0].frameElement.src)
      }
      var role_num = hrefParam.params['role']

      function parseURL(url) {
        var a = document.createElement('a')
        a.href = url
        return {
          source: url,
          protocol: a.protocol.replace(':', ''),
          host: a.hostname,
          port: a.port,
          query: a.search,
          params: (function () {
            var ret = {},
              seg = a.search.replace(/^\?/, '').split('&'),
              len = seg.length,
              i = 0,
              s
            for (; i < len; i++) {
              if (!seg[i]) {
                continue;
              }
              s = seg[i].split('=')
              ret[s[0]] = s[1]
            }
            return ret
          })(),
          file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
          hash: a.hash.replace('#', ''),
          path: a.pathname.replace(/^([^\/])/, '/$1'),
          relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
          segments: a.pathname.replace(/^\//, '').split('/')
        }
      }
      if (role_num == '1' || role_num == undefined) {
        isTip('tea');
      } else if (role_num == '2') {
        isTip();
      }
    }
  }

  //判断拖入区图片的大小
  $(".dis-area-img img").on("load", function () {
    let dranAreaW = $(this).get(0).naturalWidth,
      dranAreaH = $(this).get(0).naturalHeight;
    $('.dis-area-img').attr("data_width", dranAreaW).attr("data_height", dranAreaH);
  });
  //图片尺寸大小判断
  $(".ans-area img").on("load", function () {
    contrImgSize.call(this);
  });
  //图片自适应
  function contrImgSize() {
    var _this = $(this),
      imgWidth = _this.get(0).naturalWidth,
      imgHeight = _this.get(0).naturalHeight,
      containerScale = imgWidth / imgHeight; //容器的宽高比

    if (containerScale < 1) { //瘦高型
      if (imgHeight / 100 > baseHeight) {
        _this.css({
          height: '100%',
          width: '100%'
        });
        _this.data("size", {
          width: '100%',
          height: '100%'
        }) //记录图片开始位置尺寸
        if(staticData.imgType == 1) {
          _this.parent('.ans-area').find(".ans-revise").css({
            backgroundSize: 'auto 100%',
            backgroundPosition: '50% 50%',

          }).attr("bgsize",'auto 100%')
        }
      } else {
        _this.css({
          height: imgHeight / 100 + "rem",
          width: '100%'
        });
        _this.parent('.ans-area').find(".ans-revise").css({
          height: imgHeight / 100 + "rem",
          width: '100%'
        });
        _this.data("size", {
          width: '100%',
          height: imgHeight / 100 + "rem"
        })
        if(staticData.imgType == 1) {
          _this.parent('.ans-area').css({
            backgroundSize: 'auto auto',
            backgroundPosition: '50% 50%'
          }).attr("bgsize",'auto auto')
        }
      }
      _this.parent('.ans-area').css({
        width: baseWidth + "rem",
        height:baseHeight + "rem",
      });
      _this.parent('.ans-area').data("size", {
        width: imgWidth,
        height: imgHeight
      }) //记录图片本身尺寸，用于拖动后父容器重新调整大小
    } else { //胖矮型
      if (imgWidth / 100   > baseWidth) {
        _this.css({
          width: '100%',
          height: 'auto'
        });
        _this.data("size", {
          width: '100%',
          height: 'auto'
        })
        if(staticData.imgType == 1) {
          _this.parent('.ans-area').css({
            backgroundSize: '100% auto',
            backgroundPosition: '50% 50%'
          }).attr("bgsize",'100% auto')
        }
      } else {
        _this.css({
          width: imgWidth / 100 + "rem",
          height: 'auto'
        });
        _this.data("size", {
          width: imgWidth / 100 + "rem",
          height: 'auto'
        })
        if(staticData.imgType == 1) {
          _this.parent('.ans-area').css({
            backgroundSize: 'auto auto',
            backgroundPosition: '50% 50%'
          }).attr("bgsize",'auto auto')
        }
      }
      _this.parent('.ans-area').css({
        width: baseWidth + "rem",
        height:baseHeight + "rem",
      });
      _this.parent('.ans-area').data("size", {
        width: imgWidth,
        height: imgHeight
      })
    }

    // if(staticData.imgType == 1) {
    //   _this.parent('.ans-area').css({
    //     backgroundSize: 'cover',
    //     backgroundPosition: '50% 50%'
    //   })
    // }
  };
  //初始化雪碧图
  function initSprite() {
    $(".ans-area").each(function () {
      var eleImg = $(this).find("img");
      eleImg.hide();
      $(this).find(".ans-revise").css({
        'background': 'url(' + eleImg.attr("src") + ')',
        'background-repeat': 'no-repeat',
      });
      $(this).removeClass("sprite-border-shadow");
      imgTypeInit($(this));
      if(staticData.imgType == 1) {
        $(this).find(".ans-revise").css({
          backgroundSize:'100% 100%',
          backgroundPosition: '50% 50%',
          transform: 'translateY(0px)',
          // top:'50%',
          marginTop:0,
          marginLeft:baseWidth/5+'rem'
        })
      }
      if (staticData.imgType == 3) {
        $(this).addClass("sprite3");
      };
      if (staticData.imgType == 2) {
        $(this).addClass("sprite2")
      }
    })
  };
  //拖动雪碧图
  function dragSprite(that) {
    imgTypeStyle(that);
    var ele = that;
    var eleImg = ele.find("img");
    eleImg.hide();
    var isDrag = that.attr('isdragr'); //true 代表在右侧   还原成原大小   false 左侧   缩小到选项区域
    if (isDrag === "true") {
      that.find('.ans-revise').css({
        'background': 'url(' + that.find("img").attr("src") + ')',
        'background-repeat': 'no-repeat',
      });
      imgTypeInit(that);
    } else {
      ele.find('.ans-revise').css({
        'background': 'url(' + eleImg.attr("src") + ')',
        'background-repeat': 'no-repeat'
      });
      imgTypeInit(ele);
    }
    that.css({

    })
  };
  //拖动结束  按设置次数播放 雪碧图动画
  function spriteDragFinish(that) {
    imgTypeStyle(that);
    that.removeClass("sprite-border-shadow");
    if (palyTime != "infinite") {
      setTimeout(function () {
        if (staticData.imgType == 2) {
          that.removeClass("sprite2Ani");
        }
        if (staticData.imgType == 3) {
          that.removeClass("sprite3Ani");
        }
      }, 1000 * palyTime)
    }
  }
  //===============================
  //添加拖拽
  let $audioDrag = document.getElementsByClassName('audioDrag')[0];
  $('.ans-area').drag({
    before: function (e) {
      // $audioDrag ? $audioDrag.play() : "";
      SDK.playRudio({
        index: $audioDrag,
        syncName: $('.audioDrag').attr("data-syncaudio")
      })

    },
    process: function (e) {
      dragSprite($(this));
    },
    end: function (e) {
      childPosArr[$(this).attr('data_index')] = {
        t: $(this).attr('data-top').substring(0, $(this).attr('data-top').length - 3),
        l: $(this).attr('data-left').substring(0, $(this).attr('data-left').length - 3), //1.45为.dis-area相对于stage的left距离
      }
      let criticlaValue_1 = $('.dis-area').position().left - $(this).width() / 2,
        nowLeft_1 = parseInt($(this).attr('data-left')) * window.base;
      if (nowLeft_1 > criticlaValue_1) {

      } else {
        childPosArr[$(this).attr('data_index')] = {
          t: -1,
          l: -1,
        }
        $(this).attr('isdragr', false);
      }
      if (!isSync) {
        $(this).trigger('syncDragEnd', {
          left: $(this).attr('data-left'),
          top: $(this).attr('data-top'),
          pageX: '',
          pageY: '',
        });
        return;
      }
      setTimeout(function () {
        SDK.bindSyncEvt({
          index: $(this).data('syncactions'),
          eventType: 'dragEnd',
          method: 'drag',
          left: $(this).attr('data-left'),
          top: $(this).attr('data-top'),
          pageX: '',
          pageY: '',
          syncName: 'syncDragEnd',
          otherInfor: {
            childPosArr: childPosArr
          },
          recoveryMode: '1'
        });
      }.bind(this), 300);
    }
  });


  $('.ans-area').on("syncDragEnd", function (e, pos) {
    if (isSync) {
      let obj = pos.otherInfor;
      childPosArr = obj.childPosArr;
      if (pos == undefined || pos.operate == 1) {} else {
        //掉线直接恢复页面
        recoverSelect(pos)
        SDK.setEventLock()
        return
      }
    }
    let criticlaValue = $('.dis-area').position().left - $(this).width() / 2,
      nowLeft = parseInt(pos.left) * window.base;
    if (nowLeft > criticlaValue) {
      var _size = $(this).data('size');
      $(this).css({
        'width': _size.width / 100 / staticData.imgType + 'rem',
        'height': _size.height / 100 + 'rem',
        'line-height': '0',
        'left': pos.left,
        'top': pos.top,
      });
      $(this).find('.ans-revise').css({
        'width': _size.width / 100 / staticData.imgType + 'rem',
        'height': _size.height / 100 + 'rem',
        'background': 'url(' + $(this).find("img").attr("src") + ')',
        'background-repeat': 'no-repeat',
        'top': 0,
        'marginLeft':0,
        'marginTop':0,
        'transform':'translateY(0)'
      });

      $(this).find('img').css({
        'width': _size.width / 100 + 'rem',
        'height': _size.height / 100 + 'rem'
      });
      //超出区域后重置位置----------------
      var isOutHeight = $(".stage").height() - pos.top.split('rem')[0] * window.base < $(this).height();
      var isOutWidth = $(".stage").width() - pos.left.split('rem')[0] * window.base < $(this).width();
      if (isOutHeight) {
        var outTop = ($(".stage").height() - $(this).height()) / window.base;
        $(this).css({
          'top': outTop + 'rem',
        });
        childPosArr[$(this).attr('data_index')].t = outTop
      }
      if (isOutWidth) {
        var outLeft = ($(".stage").width() - $(this).width()) / window.base;
        $(this).css({
          'left': outLeft + 'rem',
        });
        childPosArr[$(this).attr('data_index')].l = outLeft
      }
      //---------------------------
      imgTypeInit($(this));
      $(this).attr('isdragr', true);
      spriteDragFinish($(this));
    } else {
      $(this).attr('isdragr', false);
      $(this).resetStart();
    }
    SDK.setEventLock();
  });


  /**
   * 老师点击done按钮
   */
  //提交
  var btnClickTimer = true;
  let $audioPhoto = document.getElementsByClassName('audioPhoto')[0]
  $('.done-btn').on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (btnClickTimer) {
      btnClickTimer = false;
      if (!isSync) {
        $(this).trigger('syncBtnClick');
        return;
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(this).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'syncBtnClick',
          otherInfor: {
            childPosArr: childPosArr
          },
          recoveryMode: '1'
        });
      }
    }
  });
  $('.done-btn').on('syncBtnClick', function (e, message) {
    if (isSync) {
      let obj = message.data[0].value.syncAction.otherInfor;
      childPosArr = obj.childPosArr
      if (message == undefined || message.operate == 1) {

      } else {
        //直接恢复页面 提交答案后出现掉线
        setPhoto($(this), childPosArr, message)
        SDK.setEventLock();
        return
      }
    }
    // $audioPhoto ? $audioPhoto.play() : "";
    SDK.playRudio({
      index: $audioPhoto,
      syncName: $('.audioPhoto').attr("data-syncaudio")
    })
    setPhoto($(this), childPosArr)
    SDK.setEventLock();
    btnClickTimer = true;
  });
  //老师点击 hint 按钮
  let btnStatus = true;
  let $audioMove = document.getElementsByClassName('audioMove')[0]
  $(".hint-btn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (btnStatus) {
      btnStatus = false;
      if (!isSync) {
        $(this).trigger("btnClick");
        return;
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'btnClick',
          otherInfor: '',
          recoveryMode: '1'
        });
      }
    }
  })
  $(".hint-btn").on('btnClick', function (e, message) {
    $('.hand').show();
    // $audioMove ? $audioMove.play() : "";
    SDK.playRudio({
      index: $audioMove,
      syncName: $('.audioMove').attr("data-syncaudio")
    })
    setTimeout(function () {
      handAnima();
    }, 1000)
    SDK.setEventLock();
  });
  //小手曲线动画
  function handAnima() {
    let element = $('.moveImg').get(0); // 移动元素
    let target = $('.pos_' + endPos).get(0); // 最终的元素
    let parabola = funParabola(element, target, {
      curvature: '.01',
      progress: function () {
        $(".hand").css({
          'animation': 'none',
          'background-position': '100%, 0',
        })
      },
      complete: function () {
        setTimeout(function () {
          $('.hand').hide();
          $('.moveImg').css({
            transform: 'translate(0, 0)'
          });
          $(".hand").css({
            "animation": "handClick 1s steps(4) infinite",
            'background-position': '0, 0',
          })
          btnStatus = true;
        }, 1000)
      }
    }).mark(1, 100);
    setTimeout(function () {
      parabola.init();
    }, 500)
  }
  //拖拽选项过程中掉线恢复
  function recoverSelect(pos) {
    for (let i = 0; i < pos.otherInfor.childPosArr.length; i++) {
      if (pos.otherInfor.childPosArr[i].l !== -1 && pos.otherInfor.childPosArr[i].t !== -1) {
        let childPosArrL = (childPosArr[i].l);
        let childPosArrT = (childPosArr[i].t);
        var _size = $(".ans-area").eq(i).data('size');
        $(".ans-area").eq(i).css({
          'position': 'absolute',
          'left': childPosArrL + 'rem',
          'top': childPosArrT + 'rem',
          'width': _size.width / 100 / staticData.imgType + 'rem',
          'height': _size.height / 100 + 'rem',
        })
        $(".ans-area").eq(i).find(".ans-revise").css({
          'position': 'absolute',
          'top': 0,
          'width': _size.width / 100 / staticData.imgType + 'rem',
          'height': _size.height / 100 + 'rem',
          'marginLeft':0,
          'marginTop':0,
          'transform':'translateY(0)',
          'background': 'url(' + $(".ans-area").eq(i).find("img").attr("src") + ')',
          'background-repeat': 'no-repeat',
        })
        $(".ans-area").eq(i).find('img').css({
          'width': _size.width / 100 + 'rem',
          'height': _size.height / 100 + 'rem'
        });
        imgTypeInit($(".ans-area").eq(i));
        $(".ans-area").eq(i).attr('isdragr', true);
        spriteDragFinish($(".ans-area").eq(i));
      } else {
        $(".ans-area").eq(i).resetStart();
        $(".ans-area").eq(i).attr('isdragr', false);
      }
    }
  }
  //提交拍照  that:事件触发对象  childPosArr:被拖入场景区的道具相对于场景位置坐标
  function setPhoto(that, childPosArr, message) {
    var scaleNum = 0.7;
    let dragW = $('.dis-area-img').attr("data_width"),
      dragH = $('.dis-area-img').attr("data_height");
    $(".main").css({
      height: '100%',
      top: '0'
    })

    if (message == undefined || message.operate == 1) {
      $(".photoMask").css({
        display: 'block',
      }).append($(".dis-area").clone())
      $(".showMask").css({
        animation: 'camStyle .3s  1 forwards',
        ' -webkit-animation': 'camStyle .3s  1 forwards',
      })
      $(".showMask").on('animationend  webkitAnimationEnd', function () {
        $(this).css({
          'z-index': 10
        })
      })
      $(".photoMask .dis-area").css({
        'z-index': '100',
        'border-radius': '0',
        width: dragW / 100 * scaleNum + 'rem',
        height: dragH / 100 * scaleNum + 'rem',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        animation: 'ptoStyle .5s  1 forwards',
        ' -webkit-animation': 'ptoStyle .5s  1 forwards',
      })
    } else {
      if (!$(".photoMask .dis-area").length > 0) {
        $(".photoMask").css({
          display: 'block',
          animation: 'camStyle 0.1s  1 forwards',
          ' -webkit-animation': 'camStyle 0.1s  1 forwards',
        }).append($(".dis-area").clone())
      }

      $(".photoMask .dis-area").css({
        'z-index': '100',
        'border-radius': '0',
        width: dragW / 100 * scaleNum + 'rem',
        height: dragH / 100 * scaleNum + 'rem',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        animation: 'ptoStyle 0s  1 forwards',
        ' -webkit-animation': 'ptoStyle 0s  1 forwards',
        "overflow": "hidden"
      })
    }

    $(".photoMask .dis-area").on('animationend  webkitAnimationEnd', function () {
      $(this).css({
        transform: 'scale(1.1,1.1)'
      });
    });
    $(".dis-area-img").css({
      'border-radius': '0',
      height: '100%',
      width: '100%'
    })
    $(".dis-area-img img").css({
      'border-radius': '0',
      height: '100%',
      width: '100%'
    })
    $(".dis-text").css({
      'display': 'none',
    })
    let zIndex = 0;
    for (let i = 0; i < childPosArr.length; i++) {
      if (childPosArr[i].l !== -1 && childPosArr[i].t !== -1) {
        $(".stage .ans-area").eq(i).clone().appendTo($(".photoMask .dis-area"))
        $(".stage .ans-area").eq(i).css({
          display: 'none'
        })
      }
      for (let j = 0; j < $(".photoMask .ans-area").length; j++) {
        if (i == $(".photoMask .ans-area").eq(j).attr('data_index')) {
          let childPosArrL = (childPosArr[i].l - 7.9) * scaleNum;
          let childPosArrT = (childPosArr[i].t) * scaleNum;
          $(".photoMask .ans-area").eq(j).css({
            position: 'absolute',
            left: childPosArrL + 'rem',
            top: childPosArrT + 'rem',
            width: $(".photoMask .ans-area").eq(j).find("img").get(0).naturalWidth / 100 / staticData.imgType * scaleNum + 'rem',
            height: $(".photoMask .ans-area").eq(j).find("img").get(0).naturalHeight / 100 * scaleNum + 'rem',
          });
          $(".photoMask .ans-area").eq(j).find(".ans-revise").css({
            width: $(".photoMask .ans-area").eq(j).find("img").get(0).naturalWidth / 100 / staticData.imgType * scaleNum + 'rem',
            height: $(".photoMask .ans-area").eq(j).find("img").get(0).naturalHeight / 100 * scaleNum + 'rem',
            marginTop:0,
            marginLeft:0,
            top:0,
            left:0,
          });
          $(".photoMask .ans-area").eq(j).find("img").css({
            width: $(".photoMask .ans-area").eq(j).find("img").width() / 100 * scaleNum + 'rem',
            height: $(".photoMask .ans-area").eq(j).find("img").height() / 100 * scaleNum + 'rem'
          });

        }
      }
    }
    resultWin({
      'WinIsLoop': false, // 答对声音是否重复播放 true/false
      'Mask_Z_Index': "500" // 遮罩层z_index
    });
    var bookEle = $("<div class='winBook'><img src='./image/result_perfect.png' class='perfect'></div>");
    $(".resultWin").append(bookEle);
    $(".winBook").append($(".photoMask").clone());
    $(".resultMask").css({
      "background": 'rgba(0,0,0,0.7)'
    });
    $(".main .photoMask").css({
      display: 'none'
    })
  }

  jQuery.fn.extend({
    resetStart: function (size) {
      // let rheight = '4.3rem';
      // if ($(".ans-area").length > 4) {
      //   rheight = '2.74rem';
      // }
      let rheight = baseHeight + 'rem';
      var thisObj = this;
      //sync
      var startPos = $(this).data('startPos');
      var $left = startPos.left;
      var $top = startPos.top;
      thisObj.css({
        'left': $left,
        'top': $top,
        'width': baseWidth + 'rem',
        'height': rheight,
        'line-height': rheight,
        'border-radius': '.3rem',
      });
      thisObj.find(".ans-revise").css({
        'left': 0,
        'top': 0,
        'width': $(this).attr("data-width") + 'rem',
        'height': $(this).attr("data-height") + 'rem',
        'transform': 'translate(-50%,-50%)',
        'marginLeft':baseWidth/2 + 'rem',
        'marginTop':reviseH/2 + 'rem'
      });
      var _img = thisObj.find('img');
      var _originSize = _img.data('size');
      _img.css({
        'width': _originSize.width,
        'height': _originSize.height
      });
      thisObj.attr('isdragr', false);
      initSprite(thisObj);
      thisObj.removeClass("sprite2Ani").removeClass("sprite3Ani");
    }
  });
});
