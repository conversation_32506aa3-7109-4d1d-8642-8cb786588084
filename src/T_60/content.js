var configData = {
  bg: '',
  desc: 'xxxxxxxxxxxxxxxxxxxxxxx',
  leftImg: 'image/leftImg.png',
  rightImg: 'image/rightImg.jpg',
  sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
  tg: [{
    content: "1",
    title: "1"
  }
  ],
  level: {
    high: [{
        content: "1",
        title: "1"
      }
    ],
    low: [{
      content: "1",
      title: "1"
    }]
  },
  source:{
    optionLength: 6,  //选项上限
    imgType: 1,       //1  静态图  2 2帧雪碧图  3  2帧雪碧图
    palyTime: '#',      //播放次数
    openTip: 1,     //1 启用  0 不启用
    start: 1,  //起点
    end:200,    //终点
    audio: "", //提示声音
    options:[
      // {
      //   img: "./image/sketch01-3.png",
      //   natWidth:"670",
      //   natHeight:"365",
      // },
      // {
      //   img: "./image/sketch02-3.png",
      //   natWidth:"1554",
      //   natHeight:"441",
      // },
      // {
      //   img: "./image/sketch01-3.png",
      //   natWidth:"330",
      //   natHeight:"440",
      // },
      // {
      //   img: "./image/sketch02-3.png",
      //   natWidth:"330",
      //   natHeight:"440",
      // },
      {
        img: "./image/1.png",
        natWidth:"330",
        natHeight:"440",
      },
      {
        img: "./image/twoImg.png",
        natWidth:"330",
        natHeight:"440",
      },
      {
        img: "./image/twoImg.png",
        natWidth:"330",
        natHeight:"440",
      },
      {
        img: "./image/twoImg.png",
        natWidth:"330",
        natHeight:"440",
      }
    ]
  }
};
(function(pageNo) { configData.page = pageNo })(0)
