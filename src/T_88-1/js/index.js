"use strict"
import '../../common/js/common_1v1.js'
import "../../common/template/multyDialog/index.js";
import "../../common/js/teleprompter.js"
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, CLASS_STATUS ,TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";  // 导入常量
import {getMemory} from "../../common/template/memory/index.js";

$(function () {
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      roundCount:configData.source.options.length || 0
    },
    teaData: {
      teacher_type:TEACHER_TYPE.TEACHING_INPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.LISTEN
    },
  })
  window.h5Template = {
    hasPractice: '1'
  }

  if (configData.bg == '') {
    $(".container").css({ 'background-image': 'url(./image/bg.png)' })
  }
  let memoryData = getMemory();
        //老数据兼容
        if (configData.source.initPic) {
          // 创建新的格式数据
          const newOptions = {
            initPic: configData.source.initPic,
            initPicLocation: configData.source.initPicLocation,
            selectedIds: configData.source.selectedIds || [],
            arr: [],
            showHand: configData.source.showHand || '0',
            handLocation: configData.source.handLocation || {x: '', y: ''},
          };

          // 将老的options数据转换为新格式的arr数据
          if (
            configData.source.options &&
            Array.isArray(configData.source.options)
          ) {
            newOptions.arr = configData.source.options.map((option) => {
              return {
                ...option,
              };
            });
          }

          // 替换原有数据结构
          configData.source.options = [newOptions];

          // 删除旧的属性
          delete configData.source.initPic;
          delete configData.source.initPicLocation;
          delete configData.source.selectedIds;
        }
  const getOption = () => {
    // 记忆的数组 最后一个id
    const lastSelectedId = memoryData.selectedArr.length > 0
      ? memoryData.selectedArr[memoryData.selectedArr.length - 1]
      : '';
    let options = []
    // 遍历所有选项，查找包含匹配ID的选项
    for (const option of configData.source.options) {
      // 查找选项的arr数组中是否有匹配的ID
      let matchedItem = option.arr.find(item => item.id === lastSelectedId);
      if(!matchedItem){
        matchedItem = option.arr[0]
      }

      let obj = {
        initPic: option.initPic,
        initPicLocation: option.initPicLocation,
        showHand: option.showHand,
        handLocation: option.handLocation,
        ...matchedItem
      }
      options.push(obj)
    }
    console.log(options)
    return options
  }
  let options = getOption();
  const h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  let start = true;
  let index = 0;
  let useAnimations;
  const lottieInstances = {}; // 存储所有Lottie动画实例
  const pendingLottieConfigs = []; // 存储待初始化的Lottie动画配置

  // 动画管理模块
  const animationManager = {
    stopLottieAnimation(animId) {
      if (lottieInstances[animId]) {
        lottieAnimations.stop(lottieInstances[animId]);
        return true;
      }
      return false;
    },

    stopAllAnimations() {
      Object.keys(lottieInstances).forEach(id => {
        this.stopLottieAnimation(id);
      });
    },

    async initLottieAnimation(selector, jsonUrl, animId) {
      try {
        lottieInstances[animId] = lottieInstances[animId] ? lottieInstances[animId] : await lottieAnimations.init(
          lottieInstances[animId],
          jsonUrl,
          selector
        );
        lottieAnimations.play(lottieInstances[animId]);
        return animId;
      } catch (error) {
        console.error(`初始化Lottie动画失败: ${selector}`, error);
        return null;
      }
    }
  };

  // 样式管理模块
  const styleManager = {
    isJsonFile(url) {
      if (!url) return false;
      const extension = url.split('.').pop().toLowerCase();
      return extension === 'json';
    },

    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        if (!url) {
          reject(new Error('无效的图片URL'));
          return;
        }

        if (this.isJsonFile(url)) {
          $.getJSON(url)
            .done(data => {
              const width = data.width || data.w;
              const height = data.height || data.h;
              resolve({ width, height });
            })
            .fail(() => reject(new Error('JSON文件加载失败')));
        } else {
          const img = new Image();
          img.onload = () => resolve({ width: img.width, height: img.height });
          img.error = () => reject(new Error('图片加载失败'));
          img.src = url;
        }
      });
    },

    async applyElementStyle(element, location, imageUrl, animId) {
      try {
        const isJson = this.isJsonFile(imageUrl);
        const dimensions = await this.getImageDimensions(imageUrl);

        const css = {
          position: 'absolute',
          left: `${location.x / 100}rem`,
          top: `${location.y / 100}rem`,
          width: `${dimensions.width / 100}rem`,
          height: `${dimensions.height / 100}rem`,
        };

        if (!isJson) {
          css.backgroundImage = `url(${imageUrl})`;
          css.backgroundPosition = 'center';
          css.backgroundRepeat = 'no-repeat';
          css.backgroundSize = 'contain';
        }

        $(element).css(css);

        // 如果是 JSON 文件，将配置添加到待初始化集合中
        if (isJson) {
          pendingLottieConfigs.push({
            selector: $(element).attr('data-syncactions'),
            jsonUrl: imageUrl,
            animId: animId
          });
        }
      } catch (error) {
        console.error(`设置元素样式失败:`, error);
      }
    }
  };

  const page = {
    init() {
      this.initElements()
      setHand(0)
    },
    async initElements() {
      // 添加loading状态
      $(".mainArea").addClass('loading')

      // 准备所有元素
      const elements = await Promise.all(options.map(async (item, i) => {
        // 创建元素
        const initPicEl = $('<div>').addClass('initPic').attr({
          'data-syncactions': `initPic${i}`,
          'data-index': i
        })
        const tagPicEl = $('<div>').addClass('tagPic option').attr({
          'data-syncactions': `tagPic${i}`,
          'data-index': i
        })
        const textPicEl = $('<img>').addClass('textPic option').attr({
          'src': item.textPic,
          'data-syncactions': `textPic${i}`,
          'data-index': i
        }).css('display', 'none')
        const audioEl = $('<audio>').addClass('audio').attr({
          'src': item.audio,
          'data-syncactions': `picAudio${i}`,
          'data-index': i
        })

        // 设置初始图片样式
        await styleManager.applyElementStyle(
          initPicEl,
          item.initPicLocation,
          item.initPic,
          `initPic${i}`
        )

        // 设置目标图片样式
        await styleManager.applyElementStyle(
          tagPicEl,
          item.tagPicLocation,
          item.tagPic,
          `tagPic${i}`
        )
        tagPicEl.css('visibility', 'hidden')

        // 设置文字图片样式
        if (item.textPic) {
          const dimensions = await styleManager.getImageDimensions(item.textPic)
          textPicEl.css({
            position: 'absolute',
            left: item.textPicLocation.x ? `${item.textPicLocation.x / 100}rem` : '0rem',
            top: item.textPicLocation.y ? `${item.textPicLocation.y / 100}rem` : '0rem',
            height: `${dimensions.height / 100}rem`,
            width: `${dimensions.width / 100}rem`,
            display: 'none'
          })
        }

        return [initPicEl, tagPicEl, textPicEl, audioEl]
      }))

      // 一次性添加所有元素到DOM
      const container = $('<div>').addClass('elements-container')
      elements.forEach(elementGroup => {
        elementGroup.forEach(el => container.append(el))
      })
      $(".mainArea").append(container)

      // 初始化所有 Lottie 动画
      await this.initializeLottieAnimations();

      // 移除loading状态
      $(".mainArea").removeClass('loading')
    },

    async initializeLottieAnimations() {
      // 遍历所有待初始化的动画配置
      for (const config of pendingLottieConfigs) {
        const animId = await animationManager.initLottieAnimation(
          `[data-syncactions="${config.selector}"]`,
          config.jsonUrl,
          config.animId
        );
        // 初始化完成后直接播放动画
        if (animId && lottieInstances[animId]) {
          lottieAnimations.play(lottieInstances[animId]);
        }
      }
      // 清空配置集合
      pendingLottieConfigs.length = 0;
    }
  }
  function setHand(index) {
    // region 小手是否展示设置手指位置
    if (options[index].showHand === '1') {
      $('.hands').css({
        animation: 'hand 1s steps(4) infinite',
        ' -webkit-animation': 'hand 1s steps(4) infinite',
        left: `${options[index].handLocation.x / 100}rem`,
        top: `${options[index].handLocation.y / 100}rem`,
        zIndex: 10
      }).show()
    } else {
      $('.hands').hide()
    }
    // endregion
  }
  page.init()
  $(document).on('click touchstart', '.initPic', function (e) {
    if (e.type == 'touchstart') {
      e.preventDefault()
    }
    if ($(this).attr('data-index') != index) {
      console.log('顺序错误')
      return;
    }
    if (start) {
      start = false;
      SDK.reportTrackData({
        action:'CK_FT_INTERACTION_ITEM', //事件名称
        data:{}, // 老师和学生  都需要上报的数据
        teaData:{},  // 只有老师端会上报的数据
        stuData:{
          item: Number($(this).attr('data-index'))+1
        },
      },USER_TYPE.STU)
      if (!isSync) {
        $(this).trigger('changePosSync')
        return
      }
      // 教师学生都能点
      // if (window.frameElement.getAttribute('user_type') == 'stu') {  }
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(this).attr("data-syncactions"),
        eventType: 'click',
        method: 'event',
        syncName: 'changePosSync',
        recoveryMode: '1'
      });
    }
  });
  $(document).on('changePosSync', '.initPic', async function (e, message) {
    index++
    let i = $(this).attr('data-index') //用户点击的元素index
    $('.hands').hide()
    // 设置动画元素样式
    async function setAnimatedElementsStyle(targetEl, animationsEl, animationsJson) {
      console.log(targetEl)
      let targetCenterX = targetEl.position().left + targetEl.outerWidth() / 2;
      let targetCenterY = targetEl.position().top + targetEl.outerHeight() / 2;
      console.log(targetCenterX, targetCenterY)
      const res = await $.get(animationsJson)
      animationsEl.css({
        position: 'absolute',
        left: targetCenterX,
        top: targetCenterY,
        height: `${res.h}px`,
        width: `${res.w}px`,
        transform: 'translate(-50%, -50%)',
        zIndex: 10
      })
    }

    // 播放用户自定义动画
    function playUserAnimation() {
      return new Promise(async (resolve, reject) => {
        if (configData.dynamicEffect) {
          useAnimations = useAnimations ? useAnimations : await lottieAnimations.init(
            useAnimations,
            configData.dynamicEffect,
            '.user_set_animation',
            false
          );
          await setAnimatedElementsStyle($(`[data-syncactions="tagPic${i}"]`), $('.user_set_animation'), configData.dynamicEffect)
          $('.user_set_animation').show()
          lottieAnimations.play(useAnimations);
          useAnimations.addEventListener("complete", () => {
            lottieAnimations.stop(useAnimations);
            $('.user_set_animation').hide()
            resolve();
          });
        } else {
          resolve();
        }
      })
    }


    //播放用户设置的音频
    function playUserAudio() {
      return new Promise((resolve, reject) => {
        if (options[i].audio) {
          setTimeout(async () => {
            await $(`[data-syncactions="picAudio${i}"]`).playAudioSync()
            resolve()
          }, 300)
        } else {
          resolve()
        }
      })
    }

    // 结束
    async function end() {
      if (index >= options.length) {
        // animationManager.stopAllAnimations()
        // 游戏结束不停止json动画;
        await feedbackAnimation('feedKey1')
        SDK.reportTrackData({
          action:'CK_FT_INTERACTION_COMPLETE', //事件名称
          data:{}, // 老师和学生  都需要上报的数据
          teaData:{result:'success'},  // 只有老师端会上报的数据
          stuData:{},  // 只有学生端会上报的数据
        },USER_TYPE.TEA)
        SDK.setEventLock()
      } else {
        start = true
        setHand(index)
        SDK.setEventLock()
      }
    }

    $('#gorgeous').playAudioSync()
    await playUserAnimation()
    // 展示目标图
    $(`[data-syncactions="tagPic${i}"]`).css('visibility', 'visible');
    $(`[data-syncactions="initPic${i}"]`).fadeOut()
    //如果有设置展示用户设置的文字
    if (options[i].textPic) {
      $(`[data-syncactions="textPic${i}"]`).fadeIn(200);
    }
    await playUserAudio()
    end()
  })

  // 用户点击目标图或者文字图 播放用户设置的音频
  $(document).on('click touchstart', '.option', function (e) {
    if (e.type == 'touchstart') {
      e.preventDefault()
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger('playAudio');
      return
    }
    SDK.bindSyncEvt({
      sendUser: '',
      receiveUser: '',
      index: $(e.currentTarget).data('syncactions'),
      eventType: 'click',
      method: 'event',
      syncName: 'playAudio',
      recoveryMode: '1'
    });
  })
  $(document).on('playAudio', '.option', async function (e, message) {
    let i = $(this).attr('data-index')
    if (options[i].audio) {
      await $(`[data-syncactions="picAudio${i}"]`).playAudioSync()
      SDK.setEventLock()
    } else {
      SDK.setEventLock()
    }
  })
});




