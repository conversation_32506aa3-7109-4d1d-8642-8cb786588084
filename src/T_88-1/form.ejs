<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>BHTY0001FTJY_点击变化听音FT_记忆组件</title>
    <link rel="stylesheet" href="form/css/style.css" />
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
  </head>
  <body>
    <div id="container">
      <div class="edit-form">
        <div class="h-title">BHTY0001FTJY_点击变化听音FT_记忆组件</div>

        <% include ./src/common/template/common_head %>

        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %> <%
        include ./src/common/template/multyDialog/form.ejs %> <% include
        ./src/common/template/memory/form.ejs %>

        <div class="c-group">
          <div class="c-title">设置点击内容(最多4组)</div>
          <div class="c-area upload img-upload" style="padding-top: 0">
            <ul>
              <li
                v-for="(group, groupIndex) in configData.source.options"
                style="margin-top: 20px"
              >
                <div style="border: 1px solid; border-radius: 8px">
                  <div class="group-header">
                    <span class="group-title">第{{ groupIndex + 1 }}组</span>
                    <span
                      class="dele-tg-btn"
                      v-show="configData.source.options.length > 1 && !(groupIndex === 0)"
                      v-on:click="deleGroup(group)"
                    >
                    </span>
                  </div>
                  <!-- 初始图片 -->
                  <div class="c-well">
                    <div class="field-wrap">
                      <label class="field-label">初始图片<em>*</em></label>
                      <span class="field-wrap">
                        <label
                          :for="'content-pic-initPic'+groupIndex"
                          class="btn btn-show upload"
                          v-if="!group.initPic"
                          >上传</label
                        >
                        <label
                          :for="'content-pic-initPic'+groupIndex"
                          class="btn upload re-upload"
                          v-if="group.initPic"
                          >重新上传</label
                        >
                      </span>
                      <div class="txt-info">
                        <em>JPG、PNG、JSON格式小于等于240Kb</em>
                      </div>
                      <input
                        type="file"
                        v-bind:key="Date.now()"
                        class="btn-file"
                        :id="'content-pic-initPic'+groupIndex"
                        size=""
                        accept=".jpg,.png,.json"
                        @change="feedbackUpload($event,group,'initPic',350)"
                      />
                    </div>

                    <div class="img-preview" v-if="group.initPic">
                      <img
                        :src="group.initPic.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : group.initPic"
                        alt=""
                      />
                      <div class="img-tools">
                        <span
                          class="btn btn-delete"
                          @click="delPrew(group,'initPic')"
                          >删除</span
                        >
                      </div>
                    </div>
                    <div class="field-wrap-item" style="margin-top: 10px">
                      <span>初始图片位置<em>*</em>&nbsp;&nbsp;</span>
                      X:<input
                        type="number"
                        class="c-input-txt"
                        style="
                          margin: 0 10px;
                          width: 60px !important;
                          display: inline-block;
                        "
                        oninput="if(value>1920)value=1920;if(value<0)value=0"
                        v-model="group.initPicLocation.x"
                      />
                      Y:<input
                        type="number"
                        class="c-input-txt"
                        style="
                          margin: 0 10px;
                          width: 60px !important;
                          display: inline-block;
                        "
                        oninput="if(value>1080)value=1080;if(value<0)value=0"
                        v-model="group.initPicLocation.y"
                      />
                      <br />
                    </div>
                    <div style="margin-top: 10px">
                      <span>顺序展示动态小手</span>
                      <label
                        class="inline-label"
                        :for="'imgTypeEffect'+groupIndex"
                        style="display: inline-block; margin-left: 10px"
                      >
                        <input
                          type="radio"
                          :name="'imgTypeEffect'+groupIndex"
                          value="1"
                          v-model="group.showHand"
                        />
                        是</label
                      >
                      <label
                        class="inline-label"
                        :for="'imgTypeEffect'+groupIndex"
                        style="display: inline-block; margin-left: 10px"
                      >
                        <input
                          type="radio"
                          :name="'imgTypeEffect'+groupIndex"
                          value="2"
                          v-model="group.showHand"
                        />
                        否</label
                      >
                    </div>
                    <div class="field-wrap-item" v-if="group.showHand === '1'">
                      <span>小手位置<em>*</em> &nbsp;&nbsp;</span>
                      X:<input
                        type="number"
                        class="c-input-txt"
                        style="
                          margin: 0 10px;
                          width: 60px !important;
                          display: inline-block;
                        "
                        oninput="if(value>1920)value=1920;if(value<0)value=0"
                        v-model="group.handLocation.x"
                      />
                      Y:<input
                        type="number"
                        class="c-input-txt"
                        style="
                          margin: 0 10px;
                          width: 60px !important;
                          display: inline-block;
                        "
                        oninput="if(value>1080)value=1080;if(value<0)value=0"
                        v-model="group.handLocation.y"
                      />
                      <br />
                    </div>
                  </div>
                  <!-- 选择id -->
                  <div
                    class="c-well memory-id-selection"
                    :style="{ marginBottom: group.selectedIds.length === 0 ? '2px' : '20px' }"
                  >
                    <span class="selection-title"><em>*</em>选择记忆ID</span>
                    <span style="color: red">(先勾选ID，再编辑内容)</span>
                    <div class="checkbox-container">
                      <label
                        v-for="(item, index) in configData.source.memoryForm"
                        class="checkbox-label"
                        :class="{ selected: group.selectedIds.includes(item.id) }"
                        :for="'selectId'+index"
                      >
                        <input
                          type="checkbox"
                          :name="'selectId'+index"
                          :value="item.id"
                          v-model="group.selectedIds"
                          @change="handleIdSelectionChange(groupIndex,item.id, $event)"
                        />
                        <span class="id-text">ID{{ item.id }}</span>
                        <span class="name-text" :title="item.name"
                          >--{{
                            item.name.length > 5
                              ? item.name.substring(0, 5) + "..."
                              : item.name
                          }}</span
                        >
                      </label>
                    </div>
                  </div>
                  <ul>
                    <li v-for="(item, index) in group.arr">
                      <div
                        class="c-well"
                        :style="{ marginBottom: group.arr.length - 1 === index ? '2px' : '20px' }"
                      >
                        <span
                          class="dele-tg-btn"
                          style="z-index: 9; position: relative"
                          v-on:click="deleConfig(group,item)"
                        ></span>
                        <div>
                          记忆<span style="color: red">ID{{ item.id }}</span
                          >的内容设置：
                        </div>
                        <div class="field-wrap" style="margin-top: 20px">
                          <label class="field-label">目标图片<em>*</em></label>
                          <span class="field-wrap">
                            <label
                              :for="'content-pic-tagPic'+groupIndex+index"
                              class="btn btn-show upload"
                              v-if="!item.tagPic"
                              >上传</label
                            >
                            <label
                              :for="'content-pic-tagPic'+groupIndex+index"
                              class="btn upload re-upload"
                              v-if="item.tagPic"
                              >重新上传</label
                            >
                          </span>
                          <div class="txt-info">
                            <em>JPG、PNG、JSON格式小于等于240Kb</em>
                          </div>
                          <input
                            type="file"
                            v-bind:key="Date.now()"
                            class="btn-file"
                            :id="'content-pic-tagPic'+groupIndex+index"
                            size=""
                            accept=".jpg,.png,.json"
                            @change="feedbackUpload($event,item,'tagPic',350)"
                          />
                        </div>
                        <div class="img-preview" v-if="item.tagPic">
                          <img
                            :src="item.tagPic.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.tagPic"
                            alt=""
                          />
                          <div class="img-tools">
                            <span
                              class="btn btn-delete"
                              @click="delPrew(item,'tagPic')"
                              >删除</span
                            >
                          </div>
                        </div>
                        <div class="field-wrap-item">
                          <span>目标图片位置<em>*</em> &nbsp;&nbsp;</span>
                          X:<input
                            type="number"
                            class="c-input-txt"
                            style="
                              margin: 0 10px;
                              width: 60px !important;
                              display: inline-block;
                            "
                            oninput="if(value>1920)value=1920;if(value<0)value=0"
                            v-model="item.tagPicLocation.x"
                          />
                          Y:<input
                            type="number"
                            class="c-input-txt"
                            style="
                              margin: 0 10px;
                              width: 60px !important;
                              display: inline-block;
                            "
                            oninput="if(value>1080)value=1080;if(value<0)value=0"
                            v-model="item.tagPicLocation.y"
                          />
                          <br />
                        </div>

                        <div class="field-wrap" style="margin-top: 20px">
                          <label class="field-label">文字图片</label>
                          <span class="field-wrap">
                            <label
                              :for="'content-pic-textPic'+groupIndex+index"
                              class="btn btn-show upload"
                              v-if="!item.textPic"
                              >上传</label
                            >
                            <label
                              :for="'content-pic-textPic'+groupIndex+index"
                              class="btn upload re-upload"
                              v-if="item.textPic"
                              >重新上传</label
                            >
                          </span>
                          <div class="txt-info">
                            <em
                              >JPG、PNG格式，最大尺寸不超过1000x500，小于等于50Kb
                            </em>
                          </div>
                          <input
                            type="file"
                            v-bind:key="Date.now()+'textPic'"
                            class="btn-file"
                            :id="'content-pic-textPic'+groupIndex+index"
                            size="1000*500"
                            accept=".jpg,.png"
                            @change="imageUpload($event,item,'textPic',50)"
                          />
                        </div>
                        <div class="img-preview" v-if="item.textPic">
                          <img :src="item.textPic" alt="" />
                          <div class="img-tools">
                            <span
                              class="btn btn-delete"
                              @click="delPrew(item,'textPic')"
                              >删除</span
                            >
                          </div>
                        </div>
                        <div class="field-wrap-item">
                          <span>文字图片位置: &nbsp;&nbsp;</span>
                          X:<input
                            type="number"
                            class="c-input-txt"
                            style="
                              margin: 0 10px;
                              width: 60px !important;
                              display: inline-block;
                            "
                            oninput="if(value>1920)value=1920;if(value<0)value=0"
                            v-model="item.textPicLocation.x"
                          />
                          Y:<input
                            type="number"
                            class="c-input-txt"
                            style="
                              margin: 0 10px;
                              width: 60px !important;
                              display: inline-block;
                            "
                            oninput="if(value>1080)value=1080;if(value<0)value=0"
                            v-model="item.textPicLocation.y"
                          />
                          <br />
                        </div>

                        <!-- 目标图片音频 -->
                        <div class="field-wrap" style="margin-top: 20px">
                          <label class="field-label" for="">目标图片音频</label>
                          <span>
                            <label
                              :for="'audio-upload-audio'+groupIndex+index"
                              class="btn btn-show upload"
                              v-if="item.audio==''?true:false"
                              >上传</label
                            >
                            <label
                              :for="'audio-upload-audio'+groupIndex+index"
                              class="btn upload re-upload mar"
                              v-if="item.audio!=''?true:false"
                              >重新上传</label
                            >
                          </span>
                          <div style="color: red">mp3格式，小于等于50Kb</div>

                          <div
                            class="audio-preview"
                            v-show="item.audio!=''?true:false"
                          >
                            <div class="audio-tools">
                              <p v-show="item.audio!=''?true:false">
                                {{ item.audio }}
                              </p>
                            </div>
                            <span class="play-btn" v-on:click="play($event)">
                              <audio v-bind:src="item.audio"></audio>
                            </span>
                          </div>
                          <span
                            class="btn btn-audio-dele"
                            v-show="item.audio!=''?true:false"
                            v-on:click="item.audio=''"
                            >删除</span
                          >
                          <input
                            type="file"
                            :id="'audio-upload-audio'+groupIndex+index"
                            class="btn-file upload"
                            size=""
                            accept=".mp3"
                            v-on:change="audioUpload($event,item,'audio',50)"
                            v-bind:key="Date.now()+'audio'"
                          />
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
                <!-- 组的添加按钮 -->
                <div
                  style="text-align: center; margin-top: 20px"
                  v-show="groupIndex < 3 && groupIndex === configData.source.options.length -1"
                >
                  <button
                    class="btn upload"
                    style="padding: 8px; letter-spacing: 4px"
                    @click="addGroup"
                  >
                    添加一组点击内容
                  </button>
                </div>
              </li>
            </ul>

            <div class="field-wrap" style="margin-top: 20px">
              <label class="field-label">点击后动效</label>
              <span class="field-wrap">
                <label
                  for="content-dynamic-effect"
                  class="btn btn-show upload"
                  v-if="!configData.dynamicEffect"
                  >上传json</label
                >
                <label
                  for="content-dynamic-effect"
                  class="btn upload re-upload"
                  v-if="configData.dynamicEffect"
                  >重新上传</label
                >
              </span>
              <div style="color: red">小于等于200Kb</div>
              <input
                type="file"
                v-bind:key="Date.now()+'effect'"
                class="btn-file"
                id="content-dynamic-effect"
                accept=".json"
                @change="clickEffectUpload($event)"
              />
            </div>
            <div class="img-preview" v-if="configData.dynamicEffect">
              <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
              <div class="img-tools">
                <span
                  class="btn btn-delete"
                  @click="configData.dynamicEffect=''"
                  >删除</span
                >
              </div>
            </div>
          </div>
        </div>
        <!-- 反馈动画添加 -->
        <% include ./src/common/template/feedbackAnimation/form %>
        <button class="send-btn" v-on:click="onSend">提交</button>
      </div>
      <div class="edit-show">
        <div class="show-fixed">
          <div class="show-img">
            <img
              src="form/img/preview.png?v=<%= new Date().getTime() %>"
              alt=""
            />
          </div>
          <ul class="show-txt">
            <li><em>图片格式：</em>JPG/PNG/GIF</li>
            <li><em>声音格式：</em>MP3/WAV</li>
            <li><em>视频格式：</em>MP4</li>
            <li>带有“ * ”号为必填项</li>
          </ul>
        </div>
      </div>
    </div>
  </body>
  <script src="form/js/form.js?v=<%= new Date().getTime() %>"></script>
</html>
