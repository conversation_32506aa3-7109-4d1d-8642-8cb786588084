"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量

const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      correctanswercount: configData.source.options.length || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.SPEAK,
    },
  });
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "3", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };
  // let classStatus = 0; //未开始上课 0未上课 1开始上课 2开始练习
  // if (isSync) {
  //   classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  // }

  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bj.jpg)",
    });
  }
  let options = configData.source.options, //卡牌内容图片
    backImgList = configData.source.backImgList, //卡牌背面图片
    treasureChestList = configData.source.treasureChestList, //宝箱图片
    baseMapList = configData.source.baseMapList, //九宫格下方底图
    userType =
      window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师

  let initialPositions = [
    //卡牌初始位置
    { top: 0, left: 0 },
    { top: 0, left: 510 },
    { top: 0, left: 1020 },
    { top: 270, left: 0 },
    { top: 270, left: 510 },
    { top: 270, left: 1020 },
    { top: 540, left: 0 },
    { top: 540, left: 510 },
    { top: 540, left: 1020 },
  ];
  let microphone = $(".microphone"); //麦克风
  let microphoneJson = null; //麦克风动画
  let boxAnimationJson = null; //宝箱动画
  let cards = []; //卡牌数组
  let first = {}; //第一次点击的卡牌
  let second = {}; //第二次点击的卡牌
  let last = {}; //最后一次点击的卡牌
  let randomList = []; //可随机数组
  let cardNum = 0; //卡牌点击次数
  let percentage = 0; //完成进度
  let cardClick = false; //卡牌是否可点击
  let handsssStatus = false; //小手提示是否在卡牌上
  let clickAble = false; //start是否可点击
  let soundClick = true;
  // 初始化
  optionsFn();
  function optionsFn() {
    //页面加载0.5秒后卡牌翻转，播放音效
    setTimeout(() => {
      let audio = $(".card-audio")[0];
      SDK.playRudio({
        index: audio,
        syncName: $(".card-audio").attr("data-syncaudio"),
      });
      $(".optionUl li").css({
        transform: "rotateY(0deg)",
      });
    }, 500);
    if (baseMapList[0].initialImg) {
      // 初始底图
      $(".option-div").css(
        "background-image",
        `url(${baseMapList[0].initialImg})`
      );
    }
    if (!isSync) {
      // 老师模式
      $(".start").css({
        "pointer-events": "auto",
      });
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU) {
        // 老师模式
        $(".start").css({
          "pointer-events": "auto",
        });
      }
      if (userType == USER_TYPE.TEA) {
        // 老师模式
        $(".start").css({
          "pointer-events": "auto",
        });
      }
      // moduleAuthorizationFn("teaOnly", "11");
    }

    //根据设定的次数展开数组
    let newArray = options.flatMap((item) =>
      Array.from({ length: parseInt(item.contentNumber) }, () => ({ ...item }))
    );
    //设置第一次要展示的数据和可随机的数组
    if (newArray.length < 5) {
      randomList = newArray.concat(treasureChestList[0]);
    } else {
      randomList = newArray;
    }
    // first = randomList.shift();
    second = treasureChestList[1];
    last = treasureChestList[2];

    // for (let i = randomList.length - 1; i > 0; i--) {
    //   const j = Math.floor(Math.random() * (i + 1));
    //   [randomList[i], randomList[j]] = [randomList[j], randomList[i]];
    // }
    // SDK.syncData.randomList = randomList;
    console.log(randomList, "110");
    //合并水果和宝箱数组
    newArray = newArray.concat(treasureChestList);
    // 如果当前长度不足，则循环填充
    if (newArray.length < 9) {
      let currentLength = newArray.length;
      for (let i = currentLength; i < 9; i++) {
        let sourceIndex = i % currentLength;
        newArray.push({ ...newArray[sourceIndex] });
      }
    }
    console.log(newArray);
    //数据数组和坐标数组合并
    let mergedArray = newArray.map((item, index) => ({
      ...item,
      ...initialPositions[index],
    }));
    console.log(mergedArray);
    let str = "";
    for (let i = 0; i < mergedArray.length; i++) {
      let Identity = mergedArray[i].contentImg ? "no-box" : "box";
      let cardFrontBg = mergedArray[i].contentImg
        ? mergedArray[i].contentImg
        : mergedArray[i].CloseImg;
      str += `<li class="card" data-key="${i}" data-syncactions="sele-${i}" style="top:${
        mergedArray[i].top / 100
      }rem;left:${mergedArray[i].left / 100}rem;">
                <div class="card-inner">
                  <div class="card-face card-front ${Identity}" style="background-image: url(${cardFrontBg})"></div>
                  <div class="card-face card-back" style="background-image: url(${
                    backImgList[0].backImg
                      ? backImgList[0].backImg
                      : "./image/back-img.png"
                  })"></div>
                </div>
              </li>`;
    }
    $(".optionUl").html(str);
    first = randomList.shift();
    for (let i = randomList.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [randomList[i], randomList[j]] = [randomList[j], randomList[i]];
    }
    SDK.syncData.randomList = randomList;
    clickAble = true;
  }

  // 点击start按钮
  $(".start").on("click touchstart", function (e) {
    if (clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation();
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_STARTBUTTON",
          data: {},
        },
      );
      if (soundClick) {
        soundClick = false;
        if (!isSync) {
          $(this).trigger("syncStartClick");
          return;
        }
        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "syncStartClick",
          funcType: "audio",
        });
      }
    }
  });
  const shuffleTimes = 3; // 洗牌次数
  let currentShuffle = 0;
  $(".start").on("syncStartClick", function (e, message) {
    // 去除初始页面宝箱呼吸动效
    $(".card-inner .card-face").removeClass("box");
    //隐藏点击按钮
    $(".start").hide();
    // 播放点击音效
    let audio = $(".start-audio")[0];
    SDK.playRudio({
      index: audio,
      syncName: $(".start-audio").attr("data-syncaudio"),
    });
    //洗牌
    cards = $(".optionUl li").toArray();
    // 将所有卡牌翻转到背面
    cards.forEach((card) => {
      card.classList.add("flipped");
    });

    // 延迟后开始多次洗牌动画
    setTimeout(() => {
      function nextShuffle() {
        currentShuffle++;
        if (currentShuffle <= shuffleTimes) {
          performShuffle(() => {
            if (currentShuffle < shuffleTimes) {
              setTimeout(nextShuffle, 300); // 每次洗牌间隔300ms
            } else {
              // 小手是否出现
              setTimeout(() => {
                if (handsssStatus == false) {
                  $(".handsss").show();
                } else {
                  $(".handsss").hide();
                }
              }, 5000);
              $(".optionUl li").find(".card-front").css("background-image", "");
              cardClick = true;
              moduleAuthorizationFn("stuOnly", "12");
              SDK.setEventLock();
              soundClick = true;
            }
          });
        }
      }

      // 开始第一次洗牌
      nextShuffle();
    }, 600); // 等待翻转动画完成
  });

  // 洗牌方法
  function shuffleArray(array) {
    if (currentShuffle < shuffleTimes) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    }
    //最后一次恢复初始位置
    if (currentShuffle == shuffleTimes) {
      return array;
    }
  }
  // 执行单次洗牌动画
  function performShuffle(callback) {
    // 生成随机目标位置
    var targetPositions = shuffleArray([...initialPositions]);
    // 添加随机旋转和偏移量
    cards.forEach((card, index) => {
      const target = targetPositions[index];
      const rotation = Math.random() * 60 - 30; // -30到30度随机旋转
      const offsetX = Math.random() * 40 - 20; // -20到20随机偏移
      const offsetY = Math.random() * 40 - 20;

      card.style.transform = `rotate(${rotation}deg)`;
      card.style.left = (target.left + offsetX) / 100 + "rem";
      card.style.top = (target.top + offsetY) / 100 + "rem";
      card.style.zIndex = "10";
    });

    // 动画完成后调整到精确位置
    setTimeout(() => {
      cards.forEach((card, index) => {
        const target = targetPositions[index];
        card.style.transform = "rotate(0deg)";
        card.style.left = target.left / 100 + "rem";
        card.style.top = target.top / 100 + "rem";
        card.style.zIndex = "";
      });

      if (callback) callback();
    }, 500);
  }

  /**
   * 授权模式方法
   * @param {*} type: 方法
   * @param {*} value：权限
   * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
   */
  function moduleAuthorizationFn(type, value) {
    if (!isSync) {
      return;
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      console.log(isSync, classStatus, userType, "292");
      if (classStatus == CLASS_STATUS.NOT) {
        return;
      } else {
        SDK.bindSyncCtrl({
          type: type,
          tplAuthorization: "tpl",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [
              {
                key: "classStatus",
                value: value,
                ownerUID: SDK.getClassConf().user.id,
              },
            ],
          },
        });
      }
    }
  }

  // 点击卡牌
  $(".optionUl li").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_ITEM",
        data: {},
      },
      USER_TYPE.STU
    );
    if (cardClick) {
      cardClick = false;
      if (!isSync) {
        $(this).trigger("syncCardClick");
        return;
      }

      SDK.bindSyncEvt({
        sendUser: "",
        receiveUser: "",
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "syncCardClick",
        funcType: "audio",
      });
    }
  });
  let cardLeft = 0; //卡牌左边距离
  let cardTop = 0; //卡牌上边距离
  let currentIndex = 0; //随机数组下标
  $(".optionUl li").on("syncCardClick", function (e, message) {
    var that = $(this);
    // 小手消失
    handsssStatus = true;
    $(".handsss").hide();

    cardLeft = that.position().left;
    cardTop = that.position().top;

    cardNum++;
    console.log(cardNum, "308");
    if (cardNum == 1) {
      moduleAuthorizationFn("teaOnly", "11");
      //第一个固定展示水果
      fruitDisplay(that, first.contentImg);
    } else if (cardNum == 2) {
      //第二个固定展示宝箱
      boxDisplay(that, second);
    } else if (cardNum == randomList.length + 3) {
      //最后一个固定展示宝箱
      boxDisplay(that, last);
    } else {
      //随机展示
      let randomListSDK = SDK.syncData.randomList;
      console.log(randomListSDK, "330");
      let randomObj = {};
      if (randomListSDK.length === 0) return null;
      randomObj = randomListSDK[currentIndex];
      currentIndex = (currentIndex + 1) % randomListSDK.length;

      console.log(randomObj, "351");
      if (isSync && userType == USER_TYPE.STU) {
        console.log(randomObj.contentImg, "学生端水果");
        if (randomObj.contentImg) {
          //水果
          console.log(randomObj, "水果");
          moduleAuthorizationFn("teaOnly", "11");
          fruitDisplay(that, randomObj.contentImg);
        }
        if (randomObj.CloseImg) {
          //宝箱
          console.log(randomObj, "宝箱");
          boxDisplay(that, randomObj);
        }
      }
      if (!isSync) {
        console.log(randomObj.contentImg, "教师端水果");
        if (randomObj.contentImg) {
          //水果
          console.log(randomObj, "教师端水果");
          fruitDisplay(that, randomObj.contentImg);
        }
        if (randomObj.CloseImg) {
          //宝箱
          console.log(randomObj, "教师端宝箱");
          boxDisplay(that, randomObj);
        }
      }
      if (isSync) {
        const classStatus = SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU) {
          console.log(randomObj.contentImg, "教师端水果");
          if (randomObj.contentImg) {
            //水果
            console.log(randomObj, "教师端水果");
            fruitDisplay(that, randomObj.contentImg);
          }
          if (randomObj.CloseImg) {
            //宝箱
            console.log(randomObj, "教师端宝箱");
            boxDisplay(that, randomObj);
          }
        }
        if (userType == USER_TYPE.TEA) {
          console.log(randomObj.contentImg, "教师端水果");
          if (randomObj.contentImg) {
            //水果
            console.log(randomObj, "教师端水果");
            fruitDisplay(that, randomObj.contentImg);
          }
          if (randomObj.CloseImg) {
            //宝箱
            console.log(randomObj, "教师端宝箱");
            boxDisplay(that, randomObj);
          }
        }
      }
    }
  });
  //展示水果
  function fruitDisplay(that, contentImg) {
    //水果音频
    let fruitAudio = $(".fruit-audio")[0];
    SDK.playRudio({
      index: fruitAudio,
      syncName: $(".fruit-audio").attr("data-syncaudio"),
    });
    that.find(".card-front").css("background-image", `url(${contentImg})`);
    $(".optionUl-model").show();
    $(".microphone").show();
    if (!isSync) {
      $(".correct").show();
    }
    if (isSync) {
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == CLASS_STATUS.NOT && userType == USER_TYPE.STU) {
        $(".correct").show();
      }
      if (userType == USER_TYPE.TEA) {
        $(".correct").show();
      }
    }
    that.addClass("card-big");
    that.css({
      transform: "rotateY(180deg)",
    });
    SDK.setEventLock();
  }
  //展示宝箱
  function boxDisplay(that, data) {
    let cardWidth = $(".card").width();
    let cardHeight = $(".card").height();
    that.find(".card-front").css("background-image", `url(${data.CloseImg})`);
    that.css({
      transform: "rotateY(180deg)",
    });
    setTimeout(() => {
      that.find(".card-front").css("background-image", `url(${data.OpenImg})`);
      $(".box-animation").css({
        left: `${that.position().left - cardWidth / 2}px`, // X轴移动位置
        top: `${that.position().top - cardHeight / 2}px`, // Y轴移动位置
        display: "block",
      });
      boxAnimationFun(that);
    }, 800);
  }
  //麦克风动画
  microphoneFun();
  async function microphoneFun() {
    microphoneJson = await lottieAnimations.init(
      microphoneJson,
      "./image/microphone.json",
      "#microphone",
      true
    );
    lottieAnimations.play(microphoneJson);
    microphoneJson.addEventListener(
      "microphoneComplete",
      function microphoneAnimation() {
        console.log("麦克风动画结束");
        lottieAnimations.stop(microphoneJson);
        microphone.hide();
      }
    );
  }
  //宝箱开启动画
  async function boxAnimationFun(that) {
    //宝箱音频
    let boxAudio = $(".box-audio")[0];
    SDK.playRudio({
      index: boxAudio,
      syncName: $(".box-audio").attr("data-syncaudio"),
    });
    boxAnimationJson = await lottieAnimations.init(
      boxAnimationJson,
      "./image/box-animation.json",
      "#box-animation",
      false
    );
    lottieAnimations.play(boxAnimationJson);
    boxAnimationJson.addEventListener(
      "complete",
      function boxAnimationAnimation() {
        console.log("宝箱开启动画结束");
        that.css({
          // 'transition': 'all 1s',
          transform: "rotateY(180deg) scale(0)",
          opacity: "0",
        });
        $(".box-animation").hide();
        // lottieAnimations.stop(boxAnimationJson);
        lottieAnimations.destroy(boxAnimationJson); //销毁顾客动画
        cardClick = true;
        updateProgress(25);
        SDK.setEventLock();
      }
    );
  }
  let correctClick = true; //正确按钮是否可点击
  let roteAnimations = null; //正确按钮动画
  // 点击正确按钮
  $(".correct").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_SPOKEBUTTON",
        data: {},
      },
      USER_TYPE.TEA
    );
    if (correctClick) {
      correctClick = false;
      if (!isSync) {
        $(this).trigger("syncCorrectClick");
        return;
      }

      SDK.bindSyncEvt({
        sendUser: "",
        receiveUser: "",
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "syncCorrectClick",
        funcType: "audio",
      });
    }
  });

  $(".correct").on("syncCorrectClick", async function (e, message) {
    //水果音频
    let correctAudio = $(".correct-audio")[0];
    SDK.playRudio({
      index: correctAudio,
      syncName: $(".correct-audio").attr("data-syncaudio"),
    });
    //水果彩带动效显示
    $(".fruit-ribbon").show();
    roteAnimations = await lottieAnimations.init(
      roteAnimations,
      "./image/fruit-ribbon.json",
      "#fruit-ribbon",
      false
    );
    lottieAnimations.play(roteAnimations);
    roteAnimations.addEventListener(
      "complete",
      function handleAnimationComplete() {
        console.log("水果彩带动画结束");
        $(".fruit-ribbon").hide();
        // lottieAnimations.stop(roteAnimations);
        lottieAnimations.destroy(roteAnimations); //销毁上一轮的烟雾动画
        $(".energy-ball").show();
        $(".energy-ball").animate(
          {
            top: "0rem",
          },
          {
            duration: 500,
            complete: function () {
              // 动画结束后执行的代码
              console.log("动画完成");
              $(".energy-ball").hide();
              $(".energy-ball").css({
                top: "calc(50% - 1rem)",
              });
              $(".card-big").css(
                "transform-origin",
                `${cardLeft}px ${cardTop}px`
              );
              $(".optionUl-model").hide();
              $(".microphone").hide();
              $(".correct").hide();
              $(".card-big").css({
                transform: "rotateY(180deg) scale(0)",
                opacity: "0",
              });
              $(".optionUl li").removeClass("card-big");
              $(".card").css("transform-origin", "center");
              updateProgress(10);
              correctClick = true;
              cardClick = true;
              moduleAuthorizationFn("stuOnly", "12");
              SDK.setEventLock();
            },
          }
        );
      }
    );
  });
  // 更新进度条
  function updateProgress(num) {
    //进度条音频
    let progressAudio = $(".progress-audio")[0];
    SDK.playRudio({
      index: progressAudio,
      syncName: $(".progress-audio").attr("data-syncaudio"),
    });

    percentage = percentage + num;
    $(".progress-bar").css("width", percentage + "%");
    if (cardNum >= randomList.length + 3) {
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_COMPLETE",
          data: {
            result: "success",
          },
        },
      );
      $(".optionUl li").hide(); //未消除的全部隐藏
      endGame();
    }
  }

  // 结束游戏
  function endGame() {
    if (baseMapList[0].victoryImg) {
      getImageSize(baseMapList[0].victoryImg, (width, height) => {
        // 初始底图
        $(".victory-img").css({
          "background-image": `url(${baseMapList[0].victoryImg})`,
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        });
      });
    }
    cardClick = false;
    feedback();
  }
  //正反馈动画
  async function feedback() {
    console.log("全部正确结束，执行反馈动画");
    await feedbackAnimation("feedKey1");
    SDK.setEventLock();
  }
  // 显示图片信息，播放音频
  function getImageSize(url, callback) {
    const img = new Image();
    img.src = url;
    // 确保图片加载完成后获取宽高
    img.onload = function () {
      const width = img.width;
      const height = img.height;
      callback(width, height);
    };
    // 处理加载错误
    img.onerror = function () {
      console.error("图片加载失败");
    };
  }
});
