// var domain = 'http://172.16.0.107:9011/pages/1152/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
import {
  feedbackAnimationSend,
  feedbackData,
  initializeFeedback,
  feedBackChange,
} from "../../../common/template/feedbackAnimation/form.js";
var domain = "";
var feedbackObjData1 = feedbackData({
  key: "feedKey1",
  name: "整体反馈",
});

var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    tImgX: "",
    tImgY: "",
    instructions: [{
      commandId: '-1'
    }],
    tg: [
      {
        title: "",
        content: "",
      },
    ],
    level: {
      high: [
        {
          title: "",
          content: "",
        },
      ],
      low: [
        {
          title: "",
          content: "",
        },
      ],
    },
    source: {
      optionLength: 6, //选项上限
      backImgList: [
        {
          backImg: "", //背面图片
        },
      ],
      options: [
        {
          contentImg: "", //内容图片1
          contentNumber: "3", //内容图片1个数
        },
      ],
      widthImg: {
        width: 0,
        height: 0,
      },
      isKeyCheck: false,

      treasureChestList: [
        {
          CloseImg: "",
          OpenImg: "",
        },
        {
          CloseImg: "",
          OpenImg: "",
        },
        {
          CloseImg: "",
          OpenImg: "",
        },
      ],
      baseMapList: [
        {
          initialImg: "",
          victoryImg: "",
        },
      ],
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: "2", //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
    },
    feedbackLists: [...feedbackObjData1],
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions:[],
};
Data.configData.feedbackLists.push(feedbackObjData1);

$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "2", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }
      if(!Data.configData.instructions){
        Data.configData.instructions = [{
            commandId: '-1'
        }]
      }
      initializeFeedback(Data);
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
  el: "#container",
  data: Data,
  mounted: function() {
    this.getDynamicInstructions();
  },
  watch: {},
  methods: {
    getDynamicInstructions: function() {
      var that = this;
      getDynamicInstructions(function(res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function() {
      addInstruction(this.configData);
    },
    removeInstruction: function(index) {
      removeInstruction(index, this.configData);
    },
    validateInstructions: function() {
      return validateInstructions(this.configData);
    },
    feedBackChange(item) {
      feedBackChange(item);
    },
    // feedback 上传
    feedbackUpload: function (e, item, attr, fileSize) {
      console.log(e, item, attr, fileSize);
      const file = e.target.files[0];
      if (file.type === "image/png") {
        this.imageUpload(e, item, attr, fileSize);
      } else {
        this.lottieUpload(e, item, attr, fileSize);
      }
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
        } else {
        }
      };
      reader.readAsText(file);
    },
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e);
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height + 1 > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
      }
      return checkSize;
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      // if ($(e.target).attr("accept")) {
      //   var acceptList = $(e.target).attr("accept").split(",");
      //   var fileAccept = "." + file.type.split("/")[1];
      //   var index = acceptList.indexOf(fileAccept);
      //   if (index === -1) {
      //     alert(
      //       "您上传的图片格式为" +
      //         fileAccept +
      //         ", 不符合上传图片格式要求，请检查后上传！"
      //     );
      //     return;
      //   }
      // }

      if (file.type === "application/json") {
        this.lottieUpload(e, item, attr, fileSize);
      }
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(","),
        isKey = dom.attr("isKey");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (isKey == "1") {
          if (data.width <= width && data.height <= height) {
            return true;
          } else {
            return false;
          }
        } else {
          if (data.width == width && data.height == height) {
            return true;
          } else {
            return false;
          }
        }
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：小于等于" +
            size.join("*") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
      }
      return checkSize;
    },
    checkTreasureChestStatus: function (treasureChestList) {
      if (treasureChestList.length === 0) return 1; // 空数组视为全空

      let hasEmpty = false;
      let hasNonEmpty = false;

      // 遍历所有宝箱对象
      for (const chest of treasureChestList) {
        // 检查CloseImg属性
        if (chest.CloseImg === "") {
          hasEmpty = true;
        } else {
          hasNonEmpty = true;
        }

        // 检查OpenImg属性
        if (chest.OpenImg === "") {
          hasEmpty = true;
        } else {
          hasNonEmpty = true;
        }

        // 如果同时存在空和非空则立即返回0
        if (hasEmpty && hasNonEmpty) {
          return 0;
        }
      }

      // 根据最终状态返回结果
      if (hasEmpty && !hasNonEmpty) {
        return 1; // 全为空
      } else if (!hasEmpty && hasNonEmpty) {
        return 2; // 全不为空
      }

      return 1; // 默认情况（理论上不会执行到这里）
    },
    validate: function () {
      var data = this.configData.source;
      var check = true;
      const optionsData = new Map(
        data.options.map((item, index) => [index, item])
      );
      let treasureChestListStatus = this.checkTreasureChestStatus(
        data.treasureChestList
      );
      if (!this.configData.bg) {
        alert("请上传背景图片");
        return;
      }

      for (let [key, item] of optionsData) {
        key++;
        if (!item.contentImg) {
          alert("请上传选项" + key + "内容图片");
          return;
        }
        if (!item.contentNumber) {
          alert("请填写" + key + "内容图片个数");
          return;
        }
      }
      const sum = data.options.reduce((total, item) => {
        return total + parseInt(item.contentNumber);
      }, 0);
      if (sum > 6) {
        alert("上传图片的出现次数总和在3-6次之间");
        return;
      }
      if (sum < 3) {
        alert("上传图片的出现次数总和在3-6次之间");
        return;
      }

      if (treasureChestListStatus == 0) {
        alert("请上传全部宝箱图片");
        return;
      }

      if (data.optionLength > 6) {
        alert("上传图片不超过6张");
        return;
      }
      return check;
    },
    onSend: function () {
      var data = this.configData;
      let feedbackStatus = feedbackAnimationSend(data);
      if(!feedbackStatus){
        return;
      }
      let treasureChestListStatus = this.checkTreasureChestStatus(
        data.source.treasureChestList
      );
      if (treasureChestListStatus == 1) {
        data.source.treasureChestList = [
          {
            CloseImg: "./image/close-img.png",
            OpenImg: "./image/open-img.png",
          },
          {
            CloseImg: "./image/close-img.png",
            OpenImg: "./image/open-img.png",
          },
          {
            CloseImg: "./image/close-img.png",
            OpenImg: "./image/open-img.png",
          },
        ];
      }
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == "-2") {
        //其他
        data.log.teachTime =
          data.log_editor.TeachTimeOtherM * 60 +
          data.log_editor.TeachTimeOtherS +
          "";
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长");
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther;
      }
      console.log(data, "388");
      var _data = JSON.stringify(data);
      var val = this.validate();
      console.log(data)
      if (val === true && this.validateInstructions()) {
        $.ajax({
          url: domain + "content?_method=put",
          type: "POST",
          data: {
            content: _data,
          },
          success: function (res) {
            console.log(res);
            window.parent.postMessage("close", "*");
          },
          error: function (err) {
            console.log(err);
          },
        });
      } else {
        // alert("带“*”为必填项");
      }
    },
    postData: function (file, item, attr) {
      var FILE = "file";
      var bg = arguments.length > 2 ? arguments[2] : null;
      var oldImg = item[attr];
      var data = new FormData();
      data.append("file", file);
      if (oldImg != "") {
        data.append("key", oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: "post",
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          item[attr] = domain + res.data.key;
        },
        error: function (err) {
          console.log(err);
        },
      });
    },
    audioUpload: function (e, item, attr) {
      //校验规则
      //var _type = this.rules.audio.sources.type;

      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      // if (!_type.test(type)) {
      //     alert("您上传的文件类型错误，请检查后再上传！");
      //     return;
      // }
      if ((size / 1024).toFixed(2) > 500) {
        console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      } else {
        console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      }
      if (
        !isNaN(parseInt($(e.target).attr("volume"))) &&
        (size / 1024).toFixed(2) > parseInt($(e.target).attr("volume"))
      ) {
        alert(
          "您上传的音频大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            $(e.target).attr("volume") +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      this.postData(file, item, attr);
    },
    addTg: function (item) {
      this.configData.tg.push({
        title: "",
        content: "",
      });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({ title: "", content: "" });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: "", content: "" });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    addOption: function (item) {
      if (item) {
        this.configData.source.options.push(item);
      } else {
        this.configData.source.options.push("");
      }
    },
    delOption: function (item) {
      this.configData.source.options.remove(item);
    },
  },
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
