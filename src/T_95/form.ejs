<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>JGG0001FT_九宫格FT</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>" />
  <script src="./form/js/jquery-2.1.1.min.js"></script>
  <script src="./form/js/vue.min.js"></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">JGG0001FT_九宫格FT</h3>

      <% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>

        <!-- 内容设置 -->
        <div class="c-group">
          <div class="c-title">上传卡牌内容图片</div>
          <div class="c-area upload img-upload">
            <label class="field-label">内容图片最多上传6张，剩余3个空位是宝箱，所有图片的回答次数相加范围：3-6次；上传的图片小于6张，同一张图片会展示在多个位置上。</label>
            <label class="field-label">注意：首轮不中宝箱，第二轮必中宝箱，最后一轮必中宝箱</label>
            <!-- 卡牌背面图片 -->
            <div class="field-wrap" v-for="(item,index) in configData.source.backImgList">
              <label class="field-label">卡牌背面图片</label>
              <label :for="'content-pic-back-'+index" class="btn btn-show upload" v-if="!item.backImg">上传图片</label>
              <label :for="'content-pic-back-'+index" class="btn upload re-upload"
                v-if="item.backImg!=''?true:false">重新上传</label>
              <div class="audio-tips">
                <label>
                  <span><em>JPG、PNG格式，图片尺寸420x240，小于等于50KB，不上传会有默认样式</em></span>
                </label>
              </div>
              <input type="file" v-bind:key="Date.now()" class="btn-file" size="420*240" accept=".jpg,.png,.jpeg"
                isKey="1" :id="'content-pic-back-'+index" @change="imageUpload($event,item,'backImg',50)" />

              <div class="img-preview" v-if="item.backImg">
                <img v-bind:src="item.backImg" alt="" />
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="item.backImg=''">删除</span>
                </div>
              </div>
            </div>
            <ul>
              <li v-for="(item,index) in configData.source.options">
                <div class="c-well">
                  <!-- 内容图片 -->
                  <div class="field-wrap">
                    <div class="add-field-content">
                      <label class="field-label" for="">选项{{ index + 1 }}</label>
                      <span class="dele-tg-btn" v-on:click="delOption(item)"
                        v-show="configData.source.options.length>=2"></span>
                    </div>
                    <label class="field-label">内容图片<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</label>
                    <label :for="'content-pic-one-'+index" class="btn btn-show upload"
                      v-if="!item.contentImg">上传图片</label>
                    <label :for="'content-pic-one-'+index" class="btn upload re-upload"
                      v-if="item.contentImg!=''?true:false">重新上传</label>
                    <div class="audio-tips">
                      <label>
                        <span><em>JPG、PNG格式，图片尺寸1050x600，小于等于50KB</em></span>
                      </label>
                    </div>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="1050*600" accept=".jpg,.png,.jpeg"
                      isKey="1" :id="'content-pic-one-'+index" @change="imageUpload($event,item,'contentImg',50)" />

                    <div class="img-preview" v-if="item.contentImg">
                      <img v-bind:src="item.contentImg" alt="" />
                      <div class="img-tools">
                        <span class="btn btn-delete" v-on:click="item.contentImg=''">删除</span>
                      </div>
                    </div>

                    <div class="rules-content">
                      <label class="rules-field-label">回答</label>
                      <input type="number" class="rules-input-txt" style="
                          margin: 0 10px 0 70px;
                          width: 120px !important;
                          display: inline-block;
                        " oninput="if(value>6)value=6;if(value<1)value=1" v-model="item.contentNumber"
                        placeholder="请输入" />
                      <label class="rules-field-label">次</label>
                    </div>
                  </div>
                  <br>
                </div>
              </li>
            </ul>
            <button type="button" class="add-btn"
              v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            contentImg: '',
            contentNumber: '',
          })">
              添加内容图片（最多6张）
            </button>
          </div>
        </div>

        <!-- 三个宝箱图片 -->
        <div class="c-group">
          <div class="c-title">三个宝箱图片</div>
          <div class="c-area upload img-upload">
            <label>非必填，不填展示默认样式</label>
            <div class="audio-tips">
              <label>
                <p><em>jpg、PNG格式，固定尺寸420x240，小于等于30KB</em></p>
                <p><em>开启或关闭成对配置，不能单独只配置一个状态</em></p>
              </label>
            </div>
            <ul>
              <li v-for="(item,index) in configData.source.treasureChestList">
                <div class="c-well">
                  <!-- 宝箱关闭 -->
                  <div class="field-wrap">
                    <label class="field-label">宝箱{{ index+1 }}关闭</label>
                    <label :for="'content-pic-close-'+index" class="btn btn-show upload"
                      v-if="!item.CloseImg">上传图片</label>
                    <label :for="'content-pic-close-'+index" class="btn upload re-upload"
                      v-if="item.CloseImg!=''?true:false">重新上传</label>

                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="420*240" accept=".jpg,.png,.jpeg"
                      :id="'content-pic-close-'+index" @change="imageUpload($event,item,'CloseImg',30)" />

                    <div class="img-preview" v-if="item.CloseImg">
                      <img v-bind:src="item.CloseImg" alt="" />
                      <div class="img-tools">
                        <span class="btn btn-delete" v-on:click="item.CloseImg=''">删除</span>
                      </div>
                    </div>
                  </div>

                  <!-- 宝箱开启 -->
                  <div class="field-wrap">
                    <label class="field-label">宝箱{{ index+1 }}开启</label>
                    <label :for="'content-pic-open-'+index" class="btn btn-show upload"
                      v-if="!item.OpenImg">上传图片</label>
                    <label :for="'content-pic-open-'+index" class="btn upload re-upload"
                      v-if="item.OpenImg!=''?true:false">重新上传</label>

                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="420*240" accept=".jpg,.png,.jpeg"
                      :id="'content-pic-open-'+index" @change="imageUpload($event,item,'OpenImg',30)" />

                    <div class="img-preview" v-if="item.OpenImg">
                      <img v-bind:src="item.OpenImg" alt="" />
                      <div class="img-tools">
                        <span class="btn btn-delete" v-on:click="item.OpenImg=''">删除</span>
                      </div>
                    </div>
                  </div>

                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 九宫格下方底图 -->
        <div class="c-group">
          <div class="c-title">九宫格下方底图</div>
          <div class="c-area upload img-upload">
            <label>非必填，不填展示默认样式</label>
            <ul>
              <li v-for="(item,index) in configData.source.baseMapList">
                <div class="c-well">
                  <!-- 初始底图 -->
                  <div class="field-wrap">
                    <label class="field-label">初始底图&nbsp;&nbsp;&nbsp;&nbsp;</label>
                    <label :for="'content-pic-initialImg'+index" class="btn btn-show upload"
                      v-if="!item.initialImg">上传图片</label>
                    <label :for="'content-pic-initialImg'+index" class="btn upload re-upload"
                      v-if="item.initialImg!=''?true:false">重新上传</label>

                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="900*600" accept=".jpg,.png,.jpeg"
                      :id="'content-pic-initialImg'+index" @change="imageUpload($event,item,'initialImg',80)" />

                    <div class="img-preview" v-if="item.initialImg">
                      <img v-bind:src="item.initialImg" alt="" />
                      <div class="img-tools">
                        <span class="btn btn-delete" v-on:click="item.initialImg=''">删除</span>
                      </div>
                    </div>
                    <div class="audio-tips">
                      <label>
                        <span><em>jpg、PNG格式，尺寸900x600，小于等于80KB</em></span>
                      </label>
                    </div>
                  </div>

                  <!-- 胜利后底图 -->
                  <div class="field-wrap">
                    <label class="field-label">胜利后底图</label>
                    <label :for="'content-pic-victoryImg'+index" class="btn btn-show upload"
                      v-if="!item.victoryImg">上传图片</label>
                    <label :for="'content-pic-victoryImg'+index" class="btn upload re-upload"
                      v-if="item.victoryImg!=''?true:false">重新上传</label>

                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="1920*1080"
                      accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-victoryImg'+index"
                      @change="imageUpload($event,item,'victoryImg',120)" />

                    <div class="img-preview" v-if="item.victoryImg">
                      <img v-bind:src="item.victoryImg" alt="" />
                      <div class="img-tools">
                        <span class="btn btn-delete" v-on:click="item.victoryImg=''">删除</span>
                      </div>
                    </div>
                    <div class="audio-tips">
                      <label>
                        <span><em>jpg、PNG格式，尺寸小于等于1920x1080，小于等于120KB</em></span>
                      </label>
                    </div>
                  </div>

                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 正反馈 -->
        <% include ./src/common/template/feedbackAnimation/form %>

          <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="" />
        </div>
        <ul class="show-txt">
          <li>图片格式：<em></em>JPG/PNG</li>
          <li>声音格式：<em></em>MP3</li>
          <li>视频格式：<em></em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src="./form/js/form.js?_=<%= Date.now() %>"></script>

</html>
