@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;

  .desc {
    top: 0.6rem;
  }

  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.container {
  background-size: auto 100%;
  position: relative;

  .content-main {
    .progress-container {
      margin: 0 auto;
      width: 8.1rem;
      height: 0.6rem;
      position: absolute;
      top: 0rem;
      left: 5.55rem;
      background-image: url('../image/progressa.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 6;

      .progress-img {
        position: absolute;
        width: 0.74rem;
        height: 0.69rem;
        top: 0.05rem;
        right: 0.36rem;
        background-image: url('../image/progressb.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 6;
      }

      .progress-content {
        position: absolute;
        width: 7rem;
        height: 0.2rem;
        left: 0.55rem;
        top: 0.28rem;
        border-radius: 0.12rem;
        overflow: hidden;

        .progress-bar {
          height: 100%;
          width: 0%;
          background-color: #FFD02F;
          transition: width 0.3s;
          border-radius: 0.12rem;
        }
      }
    }




    .optionUl-model {
      position: absolute;
      width: 100%;
      height: 100%;
      background: #000000;
      opacity: 0.7;
      z-index: 7;
      display: none;
    }

    .energy-ball {
      position: absolute;
      width: 2rem;
      height: 2rem;
      left: calc(50% - 1rem);
      top: calc(50% - 1rem);
      background-image: url('../image/energy-ball.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      display: none;
      z-index: 8;
    }

    .option-div {
      position: absolute;
      left: 2.4rem;
      top: 1.2rem;
      width: 14.4rem;
      height: 7.8rem;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;

      .optionUl {
        position: absolute;
        // left: 2.4rem;
        // top: 1.2rem;
        width: 100%;
        height: 100%;

        .card {
          width: 4.2rem;
          height: 2.4rem;
          position: absolute;
          perspective: 10rem;
          cursor: pointer;
          transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
          transform-style: preserve-3d;
          // transition: all 1s ease;
          transform-origin: center;
          z-index: 6;
          transform: rotateY(180deg);
        }

        .card-big {
          z-index: 8;
          left: 1.95rem !important; // X轴移动位置
          top: 0.9rem !important; // Y轴移动位置
          width: 10.5rem; // 宽度放大
          height: 6rem; // 高度放大
        }

        .card-inner {
          position: relative;
          width: 100%;
          height: 100%;
          transform-style: preserve-3d;
          transition: transform 0.6s;

          @keyframes breathing {

            0%,
            100% {
              transform: scale(1);
            }

            50% {
              transform: scale(1.1);
            }

            75% {
              transform: scale(1);
            }
          }

          .box {
            animation: breathing 3s ease-in-out infinite;
          }
        }

        .card-face {
          position: absolute;
          width: 100%;
          height: 100%;
          backface-visibility: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
          background-size: 100% 100%;
          background-position: center;
          background-repeat: no-repeat;
        }

        .card-front {
          transform: rotateY(0deg);
        }

        .card-back {
          transform: rotateY(180deg);
        }

        .card.flipped .card-inner {
          transform: rotateY(180deg);
        }

      }

    }

    .victory {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .victory-img {
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }
    }

    .microphone {
      position: absolute;
      width: 3.2rem;
      height: 2.23rem;
      left: 7.82rem;
      bottom: 0rem;
      display: none;
      z-index: 8;
    }

    .box-animation {
      position: absolute;
      width: 8.4rem;
      height: 4.8rem;
      left: 0rem;
      bottom: 0rem;
      display: none;
      z-index: 7;
    }

    .start {
      position: absolute;
      width: 2.50rem;
      height: 1.04rem;
      left: 8.35rem;
      top: 9.46rem;
      background: url("../image/start.png") no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      // display: none;
      transition: transform 0.1s ease;
      pointer-events: none;
    }

    .start:hover {
      transform: scale(0.9);
    }

    .correct {
      position: absolute;
      width: 1.2rem;
      height: 1.2rem;
      right: 6.15rem;
      bottom: 0.82rem;
      background: url("../image/JGG-right.png") no-repeat center;
      background-size: 100% 100%;
      display: none;
      cursor: pointer;
      z-index: 8;
    }

    .fruit-ribbon {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0rem;
      top: 0rem;
      z-index: 8;
      display: none;
    }

    //小手点击动画
    .handsss {
      width: 1.8rem;
      height: 1.8rem;
      background: url('../image/hands.png');
      background-size: 7.2rem 1.8rem;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.7);
      animation: handClick 0.8s steps(4) infinite;
      opacity: 1;
      cursor: pointer;
      z-index: 100;
      display: none;
      pointer-events: none;
    }

    @keyframes handClick {
      0% {
        background-position: 0 0;
      }

      100% {
        background-position: 133% 0;
      }
    }

    @-webkit-keyframes handClick {
      0% {
        background-position: 0 0;
      }

      100% {
        background-position: 133% 0;
      }
    }
  }
}
