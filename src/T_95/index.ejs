<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="JGG0001FT_九宫格FT" ; %>
    <%include ./src/common/template/index_head %>
</head>

<body>
  <div class="container" id="container" data-syncresult="1">
    <!-- 反馈动效 -->
    <%include ./src/common/template/feedbackAnimation/index.ejs %>
      <section class="commom">
        <div class="desc"></div>
        <div class="title">
          <h3></h3>
        </div>
      </section>
      <div class="content-main">
        <!-- 进度条 -->
        <div class="progress-container">
          <div class="progress-img"></div>
          <div class="progress-content">
            <div class="progress-bar"></div>
          </div>
        </div>
        <!-- 进度条音频 -->
        <audio class="progress-audio" src="./audio/energyBar.wav" data-syncaudio="progress-audio"></audio>
        <!-- 卡牌 -->
        <div class="option-div">
          <ul class="optionUl"></ul>
          <!-- 宝箱开启动效 -->
          <div class="box-animation" id="box-animation"></div>
        </div>
        <!-- 翻牌音频 -->
        <audio class="card-audio" src="./audio/flipCards.mp3" data-syncaudio="card-audio"></audio>
        <!-- 胜利后的底图 -->
        <div class="victory">
          <div class="victory-img"></div>
        </div>
        <!-- 小手 -->
        <div class="handsss"></div>
        <!-- 宝箱音频 -->
        <audio class="box-audio" src="./audio/box.mp3" data-syncaudio="box-audio"></audio>
        <!-- 水果蒙层 -->
        <div class="optionUl-model"></div>
        <!-- 能量球 -->
        <div class="energy-ball"></div>

        <!-- 开始 -->
        <div class="start" data-syncactions="startSyncactions"></div>
        <!-- start音频 -->
        <audio class="start-audio" src="./audio/card.wav" data-syncaudio="correct-audio"></audio>

        <!-- 麦克风 -->
        <div class="microphone" id="microphone"></div>

        <!-- 正确 -->
        <div class="correct" data-syncactions="correctSyncactions"></div>
        <!-- 正确音频 -->
        <audio class="correct-audio" src="./audio/correct.mp3" data-syncaudio="correct-audio"></audio>
        <!-- 水果彩带动效 -->
        <div id="fruit-ribbon" class="fruit-ribbon"></div>
        <!-- 水果音频 -->
        <audio class="fruit-audio" src="./audio/fruit.mp3" data-syncaudio="fruit-audio"></audio>
      </div>

      <script type="text/javascript">
        document.documentElement.addEventListener(
          "touchstart",
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault();
            }
          },
          false
        );
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener(
          "touchend",
          function (event) {
            var now = Date.now();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
      </script>
  </div>
  <%include ./src/common/template/index_bottom %>
    <%include ./src/common/template/lottie %>
</body>

</html>
