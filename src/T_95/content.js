var configData = {
  bg: "./assets/images/bg.png",
  desc: "",
  title: "",
  tImg: "./image/b164806a1b826b1b386f5496478573fe.png",
  tImgX: 1340,
  tImgY: 15,
  tg: [
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "1111111111111111111",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
  ],
  level: {
    high: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
    low: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
  },
  source: {
    optionLength: 6, //选项上限
    backImgList: [
      {
        backImg: "./image/back-img.png", //背面图片
      },
    ],
    options: [
      {
        contentImg: "./assets/images/fruit1.png", //内容图片
        contentNumber: "1", //内容图片个数
      },
      {
        contentImg: "./assets/images/fruit2.png", //内容图片
        contentNumber: "1", //内容图片个数
      },
      {
        contentImg: "./assets/images/fruit3.png", //内容图片
        contentNumber: "1", //内容图片个数
      },
      {
        contentImg: "./assets/images/fruit4.png", //内容图片
        contentNumber: "1", //内容图片个数
      },
      {
        contentImg: "./assets/images/fruit5.png", //内容图片
        contentNumber: "1", //内容图片个数
      },
      {
        contentImg: "./assets/images/fruit6.png", //内容图片
        contentNumber: "1", //内容图片个数
      },
    ],
    treasureChestList: [
      {
        CloseImg: "./image/close-img.png",
        OpenImg: "./image/open-img.png",
      },
      {
        CloseImg: "./image/close-img.png",
        OpenImg: "./image/open-img.png",
      },
      {
        CloseImg: "./image/close-img.png",
        OpenImg: "./image/open-img.png",
      },
    ],
    baseMapList: [
      {
        initialImg: "./assets/images/fruit1.png",
        victoryImg: "./assets/images/fruit2.png",
      },
    ],
    widthImg: {
      width: 1200,
      height: 401,
    },
    isKeyCheck: true,
  },
  feedbackLists: [
    {
      positiveFeedback: "-1",
      feedbackList: [
        { id: "-1", json: "", mp3: "" },
        { id: "0", json: "./image/prefect.json", mp3: "./audio/prefect.mp3" },
        { id: "1", json: "./image/goldCoin.json", mp3: "./audio/goldCoin.mp3" },
        { id: "2", json: "./image/FKJB.json", mp3: "./audio/resultWin.mp3" },
        { id: "9", json: "./image/guang.json", mp3: "" },
        { id: "10", json: "./image/chest-change.json", mp3: "./audio/chest-change.mp3" },
      ],
      feedbackObj: { id: "9", json: "./image/guang.json", mp3: "" },
      feedback: "./image/prefect.json",
      feedbackAudio: "./audio/prefect.mp3",
      feedbackName: "整体反馈",
      background:"0",
      loop: false,
      key: "feedKey1",
    },
  ],
};
(function (pageNo) {
  configData.page = pageNo;
})(0);
