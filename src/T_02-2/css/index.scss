@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}


.container{
	position: relative;

}
.guides {
  width: 4.5rem;
  height: 5.3rem;
  position: absolute;
  top:-0.4rem;
  left:-0.8rem;
  z-index: 9999;
  transform: rotate(90deg); /* 旋转 180 度 */
  
}
.stage{
	width: 100%;
    height: 100%;
    position: relative;
    .card_list {
        width: 9.5rem;
        height: 4.3rem;
        position: absolute;
        left: 5.5rem;
        top: 3.5rem;
        .card{
            width: 3rem;
            height: 4.3rem;
            position: absolute;
            border: .5rem;
            transform-style: preserve-3d;
            transition: .3s linear;
            .num_box{
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: 6;
                backface-visibility: hidden;
                border-radius: .2rem;
                overflow: hidden;
                // transition: .5s;
                .card_img{
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                }
                .num_img{
                    position: absolute;
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    width: 1.6rem;
                    height: auto;
                    display: none; 
                }
               /*  .num_shake{
                    animation: numshake 1s;
                }
                @keyframes numshake {
                    0%{
                        transform: scale(1)
                    }
                    20%{
                        transform: scale(1.1)
                    }
                    40%{
                        transform: scale(0.9)
                    }
                    60%{
                        transform: scale(1.1)
                    }
                    80%{
                        transform: scale(0.9)
                    }
                    100%{
                        transform: scale(1)
                    }
                } */
            }
            .img_box{
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: 4;
                transform:rotateY(-180deg);
                backface-visibility: hidden;
                border-radius: .2rem;
                overflow: hidden;
                .main_img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
        // .c_1{
        //     left: .17rem;
        // }
        // .c_2{
        //     left:3.96rem; 
        // }
        // .c_3{
        //     left:7.62rem;
        // }
        .card_rotate_one {
            transform: rotateY(180deg);
        }
    }

    .person{
        width: 5.55rem;
        height:6.54rem;
        cursor: pointer;
        position: absolute;
        bottom: 0rem;
        left: .1rem;
        // background: url('../image/person_list.png') no-repeat;
        // background-size: 33.3rem auto;
    }
    // .person_animate {
    //     animation: person steps(6) .8s 2;
    // }
    // @keyframes person {
    //     0%{
    //         background-position: 0 0;
    //     }
    //     100%{
    //         background-position: 120% 0;
    //     }
    // }
    // @-webkit-keyframes person {
    //     0%{
    //         background-position: 0 0;
    //     }
    //     100%{
    //         background-position: 120% 0;
    //     }
    // }
    .hand{
        position: absolute;
        width:1.8rem;
        height:1.8rem;
        left: 50%;
        top: 3.6rem;
        margin-left: -0.35rem;
        background: url('../image/hands.png');
        background-size: 7.2rem 1.8rem;
        cursor: pointer;
        animation: hand 1s steps(4, end) 0s infinite;
        -webkit-animation: hand 1s steps(4, end) 0s infinite;
        opacity: 0;
        z-index: 99;
    }
    @keyframes hand {
        0%{
            background-position: 0 0;
        }
        100%{
            background-position:133% 0;
        }
    }
    @-webkit-keyframes hand {
        0%{
            background-position: 0 0;
        }
        100%{
            background-position:133% 0;
        }
    }
}

