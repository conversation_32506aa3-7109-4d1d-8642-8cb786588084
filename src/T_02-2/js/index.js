"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";

$(function () {


  window.h5Template = {
    hasPractice: "1",
  };
  let h5SyncActions = parent.window.h5SyncActions;
  let configDatas = configData;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/defaultBg.png)" });
  }
  // if (isSync) {
  //     $('.hand').css('opacity','1');
  // } else {
  //     $('.hand').css('opacity','1');
  // }
  /**
   * 创建dom
   */
  let cardBgImg = configDatas.source.cardBg;
  let cardImgSrc = "./image/card.png";
  let characterNum = 0;
  if (cardBgImg) {
    cardImgSrc = cardBgImg;
  }
  initLottie();
  async function initLottie() {
    try {
      characterNum = await lottieAnimations.init(
        characterNum,
        // "//cdn.51suyang.cn/apollo/public/json/DX-09-388x474.json",
        "./image/DX-09-388x474.json",
        `#person`
      );
    } catch (error) {
      console.error("初始化 Lottie 动画失败:", error);
      return null;
    }
  }

  var posLeftThree = [0, 3.5, 7];
  var posLeftFour = [0, 3.2, 6.4, 9.6];
  var posLeft = [];
  if (configDatas.source.cardList.length == "4") {
    posLeft = posLeftFour;
    $(".card_list").css("width", "11.3rem");
    $(".card").css({
      width: "2.7rem",
      height: "4rem",
    });
  } else {
    posLeft = posLeftThree;
  }
  let cardHtml = "";
  for (let i = 0; i < configDatas.source.cardList.length; i++) {
    cardHtml += `<div class="card c_${i + 1}" style="left:${
      posLeft[i]
    }rem" data-syncactions='card_item_${i}'>
            <audio src='./audio/re.mp3' data-syncaudio="audio3"></audio>
            <div class="num_box">
                <img src=${cardImgSrc} alt="" class="card_img">
            </div>
            <div class="img_box">
                <img src=${
                  configDatas.source.cardList[i].img
                } alt="" class="main_img">
            </div>
        </div>`;
  }
  $(".card_list").html(cardHtml);
  // $('.card').addClass('card_rotate_one');

  if (configDatas.source.cardList.length == "4") {
    $(".card").css({
      width: "2.7rem",
      height: "4rem",
    });
  }
  window.generalTplData = function (message) {
    console.log("message=====>", message);
    if (message.actionType == "start") {
      if (game_num <= 3) {
        if (itemClick) {
          itemClick = false;
          if (!isSync) {
            $(".person").trigger("personClick");
            return;
          }
          SDK.bindSyncEvt({
            index: $(".person").data("syncactions"),
            eventType: "click",
            method: "event",
            funcType: "audio",
            syncName: "personClick",
            otherInfor: {
              hasClsaaArr: [],
              game_num_msg: game_num,
              clickCardNum_msg: 0,
            },
            recoveryMode: "1",
          });
        }
      }
    }
    if (message.actionType == "fingerDome") {
      $(".hand").css("opacity", "1");
    }
    if (message.actionType == "highlight_image") {
      guideAnima(message.index)
    }
  };
  function guideAnima(num) {
    console.log("guideNum", num)
    let numIndex = num -1;
    let postionTop = $(".card").eq(numIndex);
    console.log("postionTop", postionTop)
    postionTop.append(`<div id="guides${numIndex}" class="guides${numIndex} guides"></div>`);
    var slideDiv = postionTop.find('#guides');
    if(slideDiv) {
      guidesAnimationFn(numIndex);
    }

  }
  function guidesAnimationFn(num) {
    // let data ="//cdn.51suyang.cn/apollo/public/json/Animation%20-%201723183777052.json"
    let data ="./image/Animation - 1723183777052.json"
    $.get(data, {}, function (res) {
      if (res.w) {
        $(`#guides${num}`).css({
          // width: res.w / 100 + "rem",
          // height: res.h / 100 + "rem",
        });
        lottie.loadAnimation({
          //初始化
          container: document.getElementById(`guides${num}`), //在哪个dom容器中生效
          renderer: "svg", //渲染方式svg
          loop: false, //循环
          autoplay: true, //自动播放
          path: data, //动画数据
        });
      }
    });
  }
  /**
   * 点击魔术师 开始执行动画
   */
  let clickCardNum = 0;
  let game_num = 1;
  let itemClick = true;
  let cardCanClick = false;
  let hasCLArr = [];
  let cardNum = configDatas.source.cardList.length; // 图片数量

  $(".person").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (game_num <= 3) {
      if (itemClick) {
        itemClick = false;
        if (!isSync) {
          $(this).trigger("personClick");
          return;
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          funcType: "audio",
          syncName: "personClick",
          otherInfor: {
            hasClsaaArr: [],
            game_num_msg: game_num,
            clickCardNum_msg: 0,
          },
          recoveryMode: "1",
        });
      }
    }
  });

  $(".person").on("personClick", function (e, message) {
    lottieAnimations.play(characterNum);
    if (isSync && message.operate == "5") {
      var otherInfor = message.data[0].value.syncAction.otherInfor;
      clickCardNum = otherInfor.clickCardNum_msg * 1;
      hasCLArr = otherInfor.hasClsaaArr;
      game_num = otherInfor.game_num_msg * 1;
      person_fn(game_num, hasCLArr, clickCardNum);
    } else {
      hasCLArr = [];
      game_num = game_num;
      cardCanClick = false;
      $(".hand").css("opacity", "0");
      $(".card").removeClass("card_rotate_one");
      $("#person").addClass("person_animate");
      $("#person").on("animationend webkitAnimationEnd", function () {
        $("#person").removeClass("person_animate");
      });
      setTimeout(function () {
        if (cardNum == "3") {
          // $('.all')[0].onended = function () {
          setTimeout(function () {
            cardCanClick = true;
            SDK.setEventLock();
          }, 500);
          // }
        } else {
          setTimeout(function () {
            $(".card").css("cursor", "pointer");
            cardCanClick = true;
            SDK.setEventLock();
          }, 500);
          // }
        }
      }, 1000);
    }

    flipCards();

    itemClick = true;
  });
  async function flipCards() {
    const cards = $('.card');
    for (let index = 0; index < cards.length; index++) {
        const $card = $(cards[index]);
        let time = index+1;
        console.log("时间", time * 1200)
        await new Promise(resolve => setTimeout(resolve, time * 1200));
        console.log($card.data('syncactions'));
        if (game_num <= cardNum &&!$card.hasClass('card_rotate_one')) {
            console.log($card);
            if (!isSync) {
                $card.trigger('cardClick');
                continue;
            }
            hasCLArr.push($card.index());
            SDK.bindSyncEvt({
                index: $card.data('syncactions'),
                eventType: 'click',
                method: 'event',
                funcType: 'audio',
                syncName: 'cardClick',
                otherInfor: {
                    hasClsaaArr: hasCLArr,
                    game_num_msg: game_num,
                    clickCardNum_msg: clickCardNum
                },
                recoveryMode: '1'
            });
        }
    }
}

  /**
   * 卡片
   */
  $(".card").on("cardClick", function (e, message) {
    console.log("进入");
    if (isSync && message.operate == "5") {
      var otherInfor = message.data[0].value.syncAction.otherInfor;
      clickCardNum = otherInfor.clickCardNum_msg * 1;
      hasCLArr = otherInfor.hasClsaaArr;
      game_num = otherInfor.game_num_msg;
      person_fn(game_num, hasCLArr, clickCardNum);
      SDK.setEventLock();
    } else {
      // $(this).find('audio').get(0).play();
      SDK.playRudio({
        index: $(this).find("audio").get(0),
        syncName: $(this).find("audio").attr("data-syncaudio"),
      });
      if (isSync) {
        var otherInfor = message.data[0].value.syncAction.otherInfor;
        hasCLArr = otherInfor.hasClsaaArr;
      }
      clickCardNum++;
      $(this).addClass("card_rotate_one");
      if (clickCardNum >= cardNum) {
        clickCardNum = 0;
        game_num++;
        itemClick = true;
      } else {
        cardCanClick = true;
      }
      SDK.setEventLock();
    }
  });
  function person_fn(game_nums, hasCLArrs, clickCardNums) {
    cardCanClick = false;
    itemClick = false;
    $(".hand").css("opacity", "0");
    $(".card").removeClass("card_rotate_one");
    $("#person").addClass("person_animate");
    $("#person").on("animationend webkitAnimationEnd", function () {
      $("#person").removeClass("person_animate");
    });
    setTimeout(function () {
      if (cardNum == "3") {
        setTimeout(function () {
          $(".card").css("cursor", "pointer");
          cardCanClick = true;
          game_num = game_nums * 1;
          clickCardNum = clickCardNums * 1;
          clickCardNum++;
          for (let i = 0; i < hasCLArr.length; i++) {
            $(".card").eq(hasCLArr[i]).addClass("card_rotate_one");
          }
          if (clickCardNum >= cardNum) {
            clickCardNum = 0;
            game_num++;
            itemClick = true;
          } else {
            cardCanClick = true;
          }
          SDK.setEventLock();
        }, 500);
      } else {
        setTimeout(function () {
          $(".card").css("cursor", "pointer");
          $(".card").css("cursor", "pointer");
          cardCanClick = true;
          game_num = game_nums * 1;
          clickCardNum = clickCardNums * 1;
          clickCardNum++;
          for (let i = 0; i < hasCLArr.length; i++) {
            $(".card").eq(hasCLArr[i]).addClass("card_rotate_one");
          }
          if (clickCardNum >= cardNum) {
            clickCardNum = 0;
            game_num++;
            itemClick = true;
          } else {
            cardCanClick = true;
          }
          SDK.setEventLock();
        }, 500);
      }
    }, 1000);
  }
});
