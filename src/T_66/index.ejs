<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TMA0003_消消乐"; %>
        <%include ./src/common/template/index_head %>
        <% include ./src/common/template/hint/index.ejs %>
      </head>
<body>

<div class="container" id="container" data-syncresult="1">

    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>
    <!-- 正确错误音效 -->
    <audio src="./audio/complete.mp3" class="rightAudio" data-syncaudio="rightAudio"></audio>
    <audio src="./audio/wrong.mp3" class="wrongAudio" data-syncaudio="wrongAudio"></audio>
    <audio src="./audio/drag_click.mp3" class="dragAudio" data-syncaudio="dragAudio"></audio>

    <!-- 教材播放 -->
    <audio src="" class="soundAudio" data-syncaudio="soundAudio"></audio>

    <!-- 列表渲染 -->
    <div class="subject">
      <div class="img-list">
        <ul></ul>
      </div>
    </div>

    <!-- 功能遮罩层 -->
    <div class="funcMask">
      <!-- 预习模式操作区 -->
      <div class="startBox">
        <div class="demoTextBox">
          <!-- <img src="https://cdn.51talk.com/apollo/images/4dfeec15c2cd5d502bf53095ec38b9ea.png" class="startMsg" /> -->
          <img src="./image/4dfeec15c2cd5d502bf53095ec38b9ea.png" class="startMsg" />
        </div>
        <div class="demoBtnBox">
          <!-- <img src="https://cdn.51talk.com/apollo/images/57f60d04ee91d3178a98cc81d7b08001.png"
            class="demo-btnStu" /><img src="https://cdn.51talk.com/apollo/images/0250983386ad8a2c2226cac7b83be49e.png"
            class="startBtn"> -->
          <img src="./image/57f60d04ee91d3178a98cc81d7b08001.png"
            class="demo-btnStu" /><img src="./image/0250983386ad8a2c2226cac7b83be49e.png"
            class="startBtn">
        </div>

      </div>
      <!-- 倒计时 -->
      <div class="timeChangeBox hide">
        <div class="timeBg">
          <div class="numberList"></div>
          <audio src="./audio/timeLow.mp3" class="timeLowAudio_1" data-syncaudio="timeLowAudio_1"></audio>
          <audio src="./audio/timeLow.mp3" class="timeLowAudio_2" data-syncaudio="timeLowAudio_2"></audio>
          <audio src="./audio/timeLow.mp3" class="timeLowAudio_3" data-syncaudio="timeLowAudio_3"></audio>
          <audio src="./audio/timehigh.mp3" class="timeLowAudio_4" data-syncaudio="timeLowAudio_4"></audio>
        </div>
      </div>
    </div>



    <!-- 终局后的列表渲染 -->
    <div class="result-success">
      <div class="success-mask"></div>
      <div class="content">
        <ul>
          <li data-syncactions="result1"></li>
          <li data-syncactions="result2"></li>
          <li data-syncactions="result3"></li>
          <li data-syncactions="result4"></li>
        </ul>
      </div>
    </div>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<% include ./src/common/template/ribbonWin/index.ejs %>
<%include ./src/common/template/index_bottom %>
</body>
</html>


