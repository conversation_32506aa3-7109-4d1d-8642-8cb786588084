"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/drag.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";


$(function () {


  window.h5Template = {
    hasPractice: "1",
  };
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  const msg = configData.source;
  const dis = 0.3;

  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/defaultBg.jpg)" });
  }

  const emptyCount = msg.trainMsg.filter(item => item.isEmpty === "true").length

  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      correctanswercount:emptyCount || 0 ,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.PRACTICE_INPUT,
      interaction_type:INTERACTION_TYPE.CHOOSE,
      useraction_type:USERACTION_TYPE.LISTEN
    },
  })

  /**
   * 添加入场动画
   */

  window.onload = function () {
    $(".train").addClass("moveIn");
    if (isSync) {
      var current_user_id = SDK.getClassConf().user.id;
      var frame_id = $(window.frameElement).attr("id");
      var frame_user_id = $(window.frameElement).attr("user_id");
      if (
        frame_id != "h5_course_cache_frame" &&
        frame_user_id == current_user_id &&
        frame_id === "h5_course_self_frame"
      ) {
        // $('.inOutAudio').get('0').play();
        SDK.playRudio({
          index: $(".inOutAudio").get("0"),
          syncName: $(".inOutAudio").attr("data-syncaudio"),
        });
      }
    } else {
      $(".inOutAudio").get("0").play();
    }
  };

  /**
   * 创建元素
   */
  let cardListHtml = "";
  let trainHtml = "";
  let listArr = []; //  没有乱序前 底部的答案和干扰项  selectList 是乱序后的底部答案和干扰项
  $.each(msg.distracterList, function (index, val) {
    listArr.push(val);
  });

  console.log(msg.trainMsg, "----123");
  let nowTrueNum = 0; // 答对的个数

  //文字的尺寸控制
  let textSize = (function textClass() {
    if (msg.type != "img") {
      for (let i = 0; i < msg.trainMsg.length; i++) {
        if (msg.trainMsg[i].font.length > 2) {
          return "cs";
        }
      }
      for (let i = 0; i < msg.distracterList.length; i++) {
        if (msg.distracterList[i].font.length > 2) {
          return "cs";
        }
      }
    }
    return "fs";
  })();

  // 创建火车车厢
  for (let i = 0; i < msg.trainMsg.length; i++) {
    if (msg.type == "img") {
      if (msg.trainMsg[i].isEmpty == "true") {
        listArr.push(msg.trainMsg[i]);
      } else {
        nowTrueNum++;
      }
      const { targetValue } = msg.trainMsg[i];
      const imgSrc = configData.targets.find(
        (item) => item.value === targetValue
      ).img;
      trainHtml += `
                <div class="trainB_evl pos_${msg.trainMsg[i].pos} ${
        msg.trainMsg[i].isEmpty == "true" ? "hasMask" : ""
      }" data-value="${msg.trainMsg[i].targetValue}">
                    <div class="mask"></div>
                    <div class="box">
                        <img src="${imgSrc}" class="isShow ${
        msg.trainMsg[i].isEmpty == "true" ? "hide" : ""
      }">
                        <img src="./image/wenhao-change.png" class="nullpic ${
                          msg.trainMsg[i].isEmpty == "true" ? "" : "hide"
                        }">
                    </div>
                </div>
            `;
    } else {
      // todo 没有文字版本 可以视为废弃
      if (msg.trainMsg[i].isEmpty == "true") {
        listArr.push(msg.trainMsg[i]);
      } else {
        nowTrueNum++;
      }
      trainHtml += `
                <div class="trainB_evl pos_${msg.trainMsg[i].pos} ${
        msg.trainMsg[i].isEmpty == "true" ? "hasMask" : ""
      }" data-value="${msg.trainMsg[i].targetValue}">
                    <div class="mask"></div>
                    <div class="box">
                        <div class="isShow ${textSize} ${
        msg.trainMsg[i].isEmpty == "true" ? "wenhao" : ""
      }">
                            <span class="${
                              msg.trainMsg[i].isEmpty == "true" ? "hide" : ""
                            }">${msg.trainMsg[i].font}</span>
                        </div>
                    </div>
                </div>
            `;
    }
  }
  $(".trainB").html(trainHtml);

  if (configData.source.audio) {
    $(".audioBg")
      .show()
      .append(
        `<audio src="${configData.source.audio}" class="audio" data-syncaudio="audio1"></audio>`
      );
  }

  /**
   * 打乱顺序：打乱底部答案和干扰项的顺序
   */

  let seleList = []; // 打乱顺序后的数组
  let cardObj = {};
  if (listArr.length > 0) {
    if (isSync) {
      if (msg.random) {
        let pages = SDK.getClassConf().h5Course.localPage; //页码打乱顺序
        let seleListOne = window.reSort(listArr, pages);
        seleList = window.reSort(seleListOne, msg.random);
      } else {
        let pages = SDK.getClassConf().h5Course.localPage; //页码打乱顺序
        seleList = window.reSort(listArr, pages);
      }
    } else {
      if (msg.random) {
        seleList = window.reSort(listArr, msg.random);
      } else {
        seleList = window.reSort(listArr, Math.round(Math.random() * 100));
      }
    }
  }
  let initPOsLe = 0;
  switch (listArr.length) {
    case 1:
      initPOsLe = 7;
      break;
    case 2:
      initPOsLe = 5.2;
      break;
    case 3:
      initPOsLe = 4.2;
      break;
    case 4:
      initPOsLe = 3;
      break;
    case 5:
      initPOsLe = 1.5;
      break;
    case 6:
      initPOsLe = 0;
      break;
  }

  // 创建拖拽选项  seleList 乱序后的数组
  for (let i = 0; i < seleList.length; i++) {
    cardObj["card_ele_" + seleList[i].pos] = false;
    let imgSrc = "";
    if (seleList[i].img) {
      imgSrc = seleList[i].img;
    } else {
      imgSrc = configData.targets.find(
        (item) => item.value === seleList[i].targetValue
      ).img;
    }
    if (msg.type == "img") {
      cardListHtml += `
                <div class="card_ele imgC card_ele_${
                  seleList[i].pos
                }" data-syncactions="item_${i}" data-pos="${
        seleList[i].pos
      }" data-id="card_ele_${seleList[i].pos}" data-item="pos_${
        seleList[i].pos
      }" data-initT = "6.95" data-initL = ${
        (dis + 2.5) * i + initPOsLe
      } style="left:${(dis + 2.5) * i + initPOsLe}rem" >
                    <div class="cardBox"><img src="${imgSrc}"></div>
                </div>
             `;
    } else {
      cardListHtml += `
                <div class="card_ele imgF card_ele_${
                  seleList[i].pos
                }" data-syncactions="item_${i}" data-pos="${
        seleList[i].pos
      }" data-id="card_ele_${seleList[i].pos}" data-item="pos_${
        seleList[i].pos
      }"  data-initT = "6.95" data-initL = "${
        (dis + 2.5) * i + initPOsLe
      }" style="left:${(dis + 2.5) * i + initPOsLe}rem">
                    <div class="cardBox  ${textSize}">${seleList[i].font}</div>
                </div>
            `;
    }
  }
  $(".cardList").html(cardListHtml);

  if(!('ontouchstart' in window || navigator.maxTouchPoints)) {
    $(".card_ele").hover(
      function () {
        $(this).css({ transform: "scale(1.05)" });
      },
      function () {
        $(this).css({ transform: "scale(1)" });
      }
    );
  }

  

  // todo 开始发光
  // let currentIndex = 0
  function startLight() {
    console.log(nowTrueNum, msg.trainMsg.length, "jf 2");
    if (nowTrueNum >= msg.trainMsg.length) {
      $(".trainB").find(".box .nullpic").removeClass("breath");
      return;
    }
    $(".hasMask").eq(0).find(".box .nullpic").addClass("breath");
    $(".hasMask").eq(0).siblings().find(".box .nullpic").removeClass("breath");
  }

  /**
   * 碰撞  与  拖拽
   */

  let boxHalf = 1.2;
  let moveHalf = 1.25;
  let breakAre = 1.5; //碰撞的有效面积
  let canDrag = true;

  let canTragetClick = false; // 是否可以点击目标 jf

  $(".train").on("animationend webkitAnimationEnd", function () {
    // $('.inOutAudio').get('0').pause();
    canTragetClick = true;
    startLight();
    SDK.pauseRudio({
      index: $(".inOutAudio").get("0"),
      syncName: $(".inOutAudio").attr("data-syncaudio"),
    });
    /*
    $(".card_ele").drag({
      before: function (e) {
        $(".hasMask .mask").fadeIn(500);
        $(this).css({
          transition: "",
          "z-index": 21,
          transform: "scale(.8)",
        });
      },
      process: function (e) {},
      end: function (e) {
        $(".hasMask .mask").fadeOut(500);
        let moveLeft =
          ($(this).offset().left - $(".container").offset().left) /
            window.base +
          moveHalf; //拖拽体的中心left
        let moveTop =
          ($(this).offset().top - $(".container").offset().top) / window.base +
          moveHalf; //拖拽体的中心top
        let tarinBoxTop =
          ($(".pos_1").offset().top - $(".container").offset().top) /
            window.base +
          boxHalf; //火车区域的高度
        if ($(this).attr("data-pos") == "#") {
          // 干扰项
          $(this).css({
            left: $(this).attr("data-initL") + "rem",
            top: $(this).attr("data-initT") + "rem",
            transition: "all 0.5s",
            transform: "scale(1)",
            "z-index": 20,
          });
          var name = $(this).attr("data-syncactions");
          if (Math.abs(moveTop - tarinBoxTop) <= breakAre) {
            console.log("干扰项已拖拽到火车高度，回答错误", name);
            if (isSync) {
              SDK.bindSyncEvt({
                eventType: "click",
                method: "event",
                syncName: "syncDragEnd",
                otherInfor: {
                  name: name,
                  isWrong: true,
                  tag: "useless",
                  type: "interference",
                },
                recoveryMode: "1",
              });
            }
          } else {
            console.log("干扰项未拖拽到火车， 本次行为无效", name);
          }
        } else {
          let className = $(this).attr("data-item");
          // 获取正确车厢中心位置
          let boxLeft =
            ($("." + className).offset().left - $(".container").offset().left) /
              window.base +
            boxHalf; //拖拽坑的left位置
          let boxTop =
            ($("." + className).offset().top - $(".container").offset().top) /
              window.base +
            boxHalf; //拖拽坑的位置
          let sameLeft = Math.abs(moveLeft - boxLeft);
          let sameTop = Math.abs(moveTop - boxTop);
          var name = $(this).attr("data-syncactions");
          //判断中心点是否在检测区域
          if (sameLeft <= breakAre && sameTop <= breakAre) {
            console.log("答对正确", name);
            nowTrueNum++;
            cardObj[$(this).attr("data-id")] = true;
            if (canDrag) {
              canDrag = false;
              if (!isSync) {
                $(this).trigger("syncDragEnd");
                return;
              }
              SDK.bindSyncEvt({
                index: $(this).data("syncactions"),
                eventType: "click",
                method: "event",
                syncName: "syncDragEnd",
                name: name,
                otherInfor: {
                  cardObj: cardObj,
                  nowTrueNum: nowTrueNum,
                  goOut: false,
                  type: "option",
                },
                recoveryMode: "1",
              });
            }
          } else {
            //答错
            $(this).css({
              left: $(this).attr("data-initL") + "rem",
              top: $(this).attr("data-initT") + "rem",
              transition: "all 0.5s",
              transform: "scale(1)",
              "z-index": 20,
            });
            var name = $(this).attr("data-syncactions");

            if (Math.abs(moveTop - tarinBoxTop) <= breakAre) {
              console.log("选项已拖拽到火车高度，回答错误", name);
              if (isSync) {
                SDK.bindSyncEvt({
                  index: $(this).data("syncactions"),
                  eventType: "click",
                  method: "event",
                  syncName: "syncDragEnd",
                  otherInfor: {
                    name: name,
                    isWrong: true,
                    tag: "useless",
                    type: "option",
                  },
                  recoveryMode: "1",
                });
              }
            } else {
              console.log("选项未拖拽到火车，本次行为无效", name);
            }
          }
        }
      },
    });
    */

    $(".card_ele").on("click", function (e) {
      console.log('9090909090')
      if (e.type === "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation();

      if (!canTragetClick) {
        return;
      }
      SDK.reportTrackData({
        action: 'CK_FT_INTERACTION_ITEM',
        data: {}
      },USER_TYPE.STU)
      if (!isSync) {
        $(this).trigger("syncDragEnd");
        return;
      }
      var name = $(this).attr("data-syncactions");
      SDK.bindSyncEvt({
        index: $(this).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "syncDragEnd",
        name: name,
        otherInfor: {
          cardObj: cardObj,
          nowTrueNum: nowTrueNum,
          goOut: false,
          type: "option",
        },
        recoveryMode: "1",
      });
      /*
      console.log("点击了", $(this), $(this).index(), seleList);

      const index = $(this).index();
      const target = seleList[index];
      console.log(seleList);
      if (!target.targetValue) {
        console.log("选中了干扰选项，执行选错动画");
        // todo 选中了干扰选项，执行选错动画
        $(this).removeClass("shake");
        setTimeout(() => {
          $(this).addClass("shake");
          // 监听shake动画结束事件
          $(this).one("animationend", function () {
            $(this).removeClass("shake");
          });
        }, 10);
        const audio = $(".wrongAudio").get(0);
        audio.currentTime = 0;
        SDK.playRudio({
          index: $(".wrongAudio").get("0"),
          syncName: $(".wrongAudio").attr("data-syncaudio"),
        });
        return;
      }

      const currentEle = $(".hasMask").eq(0);
      const value = currentEle.data("value");
      const offset = currentEle.find(".box").offset();
      const containerOffset = $(".cardList").offset();
      console.log(offset, containerOffset);
      const left = offset.left - containerOffset.left;
      const top = offset.top - containerOffset.top;
      console.log(value, target.targetValue, currentEle, "-909090");
      if (target.targetValue == value) {
        canTragetClick = false;
        $(this).css({
          transition: "all 0.5s ease-in-out",
          left: left + "px",
          top: top + "px",
        });
        console.log("选中了正确选项，执行选对动画");
        nowTrueNum++;
        // todo 选中了正确选项，执行选对动画
        const audio = $(".targetAudio").get(0);
        audio.currentTime = 0;
        $(".targetAudio").attr("src", 123);
        SDK.playRudio({
          index: $(".rightAudio").get("0"),
          syncName: $(".rightAudio").attr("data-syncaudio"),
        });
        const timer = setTimeout(() => {
          console.log("移动结束");

          const audioSrc = configData.targets.find(
            (item) => item.value === value
          ).audio;
          playTargetAudio(audioSrc);
          if (!isSync) {
            $(this).trigger("syncDragEnd");
            return;
          }
          var name = $(this).attr("data-syncactions");
          console.log("同步拖拽结束", name);
          SDK.bindSyncEvt({
            index: $(this).data("syncactions"),
            eventType: "click",
            method: "event",
            syncName: "syncDragEnd",
            name: name,
            otherInfor: {
              cardObj: cardObj,
              nowTrueNum: nowTrueNum,
              goOut: false,
              type: "option",
            },
            recoveryMode: "1",
          });
          clearTimeout(timer);
        }, 500);
      } else {
        console.log("选错了");
        // todo 选错了 选中了干扰选项，执行选错动画
        $(this).removeClass("shake");
        setTimeout(() => {
          $(this).addClass("shake");
          // 监听shake动画结束事件
          $(this).one("animationend", function () {
            $(this).removeClass("shake");
          });
        }, 10);
        const audio = $(".wrongAudio").get(0);
        audio.currentTime = 0;
        SDK.playRudio({
          index: $(".wrongAudio").get("0"),
          syncName: $(".wrongAudio").attr("data-syncaudio"),
        });
      }
      */
    });
  });
  $(".card_ele").on("syncDragEnd", function (e, message) {
    console.log("jf 触发事件");
    const index = $(this).index();
    const target = seleList[index];
    if (!target.targetValue) {
      console.log("选中了干扰选项，执行选错动画");
      SDK.reportTrackData({
        action: 'CK_FT_ANSWER_RESULT',
        data: {
          result: 'wrong'
        }
      },USER_TYPE.STU)
      // todo 选中了干扰选项，执行选错动画
      $(this).removeClass("shake");
      setTimeout(() => {
        $(this).addClass("shake");
        // 监听shake动画结束事件
        $(this).one("animationend", function () {
          $(this).removeClass("shake");
        });
      }, 10);
      const audio = $(".wrongAudio").get(0);
      audio.currentTime = 0;
      SDK.playRudio({
        index: $(".wrongAudio").get("0"),
        syncName: $(".wrongAudio").attr("data-syncaudio"),
      });
      return SDK.setEventLock();
    }

    const currentEle = $(".hasMask").eq(0);
    const value = currentEle.data("value");
    const offset = currentEle.find(".box").offset();
    const containerOffset = $(".cardList").offset();
    console.log(offset, containerOffset);
    const left = offset.left - containerOffset.left;
    const top = offset.top - containerOffset.top;
    console.log(value, target.targetValue, currentEle, "-909090");

    if (target.targetValue == value) {
      SDK.reportTrackData({
        action: 'CK_FT_ANSWER_RESULT',
        data: {
          result: 'right'
        }
      },USER_TYPE.STU)
      canTragetClick = false;
      $(this).css({
        transition: "all 0.5s ease-in-out",
        left: left + "px",
        top: top + "px",
      });
      console.log("选中了正确选项，执行选对动画", nowTrueNum);
      // return
      nowTrueNum++;
      // todo 选中了正确选项，执行选对动画
      const audio = $(".targetAudio").get(0);
      audio.currentTime = 0;
      // $(".targetAudio").attr("src", 123);
      SDK.playRudio({
        index: $(".rightAudio").get("0"),
        syncName: $(".rightAudio").attr("data-syncaudio"),
      });
      const timer = setTimeout(() => {
        console.log("移动结束");

        const audioSrc = configData.targets.find(
          (item) => item.value === value
        ).audio;
        console.log(audioSrc,'---audioSrc')
        playTargetAudio(audioSrc, () => {
          let className = $(this).attr("data-item");
          if (msg.type == "img") {
            // 隐藏问号
            $(".hasMask").eq(0).find(".isShow").show();
            $(".hasMask").eq(0).find(".nullpic").hide();
          } else {
            $("." + className)
              .find(".isShow span")
              .show();
            $("." + className)
              .find(".isShow")
              .removeClass("wenhao");
          }
          // $("." + className).removeClass("hasMask");
          $(".hasMask").eq(0).removeClass("hasMask"); // 移除 hasMask类
          $(this).hide(); // 隐藏自身（拖拽元素）
          console.log("jf nowTrueNum 1", nowTrueNum);
          startLight();
          if (nowTrueNum >= msg.trainMsg.length) {
            console.log("jf 执行完成");
            const { feedbackLists = [] } = configData;
            const finalFeedback = feedbackLists.find((item) => {
              return item.key === "finalFeedback";
            });

            finalFeedback && execFinalFeedback();
            $(".redLight").hide();
            $(".greenLight").show();
            $(".trainH .cloud1").fadeIn(1000);
            $(".trainH .cloud2").fadeIn(1500);
            $(".trainH .cloud3").fadeIn(2000);
            if (isSync) {
              // 火车可发车，上报已全部答完
              SDK.bindSyncEvt({
                eventType: "click",
                method: "event",
                syncName: "syncDragEnd",
                otherInfor: {
                  gameover: true,
                  tag: "useless",
                },
                recoveryMode: "1",
              });

              if (window.frameElement.getAttribute("user_type") == "tea") {
                $(".alertBox").css("opacity", "1");
              }
            } else {
              $(".alertBox").css("opacity", "1");
            }
            canDrag = false;
            setTimeout(function () {
              // todo 不用播放音频了
              // console.log('播放音乐')
              // SDK.playRudio({
              //   index: $(".trueAudio").get("0"),
              //   syncName: $(".trueAudio").attr("data-syncaudio"),
              // });
              // $(".trueAudio").get("0").onended = function () {
              //   setTimeout(function () {
              //     SDK.playRudio({
              //       index: $(".trueAudio1").get("0"),
              //       syncName: $(".trueAudio1").attr("data-syncaudio"),
              //     });
              //   }, 500);
              // };
              // starFun();
            }, 800);
            $(".Allmask").show();
          } else {
            canDrag = true;
          }
          canTragetClick = true
          SDK.setEventLock();
        });
        var name = $(this).attr("data-syncactions");
        console.log("同步拖拽结束", name);
        clearTimeout(timer);
      }, 500);
    } else {
      console.log("选错了");
      SDK.reportTrackData({
        action: 'CK_FT_ANSWER_RESULT',
        data: {
          result: 'wrong'
        }
      },USER_TYPE.STU)
      // todo 选错了 选中了干扰选项，执行选错动画
      $(this).removeClass("shake");
      setTimeout(() => {
        $(this).addClass("shake");
        // 监听shake动画结束事件
        $(this).one("animationend", function () {
          $(this).removeClass("shake");
        });
      }, 10);
      const audio = $(".wrongAudio").get(0);
      audio.currentTime = 0;
      SDK.playRudio({
        index: $(".wrongAudio").get("0"),
        syncName: $(".wrongAudio").attr("data-syncaudio"),
      });
      return SDK.setEventLock();
    }

    if (isSync) {
      let otherInfor = message.data[0].value.syncAction.otherInfor;
      cardObj = otherInfor.cardObj;
      // ? nowTrueNum = otherInfor.nowTrueNum;
      if (message.operate == "5") {
        callbackFn(cardObj, otherInfor.goOut);
        return;
      }
    }

    setTimeout(() => {
      // let className = $(this).attr("data-item");
      // if (msg.type == "img") {
      //   // 隐藏问号
      //   $(".hasMask").eq(0).find(".isShow").show();
      //   $(".hasMask").eq(0).find(".nullpic").hide();
      // } else {
      //   $("." + className)
      //     .find(".isShow span")
      //     .show();
      //   $("." + className)
      //     .find(".isShow")
      //     .removeClass("wenhao");
      // }
      // // $("." + className).removeClass("hasMask");
      // $(".hasMask").eq(0).removeClass("hasMask"); // 移除 hasMask类
      // $(this).hide(); // 隐藏自身（拖拽元素）
      // console.log("jf nowTrueNum 1", nowTrueNum);
      // startLight();
      // if (nowTrueNum >= msg.trainMsg.length) {
      //   console.log('jf 执行完成')
      //   const { feedbackLists = [] } = configData
      //   const finalFeedback = feedbackLists.find(item => {
      //     return item.key === 'finalFeedback'
      //   })
      //   finalFeedback && execFinalFeedback()
      //   debugger
      //   $(".redLight").hide();
      //   $(".greenLight").show();
      //   $(".trainH .cloud1").fadeIn(1000);
      //   $(".trainH .cloud2").fadeIn(1500);
      //   $(".trainH .cloud3").fadeIn(2000);
      //   if (isSync) {
      //     // 火车可发车，上报已全部答完
      //     SDK.bindSyncEvt({
      //       eventType: "click",
      //       method: "event",
      //       syncName: "syncDragEnd",
      //       otherInfor: {
      //         gameover: true,
      //         tag: "useless",
      //       },
      //       recoveryMode: "1",
      //     });
      //     if (window.frameElement.getAttribute("user_type") == "tea") {
      //       $(".alertBox").css("opacity", "1");
      //     }
      //   } else {
      //     $(".alertBox").css("opacity", "1");
      //   }
      //   canDrag = false;
      //   setTimeout(function () {
      //     // todo 不用播放音频了
      //     // SDK.playRudio({
      //     //   index: $(".trueAudio").get("0"),
      //     //   syncName: $(".trueAudio").attr("data-syncaudio"),
      //     // });
      //     // $(".trueAudio").get("0").onended = function () {
      //     //   setTimeout(function () {
      //     //     SDK.playRudio({
      //     //       index: $(".trueAudio1").get("0"),
      //     //       syncName: $(".trueAudio1").attr("data-syncaudio"),
      //     //     });
      //     //   }, 500);
      //     // };
      //     // starFun();
      //   }, 800);
      //   $(".Allmask").show();
      // } else {
      //   canDrag = true;
      // }
      // SDK.setEventLock();
    }, 500);
  });
  //答题结束触发发送星星
  function starFun() {
    if (!isSync) {
      return false;
    }
    var classStatus =
      parent.window.h5SyncActions.classConf.h5Course.classStatus;
    var support1v1h5star =
      parent.window.h5SyncActions.classConf.serverData.objCourseInfo
        .support1v1h5star;
    var device = parent.window.h5SyncActions.classConf.h5Course.device;
    if (
      window.frameElement.getAttribute("user_type") == "stu" &&
      classStatus == 2
    ) {
      if ((device == "pc" && support1v1h5star == 1) || device != "pc") {
        console.log("学生拖动正确后发星星");
        SDK.bindSyncStart({
          type: "newH5StarData",
          num: 1,
        });
      }
    }
  }

  function playTargetAudio(src, cb) {

    // todo 没有音频项
    if(!src) {
      return cb && cb()
    }

    // todo 播放目标音效
    canTragetClick = false;
    const audioSrc = src;
    $(".targetAudio").attr("src", audioSrc);
    SDK.playRudio({
      index: $(".targetAudio").get("0"),
      syncName: $(".targetAudio").attr("data-syncaudio"),
    });
    $(".targetAudio").one("ended", function () {
      // canTragetClick = true;
      cb && cb();
    });
  }
  /**
   * 老师提示框
   */

  $(".alertBox").drag();
  let canClick = true;
  $(".btn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (canClick) {
      canClick = false;
      if (!isSync) {
        $(this).trigger("clickBtn");
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "clickBtn",
          otherInfor: {
            cardObj: cardObj,
            nowTrueNum: nowTrueNum,
            goOut: true,
          },
          recoveryMode: "1",
        });
      }
    }
  });
  $(".btn").on("clickBtn", function (e, message) {
    if (isSync) {
      let otherInfor = message.data[0].value.syncAction.otherInfor;
      cardObj = otherInfor.cardObj;
      nowTrueNum = otherInfor.nowTrueNum;
      if (message.operate == "5") {
        callbackFn(cardObj, otherInfor.goOut);
        return;
      }
    }
    $(this).css({
      background: "#bcbcbc",
      cursor: "not-allowed",
    });
    // $('.inOutAudio').get('0').play();
    SDK.playRudio({
      // index: $(".inOutAudio").get("0"),
      // syncName: $(".inOutAudio").attr("data-syncaudio"),
      index: $(".leave").get("0"),
      syncName: $(".leave").attr("data-syncaudio"),
    });
    $(".train").addClass("moveOut");
    SDK.setEventLock();
  });

  /**
   * 点击声音按钮
   */

  let audioBgClick = true;
  $(".audioBg").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (audioBgClick) {
      audioBgClick = false;
      // 判断幕布是否开启
      if (!isSync) {
        $(this).trigger("audioBgClick");
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          funcType: "audio",
          syncName: "audioBgClick",
          otherInfor: {
            cardObj: cardObj,
            nowTrueNum: nowTrueNum,
            goOut: true,
          },
          recoveryMode: "1",
        });
      }
    }
  });
  $(".audioBg").on("audioBgClick", function (e, message) {
    if (isSync) {
      let otherInfor = message.data[0].value.syncAction.otherInfor;
      cardObj = otherInfor.cardObj;
      nowTrueNum = otherInfor.nowTrueNum;
      if (message.operate == "5") {
        callbackFn(cardObj, otherInfor.goOut);
        return;
      }
    }
    $(".audioInit").hide();
    $(".audioGif").show();
    // $('.audio').get(0).play()
    SDK.playRudio({
      index: $(".audio").get("0"),
      syncName: $(".audio").attr("data-syncaudio"),
    });

    $(".audio").get(0).onended = function () {
      $(".audioInit").show();
      $(".audioGif").hide();
    };
    audioBgClick = true;
    SDK.setEventLock();
  });

  /**
   * 重新进入教室
   */

  function callbackFn(obj, status) {
    $.each(obj, function (key, val) {
      if (val) {
        let className = $("." + key).attr("data-item");
        if (msg.type == "img") {
          $("." + className)
            .find(".isShow")
            .show();
          $("." + className)
            .find(".nullpic")
            .hide();
        } else {
          $("." + className)
            .find(".isShow span")
            .show();
          $("." + className)
            .find(".isShow")
            .removeClass("wenhao");
        }
        $("." + className).removeClass("hasMask");
        $("." + key).hide();
      }
    });
    if (nowTrueNum >= msg.trainMsg.length) {
      $(".Allmask").show();
      $(".redLight").hide();
      $(".greenLight").show();
      $(".trainH .cloud1").fadeIn(1000);
      $(".trainH .cloud2").fadeIn(1500);
      $(".trainH .cloud3").fadeIn(2000);
      if (isSync) {
        if (window.frameElement.getAttribute("user_type") == "tea") {
          $(".alertBox").css("opacity", "1");
        }
      } else {
        $(".alertBox").css("opacity", "1");
      }
      canDrag = false;
    } else {
      canDrag = true;
    }
    if (status) {
      $(".train").addClass("moveOut");
      $(".btn").css({
        background: "#bcbcbc",
        cursor: "not-allowed",
      });
    }
    SDK.setEventLock();
  }

  async function execFinalFeedback() {
    console.log("最终动画执行完成！");
    SDK.reportTrackData({
      action: 'CK_FT_INTERACTION_COMPLETE',
      data: {
        result:'success',
      },
    },USER_TYPE.TEA)
    await feedbackAnimation("finalFeedback");
  }
});
