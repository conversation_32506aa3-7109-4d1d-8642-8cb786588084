@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.hide {
    display: none;
}
.container{
	position: relative;
    .Allmask{
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 22;
        display: none;
    }

}
.stage{
	width: 100%;
    height: 100%;
    position: relative;
    .audioBg {
        position: absolute;
        left: 2.1rem;
        top: 1.1rem;
        width: 1.47rem;
        height: 1.36rem;
        background: url(../image/btn-audio-bg.png) no-repeat;
        background-size: contain;
        cursor: pointer;
        display: none;
        z-index: 30;
        img {
            position: absolute;
            top: 0.41rem;
            left: 0.33rem;
            width: 0.82rem;
            height: 0.66rem;
        }
        .audioInit {
            display: block;
        }
        .audioGif {
            display: none;
        }
    }
    .light {
        width: 1.02rem;
        height: 1.95rem;
        position: absolute;
        left: .9rem;
        top: 1rem;
        z-index: 2;
        img {
            width: 100%;
            height: 100%;
            position: absolute;
        }
        .greenLight {
            display: none;
        }
    }
    .train {
       position: absolute;
       bottom: 5.02rem;
       left:19.2rem;
       width: 100%;
       height: 3.2rem;
        .trainH {
           width: 2.36rem;
           height: 2.8rem;
           position: absolute;
           top: .38rem;
           left: .07rem;
           background: url(../image/trainH-change.png) bottom no-repeat;
           background-size: contain;
           span {
               position: absolute;
               width: .26rem;
               height: .2rem;
               background: url(../image/cig.png) no-repeat;
               background-size: contain;
               display: none;
           }
            .cloud1 {
                transform: scale(1.5);
                left: .8rem;
                top: 1.1rem;
            }
            .cloud2 {
                transform: scale(3);
                opacity: .8;
                left: 1.2rem;
                top: .5rem;
            }
            .cloud3 {
                transform: scale(4);
                opacity: .6;
                left: 1.8rem;
                top: -.2rem;
            }
        }
        .trainB {
            width: auto;
            height: 3.2rem;
            position: absolute;
            left: 2.36rem;
            display: flex;
            .trainB_evl {
                width:2.72rem;
                height: 100%; 
                background: url(../image/train-change.png) bottom no-repeat;
                background-size: contain;
                margin-left: -.09rem;
                position: relative;
                .mask {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: rgba(255,255,255,.5);
                    position: absolute;
                    left: 0.2rem;
                    z-index: 2;
                    border: 2px dashed #bcd2d5;
                    border-bottom: none;
                    border-radius:.3rem .3rem 0 0;
                    display: none;
                }
                .box {
                    width: 2.5rem;
                    height: 2.5rem;
                    position: absolute;
                    left: 0.2rem;
                    text-align: center;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                    .nullpic{
                        // width: auto;
                        // height: 80%;
                        // vertical-align: middle;
                    }
                    .cs {
                        position: absolute;
                        bottom: 0;
                        width: 100%;
                        height: 1.7rem;
                        background: url(../image/box.png) no-repeat;
                        background-size: 100% 100%;
                        box-sizing: border-box;
                        span {
                            width: 100%;
                            height: 100%;
                            text-align: center;
                            line-height: 1.7rem;
                            font-size: .52rem;
                            overflow: hidden;
                            box-sizing: border-box;
                            padding: .1rem;
                        }
                    }
                    .fs {
                        position: absolute;
                        bottom: 0;
                        width: 100%;
                        height: 1.7rem;
                        background: url(../image/box.png) no-repeat;
                        background-size: 100% 100%;
                        box-sizing: border-box;
                        span {
                            width: 100%;
                            height: 100%;
                            font-size: 1.2rem;
                            text-align: center;
                            line-height: 1.7rem;
                            overflow: hidden;
                            box-sizing: border-box;
                            padding: .1rem;
                        }
                    }
                    .wenhao {
                        background: url(../image/box-wenhao.png) no-repeat;
                        background-size: 100% 100%;
                        box-sizing: border-box;
                    }

                }
            }
        }
    }
    .cardList {
        position: absolute;
        left: 1.35rem;
        top: 0;
        width: 16.5rem;
        height: 100%;
        .card_ele {
            width: 2.5rem;
            height: 2.5rem;
            position: absolute;
            top: 6.95rem;
            cursor: pointer;
        }
        .imgC {
            // background: url(../image/imgBg.png) no-repeat;
            background-size: 100% 100%;
            .cardBox {
                // width: 2.3rem;
                // height: 2.3rem;
                width: 2.5rem;
                height: 2.5rem;
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                right: 0;
                margin: auto;
                // border-radius: .5rem;
                overflow: hidden;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .imgF {
            background: url(../image/imgBg.png) no-repeat;
            background-size: 100% 100%;
            .cardBox {
                width: 2.1rem;
                height: 2.1rem;
                overflow: hidden;
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                right: 0;
                margin: auto;
                text-align: center;
                line-height: 2.1rem;
            }
            .cs {
                font-size: .52rem;
            }
            .fs {
                font-size: 1.2rem;
            }
        }
    }

    .alertBox {
        height: 1rem;
        width: 7.9rem;
        background: #fff;
        border-radius: .2rem .2rem 0 0;
        position: absolute;
        left: 50%;
        margin-left: -3.95rem;
        bottom: 0;
        cursor: move;
        z-index: 23; 
        opacity: 0;
        p{
            height: 100%;
            width: 6.4rem;
            line-height: 1rem;
            text-align: center;
            font-size: .3rem;
        }
        .btn {
            position: absolute;
            right: .6rem;
            top: .2rem;
            height: .6rem;
            width: 1rem;
            background: #f1a91e;
            color:#fff;
            text-align: center;
            line-height: .6rem;
            font-size: .4rem;
            border-radius: .3rem;
            cursor: pointer;
        }
    }
    .moveIn{
        animation: moveIn 2s linear forwards;
    }
    @keyframes moveIn {
        0%{
            left: 100%;
        }
        100%{
            left: 0;
        }
    }
    .moveOut{
        animation: moveOut 5s ease forwards;
    }
    @keyframes moveOut {
        0%{
            left: 0rem;
        }
        100%{
            left: -100%;
        }
    }
}

.feedback {
    z-index: 31 !important;
}

.breath {
//    border: 4px solid red;
    animation: breathe 3s ease-in-out infinite;
    will-change: transform;
}

@keyframes shake {
    0%, 100% { transform: translateX(0) rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px) rotate(-5deg); }
    20%, 40%, 60%, 80% { transform: translateX(10px) rotate(5deg); }
}

@keyframes breathe {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

.shake {
    animation: shake 0.5s;
    will-change: transform;
}