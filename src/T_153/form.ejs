<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>TLDDS0001_听力打地鼠FT</title>
  <link rel="stylesheet" href="form/css/style.css" />
  <script src="form/js/jquery-2.1.1.min.js"></script>
  <script src="form/js/vue.min.js"></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <div class="h-title">TLDDS0001_听力打地鼠FT</div>
      <% include ./src/common/template/common_head %>
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <div class="c-group">
          <div class="c-title">上传内容图片（最少3个，最多18个）</div>

          <div class="c-area upload img-upload">
            <div class="c-well" v-for="(item, index) in configData.source.imgs" v-bind:key="index">
              <div>
                <span class="dele-tg-btn" style="position: relative; z-index: 10" @click="delSourceImg(item)"
                  v-show="configData.source.imgs.length>3"></span>
                <div class="field-wrap">
                  <label class="field-label" for="">{{`图片${index+1}* `}}</label>
                  <span class="txt-info"><em>尺寸：420 * 300,文件大小≤40KB,格式.jpg.png *</em></span>
                  <input type="file" class="btn-file" v-bind:key="Date.now()" :id="`target-${index}`" size="420*300"
                     accept=".jpg,.jpeg,.png" @change="imageUpload($event,item,'img',40)" />
                </div>

                <div class="field-wrap">
                  <label :for="`target-${index}`" class="btn btn-show upload" v-if="!item.img">上传</label>
                  <label :for="`target-${index}`" class="btn upload re-upload" v-if="item.img">重新上传</label>
                </div>
                <div class="img-preview" v-if="item.img">
                  <img :src="item.img" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" @click="delTargetsPrew(item, 'img')">删除</span>
                  </div>
                </div>
              </div>
            </div>
            <button v-if="configData.source.imgs.length<18" type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog" @click="addSoureImg">
              添加图片
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">上传音频（最少1条，最多12条）</div>

          <div class="c-area upload img-upload">
            <div class="c-well" v-for="(item, index) in configData.source.audios" v-bind:key="index">
              <div>
                <span class="dele-tg-btn" style="position: relative; z-index: 10" @click="delDestination(item,index)"
                  v-show="configData.source.audios.length>1"></span>
                <div class="field-wrap">
                  <label class="field-label" for="">音频{{index + 1}}*</label>
                  <span class="txt-info"><em>音频大小≤30KB * </em></span>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'tarets-audio-'+index" accept=".mp3"
                    @change="audioUpload($event,item,'audio',30)" />
                </div>

                <div class="field-wrap">
                  <label :for="'tarets-audio-'+index" class="btn btn-show upload" v-if="!item.audio">上传</label>
                  <label :for="'tarets-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
                  <label class="btn upload btn-audio-dele" v-if="item.audio" @click="delAudio(item)">删除</label>
                </div>
                <div class="audio-preview" v-show="item.audio">
                  <div class="audio-tools">
                    <p v-show="item.audio">{{ item.audio }}</p>
                  </div>
                  <span class="play-btn" v-on:click="play($event)">
                    <audio v-bind:src="item.audio"></audio>
                  </span>
                </div>
              </div>
            </div>
            <button v-if="configData.source.audios.length<12" type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog" @click="addSoureAudio">
              添加音频
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">轮次设置（最少3轮，最多12轮）</div>

          <div class="c-area upload img-upload">
            <div class="c-well" v-for="(item, index) in configData.source.rounds" v-bind:key="index">
              <div>
                <span class="dele-tg-btn" style="position: relative; z-index: 10" @click="delDestination(item,index)"
                  v-show="configData.source.rounds.length>3"></span>
                <div>{{`第${index+1}轮设置`}}</div>
                <div class="field-wrap">
                  <label class="field-label" for="">内容音频*</label>
                  <select id="teachTime" v-model="item.audio" style="width: 170px;">
                    <option v-for="(sub, subIndex) in configData.source.audios" :key="sub.id" :value="sub.id">
                      音频{{subIndex + 1}}</option>
                  </select>
                </div>

                <div class="field-wrap answer-wrap">
                  <label class="field-label" for="">正确选项*</label>
                  <select id="teachTime" v-model="item.target" style="width: 170px;">
                    <option
                      :style="{display: (item.distractorOne !== sub.id && item.distractorTwo !== sub.id )? 'block' : 'none'}"
                      v-for="(sub, subIndex) in configData.source.imgs" :key="sub.id" :value="sub.id">图片{{subIndex + 1}}
                    </option>
                  </select>
                </div>

                <div class="field-wrap answer-wrap">
                  <label class="field-label" for="">干扰项1* </label>
                  <select id="teachTime" v-model="item.distractorOne" style="width: 170px;">
                    <option
                      :style="{display: (item.distractorTwo !== sub.id && item.target !== sub.id )? 'block' : 'none'}"
                      v-for="(sub, subIndex) in configData.source.imgs" :key="sub.id" :value="sub.id">图片{{subIndex + 1}}
                    </option>
                  </select>
                </div>

                <div class="field-wrap answer-wrap">
                  <label class="field-label" for="">干扰项2* </label>
                  <select id="teachTime" v-model="item.distractorTwo" style="width: 170px;">
                    <option
                      :style="{display: (item.distractorOne !== sub.id && item.target !== sub.id )? 'block' : 'none'}"
                      v-for="(sub, subIndex) in configData.source.imgs" :key="sub.id" :value="sub.id">图片{{subIndex + 1}}
                    </option>
                  </select>
                </div>
              </div>
            </div>
            <button v-if="configData.source.rounds.length<12" type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog" @click="addRounds">
              添加轮次
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">地鼠形象</div>
          <div class="c-area upload img-upload">
            <div class="c-well">
              <div class="field-wrap">
                <label class="field-label" for="">呼吸状态</label>
                <span class="txt-info"><em>文件大小≤240KB,格式.json,建议尺寸不超过 280*230 </em></span>
                <input type="file" v-bind:key="Date.now()" class="btn-file" id="dishu-breath" size="" accept=".json"
                  @change="lottieUpload($event,configData.source.dishu,'breath',240)" />
              </div>
              <div class="field-wrap">
                <label for="dishu-breath" class="btn btn-show upload" v-if="!configData.source.dishu.breath">上传</label>
                <label for="dishu-breath" class="btn upload re-upload" v-if="configData.source.dishu.breath">重新上传</label>
              </div>
              <div class="img-preview" v-if="configData.source.dishu.breath">
                <!-- <img
                  src="//cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                  alt=""
                /> -->
                <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
                <div class="img-tools">
                  <span class="btn btn-delete" @click="delWheelGamePrew(configData.source.dishu, 'breath')">删除</span>
                </div>
              </div>
            </div>

            <div class="c-well">
              <div class="field-wrap">
                <label class="field-label" for="">被打状态</label>
                <span class="txt-info"><em>文件大小≤240KB,格式.json,建议尺寸不超过 280*230 </em></span>
                <input type="file" v-bind:key="Date.now()" class="btn-file" id="dishu-yun" size="" accept=".json"
                  @change="lottieUpload($event,configData.source.dishu,'yun',240)" />
              </div>
              <div class="field-wrap">
                <label for="dishu-yun" class="btn btn-show upload" v-if="!configData.source.dishu.yun">上传</label>
                <label for="dishu-yun" class="btn upload re-upload" v-if="configData.source.dishu.yun">重新上传</label>
              </div>
              <div class="img-preview" v-if="configData.source.dishu.yun">
                <!-- <img
                  src="//cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                  alt=""
                /> -->
                <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
                <div class="img-tools">
                  <span class="btn btn-delete" @click="delWheelGamePrew(configData.source.dishu, 'yun')">删除</span>
                </div>
              </div>
            </div>
          </div>


        </div>


        <div class="c-group">
          <div class="c-title">地鼠洞图片</div>
          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">地鼠洞后</label>
                  <span class="txt-info"><em>尺寸：306 * 90,文件大小≤40KB,格式.jpg.png *</em></span>
                  <input type="file" class="btn-file" v-bind:key="Date.now()" id="hole-after" size="306*90"
                     accept=".jpg,.jpeg,.png" @change="imageUpload($event,configData.source.hole,'after',40)" />
                </div>

                <div class="field-wrap">
                  <label :for="`hole-after`" class="btn btn-show upload" v-if="!configData.source.hole.after">上传</label>
                  <label :for="`hole-after`" class="btn upload re-upload" v-if="configData.source.hole.after">重新上传</label>
                </div>
                <div class="img-preview" v-if="configData.source.hole.after">
                  <img :src="configData.source.hole.after" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" @click="delTargetsPrew(configData.source.hole, 'after')">删除</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">地鼠洞前</label>
                  <span class="txt-info"><em>尺寸：306 * 90,文件大小≤40KB,格式.jpg.png *</em></span>
                  <input type="file" class="btn-file" v-bind:key="Date.now()" id="hole-before" size="306*90"
                   accept=".jpg,.jpeg,.png" @change="imageUpload($event,configData.source.hole,'before',40)" />
                </div>

                <div class="field-wrap">
                  <label :for="`hole-before`" class="btn btn-show upload" v-if="!configData.source.hole.before">上传</label>
                  <label :for="`hole-before`" class="btn upload re-upload" v-if="configData.source.hole.before">重新上传</label>
                </div>
                <div class="img-preview" v-if="configData.source.hole.before">
                  <img :src="configData.source.hole.before" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" @click="delTargetsPrew(configData.source.hole, 'before')">删除</span>
                  </div>
                </div>
              </div>
            </div>


            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">锤子图片</label>
                  <span class="txt-info"><em>尺寸：230 * 245,文件大小≤40KB,格式.jpg.png *</em></span>
                  <input type="file" class="btn-file" v-bind:key="Date.now()" id="hole-hammer" size="230*245"
                   accept=".jpg,.jpeg,.png" @change="imageUpload($event,configData.source.hole,'hammer',40)" />
                </div>

                <div class="field-wrap">
                  <label :for="`hole-hammer`" class="btn btn-show upload" v-if="!configData.source.hole.hammer">上传</label>
                  <label :for="`hole-hammer`" class="btn upload re-upload" v-if="configData.source.hole.hammer">重新上传</label>
                </div>
                <div class="img-preview" v-if="configData.source.hole.hammer">
                  <img :src="configData.source.hole.hammer" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" @click="delTargetsPrew(configData.source.hole, 'hammer')">删除</span>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>

        <% include ./src/common/template/feedbackAnimation/form %>
          <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="" />
        </div>
        <ul class="show-txt">
          <li><em>图片格式：</em>JPG/PNG/GIF</li>
          <li><em>声音格式：</em>MP3/WAV</li>
          <li><em>视频格式：</em>MP4</li>
          <li>带有" * "号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src="form/js/form.js?v=<%=new Date().getTime()%>"></script>

</html>