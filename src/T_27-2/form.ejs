<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TDR0001FT拖拽_可选_上下FT</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TDR0001FT拖拽_可选_上下FT</div>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
      <!-- 对话框 -->
      <% include ./src/common/template/multyDialog/form %>

			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>拖入区域 </label>
					<div class="c-well">
						<div class="well-title">
							<p>图片<em>* </em>尺寸不超过：1920 x 700</em></p>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label for="img-1" class="btn btn-show upload" v-if="configData.source.img==''?true:false">上传</label><label  for="img-1" class="btn upload re-upload" v-if="configData.source.img!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤100KB)</span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" id="img-1" size="" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'img',100)">
							</div>
							<div class="img-preview" v-if="configData.source.img!=''?true:false">
								<img v-bind:src="configData.source.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="configData.source.img=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<!-- <label>问题 （显示位置：4）字符：≤48</label>
					<input type="text" class='c-input-txt' placeholder="请在此输入问题" maxlength='48' v-model="configData.source.question"> -->
					<label>小图区域 <em>（注：与拖入区图片等比的透明PNG小图展示效果最佳。）</em></label>
					<div class="c-well" v-for="(item,index) in configData.source.seleList">
						<div class="well-title">
							<!-- <p>小图{{index+1}}<em>*</em></p> -->
							<span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.seleList.length>1?true:false'></span>

            </div>
            <div class="clear"></div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">拖拽项{{index+1}}<em>*</em></label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                <!-- <span class='txt-info'>（大小：≤50KB)</span> -->
                <div class="field-tips">
                  <label>
                    <span><em>JPG、PNG格式，大小不超过80Kb</em></span>
                  </label>
                </div>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size=""  accept=".jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',80)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
						</div>

            <div class="field-wrap">
              <label class="field-label">音频&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</label>
              <input type="file" accept=".mp3,.wav" :id="'content-audio-'+index" volume="30" v-bind:key="Date.now()" class="btn-file"  v-on:change="audioUpload($event,item,'listAudio',30)">
              <label :for="'content-audio-'+index" class="btn btn-show upload" v-if="!item.listAudio">上传</label>
              <div class="audio-preview" v-show="item.listAudio">
                <div class="audio-tools">
                  <p v-show="item.listAudio">{{item.listAudio}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="item.listAudio"></audio>
                </span>
              </div>
              <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.listAudio" @click="item.listAudio=''">删除</label>
              <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.listAudio">重新上传</label>
              <div class="audio-tips">
                <label>
                  <span><em>mp3、wav，小于等于30Kb</em></span>
                </label>
              </div>
            </div>

					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele()" v-show='configData.source.seleList.length<6?true:false'>+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/previewsx.png" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
