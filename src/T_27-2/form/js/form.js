//var domain = 'http://172.16.0.107:9011/pages/10002/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
var domain = '';
// 对话框初始化数据
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: ""
     }
  ],
  messageLocationX: '', // 消息内容位置x
  messageLocationY: '', // 消息内容位置y
  roleLocationX: '100', // 角色位置x
  roleLocationY: '600', // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2" ,// 播放完之后状态
  scale: 100, //缩放比例  1-500
  autoNext: "1", // 是否自动播放下一条对话框
  hiddenStatus: "1", // 播放完是否应藏的状态
}
var Data = {
    configData: {
        bg: "",
        desc: "",
        title: "",
        tImg: '',
        tImgX: '',
        tImgY: '',
        tg: [{
            title: "",
            content: "",
        }],
        instructions: [{
            commandId: '-1'
        }],
        level:{
            high:[{
                title: "",
                content: ""
            }],
            low:[{
                title: "",
                content: "",
            }]
        },
        source: {
            dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
            question:"",
            img:'',
            seleList:[
              {img:'',listAudio:''}
            ]
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
    dynamicInstructions: [], //交互提示标签
};

$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (!Data.configData.source.question) {
        Data.configData.source.question = "";
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      if(!Data.configData.instructions){
        Data.configData.instructions = [{
          commandId: '-1'
        }]
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }
      // todo 升级IP组件多轮对话处理初始化数据
      if (!Data.configData.source.dialogs.scale) {
        Data.configData.source.dialogs.scale = 100;
      }
      if (!Data.configData.source.dialogs.autoNext) {
        Data.configData.source.dialogs.autoNext = "2";
      }
      if (!Data.configData.source.dialogs.hiddenStatus) {
        Data.configData.source.dialogs.hiddenStatus = "1";
      }
      if (
        Data.configData.source.dialogs.roleLocationX === "" ||
        Data.configData.source.dialogs.roleLocationX === undefined
      ) {
        Data.configData.source.dialogs.roleLocationX = "100";
      }
      if (
        Data.configData.source.dialogs.roleLocationY === "" ||
        Data.configData.source.dialogs.roleLocationY === undefined
      ) {
        Data.configData.source.dialogs.roleLocationY = "600";
      }
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
    el: '#container',
    data: Data,
    mounted: function() {
        this.getDynamicInstructions();
    },
    methods: {
        getDynamicInstructions: function() {
          var that = this;
          getDynamicInstructions(function(res, newIstructions) {
              that.dynamicInstructions = res;
              that.configData.instructions = newIstructions;
          }, that.configData.instructions);
        },
        addInstruction: function() {
            addInstruction(this.configData);
        },
        removeInstruction: function(index) {
            removeInstruction(index, this.configData);
        },
        validateInstructions: function() {
            return validateInstructions(this.configData);
        },

        //辅助提示图片上传
        tImageUpload: function(e, attr, fileSize) {
          console.log("tImageUpload",e);
          var file = e.target.files[0],
              size = file.size,
              naturalWidth = -1,
              naturalHeight = -1,
              that = this;
          var item = this.configData;

          if ((size / 1024).toFixed(2) > fileSize) {
              alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
              return;
          }
          item[attr] = "./form/img/loading.jpg";
          var img = new Image();
          img.onload = function() {
              naturalWidth = img.naturalWidth;
              naturalHeight = img.naturalHeight;
              var check = that.tImgCheck(e.target, {
                  height: naturalHeight,
                  width: naturalWidth
              }, item, attr);
              if (check) {
                  that.postData(file, item, attr);
                  img = null;
              } else {
                  img = null;
              }
          }
          var reader = new FileReader();
          reader.onload = function(evt) {
              img.src = evt.target.result;
          }
          reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        //辅助提示图片大小校检
        tImgCheck: function(input, data, item, attr) {
          let dom = $(input),
              size = dom.attr("size").split(",");
          if (size == "") return true;
          let checkSize = size.some(function(item, idx) {
              let _size = item.split("*"),
                  width = _size[0],
                  height = _size[1];
              if (width == data.width && (height+1) > data.height) {
                  return true;
              }
              return false;
          });
          if (!checkSize) {
              item[attr] = "";
              alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width+"*"+data.height);
          }
          return checkSize;
        },
        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过"+fileSize+"KB上限，请检查后上传！");
                e.target.value = '';
                return;
            }

            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                // 检查图像尺寸
                // if (naturalWidth > 1730 || naturalHeight > 720) {
                //     alert("上传的图片尺寸为：" + naturalWidth + "x" + naturalHeight + " 像素，超过限制的 1730x720 像素，请调整后上传！");
                //     e.target.value = '';
                //     img = null;
                //     return;
                // }
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        sourceImgCheck: function(input, data, item, attr) {
            let dom = $(input);
            let sizeAttr = dom.attr("size"); // 获取元素上的 size 属性
            let size = sizeAttr ? sizeAttr.split(",") : []; // 如果 size 存在则拆分，否则使用空数组
            // 如果 size 没有设置，或者 size 为空，则直接返回 true（可以上传任意尺寸）
            if (size.length === 0) {
                return true;
            }

            // 检查图片尺寸是否符合任一指定要求
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = parseInt(_size[0], 10),
                    height = parseInt(_size[1], 10);
                // // 判断如果为拖拽项，高度不能超过传入的尺寸高度，宽度不限制
                // if(type == 'smallImg' && height >= data.height){
                //   return true
                // }
                // 对比实际尺寸与限制的尺寸
                if (width === data.width && height === data.height) {
                    return true;
                }
                return false;
            });

            // 如果尺寸不符合，执行以下代码
            if (!checkSize) {
                alert("图片尺寸不符合要求！");
                if (input && input.target) {
                    input.target.value = ''; // 确保 input 的值重置，避免连续上传错误信息
                }
                console.error("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
                if (item) {
                    item[attr] = "";
                }
            }

            return checkSize;
        },
        validate: function() {
            if(this.configData.source.img==''){
                return false;
            }

            let isPass = this.configData.source.seleList.some(function(item){
                return item.img == ''
            });
            if(isPass == true){
                return false;
            }
            return true;

        },
        onSend: function() {
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
              data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
              if (data.log.teachTime == 0) {
                  alert("请填写正确的建议教学时长")
                  return;
              }
          } else {
              data.log.teachTime = data.log_editor.isTeachTimeOther
          }
            var _data = JSON.stringify(data);
            console.log( _data);
            var val = this.validate();
            if (val === true && this.validateInstructions()) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        //alert("提交成功");

                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                //console.error('提交失败');
                alert("带有*号的为必填项！");
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                    item[attr] = '';
                }
            })
        },
        //ip组件音频上传
        audioUpload: function (e, item, attr, fileSize) {
          console.log("audioUpload", item);
          //获取到的内容数据
          var file = e.target.files[0],
            type = file.type,
            size = file.size,
            name = file.name,
            path = e.target.value,
            that = this;
          if ((size / 1024).toFixed(2) > fileSize) {
            console.error(
              "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
            );
            alert(
              "您上传的音频大小为：" +
                (size / 1024).toFixed(2) +
                "KB, 超过" +
                fileSize +
                "KB上限，请检查后上传！"
            );
            return;
          }
          var url = URL.createObjectURL(file); //获取录音时长
          var audioElement = new Audio(url);
          var duration;
          audioElement.addEventListener("loadedmetadata", function (_event) {
            duration = audioElement.duration ? audioElement.duration : "";
            var check = that.sourceAudioCheck(e.target, {
              duration: duration,
            });
            if (check) {
              item[attr] = "./form/img/loading.jpg";
              that.postData(file, item, attr);
              audioElement = null;
            } else {
              audioElement = null;
            }
          });
        },
        //音频长度校检
        sourceAudioCheck: function(input, data) {
          let dom = $(input),
              time = dom.attr("time");
          if (time == "" || time == undefined || data.duration == '') return true;
          let checkSize = false;
          if (data.duration <= time) {
              checkSize = true;
          } else {
              alert(`您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`);
          }
          return checkSize;
        },
        addSele: function() {
            this.configData.source.seleList.push({img:'',listAudio:''});
        },
        delSele: function(item) {
            this.configData.source.seleList.remove(item);
        },
		addTg:function(item){
           this.configData.tg.push({title:'',content:''});
    	},
    	deleTg:function(item){
	        this.configData.tg.remove(item);
        },
        addH: function () {
            this.configData.level.high.push({title:'',content:''});
        },
        addL: function (item) {
            this.configData.level.low.push({title:'',content:''});
        },
        deleH:function(item){
	        this.configData.level.high.remove(item);
        },
        deleL:function(item){
	        this.configData.level.low.remove(item);
        },
        play: function(e){
            e.target.children[0].play();
        },
        // 添加对话框
      addDialog: function() {
        this.configData.source.dialogs.messages.push({
          text: "",
          audio: ""
        });
      },
      // 删除对话框
      delDialog: function (item) {
        this.configData.source.dialogs.messages.remove(item);
      },
      // 删除对话框音频
      delDialogPrew: function (item, key) {
        if (key) {
          item[key] = "";
        } else {
          item.dialog = "";
        }
      },
      // lottie 图片上传
      lottieUpload: function (e, item, attr, fileSize) {
        var file = e.target.files[0],
          size = file.size,
          that = this;
        if ((size / 1024).toFixed(2) > fileSize) {
          alert(
            "您上传的图片大小为" +
              (size / 1024).toFixed(2) +
              "KB, 超过" +
              fileSize +
              "K上限，请检查后上传！"
          );
          e.target.value = "";
          return;
        }
        const reader = new FileReader();
        reader.onload = async function (processEvent) {
          const jsonData = JSON.parse(processEvent.target.result);
          // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
          const naturalWidth = jsonData.w || jsonData.width;
          const naturalHeight = jsonData.h || jsonData.height;
          var check = that.sourceImgCheck(
            e.target,
            {
              height: naturalHeight,
              width: naturalWidth,
            },
            item,
            attr
          );
          if (check) {
            that.postData(file, item, attr);
          }
        };
        reader.readAsText(file);
      },

    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
