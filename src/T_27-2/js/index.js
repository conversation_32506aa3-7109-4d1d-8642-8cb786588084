"use strict"
import '../../common/js/common_1v1.js'
import "../../common/js/teleprompter.js"
import '../../common/js/drag.js'
import "../../common/template/multyDialog/index.js";
import "../../common/js/lottie.js";
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
	var h5SyncActions = parent.window.h5SyncActions;

	let staticData = configData.source;
	let seleList = staticData.seleList;
	let childPosArr = []; //存储所有被选中的元素相对于拖拽目标场景的位置坐标

	//判断是否使用默认背景图片
	if (configData.bg == '') {
		$(".container").css({ 'background-image': 'url(./image/defaultBg.jpg)' })
	}

	//spell
	(function fnSpell() {
		//判断question是否有内容
		var textHtml = ''
		var imgHtml = ''
		if (staticData.question) {
			textHtml = `
				<div class="dis-img" style="width:100%;height:5.62rem;border-radius: 0.6rem;overflow:hidden;position:relative;">
					<img src="${staticData.img}" style="position:absolute;"/>
				</div>
				<div class='dis-text'>
					<p>${staticData.question}</p>
				</div>
			`
			$('.dis-area').html(textHtml)
		} else {
			imgHtml = `<div class="dis-img" style="width:100%;height:100%;border-radius: 0.6rem;overflow:hidden;position:relative;">
						<img src="${staticData.img}" style="position:absolute;"/>
					</div>`
			$('.dis-area').html(imgHtml)
			$('.btns').hide()
		}

		const pos = { l: 1.45, t: 8.3 };
		let ele = '', x, y, l_val = 2.35, t_val = 2.64;
		for (let i = 0, length = seleList.length; i < length; i++) {
			childPosArr.push({ t: -1, l: -1 })
			x = pos.l + (i % 6) * l_val;
			y = pos.t + (parseInt(i / 6)) * t_val;
			ele += '<div class="ansBot-area" style="background-color: rgba(255, 255, 255, 0);left:' + x + 'rem;top:' + y + 'rem"></div><div class="ans-area scale" data_index="' + i + '" data-syncactions=item-' + i + ' style="left:' + x + 'rem;top:' + y + 'rem"><audio class="audiolist" src="'+ seleList[i].listAudio + '" data-syncaudio="audiolist-' + i + '"></audio><img src="' + seleList[i].img + '" style="left:' + x + 'rem;top:' + y + 'rem" /><i></i></div>'
		}
		$('.stage').append(ele);

		//调节拖入热区的图片大小
		$(".dis-img img").on("load", function () {
			const img = $(this);
			const naturalWidth = img[0].naturalWidth;
			const naturalHeight = img[0].naturalHeight;

			const containerWidth = $(".dis-img").width();
			const containerHeight = $(".dis-area").height();

			let newWidth, newHeight;
			if (naturalWidth / naturalHeight > containerWidth / containerHeight) {
				newWidth = containerWidth;
				newHeight = (naturalHeight / naturalWidth) * newWidth;
			} else {
				newHeight = containerHeight;
				newWidth = (naturalWidth / naturalHeight) * newHeight;
			}

			//确保宽高度不会超过容器
			if (newHeight > containerHeight) {
				newHeight = containerHeight;
				newWidth = (naturalWidth / naturalHeight) * newHeight;
			}

			img.css({
				width: naturalWidth/100 + 'rem',
				height: naturalHeight/100 + 'rem',
				top: '50%',
				left: '50%',
				transform: 'translate(-50%, -50%)'
			});

			img.parent().css({
				width: naturalWidth/100 + 'rem',
				height: naturalHeight/100 + 'rem',
			});
			// 设置 .dis-area 的宽度和高度一样，保持统一
            $(".dis-area").css({
                width: naturalWidth/100 + 'rem',
                height: naturalHeight/100 + 'rem',
            });
		});

		//图片尺寸大小判断 for draggable images
		$(".ans-area img").on("load", function () {
			contrImgSize.call(this);
		});

		function contrImgSize() {
			var _this = $(this),
				imgWidth = _this.get(0).naturalWidth,
				imgHeight = _this.get(0).naturalHeight,
				containerScale = imgWidth / imgHeight;   //容器的宽高比
			if (containerScale < 1) {//瘦高型
				if (imgHeight > 206) {
					_this.css({
						height: '100%',
						width: 'auto'
					});
					_this.data("size", { width: 'auto', height: '100%' })//记录图片开始位置尺寸
				} else {
					_this.css({
						height: imgHeight / 100 + "rem",
						width: 'auto'
					});
					_this.data("size", { width: 'auto', height: imgHeight / 100 + "rem" })
				}
				_this.parent('.ans-area').data("size", { width: imgWidth, height: imgHeight })//记录图片本身尺寸，用于拖动后父容器重新调整大小
			} else {//胖矮型
				if (imgWidth > 206) {
					_this.css({
						width: '100%',
						height: 'auto'
					});
					_this.data("size", { width: '100%', height: 'auto' })
				} else {
					_this.css({
						width: imgWidth / 100 + "rem",
						height: 'auto'
					});
					_this.data("size", { width: imgWidth / 100 + "rem", height: 'auto' })
				}
				_this.parent('.ans-area').data("size", { width: imgWidth, height: imgHeight })
			}
		}
	})()
	if (isSync && window.frameElement.getAttribute('user_type') == 'tea') {
		$('.ans-btn').text('Done');
	}
	// if( !configData.desc && !configData.source.title ) {
	// 	$('.commom').hide();
	// 	$('.stage').css('margin-top', '1.3rem');
	// }
	// if( configData.desc!='' && staticData.title=='' ) {
	// 	$('.commom .title').hide();
	// 	$('.stage').css('margin-top', '.4rem');
	// }

	if (configData.source.question) {
		if ($('.dis-text p').height() / window.base < 0.5) {
			$('.dis-text p').css({
				"height": "1.64rem",
				"line-height": "1.64rem",
				"text-align": "center"
			})
		}
	}
	//=================================
	//点击上下翻页按钮
	if (($('.dis-text p').height() / window.base - $('.dis-text').height() / window.base) <= 0) {
		$(".downBtn").addClass('noChange').find('img').css('opacity', '0.3')
	}
	let isReScrollUp = true;
	let contentReUp = 0;
	$(".upBtn").on("click touchstart", function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation();
		if (isReScrollUp) {
			isReScrollUp = false;
			if (contentReUp <= 0) {
				$(this).addClass('noChange').find('img').css('opacity', '0.3')
				isReScrollUp = true;
			} else {
				if (!isSync) {
					$(this).trigger("syncgoReUp");
					return;
				}
				SDK.bindSyncEvt({
					sendUser: '',
					receiveUser: '',
					index: $(e.currentTarget).data("syncactions"),
					eventType: 'click',
					method: 'event',
					syncName: 'syncgoReUp'
				});
			}
		}
	});
	$(".upBtn").on("syncgoReUp", function (e) {
		contentReUp -= 0.6;
		contentReUp = parseFloat(contentReUp);
		$(".downBtn").removeClass('noChange').find('img').css('opacity', '1')
		$('.dis-text p').css('top', -contentReUp + 'rem').css({
			"transition": "all 0.3s"
		});
		SDK.setEventLock();
		isReScrollUp = true;
	});
	let isScrollReDown = true;
	$(".downBtn").on("click touchstart", function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation();
		if (isScrollReDown) {
			isScrollReDown = false;
			let diffHeight = $('.dis-text p').height() / window.base - $('.dis-text').height() / window.base;
			if (contentReUp >= diffHeight) {
				$(this).addClass('noChange').find('img').css('opacity', '0.3')
				isScrollReDown = true;
			} else {
				if (!isSync) {
					$(this).trigger("syncgoRedown");
					return;
				}
				SDK.bindSyncEvt({
					sendUser: '',
					receiveUser: '',
					index: $(e.currentTarget).data("syncactions"),
					eventType: 'click',
					method: 'event',
					syncName: 'syncgoRedown'
				});
			}
		}
	});
	$(".downBtn").on("syncgoRedown", function (e) {
		contentReUp += 0.6;
		contentReUp = parseFloat(contentReUp);
		$(".upBtn").removeClass('noChange').find('img').css('opacity', '1')
		$('.dis-text p').css({ 'top': -contentReUp + 'rem', "transition": "all 0.3s" })
		SDK.setEventLock();
		isScrollReDown = true;
	});
	//鼠标按下改变状态
	$('.sameBtn').on('mousedown touchstart', function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation()
		if (!$(this).hasClass('noChange')) {
			$(this).css({
				"background": "rgba(0,0,0,0.08)",
			})
			$(this).find('img').css({
				opacity: 0.2
			})
		}
	})
	$('.sameBtn').on('mouseup touchend', function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation()
		if (!$(this).hasClass('noChange')) {
			$(this).css({
				"background": "none",
			})
			$(this).find('img').css({
				opacity: 1
			})
		}
	})
	//===============================
	//添加拖拽
	var dragBefore = true;
	var dragEnd = true;
	var dragProcess = true;
	var lock = true;
	let $audioDrag = document.getElementsByClassName('audioDrag')[0]
	function getRemInPixels() {
		// 获取 `html`（文档的根元素）元素的计算样式
		const htmlElement = document.documentElement;
		const fontSize = window.getComputedStyle(htmlElement).fontSize;

		// 返回值是形如 "16px" 的字符串，要转换成数字
		return parseFloat(fontSize);
	}
	const remInPixels = getRemInPixels();
	$('.ans-area').drag({
		before: function (e) {
			// $audioDrag ? $audioDrag.play() : "";
			if($audioDrag) {
				SDK.playRudio({
					index: $audioDrag,
					syncName: $(".audioDrag").attr("data-syncaudio")
                })
			}
			//if(dragBefore) {
			//dragBefore = false;
			$(this).removeClass('scale')
			if (!isSync) {

				$(this).trigger('syncDragBefore', {
					left: $(this).data('startPos').left,
					top: $(this).data('startPos').top,
					pageX: '',
					pageY: '',
				});
				return;
			}

			SDK.bindSyncEvt({
				index: $(this).data('syncactions'),
				eventType: 'dragBefore',
				method: 'drag',
				left: $(this).data('startPos').left,
				top: $(this).data('startPos').top,
				pageX: '',
				pageY: '',
				syncName: 'syncDragBefore'
			});
		},
		process: function (e) {

			// if (!isSync) {
			// 	$(this).trigger('syncDragProcess', {
			// 		left: $(this).attr('data-left'),
			// 		top: $(this).attr('data-top'),
			// 		pageX: '',
			// 		pageY: '',
			// 	});
			// 	return;
			// }


			// if (lock) {
			// 	lock = false;
			// 	setTimeout(function() {
			// 		SDK.bindSyncEvt({
			// 			index: $(this).data('syncactions'),
			// 			eventType: 'dragProcess',
			// 			method: 'drag',
			// 			left: $(this).attr('data-left'),
			// 			top: $(this).attr('data-top'),
			// 			pageX: '',
			// 			pageY: '',
			// 			syncName: 'syncDragProcess'
			// 		});
			// 		lock = true;
			// 	}.bind(this), 300);
			// }
		},

		end: function (e) {
			$(this).addClass('scale')
			childPosArr[$(this).attr('data_index')] = {
				t: $(this).attr('data-top').substring(0, $(this).attr('data-top').length - 3) - ($('.dis-area').position().top / remInPixels-0.1),
				l: $(this).attr('data-left').substring(0, $(this).attr('data-left').length - 3) - ($('.dis-area').position().left / remInPixels-0.1),
			};

			let criticalValueLeft = $('.dis-area').position().left - ($(this).width() / 2);
			let criticalValueRight = $('.dis-area').position().left + $('.dis-area').width();
			let criticalValueTop = $('.dis-area').position().top - ($(this).height() / 2);
			let criticalValueBottom = $('.dis-area').position().top + $('.dis-area').height();

			// 检查例如在左边或下方是否应恢复
			let nowLeft = parseInt($(this).attr('data-left')) * window.base;
			let nowTop = parseInt($(this).attr('data-top')) * window.base;

			if (nowLeft < criticalValueLeft || nowLeft > criticalValueRight || nowTop < criticalValueTop || nowTop > criticalValueBottom) {
				// 在必要情况下恢复初始位置
				$(this).resetStart();
				childPosArr[$(this).attr('data_index')] = { t: -1, l: -1 }; // 确保位置被标记为重置
			}

			if (!isSync) {
				$(this).trigger('syncDragEnd', {
					left: $(this).attr('data-left'),
					top: $(this).attr('data-top'),
					pageX: '',
					pageY: '',
				});
				return;
			}

			SDK.bindSyncEvt({
				index: $(this).data('syncactions'),
				eventType: 'dragEnd',
				method: 'drag',
				left: $(this).attr('data-left'),
				top: $(this).attr('data-top'),
				pageX: '',
				pageY: '',
				syncName: 'syncDragEnd',
				otherInfor: {
					childPosArr: childPosArr
				},
				recoverMode: '1'
			});
		}
	});

	//=======================================================
	var existNum = 0;
	$('.ans-area').on("syncDragBefore", function (e, pos) {

		//if($(this).attr('tag')!='true'){//定位层级控制
		$(this).css('z-index', existNum);
		existNum++;

		//}
		$(this).attr('tag', 'true');//是否可提交标记
		// $(this).data('startPos', {
		// 	left: pos.left,
		// 	top: pos.top
		// });
		SDK.setEventLock();
	});

	/*
	$('.ans-area').on("syncDragProcess", function(e, pos) {
		$(this).css({
			'left': pos.left,
			'top': pos.top
		});
		SDK.setEventLock();
		//lock = true;
	});
	*/
  // 获取根字体大小（最终计算值）
  var area_width = parseFloat($('.ans-area').css('width'));
  var rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);//1rem等于多少px
  var area_rem = area_width / rootFontSize;//当前计算的元素为多少rem
  var array = [] //已经拖进框内的选项
  var status = true //是否播放对应声音的状态
	$('.ans-area').on("syncDragEnd", function (e, pos) {
		if (isSync) {
			let obj = pos.otherInfor;
			childPosArr = obj.childPosArr;
			if (pos == undefined || pos.operate == 1) {
				// 处理其他情况
			} else {
				// 掉线直接恢复页面
				recoverSelect(pos);
				SDK.setEventLock();
				return;
			}
		}

		var startPos = $(this).data('startPos');

		// 计算热区的四个边界值
		let disAreaPosition = $('.dis-area').position();
		let criticalValueLeft = disAreaPosition.left - ($(this).width() / 2);
		let criticalValueRight = disAreaPosition.left + $('.dis-area').width();
		let criticalValueTop = disAreaPosition.top - ($(this).height() / 2);
		let criticalValueBottom = disAreaPosition.top + $('.dis-area').height();

		// 计算元素的四个边缘坐标
		let elementLeft = parseInt(pos.left) * window.base;
		let elementRight = elementLeft + $(this).width();
		let elementTop = parseInt(pos.top) * window.base;
		let elementBottom = elementTop + $(this).height();

		// 检查是否完全在热区以内
		if (elementLeft > criticalValueLeft && elementRight < criticalValueRight &&
			elementTop > criticalValueTop && elementBottom < criticalValueBottom) {
			// 如果元素的四个边缘均在热区内，则设置为缩放大小
			let _size = $(this).data('size');
      let cssObj = {
        'border-radius': '0',
        'line-height': '0',
        'left': pos.left,
        'top': pos.top
      }
      if( _size.width / 100 > area_rem){
        cssObj = Object.assign(cssObj,{
          'width': _size.width / 100 + 'rem',
          'height': _size.height / 100 + 'rem',
        })
      }
      $(this).css(cssObj);

			$(this).find('img').css({ 'width': _size.width / 100 + 'rem', 'height': _size.height / 100 + 'rem' });
		} else {
			// 如果元素没有完全在热区内，则恢复到初始位置
			$(this).resetStart();
		}

    // 拖拽结束后播放上传的拖拽对应音频
    //查询当前选项是否在框内
    if(array.indexOf($(this).attr('data-syncactions')) != -1){
      status = false
    }else{
      status = true
      array.push($(this).attr('data-syncactions'))
    }
    //找出所有在框内的选项
    if ($(this).attr("tag") == "true") {
      //判断当前音频是否可以播放
      if (status == true) {
        let listAudio = $(this).find("audio")[0];
        let listAudioSrc = $(this).find("audio").attr("src");
        if (listAudio && listAudioSrc && listAudioSrc != "undefined") {
          SDK.playRudio({
            index: listAudio,
            syncName: $(this).find(".audiolist").attr("data-syncaudio"),
          });
          listAudio.addEventListener("ended", function () {
            console.log("播放完成");
          });
        }
      }
    } else {//不在框内移出数组
      const index = array.indexOf($(this).attr("data-syncactions"));
      if (index !== -1) {
        array.splice(index, 1);
      }
    }
		// 检测是否可提交
		if ($(".ans-area[tag=true]").length > 0) {
      $('.ans-btn').addClass('allowSub');
		} else {
			$('.ans-btn').removeClass('allowSub');
		}
    SDK.setEventLock();
	});

	//提交
	var btnClickTimer = true;
	let $audioPhoto = document.getElementsByClassName('audioPhoto')[0]

	$('.ans-btn').on("click", function (e) {
		if (btnClickTimer) {
			btnClickTimer = false;
			if (!isSync) {
				$(this).trigger('syncBtnClick');

				return;
			}
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(this).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'syncBtnClick',
        otherInfor: {
          childPosArr: childPosArr
        },
        recoverMode:'1'
      });
		}
	});
	$('.ans-btn').on('syncBtnClick', function (e, message) {
		if (isSync) {
			let obj = message.data[0].value.syncAction.otherInfor;
			if (message == undefined || message.operate == 1) {
				childPosArr = obj.childPosArr
			} else {
				//直接恢复页面 提交答案后出现掉线
				setPhoto($(this), childPosArr, message)
				SDK.setEventLock()
				return
			}
		}
		// $audioPhoto ? $audioPhoto.play() : "";
		if($audioPhoto){
			SDK.playRudio({
				index: $audioPhoto,
				syncName: $(".audioPhoto").attr("data-syncaudio")
			})
		}
		setPhoto($(this), childPosArr)

		SDK.setEventLock();
		btnClickTimer = true;
	})

	//老师端响应
	let stuNameEle = '',
		userList = [];


	let userId = '';
	$('#container').on('syncResultClick', function (e, message) {
		var sendUserInfo = message.data[0].value.sendUserInfo;
		if (sendUserInfo.type == 'stu') {
			userId = sendUserInfo.id;
		}
		$('.stuNameList[id=' + userId + ']').addClass('stuName-active');
		SDK.setEventLock();
		btnClickTimer = true;
	})

	//拖拽选项过程中掉线恢复
	function recoverSelect(pos) {
		for (let i = 0; i < pos.otherInfor.childPosArr.length; i++) {
			if (pos.otherInfor.childPosArr[i].l !== -1 && pos.otherInfor.childPosArr[i].t !== -1) {
				$(".ans-area").eq(i).css({
					position: 'absolute',
					left: (pos.otherInfor.childPosArr[i].l) - 0 + 1.45 + 'rem',
					top: (pos.otherInfor.childPosArr[i].t) - 0 + 0.35 + 'rem'
				})
				var _size = $(".ans-area").eq(i).data('size');
        $(".ans-area").eq(i).css({
					'border-radius': '0',
					'line-height': '0',
				});
        if( _size.width / 100 > area_rem){
          $(".ans-area").eq(i).css({
            'width': _size.width / 100 + 'rem',
            'height': _size.height / 100 + 'rem',
          });
        }

				$(".ans-area").eq(i).find('img').css({ 'width': _size.width / 100 + 'rem', 'height': _size.height / 100 + 'rem' });
			} else {
				$(".ans-area").eq(i).resetStart();
			}

			if (isSync && window.frameElement.getAttribute('user_type') == 'stu') {
				$('.ans-btn').addClass('allowSub');
			}
		}
	}
	//提交拍照  that:事件触发对象  childPosArr:被拖入场景区的道具相对于场景位置坐标\
	function setPhoto(that, childPosArr, message) {
		that.text('Done').removeClass('allowSub');
		$('.cover').show();
		for (let i = 0; i < childPosArr.length; i++) {
			if (childPosArr[i].l == -1 && childPosArr[i].t == -1) {
				$(".ans-area").eq(i).resetStart();
			}
		}

		// 获取实际尺寸
		let dragW = $('.dis-area img').outerWidth();
		let dragH = $('.dis-area img').outerHeight();
		if (message === undefined || message.operate === 1) {
			$(".photoMask").css({
				display: 'block',

			}).append($(".dis-area").clone());

			$(".showMask").css({
				animation: 'camStyle .3s 1 forwards',
				'-webkit-animation': 'camStyle .3s 1 forwards'
			});

			$(".showMask").on('animationend webkitAnimationEnd', function () {
				$(this).css({ 'z-index': 10 });
			});

			$(".photoMask .dis-area").css({
				'z-index': '100',
				'border-radius': '0',
				width: `${dragW}px`, // 使用动态获取的宽度
				height: `${dragH}px`, // 使用动态获取的高度
				position: 'absolute',
				left: `calc(50% - ${dragW / 2}px)`, // 居中
				top: `calc(50% - ${dragH / 2}px)`,  // 居中
				border: '.24rem solid #fff',
				'border-bottom': '0.8rem solid #fff',
				background: '#fff',
				animation: 'ptoStyle .5s 1 forwards',
				'-webkit-animation': 'ptoStyle .5s 1 forwards',
				'box-shadow': '0 0 .4rem rgba(255, 255, 255, 0.8)'
			});
		} else {
			$(".showMask").css({ 'z-index': 10 });

			if (!$(".photoMask .dis-area").length > 0) {
				$(".photoMask").css({
					display: 'block'
				}).append($(".dis-area").clone());
			}

			$(".photoMask .dis-area").css({
				'z-index': '100',
				'border-radius': '0',
				width: `${dragW}px`,
				height: `${dragH}px`,
				position: 'absolute',
				left: `calc(50% - ${dragW / 2}px)`, // 居中
				top: `calc(50% - ${dragH / 2}px)`,  // 居中
				border: '.24rem solid #fff',
				'border-bottom': '0.8rem solid #fff',
				background: '#fff',
				animation: 'ptoStyle 0.1s 1 forwards',
				'-webkit-animation': 'ptoStyle 0.1s 1 forwards',
				'box-shadow': '0 0 .4rem rgba(255, 255, 255, 0.8)'
			});
		}

		$('.ans-area').removeClass('scale');
		$(".dis-img").css({ 'border-radius': '0', height: '100%' });
		$(".dis-text").css({ 'display': 'none' });
		$(".showMask").css({ 'background': 'rgba(51, 51, 51, 0.51)' });
		for (let i = 0; i < childPosArr.length; i++) {

			if (childPosArr[i].l !== -1 && childPosArr[i].t !== -1) {
				$(".stage .ans-area").eq(i).clone().appendTo($(".photoMask .dis-area"));
				$(".stage .ans-area").eq(i).css({ display: 'none' });
			}
			$(".photoMask .ans-area").each(function(j) {
				if (i == $(this).attr('data_index')) {
					$(this).css({
						position: 'absolute',
						left: `${childPosArr[i].l}rem`,
						top: `${childPosArr[i].t}rem`
					});
				}
			});
		}

		$(".stage .dis-area").css({ display: 'none' });
	}

});

jQuery.fn.extend({
	resetStart: function (size) {
		var thisObj = this;
		var startPos = $(this).data('startPos');
		var $left = startPos.left;
		var $top = startPos.top;
		thisObj.css({
			'left': $left,
			'top': $top,
			'width': '2.06rem',
			'height': '2.06rem',
			// 'border-radius': '.8rem'
		});
		var _img = thisObj.find('img');
		var _originSize = _img.data('size');
		_img.css({
			'width': _originSize.width,
			'height': _originSize.height
		})
		$(this).attr('tag', 'false');
	}
});
