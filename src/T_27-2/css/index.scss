@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/multyDialog/style.scss';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
.container{
	background-image: url(../image/defaultBg.jpg);
}
.desc,.title{
	display: none !important;
}
// .desc-visi{
// 	    visibility: hidden;
// }
// .tg{
// 	z-index: 999;
// }fans-area 
// .title{
// 	height: 1.3rem;
// }
.main {
	width: 100%;
	height: 100%;
}
.stage{
	width: 100%;
	height: 100%;
	position: relative;
	display: flex;
	justify-content: center;
	left: 0;
	top: 0;
	.dis-area{
		width: 100%;
		height: 70%;
		position: absolute;
        top: 50%; /* 相对于 .stage 的 50% */
        transform: translateY(-70%); /* 向上移动一半 .dis-area 的高度 (35%) */
		
	}
	.ans-area{
		@include setEle(9.85rem,0.35rem,2.06rem,2.06rem);
		// position: sticky;
		line-height: 2.06rem;
		border: .12rem transparent solid;
		background: transparent;
		// border-radius: 0.8rem;
		overflow: hidden;
		text-align: center;
		img{
			//position: absolute;
			vertical-align: middle;
			cursor: pointer;
		}
		i{
			height: 100%;
			display: inline-block;
			vertical-align: middle;
		}
	}
	.ansBot-area{
		@extend .ans-area;
	}
	.scale:hover img{
		transform: scale(1.05,1.05);
	}
}
.ansButton-area{
	width: 2.06rem;
	height: 2.06rem;
	display: flex;
	justify-content: center;
    align-items: center;
    position: absolute;
	left: 15.45rem;
	top: 8.5rem;
	.ans-btn{
		    width: 2.14rem;
            height: 1.04rem;
            // border: 0.08rem #fff solid;
            border-radius: 1.04rem;
            background-image: url(../image/submit.png);
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            text-align: center;
            line-height: 1.04rem;
            color: transparent; 
            font-size: 0.48rem;
            font-weight: 600;
            cursor: pointer;
	}
	.allowSub{
		// background: #59a500;
		background-image: url(../image/submit1.png);
	}
	.allowSub:hover{
		transform: scale(1.05,1.05)
	}
}
.showName-area{
	display: flex;
	justify-content: center;
    align-items: center;
    flex-direction: column;
	width: 2.06rem;
	height: 2.06rem;
    position: absolute;
	left: 15.45rem;
	top: 5.63rem;
	border: .12rem #fff solid;
	background: #fff;
	border-radius: 0.8rem;
	.stuNameList{
		width: 100%;
		line-height: 0.4rem;
		color: #cccccc;
		font-size: .24rem;
		font-weight: 600;
		text-align: center;
		overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	}
}
.stuName-active{
	color: #55a000!important;
}
.cover{
	position: absolute;
    z-index: 120;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
	display: none;
	// background: red;
}
.photoMask{
	// position: absolute;
	// width: 100%;
	// height: 100%;
	// top: 0;
	// left: 0;
	// display: none;
	display: none; /* 依据需要设置 */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* 加点颜色变化 */
    z-index: 9999;
    overflow: hidden;
	.showMask{
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background: rgba(8, 8, 8, 0.7);
		z-index: 199;
	}
	// .dis-area{
	// 	overflow: hidden;
	// }
}
@keyframes ptoStyle{
	0%{
		transform: scale(1.2,1.2) rotate(0deg);
		// transform-origin:left top;
	}
	100%{
		transform: scale(1.1,1.1) rotate(-3deg);
		// transform-origin:left top;
	}
}
@keyframes camStyle{
	0%{
		background: rgba(51, 51, 51, 1);
	}
	20%{
		background: rgba(255, 255, 255, 1);
	}
	40%{
		background: rgba(255, 255, 255, 0.8);
	}
	100%{
		background:rgba(51, 51, 51, 0.51);
	}
}
@-webkit-keyframes ptoStyle{
	0%{
		transform: scale(1.2,1.2) rotate(0deg);
		// transform-origin:left top;
	}
	100%{
		transform: scale(1.1,1.1) rotate(-3deg);
		// transform-origin:left top;
	}
}
@-webkit-keyframes camStyle{
	0%{
		background: rgba(51, 51, 51, 1);
	}
	20%{
		background: rgba(255, 255, 255, 1);
	}
	40%{
		background: rgba(255, 255, 255, 0.8);
	}
	100%{
		background:rgba(51, 51, 51, 0.51);
	}
}