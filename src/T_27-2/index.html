<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能示例</title>
    <style>
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
        }
        .box {
            width: 420px;
            height: 150px;
            border: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            position: relative;
            background-color: #f0f0f0;
        }
        .box-bottom {
            background-color: #d0e0f0;
            overflow: hidden; /* 确保超出内容隐藏 */
            width: 420px; /* 设置宽度为6个盒子的总宽度 */
            position: relative;
        }
        .item {
            width: 60px;
            height: 60px;
            background-color: #a0c4ff;
            margin: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .image-list {
            display: flex;
            transition: transform 0.5s ease;
            width: calc(12 * (60px + 10px)); /* 根据小盒子总数计算总宽度 */
        }
        .buttons {
            margin-bottom: 10px;
        }
        .place-holder {
            width: 60px;
            height: 60px;
            margin: 5px;
            visibility: hidden;
        }
    </style>
    <!-- 引入 jQuery 和 jQuery UI -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
</head>
<body>
<div class="container">
    <div class="buttons">
        <button id="prev">左</button>
        <button id="next">右</button>
    </div>
    <div class="box" id="top-box">
        <!-- 拖入的盒子 -->
    </div>
    <div class="box box-bottom" id="bottom-box">
        <div class="image-list">
            <!-- 小盒子将在这里动态插入 -->
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // 初始化变量
        const items = [];
        const totalItems = 12;
        const itemsPerPage = 6;

        // 创建12个小盒子
        for (let i = 1; i <= totalItems; i++) {
            items.push(`<div class="item" data-index="${i}">Box ${i}</div>`);
        }
        const $imageList = $('.image-list');
        $imageList.html(items.join(''));
        
        let currentIndex = 0;

        // 切换图像的功能
        function updateImageList() {
            const offset = -(currentIndex * (60 + 10)); // 60px (item) + 10px (margin)
            $imageList.css('transform', `translateX(${offset}px)`);
        }

        $('#prev').click(function() {
            if (currentIndex >= itemsPerPage) {
                currentIndex -= itemsPerPage;
                updateImageList();
            }
        });

        $('#next').click(function() {
            if (currentIndex < totalItems - itemsPerPage) {
                currentIndex += itemsPerPage;
                updateImageList();
            }
        });

        // 为小盒子添加拖拽功能
        $('.image-list .item').draggable({
            revert: 'invalid', // 无效放置时返回原位置
            helper: 'clone', // 用克隆外观进行拖拽
            start: function(event, ui) {
                $(this).css('z-index', 1000);
            },
            stop: function(event, ui) {
                $(this).css('z-index', '');
            }
        });

        // 为上面的盒子添加拖拽接收能力
        $('#top-box').droppable({
            accept: '.image-list .item',
            drop: function(event, ui) {
                // 标记占位符放置原位置
                let $dragged = $(ui.draggable);
                let index = $dragged.data('index');
                $dragged
                    .css({top: 0, left: 0, position: 'relative'})
                    .appendTo($(this));

                // 在原位置插入占位符
                $imageList.find(`[data-index="${index}"]`).replaceWith(`<div class="place-holder"></div>`);
            }
        });

        // 为下面的盒子添加拖拽接收能力
        $('#bottom-box').droppable({
            accept: '#top-box .item',
            drop: function(event, ui) {
                // 将放上去的还原并保持位置
                let $dragged = $(ui.draggable);
                let index = $dragged.data('index');

                // 移回原下面位置
                $imageList.find(`.place-holder`).first().replaceWith($dragged);
                updateImageList();
            }
        });

        // 初始化显示
        updateImageList();
    });
</script>
</body>
</html>