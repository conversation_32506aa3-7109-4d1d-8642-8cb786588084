<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TMA0003_消消乐_中文版</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">TMA0003_消消乐_中文版</h3>
      <!-- 公共区域 -->
      <% include ./src/common/template/common_head_cn %>

      <!-- 每轮游戏设计 -->
      <div class="c-group">
        <div class="c-title">每轮游戏设计</div>
        <div class="c-area upload img-upload radio-group">
          <div class="well-left well-group-left">
            <div class="left">
              <p>- 共2～4组游戏，每组2个图片。</p>
              <p>- 填写位置时，位置序号请参考右图。</p>
              <p>- 封面图片指最终呈现给用户的卡片。</p>
            </div>
            <div class="right">
              <ul>
                <li>1</li>
                <li>3</li>
                <li>5</li>
                <li>7</li>
                <li>2</li>
                <li>4</li>
                <li>6</li>
                <li>8</li>
              </ul>
            </div>
          </div>
          <div>注：<br>两组序号请选择1～4<br>  三组序号请选择1～6  <br>四组请选择1～8</div>

          <div v-for="(item,index) in configData.source.options">
            <div class="c-well">
              <label class="field-label field-label-text"  for="">第{{index+1}}组配对</label>
              <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>2"></span>
              <div class="field-wrap answerText">
                <span class="txt-info">答案文本<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;</span>
                <label><input type="text" maxlength="25" placeholder="请在此输入文本" v-model="item.title" class="c-input-txt"></label>&nbsp;
                <span class="explain">建议13个字母以内</span>
              </div>

              <div class="field-wrap answer-content">
                <label class="field-label"  for="">答案音效<em>*</em>&nbsp;&nbsp;&nbsp;</label>
                <input type="file" accept=".mp3" :id="'content-audio-'+index" volume="20" v-bind:key="Date.now()" class="btn-file" v-on:change="audioUpload($event,item,'audio')">
                <label :for="'content-audio-'+index"  class="btn btn-show upload" v-if="!item.audio">上传</label>
                <div class="audio-preview" v-show="item.audio">
                  <div class="audio-tools">
                    <p v-show="item.audio">{{item.audio}}</p>
                  </div>
                  <span class="play-btn" v-on:click="play($event)">
                    <audio v-bind:src="item.audio"></audio>
                  </span>
                </div>
                <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.audio" @click="item.audio=''">删除</label>
                <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
                <label><em>大小：≤20KB</em></label>
                <label class="field-label">
                  <span class="game_span">答案声音长度<em>*</em></span>
                  <input type="number" class="c-input-txt game_input" v-model="item.audioTime"
                    oninput="if(value<1000)value=1000">
                  <em class="game_em">毫秒，(1秒等于1000毫秒，不低于1000毫秒，不高于6000毫秒)</em>
                </label>
              </div>

              <div class="answer-content">
                <div class="field-wrap">
                    <span class='txt-info'>选项图片1<em>*</em>&nbsp;&nbsp;&nbsp;
                    <label :for="'content-pic-'+index"class="btn btn-show upload" v-if="item.img==''?true:false">上传</label>&nbsp;尺寸:344*344&nbsp;&nbsp;大小:≤30KB</span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size="344*344" v-on:change="imageUpload($event,item,'img',30)">
                    <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img">重新上传</label>
                </div>
                <div class="img-preview" v-if="item.img">
                  <img v-bind:src="item.img" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                    </div>
                </div>
                <label class="label-img">位置序号<em>*</em>&nbsp;
                    <!-- <input type="number" class="c-input-txt input60" v-model="item.positionOne"> -->
                    <select v-model="item.positionOne" class="select-class">
                      <option v-for="conPos in updata" name="optive" :value="conPos"
                      >{{conPos}}</option>
                    </select>
                </label>
              </div>
              <div class="field-wrap">
                <span class="txt-info">选项图片2<em>*</em></span>
                <label for="imgtype" class="inline-label">
                  <input type="radio" :name="'imgtype' + index" value="1" v-model="item.imgType">&nbsp;和图片1相同
                </label>
                <label for="imgtype" class="inline-label">
                  <input type="radio" :name="'imgtype' + index" value="2" v-model="item.imgType">&nbsp;新的图片
                </label>
                <div class="serial-new-img" v-if="item.imgType == 2">
                  <div class="field-wrap" style="margin-left: 80px;">
                    <label :for="'content-picNew-'+index"class="btn btn-show upload" v-if="item.imgNew==''?true:false">上传</label>&nbsp;尺寸:344*344&nbsp;&nbsp;大小:≤30KB</span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'content-picNew-'+index" size="344*344" v-on:change="imageUpload($event,item,'imgNew',30)">
                    <label :for="'content-picNew-'+index" class="btn upload re-upload" v-if="item.imgNew">重新上传</label>
                  </div>
                  <div class="img-preview" v-if="item.imgNew">
                    <img v-bind:src="item.imgNew" alt=""/>
                      <div class="img-tools">
                        <span class="btn btn-delete" v-on:click="item.imgNew=''">删除</span>
                      </div>
                  </div>

                  <label>
                    <span>封面图片</span>
                    <label for="cover" class="inline-label">
                      <input type="radio" :name="'cover' + index" value="1" v-model="item.cover">&nbsp;图片1</label>
                    <label for="cover" class="inline-label">
                      <input type="radio" :name="'cover' + index" value="2" v-model="item.cover">&nbsp;图片2
                    </label>
                  </label>
                </div>
                <div class="serial-number">
                  <label>位置序号<em>*</em>&nbsp;
                    <!-- <input type="number" class="c-input-txt input60" v-model="item.positionTwo"> -->
                    <select v-model="item.positionTwo" class="select-class">
                      <option v-for="conPos in updata" name="optive" :value="conPos"
                      >{{conPos}}</option>
                    </select>
                  </label>
                </div>
              </div>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-show="configData.source.options.length<4"
              v-on:click="addOption({title:'', img:'', imgNew:'', positionOne:'', positionTwo:'', imgType:1, cover:1, audio:''})">+</button>
        </div>
      </div>

			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.png?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
