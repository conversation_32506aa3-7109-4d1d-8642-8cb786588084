"use strict"
import { data } from 'jquery';
import '../../common/js/common_1v1.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import {
  resultWin,
  resultHide
} from '../../common/template/ribbonWinCn/index.js';

import {
  templateScale,
  handClick,
  handHide,
} from "../../common/template/hint/index.js";

$(function () {

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  //是否在控制器显示功能按钮
  window.h5Template = {
    hasDemo: '1', //0 默认值，无提示功能  1 有提示功能
    hasPractice: '2' //0 无授权功能  1  默认值，普通授权模式  2 start授权模式
}

  // 左右背景图片预加载
  if(configData.bg == ''){
    $(".container").css({
      'background-image':'url(./image/bj.jpg)'
    })
  } else {
    $('.container').css('background-image','url('+ configData.bg +')')
  }
  let classStatus = 1; //教室状态

  // 多语言
  if (configData.lang == 2) {//简体中文
    // $(".startMsg").attr('src', 'https://cdn.51talk.com/apollo/images/240270121463efa9fca1bd204f165c06.png')
    $(".startMsg").attr('src', './image/240270121463efa9fca1bd204f165c06.png')
    // 按钮2个
    // $(".demo-btnStu").attr('src','//cdn.51talk.com/apollo/images/6632e5ad4090fbb1a4e30722a133b3b1.png')
    // $(".startBtn").attr('src','//cdn.51talk.com/apollo/images/519210d78b059247ad1e36b41d94d2ca.png')
    $(".demo-btnStu").attr('src','./image/6632e5ad4090fbb1a4e30722a133b3b1.png')
    $(".startBtn").attr('src','./image/519210d78b059247ad1e36b41d94d2ca.png')
  }

  let userType = window.frameElement && window.frameElement.getAttribute('user_type'); //用户身份学生还是老师
  let isFirst = true;
  let options = configData.source.options, //图片列表
      isCheck = true, //是否可点击选择
      time = 3000, //消失终局特效的时间
      firstDataIndex = null, // 存储当前第一个选中的组内序号
      firstKey = null, // 存储当前第一个选中的key值
      twoKey = null, //存储当前第二个选中的key值
      curvOne = [],  // 存储当前第一个选中的坐标
      isSyncKey = null, //断线重连第一次点击时的key值
      isSyncFirstKey = [],  //断线重连消除后的第一个key值集合
      isSyncLastKey = [], //断线重连消除后的第二个key值集合
      completeArray = [],//存储选中后的内容（图片、标题、音频）
      hintIndex = 0, //hint模式选中的key值
      isHint = false, //是否是hint模式 true:hint模式
      half = 2, //链接时距离的倍数 默认为2，hint为1.45
      timeInt = null, // 定时器
      isAnyOneOut = false,//是否断线  true 已断线
      isDemo = false, //是否是demo模式
      ismatelSound = true; //音效是否播放


    //判断用户角色，显示不同功能
    isShowBtn();

    //判断用户角色，显示不同功能(如老师的底部文字提示，学生的预习模式等)
    function isShowBtn() {
      if (isSync) { //同步模式
          classStatus = SDK.getClassConf().h5Course.classStatus;
          if (classStatus == 0 && userType == "stu") {
              $(".funcMask").show(); //预习模式和倒计时
          }
      } else { //非同步模式
          var hrefParam = parseURL("http://www.example.com");
          if (top.frames[0] && top.frames[0].frameElement) {
              hrefParam = parseURL(top.frames[0].frameElement.src)
          }
          var role_num = hrefParam.params['role']

          function parseURL(url) {
              var a = document.createElement('a')
              a.href = url
              return {
                  source: url,
                  protocol: a.protocol.replace(':', ''),
                  host: a.hostname,
                  port: a.port,
                  query: a.search,
                  params: (function() {
                      var ret = {},
                          seg = a.search.replace(/^\?/, '').split('&'),
                          len = seg.length,
                          i = 0,
                          s
                      for (; i < len; i++) {
                          if (!seg[i]) {
                              continue;
                          }
                          s = seg[i].split('=')
                          ret[s[0]] = s[1]
                      }
                      return ret
                  })(),
                  file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
                  hash: a.hash.replace('#', ''),
                  path: a.pathname.replace(/^([^\/])/, '/$1'),
                  relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
                  segments: a.pathname.replace(/^\//, '').split('/')
              }
          }
          if (role_num == '1' || role_num == '2' || role_num == undefined) {
              $(".funcMask").show();
          }
      }
    }
  /**
   *  开始游戏
   *  2种途径可开启游戏，1.老师通过控制器点击开始 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击开始
  window.SDK.actAuthorize = function(message) {
      if (isSync) {
        // 添加手型样式
        $(".img-list").addClass("img-list-cursor");
          if (userType == 'tea' && SDK.getClassConf().h5Course.classStatus == 5) {
              //老师显示下方提示条
              $(".doneTip").removeClass('hide');
          }

          if (message && message.operate == 5) {
              isFirst = false;
          }
          if (message && message.type == 'practiceStart') {
              //第一次授权才显示倒计时
              if (isFirst) {
                  isFirst = false;
                  $(".funcMask").show();
                  threeTwoOne(); //321倒计时
                  // gameProcessBidSync(); //上报游戏数据
              }
          }
      }
  }
  //2.非课中模式，如预习，学生点击开启
  let startBtnStatus = true; //开始按钮是否可点击(预习模式)
  $(".startBtn").on("click touchstart", function(e) {
     $(".img-list").addClass("img-list-cursor");
      if (e.type == "touchstart") {
          e.preventDefault()
      }
      e.stopPropagation();
      if (startBtnStatus) {
          startBtnStatus = false;
          threeTwoOne(); //321倒计时
      }
  });

  /**
   *  demo模式
   *  2种途径可开启demo模式，1.老师通过控制器点击提示 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击提示
  window.SDK.actDemo = function(message) {
      demoFun(false); //demo模式
      //老师上传一份空白cldata,来覆盖以前的数据。
      if (isSync) {
          SDK.bindSyncEvt({
              index: 'clear',
              eventType: 'click',
              method: 'event',
              syncName: 'clear', //SDK.js中对其特殊处理
              recoveryMode: '1'
          });
      }
      SDK.setEventLock();
  }
  //2.非课中模式，如预习，学生点击开启
  $(".demo-btnStu").on("click touchstart", function(e) {
      if (e.type == "touchstart") {
          e.preventDefault()
      }
      e.stopPropagation();
      demoFun(true); //demo模式
  });


  // 3 2 1倒计时
  function threeTwoOne() {

    $('.startBox').hide()
    SDK.setEventLock();
    $('.funcMask').hide();
    $('.timeChangeBox').hide();
    timeStartFn(); //计时器倒计时

      // let q = 1;
      // $('.startBox').hide().siblings('.timeChangeBox').show().find('.numberList');
      // SDK.playRudio({
      //     index: $('.timeLowAudio_' + q).get(0),
      //     syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
      // })
      // let audioPlay = setInterval(function() {
      //     q++;
      //     if (q > 4) {
      //         clearInterval(audioPlay);
      //         //3210倒计时结束
      //         SDK.setEventLock();
      //         $('.funcMask').hide();
      //         $('.timeChangeBox').hide();
      //         // optionsInit(timeIndex); //本轮游戏选项初始化
      //         // addEvent(); //泡泡选项添加事件
      //         // sorceTimeInit(); //进度条初始化
      //         timeStartFn(); //计时器倒计时
      //         // gameProcessBidSync(); //上报游戏数据
      //     } else {
      //         // 播放倒计时声音
      //         SDK.playRudio({
      //             index: $('.timeLowAudio_' + q).get(0),
      //             syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
      //         })
      //         $('.numberList').css({
      //             'background-position-x': -(1.5 * (q - 1)) + 'rem'
      //         })
      //     }
      // }, 1000)
  }

  //计时器倒计时
  function timeStartFn() {
    window.localStorage.setItem('countDownTime', time); //将本轮倒计时存储在缓存中
    if (timeInt) {
        clearInterval(timeInt);
        timeInt = null;
    }
    timeInt = setInterval(function() {
        if (isAnyOneOut) { //如断线则停止游戏
            clearInterval(timeInt);
            soundControl().pause(); //停止声音
        }
        time--; //本轮游戏时间
        // isEndGameFun(time); //本轮游戏是否结束
        window.localStorage.setItem('countDownTime', time); //每秒更新一次时间
        // moveItemW = moveItemW - everLen; //每秒更新一次进度条长度    新进度条长度 = 原进度条长度 - 每秒移动距离
        // $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);
    }, 1000);

    // 重置数据
    resetFn()
  };

  /**
   * demo hint 模式
   * showfuncMask 是否是学生
   */
  // demoFun()
  function demoFun(showfuncMask) {
     // 重置数据
     resetFn()
     isDemo = true;
    // 添加模版缩放模式
    templateScale({
      show: true,
      className: $(".container")
    })
    if (showfuncMask) { //是否显示学生用的预习模式弹窗
      $(".funcMask").hide();
    }
    // 是否为hint模式
    isHint = true;
    // 点击第一个模块的位置
    hintMoveFn()
     // 执行队列触发事件
     $(".img-list li").eq(0).delay(1000).queue(function() {
        half = 1.45
         // 第一个元素触发事件
        $(".img-list li").eq(0).trigger("syncItemClick");
        $(this).dequeue();
     }).delay(2000).queue(function() {
        half = 1.45
       //点击能跟第一个消除的位置
       hintMoveFn()
       setTimeout(function() {
         //第二次触发事件
          $(".img-list li").eq(hintIndex).trigger("syncItemClick");
           // hint 隐藏小手
           handHide(true)
       },1500)
       $(this).dequeue();
     })

  }

  /**
    * 退出demo模式,恢复正常模式
    * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
  */
  function demoOut(showfuncMask, $this, isFirstList) {
    // 消除后1.5s后恢复初始化状态（hint模式消失）
    setTimeout(function(){
      // hint 隐藏
      half = 2;
      templateScale({
        className: $(".container"),
        show: false,
      })

      // 重置数据
      resetFn()
      // 恢复hint状态
      isHint = false;
      hintIndex = 0,
      // 恢复隐藏的列表
      $this.show();
      isFirstList.show();
      // 重置相关状态
      $this.parent().removeClass("successful-list")
      isFirstList.parent().removeClass("successful-list")

      isFirstList.css({
        'transform': 'translate(0)',
        'transition': 'transform 1ms ease 1ms'
      })
      $this.css({
        'transform': 'translate(0)',
        'transition': 'transform 1ms ease 1ms'
      })
      //告知控制器取消demo模式
      if (userType == 'tea') {
        SDK.bindSyncCtrl({
            'type': 'tplDemoOut',
            'data': {
                CID: SDK.getClassConf().course.id + '', //教室id 字符串
                operate: '1',
                data: []
            }
        });
      }
      if(showfuncMask || (classStatus == 0 && userType == "stu") || !userType) {//是否显示学生用的预习模式弹窗
        $(".funcMask").show();
      }
      //退出demo模式
      isDemo = false;
    },1500)
  }

    /**
   * hint移动位置
   */
  function hintMoveFn() {
    let postionTop = $(".img-list ul li").eq(hintIndex).offset().top,
        postionLeft = $(".img-list ul li").eq(hintIndex).offset().left;
    handClick(postionTop-25, postionLeft-15);
  }

  /**
   *重置demo模式选中的数据
   */
  function resetFn(){
    firstDataIndex = null; // 存储当前第一个选中的组内序号
    firstKey = null; // 存储当前第一个选中的key值
    isSyncKey = null, //断线重连第一次点击时的key值
    isSyncFirstKey = [];  //断线重连消除后的第一个key值集合
    isSyncLastKey = []; //断线重连消除后的第二个key值集合
    completeArray = [];
    $(".img-list ul").find("li").removeClass("clickable")
  }


  /**
   * 初始化列表渲染
   */
  optionsListTemplate()
  function optionsListTemplate() {
    let str = '',
        templateDataOne = [], //位置一的数据
        templateDataTwo = [],//位置二的数据
        templateData = []; //合并后的数据
    // 重组数组
    for(let i = 0; i < options.length; i++) {
      let data = options[i];
      if(data.imgType == 1) {
        data.imgNew = data.img
      }
      // 选项图1
      templateDataOne.push({
        img: data.img, //图片
        position: data.positionOne, //选项1的位置
        dataIndex: i, //选项图1和选项图2是否属于同一类并能消除
        cover: data.cover, //是否是默认封面
        index: 1, //标示是否是选项图1
        title: data.title, //文本内容
        audio: data.audio, //音频
        audioTime: data.audioTime, //音频时长
      })
      // 选项图2
      templateDataTwo.push({
        img: data.imgNew,
        position: data.positionTwo, //选项2的位置
        dataIndex: i,//选项图1和选项图2是否属于同一类并能消除
        cover: data.cover,
        index: 2, //标示是否是选项图2
        title: data.title,
        audio: data.audio,
        audioTime: data.audioTime, //音频时长
      })
    }
    // 合并数组
    templateData = templateDataOne.concat(templateDataTwo)


    // 对序号进行排序（默认根据[1,3,5,7,2,4,6,8],如果排序编辑器改变此逻辑也需变更）
    templateData.sort(function (a,b) {
        // 如果是a是偶数,b是奇数
        if(a.position%2 === 0 && b.position%2 !== 0){
            return 1;
        }
        // 如果是a是奇数,b是偶数
        if(a.position%2 !== 0 && b.position%2 === 0){
            return -1;
        }
        // 如果是a,b都是偶数或者奇数
        if((a.position%2 === 0 && b.position%2 === 0) || (a.position%2 !== 0 && b.position%2 !== 0)){
            return a.position - b.position;
        }
     })
    for(let i = 0; i < templateData.length; i++) {
      let data = templateData[i];
      str += `<li data-cover="${data.cover}"
                  data-position="${data.position}"
                  data-cover-index="${data.index}"
                  data-index="${data.dataIndex}"
                  data-syncactions="onLine-${i}"
                  data-title="${data.title}"
                  data-img="${data.img}"
                  data-audio="${data.audio}"
                  data-audio-time="${data.audioTime}"
                  data-key="${i}">
                  <div class="s-list">
                    <img src="./image/img_bj.png" class="img-bj">
                    <div class="success-img">
                      <img src="${data.img}" class="dataimg">
                      <span class="data-title">${data.title}</span>
                    </div>
                  <div>
            </li>`
    }
    // 根据组数个数动态改变容器宽度
    $(".img-list ul").css({
      width: (400*options.length)/100 + 'rem'
    })
    $(".img-list ul").html(str);
  }

  /**
   * 选择泡泡事件
   */
  $(".img-list li").on("click touchstart",  function(e) {
    //demo模式不能点击
    if (isDemo) {
      return;
    }
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation(); //阻止事件进一步传播
     //禁止老师点击
     if (isSync) {
        classStatus = SDK.getClassConf().h5Course.classStatus;
        if (classStatus != 0 && userType == 'tea') {
            SDK.setEventLock();
            return false;
        }
      }
    // 本地模式
    if (!isSync) {
      $(this).trigger("syncItemClick");
      return;
    }

    /**
     * 执行逻辑
     */
    syncItemEventFn($(this))


    if (window.frameElement.getAttribute('user_type') == 'stu') {
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data("syncactions"),
        eventType: 'click',
        method: 'event',
        syncName: 'syncItemClick',
        otherInfor: {
          isSyncKey: isSyncKey, //是否没消除且只选中了一个
          isSyncFirstKey: isSyncFirstKey, //第一次选中的key值集合
          isSyncLastKey: isSyncLastKey, //第二次选中的key值集合
          completeArray: completeArray, //所有消除后的数据集合
          firstDataIndex: firstDataIndex, //存储当前第一个选中的组内序号
        },
        recoveryMode: '1'
      });
    }


  });

  $(".img-list li").on("syncItemClick",  function(e, message) {
    // 断线重连
    recoverList(message,$(this))
    SDK.setEventLock();
  });

  /**
   * 列表执行逻辑
   */
  function syncItemEventFn($this) {
    ismatelSound = false;
    let curveTwo = [];
    // 消除后的不可点击
    if($this.hasClass("clickable")) {
      return
    }
    if(isCheck) {
      // 当前是否是选中状态
      if($this.hasClass("on")) {
        return;
      }
      // 是否开始点击链接
      if(firstDataIndex) {
        ismatelSound = true;
        // 是否为同一组内的元素
        if($this.attr("data-index") == firstDataIndex) { //正确消除
          console.log('选择消除正确')
          // 当前二次选中的key值
          twoKey = $this.attr("data-key")
          isSyncKey = null;
          SDK.setEventLock();
          isCheck = false;
          // 获取当前坐标
          curveTwo.push($this.offset().top);
          curveTwo.push($this.offset().left);
          $this.addClass("on");
          // 点击时的音频
          rightWrongAudio(false).dragAudio();
          // 消除后的数据存储
          completeDataFn($this)
          // 消除事件
          moveFn(curvOne, curveTwo, $this);
          SDK.setEventLock();
        } else { //消除错误
          console.log('选择错误')
          isCheck = false;
          isSyncKey = null;
          SDK.setEventLock();
          // 第二次选择清除所有的选中状态
          $(".img-list li").removeClass("on");
          // 选中错误时的标识
          $this.addClass("error shake");
          $(".img-list li").eq(firstKey).addClass("error shake");
          // 消除时错误音效
          rightWrongAudio().wrong();
          // 执行队列清除动画
          setTimeout(function(){
            $(".img-list li").removeClass("error shake");
            isCheck = true;
          },500)
        }
        // 重置第一次选中的内容
        firstDataIndex = null;
        curvOne = [];
        SDK.setEventLock();
      } else {
        ismatelSound = true;
        console.log('第一次点击')
        $this.addClass("on");
        // 获取当前的key值
        isSyncKey = $this.attr("data-key");
        SDK.setEventLock();
        // 点击时的音频
        $(".soundAudio").attr("src", $this.attr("data-audio"))
        rightWrongAudio(true).dragAudio();
        //隐藏小手
        setTimeout(function() {
          handHide(true);
        }, 3600)  //注意调用handClick后 不低于
        // 获取hint模式下的匹配泡泡的key值
        for(let i = 0; i < $(".img-list li").length; i++) {
          let data = $(".img-list li").eq(i)
          if(data.attr("data-index") == $this.attr("data-index")) {
            // hint模式时获取跟第一个匹配的泡泡的key值
            hintIndex = data.attr("data-key")
          }
        }

        // 第一次点击泡泡的事件
        firstClickFn($this)
      }
    }

  }

  /**
   *
   * @param {*}断线重连--恢复列表数据
   */
  function recoverList(message, $this){
    // 断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor,
          isSyncKey = obj.isSyncKey, //是否没消除且只选中了一个
          firstKey = obj.isSyncFirstKey, //第一次选中的key值集合
          lastKey = obj.isSyncLastKey, //第二次选中的key值集合
          imgListLi = $(".img-list li");
      if(isSyncKey) { //只选中一个
        imgListLi.eq(isSyncKey).addClass("on");
        firstClickFn(imgListLi.eq(isSyncKey));
      }

      //已经消除一对泡泡，此次需隐藏对应的泡泡
      for(let i = 0; i < lastKey.length; i++) {
        imgListLi.eq(Number(lastKey[i].key)).find(".s-list").hide();
      }
      for(let i = 0; i < firstKey.length; i++) {
        imgListLi.eq(Number(firstKey[i].key)).find(".s-list").hide();
      }

      //所有消除后的数据集合
      completeArray = obj.completeArray;
      // 存储当前第一个选中的组内序号
      firstDataIndex = obj.firstDataIndex;
      // 赋值第一次和第二次的key集合
      isSyncFirstKey = firstKey;
      isSyncLastKey = lastKey;
      // 是否消除完毕
      if(completeArray.length == options.length) {
        console.log("已经消除完毕")
        // 消除完毕后的展示
        removeCompleteFn(false);
      }

      SDK.setEventLock();
      return;
    }
    syncItemEventFn($this)
  }

  /**
   * 第一次点击后的事件
  */
  function firstClickFn($this) {
    // 获取第一次点击的序号和key值
    firstDataIndex = $this.attr("data-index");
    firstKey = Number($this.attr("data-key"));
    // 获取当前坐标
    curvOne.push($this.offset().top);
    curvOne.push($this.offset().left);

    SDK.setEventLock();
  }

  /**
   *
   * @param {*} 消消乐消除开始状态的数据存储
   */

  function completeDataFn($this){
    let completeImg = '', //消除后的图片
        completeTitle = '', //文本内容
        completeAudio = '', //视频
        cover = $this.attr("data-cover"),//1:封面1  2: 封面2
        coverIndex = $this.attr("data-cover-index"), //1:选项图1  2:选项图2
        isThis = ''; //消除后的内容
        // if(cover == 2 && coverIndex == 2) {//选中的是否是封面
        if(cover == 2 &&  coverIndex == 2) {//选中的是否是封面
          isThis = $this;
        } else if(cover == 1 && coverIndex == 1) {
          isThis = $(".img-list li").eq(twoKey);
        } else {
          isThis = $(".img-list li").eq(firstKey);
        }

        // 消除泡泡后获取对应的数据
        completeImg = isThis.attr("data-img");
        completeTitle = isThis.attr("data-title");
        completeAudio = isThis.attr("data-audio");

        // 断线重连消除后纪录的key值
        isSyncLastKey.push({
          key: $this.attr("data-key")
        })
        isSyncFirstKey.push({
          key: firstKey
        })

        //存储消除后的数据
        completeArray.push({
          img: completeImg,
          title: completeTitle,
          audio: completeAudio
        });
  }


  /**
   * 连连碰相关功能
   */
  function moveFn(_curve,curve, $index) {
    var curveX = null,//链接中间点的x
        curveY = null,//链接中间点的y
        $this = $index.find(".s-list"),
        isFirstList = $(".img-list li").eq(firstKey).find(".s-list"),
        cover = $index.attr("data-cover"),//1:封面1  2: 封面2
        coverIndex = $index.attr("data-cover-index"), //1:选项图1  2:选项图2
        isThis = ''; //消除后的内容

    // 判断两元素坐标的上下关系
    if(curve[0] > _curve[0]) {
      curveX = curve[0] - _curve[0]
    } else {
      curveX = _curve[0] - curve[0]
    }

    if(curve[1] > _curve[1]) {
      curveY = curve[1] - _curve[1]
    } else {
      curveY = _curve[1] - curve[1]
    }


    // 初始位置是否在高点
    setTimeout(function(){
      if(_curve[0] >= curve[0] && _curve[1] <= curve[1]) {
        // 第一象限
        isFirstList.css({
          'transform': 'translate(' + curveY/half/window.base+ 'rem,' + -curveX/half/window.base + 'rem)',
        })
        $this.css({
          'transform': 'translate(' + -curveY/half/window.base+ 'rem,' + curveX/half/window.base + 'rem)',
        })
      } else if(_curve[0] <= curve[0] && _curve[1] >= curve[1]){
        // 第三象限
        isFirstList.css({
          'transform': 'translate(' + -curveY/half/window.base+ 'rem,' + curveX/half/window.base + 'rem)',
        })
        $this.css({
          'transform': 'translate(' + curveY/half/window.base+ 'rem,' + -curveX/half/window.base + 'rem)',
        })

      } else if(_curve[0] >= curve[0] && _curve[1] >= curve[1]) {
        // 第二象限
        isFirstList.css({
          'transform': 'translate(' + -curveY/half/window.base+ 'rem,' + -curveX/half/window.base + 'rem)',
        })
        $this.css({
          'transform': 'translate(' + curveY/half/window.base+ 'rem,' + curveX/half/window.base + 'rem)',
        })
      } else {
        // 第四个象限
        isFirstList.css({
          'transform': 'translate(' + curveY/half/window.base+ 'rem,' + curveX/half/window.base + 'rem)',
        })
        $this.css({
          'transform': 'translate(' + -curveY/half/window.base+ 'rem,' + -curveX/half/window.base + 'rem)',
        })
      }
      isFirstList.css({
        'transition': 'transform 500ms ease 100ms'
      })
      $this.css({
        'transition': 'transform 500ms ease 100ms',
        'zIndex': 10,
      })
    },500)

    // if(cover == 2 && coverIndex == 2) {//选中的是否是封面
    if(cover == 2 &&  coverIndex == 2) {//选中的是否是封面
      isThis = $index;
    } else if(cover == 1 && coverIndex == 1) {
       isThis = $(".img-list li").eq(twoKey);
    } else {
      isThis = $(".img-list li").eq(firstKey);
    }
    // 执行队列清除动画
    $this.delay(400).queue(function() { //刚消除时的事件处理
      console.log('消消乐消除开始状态')
      //刚消除时的音效
      $(".soundAudio").attr("src", isThis.attr("data-audio"))
      rightWrongAudio().right();
      $(this).dequeue();
    }).delay(600).queue(function(){ //消除泡泡后添加状态
      console.log("消消乐消除正要碰撞时")
      isThis.addClass("successful-list");
       // 第二次选择清除所有的选中状态
       $(".img-list li").removeClass("on");
      $(this).dequeue();
    }).delay(1500 + Number(isThis.attr("data-audio-time"))).queue(function() { //消除后的事件处理
      console.log("消消乐消除后的状态")
      // $this.parent().addClass("successful-list");
      // 消除后消失
      $this.hide();
      isFirstList.hide();
      $this.parent().addClass("clickable");
      isFirstList.parent().addClass("clickable");

      // hint模式第二次消除后相关逻辑
      if(isHint) {
        demoOut(false, $this, isFirstList)
      }

      isCheck = true;
      // 是否消除完毕
      if(completeArray.length == options.length) {
        console.log("已经消除完毕")
        // 消除完毕后的展示
        removeCompleteFn(true);
      }
      $(this).dequeue();
    })

    SDK.setEventLock();

  }

  /**
   * 正确与错误逻辑（音频播放）
   */
  function rightWrongAudio(type) {
    // soundControl().pause(); //停止
    return {
      // 正确答案
      "right": function() {
        SDK.playRudio({
          index: $(".rightAudio")[0],
          syncName: $(".rightAudio").attr("data-syncaudio")
        })
        $(".rightAudio")[0].onended = function() {
            // soundControl().play();
            SDK.playRudio({
              index: $(".soundAudio")[0],
              syncName: $(".soundAudio").attr("data-syncaudio")
            })
        }
      },
      // 错误答案
      "wrong": function() {
        SDK.playRudio({
          index: $(".wrongAudio")[0],
          syncName: $(".wrongAudio").attr("data-syncaudio")
        })
        $(".wrongAudio")[0].onended = function() {
            // soundControl().play();
        }
      },
      //选中时
      "dragAudio": function() {
        SDK.playRudio({
          index: $(".dragAudio")[0],
          syncName: $(".dragAudio").attr("data-syncaudio")
        })
        $(".dragAudio")[0].onended = function() {
            if(type) {
              setTimeout(function(){
                if(isPlaySound) {
                  SDK.playRudio({
                    index: $(".soundAudio")[0],
                    syncName: $(".soundAudio").attr("data-syncaudio")
                  })
                } else {
                  SDK.pauseRudio({
                    index: $(".soundAudio")[0],
                    syncName: $(".soundAudio").attr("data-syncaudio")
                  })
                }
              },200)
            }
        }
      }
    }
  }

  /**
   * 消除完毕后的展示
   * isShowWin:是否显示特效
   */
  function removeCompleteFn(isShowWin) {
    // isShow 断线重连后不显示特效
    if(isShowWin){
      // 终局特效
      resultWin({
        'WinIsLoop': false, // 答对声音是否重复播放 true/false
        'Mask_Z_Index': "500" // 遮罩层z_index
      });
      var prefect = $("<div class='perfectBox'><img src='./image/light.png' class='light'><img src='./image/prefect.png' class='perfect'></div>");
      $(".resultWin").append(prefect);
    } else {
      time = 0;
    }

    //如果是老师 告知控制器，将授权状态改为老师控制，classstatus为6
    if (userType == 'tea' && (SDK.getClassConf().h5Course.classStatus == '5' || SDK.getClassConf().h5Course.classStatus == '1')) {
      SDK.bindSyncCtrl({
          'type': 'gameOverToCtrl',
          'data': {
              CID: SDK.getClassConf().course.id + '', //教室id 字符串
              operate: '1',
              data: [{
                  key: 'classStatus',
                  value: '6',
                  ownerUID: SDK.getClassConf().user.id
              }]
          }
      });
    }

    //选中后的终局列表展示
    setTimeout(function() {
      resultHide();
      let resultStr = "",
          resultLi = $(".result-success ul li");
      completeArray.forEach(function(value,index,array){
        resultStr = $(`<div class="list"><img src="${value.img}" class="img">
                        <p class="complete-title">${value.title}</p>
                        <div class="example-audio sound" data-syncactions="audio-1">
                          <img src="./image/sound.gif" alt="" class="gif small">
                          <img src="./image/sound.png" alt="" class="png small">
                          <audio src="${value.audio}" data-syncaudio="audioExample"></audio>
                        </div></div>`);
        if(array[index] == value) {
          resultLi.eq(index).append(resultStr)
        }
      });

      // 根据组数个数动态改变容器宽度
      let resultWidth = 470;
      if(options.length <= 2) {
        resultWidth = 520
      }
      $(".result-success ul").css({
        width: (resultWidth*options.length)/100 + 'rem'
      });
      $(".result-success").show();

    },time)
  }

  /**
   * 结局点播事件
   */
  let isPlaySound = true;
  $(".result-success ul").on("click touchstart", "li", function(e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("synResultClick");
      return;
    }
    // if (window.frameElement.getAttribute('user_type') == 'tea') {
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data("syncactions"),
        eventType: 'click',
        method: 'event',
        syncName: 'synResultClick',
        otherInfor: {
          index: $(this).index(),
          completeArray: completeArray
        },
        recoveryMode: '1'
      });
    // }
  });


  $(".result-success ul").on("synResultClick", "li", function(e, message) {
    // 断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor;
      let index = obj.index;
      completeArray = obj.completeArray
      // 消除完毕后的展示
      removeCompleteFn(false);
      $(".result-success ul li").eq(index).addClass("on");

      SDK.setEventLock();
      return;
    }
    let gif = $(this).find('.gif'),
        png = $(this).find('.png'),
        audio = $(this).find('audio')[0];
    // 开始播放
    if(isPlaySound) {
      SDK.playRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.show();
      png.hide();
    } else { //停止播放
      SDK.pauseRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.hide();
      png.show();
    }
    // 播放结束
    audio.onended = function () {
      gif.hide();
      png.show();
      isPlaySound = true;
    }.bind(this);
    // 选中元素添加状态
    $(this).addClass("on").siblings().removeClass("on");
    SDK.setEventLock();
  });
})
