// var domain = 'http://172.16.0.107:9011/pages/1152/';
var domain = '';
var updata = [1, 2, 3, 4, 5, 6, 7, 8];
var Data = {
    configData: {
        bg: '',
        lang:1,
        leftImg: '',
        rightImg: '',
        desc: '',
        title: '',
        sizeArr: ['', '', '', '1350*600'], //图片尺寸限制
        tg: [{
            title: '',
            content: ''
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
            imgWidth: 344, //上传图片的宽度
            imgHeight: 344, //上传图片的高度
            positionNum: 1, //位置的总序号
            options: [{
                    title: "", //答案文本内容
                    img: "", //选项图
                    imgNew: "", //新的选项图
                    positionOne: 1, //选项图1的位置
                    positionTwo: 2, //选项图2的位置
                    imgType: 1, //1：和图片1相同  2：新的图片
                    cover: 1, //1:封面默认为1  2: 封面2
                    audio: "", //音频,
                    audioTime: "", //音频长度
                },
                {
                    title: "", //答案文本内容
                    img: "", //选项图
                    imgNew: "", //新的选项图
                    positionOne: 3, //选项图1的位置
                    positionTwo: 4, //选项图2的位置
                    imgType: 2, //1：和图片1相同  2：新的图片
                    cover: 1, //1:封面默认为1  2: 封面2
                    audio: "", //音频，
                    audioTime: "", //音频长度
                }
            ]
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
};
$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    watch: {
        'configData.source.options[0].imgType': function() {
            console.log(1111)
            var type = this.configData.source.imgType;
            switch (type) {
                case '1':
                    console.log(111)
                    break
                case '2':
                    console.log(222)
                    break

            }
        }
    },
    methods: {

        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;


            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        sourceImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                //console.error("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source
            var check = true

            if (!this.configData.bg) {
                alert("请上传背景图片")
                return
            }

            for (let x = 0; x < data.options.length; x++) {
                let i = data.options[x],
                    index = x + 1;

                if (!i.title) {
                    alert("请填写第" + index + "组答案文本")
                    return
                }
                if (!i.audio) {
                    alert("请上传第" + index + "组动画音效")
                    return
                }
                if (!i.audioTime) {
                    alert("请填写第" + index + "组的题干音频长度")
                    return
                }
                if (i.audioTime > 6000) {
                    alert("请填写第" + index + "组正确的题干音频长度")
                    return
                }
                if (!i.img) {
                    alert("请上传第" + index + "组选项图片1")
                    return
                }
                if (i.imgType == 2 && !i.imgNew) {
                    alert("请上传第" + index + "组选项图片2")
                    return
                }
                if (!i.positionOne) {
                    alert("请填写第" + index + "组选项图1的位置")
                    return
                }
                if (!i.positionTwo) {
                    alert("请填写第" + index + "组选项图2的位置")
                    return
                }
                if (i.positionOne > data.options.length * 2 || i.positionTwo > data.options.length * 2) {
                    alert("请填写第" + index + "组正确的位置序号")
                    return
                }

                if (i.positionOne == i.positionTwo) {
                    alert("序号不能重复")
                    return
                }
                if ((x + 1) < data.options.length) {
                    if (i.positionOne == data.options[x + 1].positionOne || i.positionTwo == data.options[x + 1].positionTwo || i.positionOne == data.options[x + 1].positionTwo || i.positionTwo == data.options[x + 1].positionOne) {
                        alert("序号不能重复")
                        return
                    }
                }
            }

            return check
        },
        onSend: function() {
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            var val = this.validate();
            if (val === true) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                // alert("带“*”为必填项");
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            bg = arguments.length > 2 ? arguments[2] : null;
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    console.log(res.data.key);
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },
        audioUpload: function(e, item, attr, fileSize) {
            //校验规则
            //var _type = this.rules.audio.sources.type;

            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            // if (!_type.test(type)) {
            //     alert("您上传的文件类型错误，请检查后再上传！");
            //     return;
            // }
            if ((size / 1024).toFixed(2) > 500) {
                console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            } else {
                console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            }
            if ((size / 1024).toFixed(2) > fileSize) {
                console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
                alert("您上传的声音大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            this.postData(file, item, attr);
        },
        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        addBox: function() {
            this.configData.source.colorList.push({
                name: 1,
            })
            // this.configData.source.updata.push(this.configData.source.updata[this.configData.source.updata.length-1]+1)
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addOption: function(item) {
            if (item) {
                this.configData.source.options.push(item)
            } else {
                this.configData.source.options.push('')
            }
        },
        // setAnswer: function(item) {
        //   this.configData.source.right = item;
        // },
        addOption: function(item) {
            if (item) {
                this.configData.source.options.push(item)
            } else {
                this.configData.source.options.push('')
            }
        },
        delOption: function(item) {
            this.configData.source.options.remove(item)
            // this.configData.source.right = "-1"
        },

    },
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
