<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TCH0001_两项选图"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">

    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>

    <section class="content-main">
        <div class="content">
            <div class="left-item item" data-syncactions='sele-1'>
                <img class="pic" src="" alt="">
                <div class="right-answer-left shade">
                    <img src="./image/right.png" alt="">
                </div>
                <div class="item-option">
                    <img src="./image/a.png" alt="">
                </div>
            </div>
            <div class="right-item item" data-syncactions='sele-2'>
                <img class="pic" src="" alt="">
                <div class="right-answer-right shade">
                    <img src="./image/right.png" alt="">
                </div>
                <div class="item-option">
                    <img src="./image/b.png" alt="">
                </div>
            </div>
            <div class="example">
                <img src="" alt="" class="example-pic">
                <p class="example-text"></p>
                <div class="example-audio sound" data-syncactions="audio-1">
                    <img src="./image/sound1.gif" alt="" class="gif small">
                    <img src="./image/sound1.png" alt="" class="png small">
                    <audio src="" data-syncaudio="audioExample"></audio>
                </div>
            </div>
        </div>
        <audio class="wrong" src="./audio/wrong.mp3" data-syncaudio="audiowrong"></audio>
        <audio class="right" src="./audio/right.mp3" data-syncaudio="audioright"></audio>
    </section>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
