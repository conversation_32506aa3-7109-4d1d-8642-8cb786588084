"use strict"
import '../../common/js/common_1v1.js'
import '../../common/js/commonFunctions.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: '1' // 是否有授权按钮 1：是 0：否
  }

  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/defaultBg.jpg)'
    })
  }

  let items = configData.source.items,
    rightItem = configData.source.rightItem, // 正确选项
    exampleImg = configData.source.example,
    text = configData.source.text,
    audio = configData.source.audio;

  // DOM 初始化
  ~ function () {
    // 渲染选项
    $('.content .pic').each((i, n) => {
      $(n).attr('src', items[i].img);
    })
    $('.example .example-pic').attr('src', exampleImg);
    $('.example-text').html(text);
    $('.example audio').attr('src', audio);

    if (exampleImg) {
      $('.example').css('background', '#fff');
    }
    if (!audio) {
      $('.example-audio').hide();
    }
    if (text) {
      $('.example').addClass('text');
      $('.example .example-pic').hide();
      $('.example .example-text').css('display', 'flex');
      $('.example').css('background', '');

      if (text.length <= 15) {
        $('.example .example-text').css('justify-content', 'center');
      }

      // if( text.length <= 6 ) {
      // 	$('.example-text').css('fontSize', '.72rem');
      // }else if( 6 < text.length && text.length <= 10 ) {
      // 	$('.example-text').css('fontSize', '.52rem');
      // }else if( 10 < text.length ) {
      // 	$('.example-text').css('fontSize', '.36rem');
      // }
    } else {
      $('.example .example-text').hide();
    }
    if (audio && !exampleImg && !text) {
      $('.example').addClass('hide');
      $('.example-pic').hide();
      $('.example-text').hide();
      $('.example-audio img').removeClass('small');
      $('.example-audio').css({
        width: '2.1rem',
        height: '1.98rem',
        position: 'absolute',
        top: '0',
        left: '.55rem'
      });
    }
  }()

  let soundClick = true,
    isPlaySound = true;
  $('.sound').on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();

    if (soundClick) {
      soundClick = false;
      if (!isSync) {
        $(this).trigger('syncSoundClick');
        return;
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'syncSoundClick',
          funcType: 'audio'
        });
      } else {
        $(this).trigger('syncSoundClick');
        return;
      }
    }
  })

  $('.sound').on('syncSoundClick', function (e, message) {

    let gif = $(this).find('.gif');
    let png = $(this).find('.png');
    let audio = $(this).find('audio')[0];

    if (isPlaySound) {
      // audio.play();
      SDK.playRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.show();
      png.hide();
    } else {
      // audio.pause();
      SDK.pauseRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.hide();
      png.show();
    }
    audio.onended = function () {
      gif.hide();
      png.show();
      isPlaySound = true;
    }.bind(this);

    isPlaySound = !isPlaySound;

    SDK.setEventLock();
    soundClick = true;

  });

  $(".left-item").hover(function () {
    // 答对后就没有移动效果了
    if ($(this).hasClass('isRight')) {
      return;
    }
    $(this).find(".pic").css({
      "border": "solid 0.1rem #FFB900"
    });
    $(this).find(".item-option").addClass("big");
  }, function () {
    // 答对后就没有移动效果了
    if ($(this).hasClass('isRight')) {
      return;
    }
    $(this).find(".pic").css({
      "border": "none"
    });
    $(this).find(".item-option").removeClass("big");
  });

  $(".right-item").hover(function () {
    // 答对后就没有移动效果了
    if ($(this).hasClass('isRight')) {
      return;
    }
    $(this).find(".pic").css("border", "solid 0.1rem #2CD4F1");
    $(this).find(".item-option").addClass("big");
  }, function () {
    // 答对后就没有移动效果了
    if ($(this).hasClass('isRight')) {
      return;
    }
    $(this).find(".pic").css("border", "none");
    $(this).find(".item-option").removeClass("big");
  });

  let itemClick = true;
  $('.item').on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();

    if (itemClick) {
      itemClick = false;

      if (!isSync) {
        $(this).trigger('syncItemClick');
        return
      }
      if (window.frameElement.getAttribute('user_type') == 'stu') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'syncItemClick',
          otherInfor: {
            answer: $(this).index() === rightItem
          },
          recoveryMode: '1'
        });
      }
    }
  })

  $('.item').on('syncItemClick', function (e, message) {
    // 答对后不可再选择
    if ($(this).hasClass('isRight')) {
      SDK.setEventLock();
      itemClick = false;
      return;
    }

    //断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor;
      let answer = obj.answer;
      if (answer) {
        $('.item').addClass('isRight');
        let mask = $('.shade').eq($(this).index());
        mask.addClass('flex');
      }
      SDK.setEventLock();
      return;
    }

    if ($(this).index() === rightItem) {
      console.log('c-page:----------------%s: 答对了！');
      if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
        // $('.right')[0].play();
        SDK.playRudio({
          index: $('.right')[0],
          syncName: $('.right').attr("data-syncaudio")
        })
      }
      $(this).find(".pic").css({
        "border": "none"
      });
      $(this).find(".item-option").removeClass("big");

      $('.item').addClass('isRight');
      let mask = $('.shade').eq($(this).index());
      mask.addClass('flex');
      SDK.setEventLock();
      starFun();
      itemClick = false;
    } else {
      console.log('c-page:----------------%s: 答错了！');
      if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
        // $('.wrong')[0].play();
        SDK.playRudio({
          index: $('.wrong')[0],
          syncName: $('.wrong').attr("data-syncaudio")
        })
      }
      $(this).addClass('shake');
      $(this).on('animationend webkitAnimationEnd', function () {
        $(this).removeClass('shake');
        SDK.setEventLock();
        itemClick = true;
      })
    }

  });
  //答题结束触发发送星星
  function starFun() {
    if (!isSync) {
      return false;
    }
    console.log('进入发星')
    var classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
    var support1v1h5star = parent.window.h5SyncActions.classConf.serverData.objCourseInfo.support1v1h5star;
    var device = parent.window.h5SyncActions.classConf.h5Course.device;
    if (window.frameElement.getAttribute('user_type') == 'stu' && classStatus == 2) {
      if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
        console.log("学生答题正确后发星星")
        SDK.bindSyncStart({
          type: "newH5StarData",
          num: 1
        });
      }

    }
  }


})
