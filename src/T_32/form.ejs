<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TCH0001_两项选图</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">TCH0001_两项选图</h3>

			<% include ./src/common/template/common_head %>

			<!-- 编辑问题 -->
			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<!-- <label>问题 （显示位置：2）字符：≤100（非必填）</label>
					<input type="text" class='c-input-txt' placeholder="请在此输入问题" v-model="configData.source.titleSecond" maxlength="100">
					<br> -->

					<div class="field-wrap">
						<label class="field-label"  for="">图片</label>
						<span class='txt-info'>（显示位置：4）<em> * </em>尺寸：320x240，大小≤50KB</span>
						<input type="file"  id="content-pic" class="btn-file"
							:disabled="configData.source.text!=''"
							v-bind:key="Date.now()" size="320*240"
							v-on:change="imageUpload($event,configData.source,'example',50)">
					</div>
					<div class="field-wrap">
						<label for="content-pic" class="btn btn-show upload"
						:class="{disabled: configData.source.text!=''}" v-if="!configData.source.example">上传</label>
						<label for="content-pic" class="btn upload re-upload" v-if="configData.source.example">重新上传</label>
					</div>
					<div class="img-preview" v-if="configData.source.example">
						<img v-bind:src="configData.source.example" alt=""/>
						<div class="img-tools">
							<span class="btn btn-delete" v-on:click="configData.source.example=''">删除</span>
						</div>
					</div>
					<br>

					<label>单词 （显示位置：4）字符：≤46（与位置4图片二选一）</label>
					<input type="text" :disabled="configData.source.example!=''" class='c-input-txt' placeholder="请在此输入单词" v-model="configData.source.text" maxlength="46">
					<br>

					<div class="field-wrap">
						<label class="field-label"  for="">声音</label>
						<span class='txt-info'>（显示位置：5） 大小：≤50KB</span>
						<input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-audio" v-on:change="audioUpload($event,configData.source,'audio')">
					</div>
					<div class="field-wrap">
						<label for="content-audio" class="btn btn-show upload" v-if="!configData.source.audio">上传</label>
						<div class="audio-preview" v-show="configData.source.audio">
							<div class="audio-tools">
								<p v-show="configData.source.audio">{{configData.source.audio}}</p>
							</div>
							<span class="play-btn" v-on:click="play($event)">
								<audio v-bind:src="configData.source.audio"></audio>
							</span>
						</div>
						<label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.audio" @click="configData.source.audio=''">删除</label>
						<label for="content-audio" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
					</div>
					<br>

					<label>选项 <em> * </em>&nbsp;&nbsp; (显示位置：3)</label>
					<div class="c-well" v-for="(item,index) in configData.source.items">
						<div class="well-title">
							<p>选项{{index+1}}&nbsp;&nbsp;&nbsp;&nbsp;({{item.pos}})</p>
							<div class="field-tools">
								<div class="field-radio-wrap radio">
									<div class="circle radio-outer " v-bind:class="{ active:configData.source.rightItem==item.isRight }" v-on:click="configData.source.rightItem=item.isRight">
										<div class="circle radio-inner pos-center" ></div>
									</div>
									<label class='field-label' for="true-radio">正确答案</label>
								</div>
							</div>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label>
								<label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label>
								<label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
								<span class='txt-info'>（尺寸：800X450，大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="800*450" volume="50" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
							<!-- <div class="field-wrap">
								<label class="field-label"  for="">上传声音</label>
								<div class="audio-preview" v-show="item.audio!=''?true:false">
									<div class="audio-tools">
										<p v-show="item.audio!=''?true:false">{{item.audio}}</p>
										<img src="" alt="" v-show="item.audio==''?true:false">
									</div>
									<span class="play-btn" v-on:click="play($event)">
										<audio v-bind:src="item.audio"></audio>
									</span>
								</div>
								<span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio=''">删除</span>
								<label v-bind:for="'audio-upload'+index" class="btn btn-show upload" v-if="item.audio==''?true:false">上传</label>
								<label  v-bind:for="'audio-upload'+index" class="btn upload re-upload mar" v-if="item.audio!=''?true:false">重新上传</label>
								<span class='txt-info'>（大小：≤50KB)</span>
								<input type="file" v-bind:id="'audio-upload'+index" class="btn-file upload" size="" volume="50" accept=".mp3" v-on:change="audioUpload($event,item,'audio')" v-bind:key="Date.now()">
							</div> -->
						</div>
					</div>

				</div>
			</div>

			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
