@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}

.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	height: 2.2rem;

	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
	.title-second {
		// margin: 0 auto;
		display: block;
		width: 100%;
		position: relative;
		bottom: 0;
		height: .4rem;
		// max-width: 11.15rem;
		font-size: .36rem;
		color: #333;
		font-weight: bold;
		text-align: center;
	}
}
.container {
	background-size: auto 100%;
}

.big{
	transform: scale(1.05);
}

.content {
	width: 16.4rem;
	height: 4.5rem;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	margin: 0 auto;
	margin-left: 1.4rem;
	position: absolute;
	bottom: .9rem;

	.left-item, .right-item {
		position: absolute;
		top: 0;
		width: 8rem;
		height: 4.5rem;
		border-radius: .64rem;
		cursor: pointer;
		// transition: .2s;
		.flex {
			display: -webkit-flex;
			display: flex;
		}
		.pic { 
			box-sizing: border-box;
			width: 100%;
			height: 100%;
			border-radius: .64rem;
			-webkit-box-shadow: 0 0 0.14rem rgba(0,0,0,0.25);
	    box-shadow: 0 0 0.14rem rgba(0,0,0,0.25);
		}
		.item-option {
			position: absolute;
			width: 1.32rem;
			height: 1.3rem;
			top: -.7rem;
			left: 3.34rem; 
			img {
				width: 100%;
				height: 100%;
			}
		}
	}

	.left-item {
		left: 0;
	}

	.right-item {
		right: 0;
	}

	.right-answer-left, .right-answer-right {
		display: none;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 0;
		width: 8rem;
		height: 4.5rem;
		border-radius: .64rem;
		cursor: pointer;
		background: rgba(0, 0, 0, .5);
		img {
			width: 1.4rem;
			height: 1.38rem;
		}
	}
	.right-answer-left {
		left: 0;
	}
	.right-answer-right {
		right: 0;
	}

	.example {
		position: absolute;
		top: -2.8rem;
		left: 6.6rem;
		width: 3.2rem;
		height: 2.4rem;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: .32rem;
		box-shadow: 0 0 0.14rem rgba(0,0,0,0.25);

		.example-pic {
			width: 3.2rem;
			height: 2.4rem;
			border-radius: .32rem;
    		box-shadow: 0 0 0.14rem rgba(0,0,0,0.25);
			// display: none;
		}
		p {
			// width: 4.40rem;
			height: 1.6rem;
			// display: none;
			display: flex;
			justify-content: center;
			align-items: center;
			background: blue;
			// background: url(../image/text-bg-2.png) no-repeat;
			// background-size: cover;
			background: #fff;
			border-radius: 1.72rem;
			font-size: .6rem;
			font-weight: bold;
			// letter-spacing: 2px;
			color: #402109;
	    	box-shadow: 0 0 0.14rem rgba(0,0,0,0.25);
			// display: none;
		}
		.example-audio {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			top: .6rem;
			right: -1.57rem;
			// display: block;
			width: 1.45rem;
			height: 1.34rem;
			background: url(../image/sound2.png) no-repeat center;
			background-size: cover;
			cursor: pointer;

			.small {
				width: 0.9rem;
				margin-top: 1px;
			}
			img {
				width: 1.45rem;
			}
			.gif {
				display: none;
			}
		}
	}
	.hide {
		box-shadow: none;
		left: 6.6rem;
	}
	.example.text {
		width: initial;
		height: 1.28rem;
		position: relative;
		top: -4.21rem;
		left: inherit;
		-webkit-box-shadow: none;
		box-shadow: none;
		.example-text {
			min-width: 2.5rem;
			height: 1.28rem;
			padding-right: 1rem;
			text-align: left;
			text-indent: .46rem;
			font-size: .55rem;
			letter-spacing: .02rem;
			justify-content: left;
			span {
				font-size: .4rem;
			}
		}
		.example-audio {
			position: absolute;
			right: -.65rem;
			top: .0rem;
		}
	}
	
	.audio-big {
		width: 2.1rem;
		height: 1.98rem;
		position: absolute;
		cursor: pointer;
		top: -2.68rem;
		display: flex;
		justify-content: center;
		align-content: center;
		.png {
			width: 2.02rem;
			height: 1.86rem;
		}
		.gif {
			position: absolute;
			display: none;
		}
	}
}
.shake{
	animation: shakeUp 0.4s both ease-in;
}
@keyframes shakeUp{
	0% {
        transform: translateX(10px);
    }
    20% {
        transform: translateX(-10px);
    }
    40% {
        transform: translateX(10px);
    }
    60% {
        transform: translateX(-10px);
    }
    80% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0px);
    }
}