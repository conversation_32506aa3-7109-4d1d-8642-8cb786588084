// var domain = 'http://172.16.0.107:9011/pages/10003/';
import {
  feedbackAnimationSend,
  feedbackData,
  initializeFeedback,
  feedBackChange
} from "../../../common/template/feedbackAnimation/form.js";
import {
  avatarUpWallData,
  avatarUpWallSend,
  teacherChange,
  studentChange,
  initializeAvatarUpWall
} from "../../../common/template/avatarUpWall/form.js";
var feedbackObjData1 = feedbackData({
  key:'feedKey1',
  name:'整体反馈',
});
var domain = '';
const multyAnimationInitData = [
  {
    roleLocationX: '', // 角色位置x
    roleLocationY: '', // 角色位置y
    roleImg: "", // 角色图片,
    scale: 100, //缩放比例  1-500
  },
]
// 对话内容
const gameContents = {
  isShowHandle: true, // 是否显示小手
  handleLocationX: '', // 小手位置x
  handleLocationY: '', // 小手位置y
  contentImg: '', // 内容图片
  contentImgLocationX: '', // 内容图片位置x
  contentImgLocationY: '', // 内容图片位置y
  contentAudio: '', // 内容音频
}
// 对话选项
const gameOptions = {
  optionImg: '', // 选项图片
  isAnswer: false, // 是否是答案
  optionAudio: '', // 选项音频
}
var Data = {
    configData: {
        bg: "",
        desc: "",
        tImg: '',
        tImgX: '',
        tImgY: '',
        title: "",
        tg: [{
            title: "",
            content: "",
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
          multyAnimation: multyAnimationInitData,
          gameList: [
            {
              gameContents: [
                JSON.parse(JSON.stringify(gameContents))
              ], // 游戏内容
              gameOptions:[
                JSON.parse(JSON.stringify(gameOptions))
              ] // 游戏选项
            }
          ]
        },        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        },
        feedbackLists:[
          ...feedbackObjData1,
        ],
        avatarUpWallData: avatarUpWallData(),
    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
};
Data.configData.feedbackLists.push(feedbackObjData1)

$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
          Data.configData = JSON.parse(res.data);
          if(!Data.configData.tImg){
              Data.configData.tImg = '';
          }
          if(!Data.configData.tImgX){
              Data.configData.tImgX = 1340
          }
          if(!Data.configData.tImgY){
              Data.configData.tImgY = 15
          }
          if (!Data.configData.level) {
              Data.configData.level = {
                  high: [{
                      title: "",
                      content: "",
                  }],
                  low: [{
                      title: "",
                      content: "",
                  }]
              }
          }
          //老模板未保存log信息，放入默认log
          if (!Data.configData.log) {
              Data.configData.log = {
                  teachPart: -1, //教学环节 -1未选择
                  teachTime: -1, // 整理好的教学时长
                  tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
              }
              Data.configData.log_editor = {
                  isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                  TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                  TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
              }
          }
          initializeFeedback(Data)
          initializeAvatarUpWall(Data);
        }
    },
    error: function(res) {
        console.log(res)
    }
});
new Vue({
    el: '#container',
    data: Data,
    methods: {
        //辅助提示图片上传
        tImageUpload: function(e, attr, fileSize) {
          console.log("tImageUpload",e)
          var file = e.target.files[0],
              size = file.size,
              naturalWidth = -1,
              naturalHeight = -1,
              that = this;
          var item = this.configData;

          if ((size / 1024).toFixed(2) > fileSize) {
              alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
              return;
          }
          item[attr] = "./form/img/loading.jpg";
          var img = new Image();
          img.onload = function() {
              naturalWidth = img.naturalWidth;
              naturalHeight = img.naturalHeight;
              var check = that.tImgCheck(e.target, {
                  height: naturalHeight,
                  width: naturalWidth
              }, item, attr);
              if (check) {
                  that.postData(file, item, attr);
                  img = null;
              } else {
                  img = null;
              }
          }
          var reader = new FileReader();
          reader.onload = function(evt) {
              img.src = evt.target.result;
          }
          reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        //辅助提示图片大小校检
        tImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && (height+1) > data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width+"*"+data.height);
            }
            return checkSize;
        },
        feedBackChange(item) {
          feedBackChange(item)
        },
        // feedback 上传
        feedbackUpload:function(e, item, attr, fileSize) {
          console.log(e, item, attr, fileSize);
          const file = e.target.files[0];
          if (file.type === "image/png") {
            this.imageUpload(e, item, attr, fileSize);
          } else {
            this.lottieUpload(e, item, attr, fileSize);
          }
        },
        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                e.target.value = '';
                return;
            }

            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                });
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        lottieUpload: function (e, item, attr, fileSize) {
          var file = e.target.files[0],
            size = file.size,
            that = this;
          if ((size / 1024).toFixed(2) > fileSize) {
            alert(
              "您上传的图片大小为" +
                (size / 1024).toFixed(2) +
                "KB, 超过" +
                fileSize +
                "K上限，请检查后上传！"
            );
            e.target.value = "";
            return;
          }
          const reader = new FileReader();
          reader.onload = async function (processEvent) {
            const jsonData = JSON.parse(processEvent.target.result);
            // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
            const naturalWidth = jsonData.w || jsonData.width;
            const naturalHeight = jsonData.h || jsonData.height;
            var check = that.sourceImgCheck(
              e.target,
              {
                height: naturalHeight,
                width: naturalWidth,
              },
              item,
              attr
            );
            if (check) {
              that.postData(file, item, attr);
            } else {
            }
          };
          reader.readAsText(file);
        },
        sourceImgCheck: function(input, data) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                alert("应上传图片大小为：" + size.join("或") + ", 上传图片尺寸为：" + data.width + "*" + data.height);
                input.value = '';
            }
            return checkSize;
        },
        validate: function() {
            let gameList = this.configData.source.gameList;
            let check = true;
            let hasContentImgEmpty = 0;
            let hasContentAudioEmpty = 0;
            let hasOptionLessThatTwo = 0;
            let hasOptionImgEmpty = 0;
            let hasOptionIsAnswer = 0;
            let isTipOneAnswer = false;
            for(let i = 0; i < gameList.length; i++) {
              hasOptionIsAnswer = 0;
              const gameContents = gameList[i].gameContents;
              const gameOptions = gameList[i].gameOptions;
              for(let j = 0; j < gameContents.length; j++) {
                if(gameContents[j].contentImg == '') hasContentImgEmpty++;
                if(gameContents[j].contentAudio == '') hasContentAudioEmpty++;
              }
              if(gameOptions.length > 0 && gameOptions.length <2){
                hasOptionLessThatTwo++;
              } else {
                for (let j = 0; j < gameOptions.length; j++) {
                  if(gameOptions[j].optionImg == '') hasOptionImgEmpty++;
                  if(gameOptions[j].isAnswer) hasOptionIsAnswer++;
                }
                if(gameOptions.length && !hasOptionIsAnswer) isTipOneAnswer = true;
              }
            }
            if (hasContentImgEmpty > 0) {
              check = false;
              alert('请设置对话内容图片');
              return;
            }
            if (hasContentAudioEmpty > 0) {
              check = false;
              alert('请设置对话音频');
              return;
            }
            if (hasOptionLessThatTwo > 0) {
              check = false;
              alert('请设置2-3个选项');
              return;
            }
            if (hasOptionImgEmpty > 0) {
              check = false;
              alert('请设置选项图片');
              return;
            }
            if (isTipOneAnswer) {
              check = false;
              alert('请设置选项至少有一个正确答案');
              return;
            }
            return check;
        },
        onSend: function() {
            var data = this.configData;
            let feedbackStatus = feedbackAnimationSend(data);
            if (!feedbackStatus) {
              return;
            }
            avatarUpWallSend(data);
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            // console.log(_data, 'aaa');
            var val = this.validate();
            if (val === true) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                //alert('带*号的为必填项')
                // alert('图片、音频和文字不能同时为空')
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            var _this = this;
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },
        audioUpload: function(e, item, attr, fileSize) {
            console.log(attr)
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的声音大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                e.target.value = '';
                return;
            }
            this.postData(file, item, attr);
        },
        // 添加动效
        addAnimation: function() {
          this.configData.source.multyAnimation.push({
            roleLocationX: '', // 角色位置x
            roleLocationY: '', // 角色位置y
            roleImg: "", // 角色图片,
            scale: 100, //缩放比例  1-500
          });
        },
        // 删除动效
        delAnimation: function (item) {
          this.configData.source.multyAnimation.remove(item);
        },
        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addGameContent: function(item) {
          item.push(JSON.parse(JSON.stringify(gameContents)));
        },
        addGameOption: function(item) {
          item.push(JSON.parse(JSON.stringify(gameOptions)));
        },
        addGameRound: function(item) {
          item.push({
            gameContents: [
              JSON.parse(JSON.stringify(gameContents))
            ],
            gameOptions:[
              JSON.parse(JSON.stringify(gameOptions))
            ]
          });
        },
        delPrew: function(item) {
            item.themePic = ''
        },
        delOption: function(arr, item) {
          arr.remove(item)
        },
        setAnswer: function(item) {
            this.configData.source.right = item;
        },
        teacherChange: function(data) {
          teacherChange(data);
        },
        studentChange: function(data) {
          studentChange(data);
        },
    },
    mounted: function() {

    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
