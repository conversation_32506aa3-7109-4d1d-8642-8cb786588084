var configData = {
	bg:'',
	desc:'',
	title:'',
  tImg: 'assets/images/jianbantg.png',
  tImgX: 1340,
  tImgY: 15,
	tg:[
		{
          content:"eegeg appy family. My father i appy family. My father i",
          title:"T116-v2.0weqwf appy family. My father i"
        },
        {
          content:"eegeg appy family. My father i appy family. My father i",
          title:"weqwf appy family. My father i"
        },
        {
          content:"eegeg appy family. My father i appy family. My father i",
          title:"weqwf appy family. My father i"
        }
	],
	level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
				content: "eegeg appy family. My father i appy family. My father i",
				title: "weqwf appy family. My father i"
			}]
	},
	source:{
    multyAnimation: [
      {
        roleLocationX: '100', // 角色位置x
        roleLocationY: '200', // 角色位置y
        roleImg: "assets/images/Doll_turn1.json", // 角色图片,
        scale: 100, //缩放比例  1-500
      },
      {
        roleLocationX: '1000', // 角色位置x
        roleLocationY: '0', // 角色位置y
        roleImg: "assets/images/Doll_turn1.json", // 角色图片,
        scale: 100, //缩放比例  1-500
      }
    ],
    gameList: [
      {
        gameContents: [
          {
            isShowHandle: false, // 是否显示小手
            handleLocationX: '100', // 小手位置x
            handleLocationY: '200', // 小手位置y
            contentImg: './assets/images/duihua1.png', // 内容图片
            contentImgLocationX: '', // 内容图片位置x
            contentImgLocationY: '', // 内容图片位置y
            contentAudio: './assets/audio/goat.mp3', // 内容音频
          },
          {
            isShowHandle: true, // 是否显示小手
            handleLocationX: '500', // 小手位置x
            handleLocationY: '200', // 小手位置y
            contentImg: './assets/images/duihua2.png', // 内容图片
            contentImgLocationX: '550', // 内容图片位置x
            contentImgLocationY: '200', // 内容图片位置y
            contentAudio: './assets/audio/hippo.mp3', // 内容音频
          },
          {
            isShowHandle: true, // 是否显示小手
            handleLocationX: '500', // 小手位置x
            handleLocationY: '300', // 小手位置y
            contentImg: './assets/images/duihua2.png', // 内容图片
            contentImgLocationX: '550', // 内容图片位置x
            contentImgLocationY: '300', // 内容图片位置y
            contentAudio: './assets/audio/goat.mp3', // 内容音频
          },
        ], // 游戏内容
        gameOptions:[
          {
            optionImg: './assets/images/duihua2.png', // 选项图片
            isAnswer: true, // 是否是答案
            optionAudio: '', // 选项音频
          },
          {
            optionImg: './assets/images/duihua1.png', // 选项图片
            isAnswer: true, // 是否是答案
            optionAudio: '', // 选项音频
          },
          {
            optionImg: './assets/images/duihua1.png', // 选项图片
            isAnswer: false, // 是否是答案
            optionAudio: './assets/audio/goat.mp3', // 选项音频
          }
        ] // 游戏答案
      },
      {
        gameContents: [
          {
            isShowHandle: false, // 是否显示小手
            handleLocationX: '200', // 小手位置x
            handleLocationY: '300', // 小手位置y
            contentImg: './assets/images/duihua2.png', // 内容图片
            contentImgLocationX: '150', // 内容图片位置x
            contentImgLocationY: '200', // 内容图片位置y
            contentAudio: './assets/audio/goat.mp3', // 内容音频
          },
          {
            isShowHandle: false, // 是否显示小手
            handleLocationX: '600', // 小手位置x
            handleLocationY: '300', // 小手位置y
            contentImg: './assets/images/duihua1.png', // 内容图片
            contentImgLocationX: '550', // 内容图片位置x
            contentImgLocationY: '200', // 内容图片位置y
            contentAudio: './assets/audio/hippo.mp3', // 内容音频
          },
          {
            isShowHandle: true, // 是否显示小手
            handleLocationX: '600', // 小手位置x
            handleLocationY: '400', // 小手位置y
            contentImg: './assets/images/duihua2.png', // 内容图片
            contentImgLocationX: '650', // 内容图片位置x
            contentImgLocationY: '400', // 内容图片位置y
            contentAudio: './assets/audio/goat.mp3', // 内容音频
          },
        ], // 游戏内容
        gameOptions:[
          {
            optionImg: './assets/images/duihua2.png', // 选项图片
            isAnswer: true, // 是否是答案
            optionAudio: './assets/audio/goat.mp3', // 选项音频
          },
          {
            optionImg: './assets/images/duihua1.png', // 选项图片
            isAnswer: true, // 是否是答案
            optionAudio: '', // 选项音频
          },
          {
            optionImg: './assets/images/duihua1.png', // 选项图片
            isAnswer: false, // 是否是答案
            optionAudio: './assets/audio/goat.mp3', // 选项音频
          }
        ] // 游戏答案
      }
    ],
	},
  feedbackLists:[
    {
      positiveFeedback: '-1',
      feedbackList:[
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./assets/images/prefect.json', mp3:'./assets/audios/prefect.mp3' },
        { id:'1', json:'./assets/images/goldCoin.json', mp3:'./assets/audios/goldCoin.mp3' },
        { id:'2', json:'./assets/images/FKJB.json', mp3:'./assets/audios/resultWin.mp3' },
        { id:'9', json: './assets/images/feedback.json', mp3: './assets/audios/feedback01.mp3' },
      ],
      feedbackObj:{ id:'9', json:'./assets/images/feedback.json', mp3:'./assets/audios/feedback01.mp3' },
      feedback:'./assets/images/duihua1.png',
      feedbackAudio:'./assets/audio/goat.mp3',
      feedbackName:'整体反馈',
      key:'feedKey1',
    }
  ]

};
