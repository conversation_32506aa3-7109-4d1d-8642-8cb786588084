<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>DSM0001FT_对话选择</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">DSM0001FT_对话选择</div>

			<% include ./src/common/template/common_head %>
      <!-- 多个Json 动效 -->
			<% include ./src/common/template/multyAnimation/form %>
			<!-- 添加选项 -->
			<div class="c-group">
				<div class="c-title">添加选项</div>
        <div class="pic-title">
          <p class="title">最少1轮，最多6轮</p>
        </div>
				<div class="c-area upload img-upload" v-for="(item, index) in configData.source.gameList">
          <span class="dele-tg-btn" v-on:click="delOption(configData.source.gameList, item)" v-show="configData.source.gameList.length>1"></span>
          <h2>第{{index + 1}}轮</h2>
          <div class="c-well upload img-upload radio-group" v-for="(content, contentIndex) in item.gameContents">
            <h3>对话{{contentIndex+1}}</h3>
            <span class="dele-tg-btn" v-on:click="delOption(item.gameContents, content)" v-show="item.gameContents.length>1"></span>
            <!-- 小手配置 -->
            <div class="field-wrap">
              <span class="fixed-width">小手显示（点击后展示图片播放音频）</span>
              <label class="inline-label" :for="`is-show-handle${index}${contentIndex}1`"><input type="radio" :name="`is-show-handle${index}${contentIndex}1`" :value="true"
                v-model="content.isShowHandle"> 是</label>
                <label class="inline-label" :for="`is-show-handle${index}${contentIndex}2`"><input type="radio" :name="`is-show-handle${index}${contentIndex}2`" :value="false"
                    v-model="content.isShowHandle"> 否</label>
              <div class="field-wrap-item">
                <span>动态小手显示位置</span>
                X:<input type="number" class="c-input-txt " style="margin: 0 10px;width: 60px!important;display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="content.handleLocationX">
                Y:<input type="number" class="c-input-txt " style="margin: 0 10px;width: 60px!important;display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="content.handleLocationY">
                <br>
                <em>数字,0<=x<=1920,0<=y<=1080</em>
              </div>
            </div>
            <!-- 图片添加 -->
            <div class="field-wrap">
              <span class='txt-info'>图片{{contentIndex+1}}*</span>
              <label class="btn btn-show upload" :for="`content-pic-${index}${contentIndex+1}`" v-if="!content.contentImg">上传</label>
              <label class="btn upload re-upload" :for="`content-pic-${index}${contentIndex+1}`" v-if="content.contentImg!=''?true:false">重新上传</label>
              <span class='txt-info'><em>&nbsp;&nbsp; 大小:≤30KB </em></span>
              <input type="file" accept=".jpg,.png" v-bind:key="Date.now()" :id="`content-pic-${index}${contentIndex+1}`" class="btn-file" size=""  @change="imageUpload($event,content,'contentImg',30)">
              <div class="img-preview" v-if="content.contentImg">
                <img v-bind:src="content.contentImg" alt=""/>
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="content.contentImg=''">删除</span>
                </div>
              </div>
              <div class="field-wrap-item">
                <span>位置</span>
                X:<input type="number" class="c-input-txt " style="margin: 0 10px;width: 60px!important;display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="content.contentImgLocationX">
                Y:<input type="number" class="c-input-txt " style="margin: 0 10px;width: 60px!important;display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="content.contentImgLocationY">
                <br>
                <em>数字,0<=x<=1920,0<=y<=1080</em>
              </div>
            </div>
            <!-- 音频添加 -->
            <div class="field-wrap">
              <span class="fixed-width">音频{{contentIndex+1}}*</span>
              <input type="file" accept=".mp3,wav" v-bind:key="Date.now()" class="btn-file" :id="`content-audio-modle-${index}${contentIndex}`"
                v-on:change="audioUpload($event,content,'contentAudio',200)">
              <label :for="`content-audio-modle-${index}${contentIndex}`" class="btn btn-show upload"
                v-if="!content.contentAudio">上传</label>
                <div class="audio-preview" v-show="content.contentAudio">
                  <div class="audio-tools">
                    <p v-show="content.contentAudio">{{content.contentAudio}}</p>
                  </div>
                  <span class="play-btn" v-on:click="play($event)">
                    <audio v-bind:src="content.contentAudio"></audio>
                  </span>
                </div>
                <label :for="`content-audio-modle-${index}${contentIndex}`" class="btn upload btn-audio-dele" v-if="content.contentAudio" @click="content.contentAudio=''">删除</label>
                <label :for="`content-audio-modle-${index}${contentIndex}`" class="btn upload re-upload" v-if="content.contentAudio">重新上传</label>
                <div class="audio-tips">
                  <span class='txt-info'><em class="game_em">支持mp3、wav，大小：≤200KB</em></span>
                </div>
            </div>
          </div>
          <button type="button" class="add-tg-btn text-add-btn" v-show="item.gameContents.length<4" v-on:click="addGameContent(item.gameContents)">添加对话</button>
          <div class="c-well upload img-upload radio-group" v-for="(option, optionIndex) in item.gameOptions">
            <h3>选项{{optionIndex+1}}</h3>
            <span class="dele-tg-btn" v-on:click="delOption(item.gameOptions, option)"></span>
            <!-- 图片添加 -->
            <div class="field-wrap">
              <span class='txt-info'>选项图片{{optionIndex+1}}*</span>
              <label class="btn btn-show upload" :for="`option-pic-${index}${optionIndex+1}`" v-if="!option.optionImg">上传</label>
              <label class="btn upload re-upload" :for="`option-pic-${index}${optionIndex+1}`" v-if="option.optionImg!=''?true:false">重新上传</label>
              <span class='txt-info'><em>&nbsp;&nbsp; 尺寸300*200，大小:≤30KB，图片支持jpg、pgn格式 </em></span>
              <input type="file" accept=".jpg,.png" v-bind:key="Date.now()" :id="`option-pic-${index}${optionIndex+1}`" class="btn-file" size="300*200"  @change="imageUpload($event,option,'optionImg',30)">
              <div class="img-preview" v-if="option.optionImg">
                <img v-bind:src="option.optionImg" alt=""/>
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="option.optionImg=''">删除</span>
                </div>
              </div>
            </div>
            <!-- 音频添加 -->
            <div class="field-wrap">
              <span class="fixed-width">音频{{optionIndex+1}}</span>
              <input type="file" accept=".mp3,wav" v-bind:key="Date.now()" class="btn-file" :id="`option-audio-modle-${index}${optionIndex}`"
                v-on:change="audioUpload($event,option,'optionAudio',200)">
              <label :for="`option-audio-modle-${index}${optionIndex}`" class="btn btn-show upload"
                v-if="!option.optionAudio">上传</label>
              <div class="audio-preview" v-show="option.optionAudio">
                <div class="audio-tools">
                  <p v-show="option.optionAudio">{{option.optionAudio}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="option.optionAudio"></audio>
                </span>
              </div>
              <label :for="`option-audio-modle-${index}${optionIndex}`" class="btn upload btn-audio-dele" v-if="option.optionAudio" @click="option.optionAudio=''">删除</label>
              <label :for="`option-audio-modle-${index}${optionIndex}`" class="btn upload re-upload" v-if="option.optionAudio">重新上传</label>
              <div class="audio-tips">
                <em class="game_em">支持mp3、wav，大小：≤200KB</em>
              </div>
            </div>
            <!-- 是否为正确答案配置 -->
            <div class="field-wrap">
              <span class="fixed-width">是否为正确答案</span>
              <label class="inline-label" :for="`is-answer${index}${optionIndex}1`"><input type="radio" :name="`is-answer${index}${optionIndex}1`" :value="true"
                v-model="option.isAnswer"> 是</label>
              <label class="inline-label" :for="`is-answer${index}${optionIndex}2`"><input type="radio" :name="`is-answer${index}${optionIndex}2`" :value="false"
                  v-model="option.isAnswer"> 否</label>
            </div>
          </div>
          <button type="button" class="add-tg-btn text-add-btn" v-show="item.gameOptions.length<3" v-on:click="addGameOption(item.gameOptions)">添加选项</button>
        </div>
        <button type="button" class="add-tg-btn text-add-btn"
          v-show="configData.source.gameList.length < 6"
          v-on:click="addGameRound(configData.source.gameList)">
            添加一轮
        </button>
			</div>
      <!-- 头像上墙 -->
      <% include ./src/common/template/avatarUpWall/form %>
      <% include ./src/common/template/feedbackAnimation/form %>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/bg.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
