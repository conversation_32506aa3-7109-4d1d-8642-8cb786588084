@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../record/css/record.css';
@import "../../common/template/avatarUpWall/style.scss";

@mixin setEle($l:0rem, $t:0rem, $w:0rem, $h:0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.hide {
  display: none;
}

.main {
  .game-box{
    .game-content-list{
      .game-content-item{
        display: none;
        .img{
          position: absolute;
          display: none;
          z-index: 10;
          .play-audio-btn{
            position: absolute;
            left: 0;
            top: -0.54rem;
            height: 1.08rem;
            width: 1.08rem;
          }
          img{
            display: none;
          }
        }
      }
    }
    .game-option-list{
      width: 13rem;
      height: 3.2rem;
      position: absolute;
      z-index: 10;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 3.2rem);
      display: flex;
      background: url('../image/option-bg.png') no-repeat;
      background-size: cover;
      justify-content: space-evenly;
      padding: 0.6rem 0.45rem;
      .game-option-item{
        width: 3rem;
        height: 2rem;
        position: relative;
        .right-icon{
          width: 0.9rem;
          height: 0.9rem;
          position: absolute;
          left: -0.24rem;
          top: -0.26rem;
          z-index: 1;
          display: none;
        }
        .right-box{
          width: 3.2rem;
          height: 2.2rem;
          position: absolute;
          left: -0.1rem;
          top: -0.1rem;
          display: none;
        }
        .error-box {
          width: 3.2rem;
          height: 2.2rem;
          position: absolute;
          left: -0.1rem;
          top: -0.1rem;
          display: none;
        }
        .img{
          border: 0.06rem solid #fff;
          border-radius: 0.3rem;
          overflow: hidden;
          height: 100%;
          width: 100%;
          cursor: pointer;
          img{
            display: block;
            width: 100%;
            height: 100%;
          }
        }
      }
      .game-option-right{
        .right-icon {
          display: block;
        }
        .right-box{
          display: block;
        }
      }
      .game-option-error{
        .error-box{
          display: block;
        }
      }
    }
  }
}
.nextLastBtnBox {
  display: none;
  .btn{
    width: 1rem;
    height: 1.26rem;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 101;
    img{
      width: 100%;
      height:100%;
      position: absolute;
    }
    .disalble{
      display: none;
    }
    .normal{
      display: block;
    }
  }
  .btn:hover{
    transform: translateY(-50%) scale(1.05);
  }
  .btn:active{
    transform: translateY(-50%);
  }
  .btn-disable{
    .normal{
      display: none;
    }
    .disalble{
      display: block;
    }
  }
  .lastBtn{
    left: 0.4rem;
  }
  .nextBtn{
    right: 0.4rem;
  }
}
.handsss{
  width:1.8rem;
  height:1.8rem;
  background: url('../image/hands.png');
  background-size: 7.2rem 1.8rem;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.7);
  animation: handClick 0.8s steps(4) infinite;
  opacity: 1;
  cursor: pointer;
  z-index: 100;
}
@keyframes handClick {
  0%{
    background-position: 0 0;
	}
	100%{
    background-position:133% 0;
	}
}
@-webkit-keyframes handClick {
  0%{
    background-position: 0 0;
	}
	100%{
    background-position:133% 0;
	}
}
.shake{
  animation: shake 0.5s ease-in-out;
}
@keyframes shake {
  0% {
		transform: translateX(6px) ;
	}
	20% {
		transform: translateX(-6px);
	}
	40% {
		transform: translateX(6px);
	}
	60% {
		transform: translateX(-6px);
	}
	80% {
		transform: translateX(6px);
	}
	100% {
		transform: translateX(0px);
	}
}
@-webkit-keyframes shake {
  0% {
		transform: translateX(6px) ;
	}
	20% {
		transform: translateX(-6px);
	}
	40% {
		transform: translateX(6px);
	}
	60% {
		transform: translateX(-6px);
	}
	80% {
		transform: translateX(6px);
	}
	100% {
		transform: translateX(0px);
	}
}
.slide-top {
  animation: slide-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@-webkit-keyframes slide-top {
  0% {
    -webkit-transform: translate(-50%, 3.2rem);
            transform: translate(-50%, 3.2rem);
  }
  100% {
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
  }
}
@keyframes slide-top {
  0% {
    -webkit-transform: translate(-50%, 3.2rem);
            transform: translate(-50%, 3.2rem);
  }
  100% {
    -webkit-transform: translate(-50%, 0);
            transform: translate(-50%, 0);
  }
}

.container {
  background-size: auto 100%;
  position: relative;
}
