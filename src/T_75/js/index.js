"use strict"
import '../../common/js/lottie.js'
import '../../common/js/common_1v1.js'
import '../record/js/record.js'
import "../../common/template/multyAnimation/index.js";
import "../../common/js/teleprompter.js"
import "../../common/template/avatarUpWall/index.js";
import {
  feedbackAnimation
} from "../../common/template/feedbackAnimation/index.js";
import {USER_TYPE, CLASS_STATUS} from "../../common/js/constants.js";

$(function () {
  window.h5Template = {
    hasPractice: '1'// 普通授权模式
  }
  const h5SyncActions = parent.window.h5SyncActions;
  const isSync = h5SyncActions && h5SyncActions.isSync;
  const classStatus = h5SyncActions && h5SyncActions.classConf.h5Course.classStatus;

  if (configData.bg == '') {
    $(".container").css({ 'background-image': 'url(./image/defaultBg.jpg)' })
  }
  // 用户属性
  const userType = window.frameElement && window.frameElement.getAttribute('user_type');
  let currentPage = 1; //当前页码
  let currentPageContent = 1; //当前页码的当前内容
  const gameList = configData.source.gameList;
  const optionsNum = gameList.length - 1;
  // 正确选项个数
  let gameOptionRight = 0;
  // 当前已经选择正确的选项个数
  let currentGameOptionRight = 0;
  // 如果页面超过1页，则显示下一页按钮，否则不显
  if(optionsNum != 0) {
    $('.nextLastBtnBox').show();
  }
  // 音频动效
  let playAudioAnimation = 0;

  // 填充内容
  const page = {
    // 初始化当前轮页面及配置
    init () {
      currentPageContent = 1;
      currentGameOptionRight = 0;
      gameOptionRight = 0;
      this.initGameContent(gameList[currentPage-1].gameContents);
      this.initGameOption(gameList[currentPage-1].gameOptions || []);

      this.handleCurrentPage();
    },
    // 初始化动画
    async initPlayAudioAnimation(elClass) {
      playAudioAnimation = await lottieAnimations.init(playAudioAnimation, './image/laba.json', elClass, true, true);
    },
    // 初始化对话内容
    initGameContent(gameContents) {
      const gameContentListEl = $('.game-content-list');
      let gameContentEl = '';
      for(let i = 0; i < gameContents.length; i++) {
        const item = gameContents[i];
        // 小手元素
        const gameContentHandleEl = item.isShowHandle
          ? `<div class="handsss" style="top: ${item.handleLocationY/100}rem;left: ${item.handleLocationX/100}rem" data-syncactions="btn_handsss${i+1}"></div>`
          : "";
        // 内容元素
        gameContentEl += `
          <div class="game-content-item" data-content-id="${i+1}">
          ${gameContentHandleEl}
          <div class="img" style="top: ${item.contentImgLocationY/100}rem;left: ${item.contentImgLocationX/100}rem">
              <div class="play-audio-btn play-audio-btn${i+1}"></div>
              <audio src="${item.contentAudio}" class="audio" data-syncaudio="content-audio-${i}"></audio>
              <img src="${item.contentImg}"  alt="">
            </div>
          </div>
        `;
      }
      gameContentListEl.html(gameContentEl);
      this.handleContentImgSize();
    },
    // 处理当前页当前内容图片大小
    handleContentImgSize() {
      const constentImgs = $('.game-content-item .img img');
      for(let i = 0; i < constentImgs.length; i++) {
        const item = constentImgs[i];
        $(item).on('load', function() {
          $(this).css({
            display: 'block',
            height: `${item.naturalHeight/100}rem`,
            width: `${item.naturalWidth/100}rem`
          })
        })
      }
    },
    // 处理当前页当前内容事件、音频、小手
    handleCurrentPage() {
      const currentContent = gameList[currentPage - 1].gameContents[currentPageContent - 1];
      const currentContentEl = $(`.game-content-item[data-content-id="${currentPageContent}"]`);
      currentContentEl.show();
      if(currentContent.isShowHandle) {
        this.initHandsssClick();
        return;
      } else {
        currentContentEl.find('.img').show();
      }
      const animationClass = `.play-audio-btn${currentPageContent}`
      this.initPlayAudioAnimation(animationClass);
      this.playCurrentContentAudio(currentContentEl);
    },
    // 播放当前页内容音频
    playCurrentContentAudio(currentContentEl) {
      const contentLen = gameList[currentPage - 1].gameContents.length;
      const gameOptions = gameList[currentPage-1].gameOptions || [];
      const audio =  $(currentContentEl).find('.audio');
      audio[0].currentTime = 0;
      SDK.playRudio({
        index: audio[0],
        syncName: audio.attr("data-syncaudio")
      });
      audio.one('ended', () => {
        // currentContentEl.hide();
        $(currentContentEl).find('.play-audio-btn').hide();
        SDK.setEventLock();
        if(currentPageContent >= contentLen) {
          // 最后一次内容播放之后继续选项逻辑
          gameOptions.length && $('.game-option-list').addClass('slide-top');
          return;
        };
        currentPageContent++;
        this.handleCurrentPage();
      })
    },
    // 初始化小手点击事件
    initHandsssClick() {
      const currentContentEl = $(`.game-content-item[data-content-id="${currentPageContent}"]`);
      const handsssEl = $(currentContentEl).find('.handsss');
      handsssEl.on('click touchstart', (e) => {
        if (e.type == "touchstart") {
          e.preventDefault()
        }
        if (!isSync) {
          handsssEl.trigger('handsssClickSync')
          return
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'handsssClickSync',
          recoveryMode: 1,
          otherInfor: {
            currentPage: currentPageContent - 1
          }
        });
      })
      handsssEl.on('handsssClickSync', () => {
        currentContentEl.find('.img').show();
        handsssEl.hide();
        const animationClass = `.play-audio-btn${currentPageContent}`
        this.initPlayAudioAnimation(animationClass);
        this.playCurrentContentAudio(currentContentEl);
      })
    },
    // 初始化选项选择事件
    initOptionSelect() {
      const that = this;
      const gameOptionItemEl = $('.game-option-list .game-option-item');
      gameOptionItemEl.on('click touchstart', function(e) {
        if (e.type == "touchstart") {
          e.preventDefault()
        }
        if (!isSync) {
          $(this).trigger('optionClickSync')
          return
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data('syncactions'),
          eventType: 'click',
          method: 'event',
          syncName: 'optionClickSync',
          recoveryMode: 1
        });
      })
      gameOptionItemEl.on('optionClickSync', function() {
        // 如果已经选择了，且为正确元素再次点击不做操作
        if($(this).hasClass('game-option-right')){
          return;
        }
        const audioRightEl = $('.runRight');
        const audioWrongEl = $('.runWrong');
        // 获取当前页选项
        const currentGameOptions = gameList[currentPage - 1].gameOptions;
        const currentGameOption = currentGameOptions[$(this).data('option-id') - 1];
        // 当前元素下的音频元素
        const audioEl = $(this).find('.audio');
        if(currentGameOption.isAnswer){
          // 选择正确逻辑
          $(this).addClass('game-option-right');
          audioRightEl[0].currentTime = 0;
          SDK.playRudio({
            index: audioRightEl[0],
            syncName: audioRightEl.attr("data-syncaudio")
          });
          // 播放正确音频之后播放选项音频
          audioRightEl.one('ended', () => {
            that.gameOptionAudioPlay(audioEl, ()=> {
              currentGameOptionRight++;
              that.handleSureCallback();
              SDK.setEventLock();
            })
          })
        } else {
          // 选择错误逻辑
          $(this).addClass('game-option-error shake');
          audioWrongEl[0].currentTime = 0;
          SDK.playRudio({
            index: audioWrongEl[0],
            syncName: audioWrongEl.attr("data-syncaudio")
          });
          audioWrongEl.one('ended', () => {
            $(this).removeClass('game-option-error shake');
            that.gameOptionAudioPlay(audioEl, ()=> {
              SDK.setEventLock();
            })
          })
        }
      })
    },
    // 选项答题音频播放
    gameOptionAudioPlay(audioEl, callback) {
      if(!audioEl[0]) {
        callback && callback();
        return;
      }
      audioEl[0].currentTime = 0;
      SDK.playRudio({
        index: audioEl[0],
        syncName: audioEl.attr("data-syncaudio")
      });
      // 播放选项音频之后执行正反馈逻辑
      audioEl.one('ended', () => {
        callback && callback();
      })
    },
    // 处理正确反馈，如果是最后一轮且所有正确答案选择完成执行正反馈
    handleSureCallback(){
      if(currentPage !== gameList.length) return;
      const gameOptions = gameList[currentPage - 1].gameOptions || [];
      if(!gameOptions.length) return;
      if(gameOptionRight && gameOptionRight == currentGameOptionRight) {
        feedbackAnimation('feedKey1');
      }
    },
    // 初始化答案选项
    initGameOption(gameOptions) {
      const gameOptionListEl = $('.game-option-list');
      gameOptionListEl.html('');
      // 设置gameOptionListEl元素隐藏
      gameOptionListEl.css({
        'transform': 'translate(-50%, 3.2rem)'
      });
      gameOptionListEl.removeClass('slide-top');
      if(!gameOptions.length) {
        return;
      };
      this.fetchGameOptionRightNum(gameOptions);
      let gameOptionEl = '';
      for(let i = 0; i < gameOptions.length; i++) {
        const item = gameOptions[i];
        // 内容元素
        gameOptionEl += `
          <div class="game-option-item" data-option-id="${i+1}" data-syncactions="option-${i}">
            <img class="right-icon" src="./image/answer-right.png">
            <img class="right-box" src="./image/right-box.png" alt=""/>
            <img class="error-box" src="./image/error-box.png" alt=""/>
            <div class="img">
              ${item.optionAudio ? `<audio src="${item.optionAudio}" class="audio" data-syncaudio="option-audio-${i}"></audio>` : ''}
              <img src="${item.optionImg}" alt="">
            </div>
          </div>
        `;
      }
      gameOptionListEl.html(gameOptionEl);
      this.initOptionSelect();
    },
    // 获取正确答案数量
    fetchGameOptionRightNum(gameOptions) {
      for(let i = 0; i < gameOptions.length; i++) {
        const item = gameOptions[i];
        if(item.isAnswer) {
          gameOptionRight++;
        }
      }
    }
  }
  page.init();
  // 上一页
  $(".lastBtn").on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    if (currentPage <= 1) {
      return
    }
    if (!isSync) {
      $(this).trigger('lastClickSync')
      return
    }
    // 老师或者学生预览模式可点击
    if (userType == USER_TYPE.TEA || classStatus == CLASS_STATUS.NOT) {
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'lastClickSync',
        recoveryMode: 1,
        otherInfor: {
          currentPage: currentPage - 1
        }
      });
    }
  })

  $(".lastBtn").on('lastClickSync', function (e, message) {
    $(".nextBtn").removeClass('btns')
    $(".nextBtn").removeClass("btn-disable")
    $(this).removeClass('btns')
    if (currentPage == 2) {
      $(this).addClass("btn-disable")
    }
    currentPage--
    let obj = '';
    if (!isSync) {
      currentPage = currentPage
    } else {
      obj = message.data[0].value.syncAction.otherInfor;
      if (message == undefined || message.operate == '1') {
        currentPage = obj.currentPage
        if (currentPage > 1) {
          $(".lastBtn").removeClass('btns')
          $(".lastBtn img").removeClass("hide")
        }
      } else {
        if (obj.currentPage > 1) {
          $(".lastBtn").removeClass('btns')
          $(".lastBtn img").removeClass("hide")
        }
      }
    }
    if (isSync && message.operate != '1') {
      currentPage = obj.currentPage;
    }
    page.init();
    SDK.setEventLock()
  })
  // 下一页
  $(".nextBtn").on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }

    if (currentPage > optionsNum) {
      return
    }
    if (!isSync) {
      $(this).trigger('nextClickSync')
      return
    }
    if (userType == USER_TYPE.TEA || classStatus == CLASS_STATUS.NOT) {
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'nextClickSync',
        recoveryMode: 1,
        otherInfor: {
          currentPage: currentPage + 1
        }
      });
    }
  })
  $(".nextBtn").on('nextClickSync', function (e, message) {
    $(".lastBtn").removeClass('btns')
    $(".lastBtn").removeClass("btn-disable")
    $(this).removeClass('btns');
    if (currentPage == optionsNum) {
      $(this).addClass("btn-disable")
    }
    currentPage++
    if (!isSync) {
      currentPage = currentPage
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      currentPage = obj.currentPage
    }
    page.init();
    SDK.setEventLock()
  })
});

