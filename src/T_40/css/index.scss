@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
    background: transparent
}
.lightBox{
    position: absolute;
    bottom: -3rem;
    left: 50%;
    width: 19.2rem;
    height: 6rem;
    margin-left: -9.6rem;
}

.stars{
    position: relative;
    .starList{
        opacity: .3;
        position: absolute;
        background:url('../image/star1.png') no-repeat;
    }
    .star0{
        top:0;
        left:3.1rem;
        width: 1.12rem;
        height: 1.12rem;
        // background-size:2.24rem 1.12rem ;
        background-size: contain;
    }
    .star1{
        top: 1.7rem;
        left: .5rem;
        width: 1.3rem;
        height: 1.3em;
       //background-size:2.6rem 1.3rem ;
        background-size: contain;
    }
    .star2{
        top: 2rem;
        left: 16.5rem;
        width: 1.5rem;
        height: 1.5rem;
        //background-size:3rem 1.5rem ;
        background-size: contain;
    }
    .star3{
        top: 9rem;
        left: 14.5rem;
        width: 1.8rem;
        height: 1.8rem;
        //background-size:3.6rem 1.8rem ;
        background-size: contain;
    }
    .star4{
        top: 8.05rem;
        left: 1.15rem;
        width: 2rem;
        height: 2rem;
       // background-size:4rem 2rem ;
        background-size: contain;
    }
}
.points{
    position: relative;
    .pointList{
        position: absolute;
        width: .8rem;
        height: .8rem;
        background:url('../image/point.png') no-repeat;
        background-size: contain;
    }
    .point0{
        left: .3rem;
        top: 2.9rem;
    }
    .point1{
        left: .25rem;
        top: 5.4rem;   
    }
    .point2{
        left: 18.2rem;
        top: 2.6rem;   
    }
    .point3{
        left: 18.5rem;
        top: 3.5rem;   
    }
    .point4{
        left: 18.46rem;
        top: 8.9rem;  
    }
}
@keyframes starAnima{
    0%{
        // background-position: 0 0;
        background:url('../image/star1.png') no-repeat;
        background-size: contain;
    }
    100%{
        // background-position: -2.24rem 0;
        background:url('../image/star2.png') no-repeat;
        background-size: contain;
    }
}
@-webkit-keyframes starAnima{
    0%{
        background:url('../image/star1.png') no-repeat;
        background-size: contain;
    }
    100%{
        background:url('../image/star2.png') no-repeat;
        background-size: contain;
    }
}
@keyframes pointAnima{
    0%{
        opacity: 0.5;
    }
    50%{
        opacity: 1;
    }
    100%{
        opacity: 0.5;
    }
}
@-webkit-keyframes pointAnima{
    0%{
        opacity: 0.5;
    }
    50%{
        opacity: 1;
    }
    100%{
        opacity: 0.5;
    }
}

.desc-visi{
	visibility: hidden;
}
.container{
	position: relative;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.mainArea{
    width: 100%;
    height: 7.28rem;
    margin-top: 2.27rem;
    position: absolute;
    bottom: 0;
    .option{
        width: 16rem;
        height:auto;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 1.6rem;
        ul{
            position: relative;
        }
        .optList{
            width: 14.8rem;
            height:2.4rem;
            // margin-bottom: 1rem;
            position: relative;
            font-weight: bold;
            background: url('../image/list.png') no-repeat center center;
            background-size:100% 100%;
            // border-radius: 0.84rem;
            left: 50%;
            transform: translateX(-50%);
        }
        .showFontBg {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
            display: none;
            border-radius: 0.84rem;
        }
        .fontBox {
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0;
            top: -0.07rem;
            display: flex;
            display: -webkit-flex;
            align-items: center;
            -webkit-align-items: center;
            -webkit-box-align: center;
            box-sizing: border-box;
            padding: 0 1.2rem;
            font-size: 0.52rem;
            line-height: .6rem;
            color: #717171;
        }
        .optListOne{
            color: #b6ea52;
        }
        .optList_selectwo{
            color: #470000;
        }
    }
}




