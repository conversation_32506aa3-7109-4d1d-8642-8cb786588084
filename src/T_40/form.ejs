<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>AHI0002_句子霓虹灯</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">AHI0002_句子霓虹灯</div>
			
			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">设置组： 文字 + 文字底图</div>
				<div class="c-area upload img-upload">
					<label>需换行时输入“&lt;br&gt;”符号</label>
					<ul>
						<li v-for="(item, index) in configData.source.options">
							<div class="c-well">
								<span class="dele-tg-btn" @click="delOption(item)" v-show="configData.source.options.length>2"></span>
								<label>第{{index+1}}项（显示位置：1）</label>
								
								<label>句子：<em>( 必填 ) *</em>&nbsp;&nbsp;<em>字符：≤100</em></label>
								<input type="text" class='c-input-txt' placeholder="请在此输入句子" v-model="item.text" maxlength="100">

								<div class="field-wrap">
									<label class="field-label" for="">上传图片<em> ( 必填 ) *</em></label>
									<span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤30KB 尺寸大小:1480 X 240px</em></span>
									<input type="file" v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size="1480*240" @change="imageUpload($event,item,'imgBk',50)">
								</div>
							
								<div class="field-wrap">
									<label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.imgBk">上传</label>
									<label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.imgBk">重新上传</label>
								</div>
								<div class="img-preview" v-if="item.imgBk">
									<img :src="item.imgBk" alt="" />
									<div class="img-tools">
										<span class="btn btn-delete" @click="item.imgBk =''">删除</span>
									</div>
								</div>
							</div>
						</li>
					</ul>
				</div>
				<button v-if="configData.source.options.length<3" type="button" class="add-tg-btn" v-on:click="addSele" >+</button>
			</div>


			<div class="c-group">
				<div class="c-title">图片素材_暗灰色的灯</div>
				<div class="c-area upload img-upload">
					<div class="c-well">
						<div class="field-wrap">
							<label class="field-label" for="">上传图片<em> ( 非必填 ) 默认有图</em></label>
							<span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤30KB 尺寸:1480 X 240px</em></span>
							<input type="file" v-bind:key="Date.now()" class="btn-file" id="content-pic-bg" size="1480*240" @change="imageUpload($event,configData.source,'initFontBg',50)">
						</div>
					
						<div class="field-wrap">
							<label for="content-pic-bg" class="btn btn-show upload" v-if="!configData.source.initFontBg">上传</label>
							<label for="content-pic-bg" class="btn upload re-upload" v-if="configData.source.initFontBg">重新上传</label>
						</div>
						<div class="img-preview" v-if="configData.source.initFontBg">
							<img :src="configData.source.initFontBg" alt="" />
							<div class="img-tools">
								<span class="btn btn-delete" @click="configData.source.initFontBg=''">删除</span>
							</div>
						</div>				
					</div>
				</div>
			</div>

			<div class="c-group">
				<div class="c-title">环境素材：终局动画_底部灯的颜色</div>
				<div class="c-area upload img-upload">
					<label class="field-label" for="">选择颜色：</label>
					<div class="radioBox" style="display: flex;">
						<div class="field-tools" style="display: flex;">
							<div class="field-radio-wrap radio ">
								<div class="circle radio-outer " v-bind:class="{ active:configData.source.colorNum == '0'}" v-on:click="isType(0)">
									<div class="circle radio-inner pos-center"></div>
								</div>
								<label class='field-label' for="">紫色</label>
							</div>
							<div class="field-radio-wrap radio ">
								<div class="circle radio-outer " v-bind:class="{ active:configData.source.colorNum == '1'}" v-on:click="isType(1)">
									<div class="circle radio-inner pos-center"></div>
								</div>
								<label class='field-label' for="">黄色</label>
							</div>
							<div class="field-radio-wrap radio ">
								<div class="circle radio-outer " v-bind:class="{ active:configData.source.colorNum == '2'}" v-on:click="isType(2)">
									<div class="circle radio-inner pos-center"></div>
								</div>
								<label class='field-label' for="">绿色</label>
							</div>
							<div class="field-radio-wrap radio ">
								<div class="circle radio-outer " v-bind:class="{ active:configData.source.colorNum == '3'}" v-on:click="isType(3)">
									<div class="circle radio-inner pos-center"></div>
								</div>
								<label class='field-label' for="">白色</label>
							</div>
						</div>
					</div>
				</div>

			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
