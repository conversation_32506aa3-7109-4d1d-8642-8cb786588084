// var domain = 'http://172.16.0.107:9011/pages/1152/';
var domain = '';

var Data = {
    configData: {
        lang:1,
        bg: "",
        desc: "",
        title: "",
        leftImg: '',
        rightImg: '',
        sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
        tg: [{
            title: '',
            content: ''
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
            isVideo: '1', //是否有题干音频（无：1  有：2）
            videoUrl: '', //音频文件
            videoX: '', //音频横向位置
            videoY: '', //音频纵向位置
            videoDuration: '', //题干声音长度
            openWay: '1', //是否是默认起点位置（是：1  否：2）
            isEnding: '2', //是否出现终局页（是：1 否：2）
            endFigure: '', //结局页雪碧图
            endVideo: '', //结局页音频
            endDuration: '', //结局音频时长
            options: [{
                isRightAnswers: '1', //是否是正确答案（是：1  否：2）
                img: '', //上传选项图片
                natWidth: '', //选项图片的宽度
                natHeight: '', //选项图片的高度
                imgStartX: '', //图片起点横向位置
                imgStartY: '', //图片起点纵向位置
                imgStartWidth: '', //图片起点的宽度
                imgStartHeight: '', //图片起点的高度
                imgEndX: '', //图片终点横向位置
                imgEndY: '', //图片终点纵向位置
                imgEndWidth: '', //图片终点的宽度
                imgEndHeight: '', //图片终点的高度
                areaX: '', //正确区域横向位置
                areaY: '', //正确区域纵向位置
                areaWidth: '', //正确区域宽度
                areaHeight: '', //正确区域高度
            }, ]
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
};
$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
            console.log('data--------')
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    watch: {
        'configData.source.isVideo': function() {
            this.configData.source.videoUrl = '';
            this.configData.source.videoX = '';
            this.configData.source.videoY = '';
            this.configData.source.videoDuration = '';
            // this.configData.source.options = [{img: ""}, {img: ""}];
        },
        'configData.source.isEnding': function() {
            this.configData.source.endFigure = '';
            this.configData.source.endVideo = '';
            this.configData.source.endDuration = '';
            // this.configData.source.options = [{img: ""}, {img: ""}];
        },
        'configData.source.options': {
          handler: function(val){
            for(let i = 0; i  < val.length; i++) {
              let isAnsersData = val[i].isRightAnswers
              if(isAnsersData == 2) {
                let isRightKey = this.configData.source.options[i];
                isRightKey.areaHeight = '';
                isRightKey.areaWidth = '';
                isRightKey.areaX = '';
                isRightKey.areaY = '';
                isRightKey.imgEndHeight = '';
                isRightKey.imgEndWidth = '';
                isRightKey.imgEndX = '';
                isRightKey.imgEndY = '';
              }
            }
          },
          deep: true
        },
    },
    methods: {

        imageUpload: function(e, item, attr, fileSize, index) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }

            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                if (index != undefined) {
                    var data = Data.configData.source
                    console.log('naturalWidth---', naturalWidth)
                    data.options[index].natWidth = naturalWidth
                    data.options[index].natHeight = naturalHeight
                    console.log('item----', item)
                }
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        sourceImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                alert("应上传图片大小为：" + size.join("或") + ", 上传图片尺寸为：" + data.width + "*" + data.height);
                item[attr] = "";
                // alert("图片尺寸不符合要求！");
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source;
            var checkMsg = false;

            // if(!data.bg) {
            //   checkMsg = "请上传底部背景图";
            //   return checkMsg;
            // }
            // 题干音频
            if (data.isVideo == 2) {
                if (!data.videoUrl) {
                    checkMsg = "请上传题干音频文件";
                    return checkMsg;
                }
                if (!data.videoX || !data.videoY) {
                    checkMsg = "请上传题干音频位置";
                    return checkMsg;
                }
                if (!data.videoDuration) {
                    checkMsg = "请上传题干音频题干声音长度";
                    return checkMsg;
                }
            }
            // 结局方式
            if (data.isEnding == 1) {
                if (!data.endFigure) {
                    checkMsg = "请上传结局页雪碧图";
                    return checkMsg;
                }
                if (!data.endVideo) {
                    checkMsg = "请上传结局页音频";
                    return checkMsg;
                }
                if (!data.endDuration) {
                    checkMsg = "请上传结局页音频长度";
                    return checkMsg;
                }
            }
            // 选项列表
            for (let x = 0; x < data.options.length; x++) {
                let i = data.options[x],
                    index = x + 1;
                if (!i.img) {
                    checkMsg = "请上传选项" + index + "的图片";
                    return checkMsg;
                }
                if (!i.imgStartX || !i.imgStartY) {
                    checkMsg = "请上传选项" + index + "的图片起点位置";
                    return checkMsg;
                }
                if (!i.imgStartWidth || !i.imgStartHeight) {
                    checkMsg = "请上传选项" + index + "的图片起点的宽高";
                    return checkMsg;
                }
                if (i.isRightAnswers == 1) {
                    if (!i.imgEndX || !i.imgEndY) {
                        checkMsg = "请上传选项" + index + "的图片终点位置";
                        return checkMsg;
                    }
                    if (!i.imgEndWidth || !i.imgEndHeight) {
                        checkMsg = "请上传选项" + index + "的图片终点的宽高";
                        return checkMsg;
                    }
                    if (!i.areaX || !i.areaY) {
                        checkMsg = "请上传选项" + index + "的正确区域位置";
                        return checkMsg;
                    }
                    if (!i.areaWidth || !i.areaHeight) {
                        checkMsg = "请上传选项" + index + "的正确区域宽高";
                        return checkMsg;
                    }
                }
            }

            return checkMsg
        },
        onSend: function() {
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            var val = this.validate();
            if (!val) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                alert(val);
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            bg = arguments.length > 2 ? arguments[2] : null;
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    console.log(res.data.key);
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },
        audioUpload: function(e, item, attr) {
            //校验规则
            //var _type = this.rules.audio.sources.type;

            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            // if (!_type.test(type)) {
            //     alert("您上传的文件类型错误，请检查后再上传！");
            //     return;
            // }
            if ((size / 1024).toFixed(2) > 500) {
                console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            } else {
                console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            }
            if ((size / 1024).toFixed(2) > 50) {
                console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
                alert("您上传的声音大小为：" + (size / 1024).toFixed(2) + "KB, 超过50KB上限，请检查后上传！");
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            this.postData(file, item, attr);
        },
        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addOption: function(item) {
            if (item) {
                this.configData.source.options.push(item)
            } else {
                this.configData.source.options.push('')
            }
        },
        delOption: function(item) {
            this.configData.source.options.remove(item)
        },
        setAnswer: function(item) {
            this.configData.source.right = item;
        },
        inputPlayNum: function() {
            var reg = new RegExp("^([1-9][0-9]{0,1}|[2][0]{0,1})$");
            var regResult = reg.test(Data.configData.source.palyTime);
            if (!regResult && Data.configData.source.palyTime != "#") {
                Data.configData.source.palyTime = "";
            }
        }
    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
