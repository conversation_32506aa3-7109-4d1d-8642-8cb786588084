var configData = {
	bg:'',
	desc:'weqwf appy family. My father i',
	title:'eegeg a eegeg appy family. My father i appy family. My father ippy family. My father i appy family. My father i',
	tg:[
		{
          content:"eegeg appy family. My father i appy family. My father i",
          title:"T116-v2.0weqwf appy family. My father i"
        },
        {
          content:"eegeg appy family. My father i appy family. My father i",
          title:"weqwf appy family. My father i"
        },
        {
          content:"eegeg appy family. My father i appy family. My father i",
          title:"weqwf appy family. My father i"
        }
	],
	level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
				content: "eegeg appy family. My father i appy family. My father i",
				title: "weqwf appy family. My father i"
			}]
	},
	source:{
    topicType: 2, //1 文字  2 图片
		options:[
			{
        wordsList:{
          letter:[{w:'a'},{w:'d d'},{w:'d'},{w:''}],
          color:[{isRed:true},{isRed:false},{isRed:false},{isRed:false}]   //单词拆分
        },   //单词拆分
				recordStatus:false,  //录音状态  true为添加录音按钮
				timeLong:10,   //录音时长
				words:'add',   //合并单词
				color:[-1,-1,-1,-1]
			},
			{
        wordsList:{
          letter:[{w:'t'},{w:'a'},{w:'lk'},{w:''}],
          color:[{isRed:false},{isRed:true},{isRed:false},{isRed:false}]   //单词拆分
        },
				recordStatus:false,
				timeLong:5,
				words:'talk'   ,
				color:[-1,-1,-1,-1]
			},
			{
        wordsList:{
        letter:[{w:'c'},{w:'o'},{w:'o'},{w:''}],
        color:[{isRed:false},{isRed:true},{isRed:false},{isRed:true}]   //单词拆分
        },
				recordStatus:false,
				timeLong:5,
				words:'cooer',
				color:[-1,-1,-1,-1]
			},
			// {
      //   wordsList:{
      //     letter:[{w:'m'},{w:'a'},{w:'th'},{w:'er'}],
      //     color:[{isRed:false},{isRed:true},{isRed:false},{isRed:false}]   //单词拆分
      //   },
			// 	recordStatus:false,
			// 	timeLong:5,
			// 	words:'mather',
			// 	color:[-1,-1,-1,-1]
			// }
		],
    optionsData: [
      {
        picsList:{
          letter:[
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            },
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            }
          ]
        },
      },
      {
        picsList:{
          letter:[
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            },
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            }
          ]
        },
      },
      {
        picsList:{
          letter:[
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            },
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            }
          ]
        },
      },
      {
        picsList:{
          letter:[
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            },
            {
              w: './image/home1.jpg'
            },
            {
              w: './image/home2.png'
            }
          ]
        },
      }
    ],
	}
};
