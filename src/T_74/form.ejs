<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>PSO0003AI_showtime幕布AI</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">PSO0003AI_showtime幕布AI</h3>

			<% include ./src/common/template/common_head %>

       <!-- 动画组合 -->
       <div class="c-group">
        <div class="c-title">动画组合</div>
        <div class="c-area upload img-upload details-filed-wrap">
          <div class="field-wrap animation-upload" v-for="(item,index) in configData.source.options">
            <div class="field-title">动画组合{{index+1}}</div>
            <div class="c-well">
              <!-- <label v-if="!item.isImgShow" class="title-label isAnimation">
                <span class="title-t">动画</span>
                <span class="details-btn" v-on:click="addAnimationFn(index)">添加动画</span>
              </label> -->
              <!-- 上传动画 -->
              <div class="label-img">
                <label>动画
                  <span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.options.length>1?true:false'></span>
                </label>
                <div class="details">
                  <div class='txt-info'>单词<em>*</em>
                    <input type="text" class='c-input-txt' placeholder="请在此输入玩具key" v-model="item.key">
                  </div>
                  <div class='txt-info'>直行&消失json动画<em>*</em>
                    <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
                    <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤5000KB </em></span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1" :id="'content-pic-'+index" @change="imageUpload($event,item,'img',5000)">
                  </div>
                  <div class="img-preview" v-if="item.img">
                    <!-- <img src="https://cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
                    <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                    </div>
                  </div>
                  <div class='txt-info'>转身json动画<em>*</em>
                    <label :for="'content-change-'+index" class="btn btn-show upload" v-if="!item.changeImg">上传</label>
                    <label :for="'content-change-'+index" class="btn upload re-upload" v-if="item.changeImg!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤5000KB </em></span>
                    <input type="file" v-bind:key="Date.now() + 1" class="btn-file" size="" isKey="2" :id="'content-change-'+index" @change="imageUpload($event,item,'changeImg',5000)">
                  </div>
                  <div class="img-preview" v-if="item.changeImg">
                    <!-- <img src="https://cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
                    <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.changeImg=''">删除</span>
                    </div>
                  </div>

                  <div class='txt-info'>出场音效<em>*</em>
                    <label :for="'content-audio-'+index" class="btn btn-show upload" v-if="!item.audioUrl">上传</label>
                    <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.audioUrl!=''?true:false">重新上传</label>
                    <!-- <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤5000KB </em></span> -->
                    <input type="file" accept=".mp3" v-bind:key="Date.now() + 2" class="btn-file" size="" isKey="3" :id="'content-audio-'+index" v-on:change="audioUploads($event,item,'audioUrl')">
                  </div>
                  <div class="img-preview" v-if="item.audioUrl">
                    <!-- <img src="https://cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
                    <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.audioUrl=''">删除</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button type="button" class="add-tg-btn" v-on:click="addSele()" v-if='configData.source.options.length<4?true:false'>+</button>
        </div>
       </div>
			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
