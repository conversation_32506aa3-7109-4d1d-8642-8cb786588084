"use strict";
import "../../common/js/common_1v1.js";

// import '../../common/js/commonFunctions.js'
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: "0", // 是否有授权按钮 1：是 0：否
  };
  $("#wrapper").css({
    "background-image": "url(./image/bj.png)",
  });
  $(".curtain-left").css({
    "background-image": "url(./image/left.png)",
  });
  $(".curtain-right").css({
    "background-image": "url(./image/right.png)",
  });
  $(".door").css({
    "background-image": "url(./image/door.png)",
  });
  $(".doll-bj").css({
    "background-image": "url(./image/r.jpg)",
  });
  // if (configData.bg == "") {
  //   $(".wrapper").css({
  //     "background-image": "url(./image/bj.png)",
  //   });
  // }else {
  //   $(".wrapper").css({
  //     "background-image": `url(${configData.bg})`,
  //   });
  // }
  // $("#btn-list").hide();
  if (SDK.getUserType() == "tea") {
    // $("#btn-list").show();
  }
  let options = configData.source.options, //素材
    isCheck = true; //是否可点击

  //初始化
  var animations = 0;
  var animationsTwo = 0;

  // 开幕
  // window.generalTplData({type: 'openCurtain'})
  $(".btn-open").on("click", function () {
    window.generalTplData({ actionType: "openCurtain" });
  });

  // 关幕
  // window.generalTplData({type: 'closeCurtain'})
  $(".btn-close").on("click", function () {
    window.generalTplData({ actionType: "closeCurtain" });
  });

  // 执行娃娃平移动画
  // window.generalTplData({type: 'open', key:"monster"})
  $(".btn-monster").on("click", function () {
    window.generalTplData({ actionType: "open", key: options[1].key });
  });

  // 执行娃娃平移结束等待指令
  // window.generalTplData({type: 'close'})
  $(".btn-over").on("click", function () {
    window.generalTplData({ actionType: "close" });
  });

  // H5推送给原生交互
  //  if (isSync) {
  //   console.log("over");
  // SDK.bindSyncEvt({
  //   method: "ai_event",
  //   syncName: "over",
  // });
  // }

  window.generalTplData = function (message) {
    $("#doll-animation1").show();
    if (message.actionType == "openCurtain") {
      curtainAnimations();
      $("#doll-animation1").hide();
    } else if (message.actionType == "closeCurtain") {
      close_curtain();
      $("#doll-animation1").hide();
    } else if (message.actionType == "closeCurtain") {
      close_curtain();
    } else if (message.actionType == "open") {
      if (animations != 0) {
        animations.destroy(); // 销毁动画实例
        // animationsTwo.destroy()
      }
      getdata(message.key);
    } else {
      // getdata(message.key);
      translationAnimationsTwo(message);
    }
    SDK.setEventLock();
  };
  //获取动画的宽高，并初始化动画
  function getdata(item) {
    if (item === undefined) return;
    for (let i = 0; i < options.length; i++) {
      if (options[i].key == item) {
        item = options[i];
        break;
      }
    }
    translationAnimations(item);
    console.log("itemitem", item);
    playLottieAnimation(item.img);
  }
  function playLottieAnimation(item) {
    console.log("itemitem", item);
    $("#doll-animation2").hide();
    $.get(item, {}, function (res) {
      if (res.w) {
        console.log("宽高", res.w, res.h);
        $(`#doll-animation1`).css({
          width: res.w / 100 + "rem",
          height: res.h / 100 + "rem",
        });
        animations = lottie.loadAnimation({
          container: document.getElementById(`doll-animation1`),
          renderer: "svg",
          loop: true,
          autoplay: false,
          path: item,
        });
        animations.play(); //播放动画
        animations.addEventListener("complete", function () {
          console.log("动画播放完成 1");
        });
      }
    });
  }
  function playLottieAnimationTwo(item) {
    console.log("itemitem", item);
    $.get(item, {}, function (res) {
      if (res.w) {
        console.log("宽高", res.w, res.h);
        $(`#doll-animation2`).css({
          width: res.w / 100 + "rem",
          height: res.h / 100 + "rem",
        });
        animationsTwo = lottie.loadAnimation({
          container: document.getElementById(`doll-animation2`),
          renderer: "svg",
          loop: false,
          autoplay: false,
          path: item,
        });
        animationsTwo.play();
        animationsTwo.addEventListener("complete", function () {
          console.log("动画播放完成");
          $("#doll-animation1").hide();
          $("#doll-animation2").show();
        });
      }
    });
  }

  function translationAnimations(item) {
    $(".itemAudio").attr("src", item.audioUrl);

    SDK.playRudio({
      index: $(".itemAudio")[0],
      syncName: $(".itemAudio").attr("data-syncaudio"),
    });
    $("#doll-animation").animate({ left: "20%" }, 2000, function () {
      console.log("第一步平移完成");
      playLottieAnimationTwo(item.changeImg);
      setTimeout(function () {
        SDK.pauseRudio({
          index: $(".itemAudio")[0],
          syncName: $(".itemAudio").attr("data-syncaudio"),
        });
      }, 1000);
      // $("#doll-animation1").hide();
      // $("#doll-animation2").show();
    });

    // 玩具开始上报埋点
    trackify.pind_send({
      key: item.key,
      type: "begin",
      time: new Date().getTime(),
    });
    // 把当前的item.key保存下来，在玩具结束的时候使用
    window.currentKey = item.key;
  }
  function translationAnimationsTwo(item) {
    $("#doll-animation2").hide();
    $("#doll-animation1").show();
    $("#doll-animation").animate({ left: "40%" }, 2000, function () {
      console.log("第二步平移完成", item);
      $("#doll-animation2").hide();
      $("#doll-animation1").hide();
      $(this).animate({ left: "0" }, 0);
      if (isSync) {
        console.log("over");
        SDK.bindSyncEvt({
          method: "ai_event",
          syncName: "over",
        });
      }
      animationsTwo.destroy();
      animations.destroy(); 
      console.log("animationsTwo",animationsTwo,animations)
    });

    // 玩具结束上报埋点
    trackify.pind_send({
      key: window.currentKey,
      type: "end",
      time: new Date().getTime(),
    });
  }

  function curtainAnimations() {
    $("#curtain1").animate({ width: 0 }, 2200);
    $("#curtain2").animate({ width: 0 }, 2200);
    SDK.playRudio({
      index: $(".openAudio")[0],
      syncName: $(".openAudio").attr("data-syncaudio"),
    });
    SDK.setEventLock();
  }
  function close_curtain() {
    $("#curtain1").animate({ width: "51%" }, 2000);
    $("#curtain2").animate({ width: "51%" }, 2000);
    SDK.setEventLock();
  }
});
