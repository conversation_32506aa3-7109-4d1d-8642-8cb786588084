@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}
.hide {
  display: none;
}
.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;
  .desc {
    top: 0.6rem;
  }
  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}
#wrapper {
  text-align: center;
  margin: 0 auto;
  padding: 0px;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 20;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}
.bj {
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}
.curtain {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 300;
}
.curtain-left {
  width: 51%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 300;
}
.curtain-right {
  width: 51%;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 300;
}
.door {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 300;
}
.doll-list {
  width: 100%;
  height: 100%;
  position: relative;
}
.doll-bj {
  width: 260px;
  height: 400px;
  position: absolute;
  top: 62%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}
#doll-animation {
  position: absolute;
  bottom: 1.1rem;
  left:0;
}
#btn-list {
  position: absolute;
  bottom: 20px;
  left:40%;
  z-index: 500;
  display: flex;
  div {
   padding: 20px 20px;
   font-size: 12px;
   color: #ffffff;
   background: #b43104;
   border-radius: 5px;
   margin-right: 20px;
   cursor: pointer;
  }
}

