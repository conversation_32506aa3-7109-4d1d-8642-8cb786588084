"use strict"
import '../../common/js/common_1v1.js'
import './drag.js'

$(function () {
  window.h5Template = {
    hasPractice: '1'
  }
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/bg.jpg)'
    })
  }
  let staticData = configData.source;
  let answePos = []; //答案区的盒子位置
  let answeCenterPos = [] //答案区盒子的中心位置
  let answerArr = [] //存储交互中关键的数据
  let chooseArr = [] //提交后已选中的答案
  let targetCardPos = []

  let articleHtml = '' //回答文章
  let conTxt = staticData.conTxt; //文本内容，带下划线的
  let allOptions = [] //存储所有选项
  let interfereTxt = staticData.interfereTxt; //干扰选项
  let textType = staticData.textType; //大小号字
  let WholeText = staticData.WholeText; //完整文本
  let answerText = staticData.answerText;
  let rightTxt = [] //存储正确选项
  window.isDragFinish = true; //是否拖拽结束
  init();

  function init() {
    doByRole();
    renderDragArea();
    disorderOptions();
    renderOptions();
    waitChooseWidth();
    getAnsBoxPos();
    getStartPos();
    dragFun();
    initHand();
    initAudio();
    spriteImg();
  }

  /**
   *
   * 动画雪碧图替换
   */
  function spriteImg() {
    if (!staticData.spriteImg) return
    $(".sprite").css({
      'background-image': 'url('+ staticData.spriteImg +')'
    })
  }


  // 身份判断
  function doByRole() {
    if (!isSync) {
      $(".doneTip p").html('Key: ' + answerText).show();
      return false;
    }
    if (window.frameElement.getAttribute('user_type') == 'stu') {
      $(".doneTip").hide();
    } else {
      $(".doneTip p").html('Key: ' + answerText).show();
    }
  }
  //渲染句子区域
  function renderDragArea() {
    var waitIndex = 0;
    var liIndex = 0;
    conTxt.forEach(function (item, i) {
      let fragment = item.insert;
      fragment = fragment.replace('\n', '');
      let textArr = fragment.split(' ');
      textArr.forEach(function (text, k) {
        if (text) {
          if (item.hasOwnProperty('attributes') && item.attributes.hasOwnProperty('underline')) {
            //正则转换答案中中文符号
            let trueAnswear = fragment.trim();
            articleHtml += `<li class="${'list'+liIndex} list listOptions waitAddOptions" data-syncactions="list${liIndex}" waitindex="${waitIndex}"></li>`;
            waitIndex++;
            rightTxt.push(trueAnswear);
            allOptions.push(trueAnswear)
          } else {
            articleHtml += `<li class="${'list'+liIndex} list listOptions staticOptions" data-syncactions="list${liIndex}">${text}</li>`
          }
          liIndex++;
        }
      })
    });
    $('.conTxt-ul').html(articleHtml);
  }

  //拼接打乱选项顺序
  function disorderOptions() {
    for (var i = 0; i < interfereTxt.length; i++) {
      if(interfereTxt[i].text) {
        allOptions.push(interfereTxt[i].text);
      }
    }
    allOptions = reSort(allOptions, parseInt(staticData.random * allOptions.length))
    allOptions.splice(1, 0, allOptions.shift());
    allOptions.splice(1, 0, allOptions.pop());
  };

  //渲染选项区域
  function renderOptions() {
    var optionsTxt = "";
    for (var i = 0; i < allOptions.length; i++) {
      optionsTxt += `<li class="${'index'+i} card cardOptions sourceCard" data-syncactions="card${i}">${allOptions[i]}</li>`;
    }
    $(".optionTxt-ul").html(optionsTxt);
    bigOrSmall();
    let paddingDistance =  (19.2 - 16) / 2;
    var allWidth = 0;
    $(".optionTxt-ul li").each(function (index) {
        allWidth += $(this).outerWidth() / window.base + 0.4;
    });
    paddingDistance = (19.2 - allWidth) / 2;
    if(allWidth >16) {
      paddingDistance = (19.2 - 16) / 2;
    }
    $(".optionTxt-ul li").each(function (index) {
      $(this).css({
        'left': paddingDistance + 'rem',
        'top': '7.7rem'
      })
      $(this).attr('data_left', paddingDistance)
      $(this).attr('data_top', '7.7rem');
      paddingDistance += $(this).outerWidth() / window.base + 0.4;
    })

  }


  //处理未填写单词处宽度(获取最宽选项的宽度)
  function waitChooseWidth() {
    $('.waitAddOptions').css({
      width: '2rem',
      border: '0.05rem dashed #fff',
      borderRadius: '0.1rem'
    })
  };

  //大小号字处理
  function bigOrSmall() {
    if (textType == 1) {
      $('.listOptions').addClass("smallText");
      $('.waitAddOptions').css({
        'height': "1.2rem",
        'fontSize': '0.8rem'
      });
      $('.card').css({
        'height': "1.1rem",
        'lineHeight': '1.1rem',
        'fontSize': '0.8rem'
      });
    }
    if (textType == 2) {
      $('.listOptions').addClass("bigText");
      $('.waitAddOptions').css({
        'height': "2.1rem"
      });
      $('.card').css({
        'height': "2rem",
        'lineHeight': '2rem',
        'fontSize': '1rem'
      });
    }
  }

  //当前高亮区域
  // function currentLight(index) {
  //   $(".waitAddOptions").css({
  //     border: '0.04rem dashed #fff',
  //   }).removeClass("current");
  //   //说明是最后一个
  //   if (!index) {
  //     // index = 0;
  //   }
  //   //说明已经有选项放进去了
  //   if ($(".waitAddOptions").eq(index).hasClass('noDrag')) {
  //     $(".waitAddOptions").each(function () {
  //       if (!$(this).hasClass("noDrag")) {
  //         index = $(this).attr("waitindex");
  //       }
  //     })
  //   }
  //   //已填满
  //   if ($(".list.noDrag").length == $('.waitAddOptions').length) {
  //     return false;
  //   }
  //   $(".waitAddOptions").eq(index).css({
  //     border: '0.04rem dashed #fff601',
  //   }).addClass('current');
  // }
  //存储选项区初始化坐标
  function getAnsBoxPos() {
    answePos = []
    answeCenterPos = []
    $(".card").each(function (i, arr) {
      let L = $(arr).offset().left - $('.container').offset().left
      let T = $(arr).offset().top - $('.container').offset().top
      answePos.push({
        left: L / window.base,
        top: T / window.base
      })
      answeCenterPos.push({
        centerL: L / window.base,
        centerT: T / window.base
      });
    });
  }
  //存储初始化位置
  function getStartPos() {
    targetCardPos = []
    let lis = $(".card")
    for (let i = 0; i < lis.length; i++) {
      targetCardPos.push({
        left: ($(".card").eq(i).offset().left - $(".container").offset().left) / window.base,
        top: ($(".card").eq(i).offset().top - $(".container").offset().top) / window.base
      })
    }
  }
  //初始化音频
  function initAudio() {
    $(".rightAudio").attr("src", staticData.successAudio);
    $(".wrongAudio").attr("src", staticData.fialAudio);
    if (staticData.audio) {
      $(".metrialAudio").attr("src", staticData.audio);
    } else {
      $(".audioImg,.audioList").hide();
    }

  }
  // 拖拽卡片
  var isDrag = false; //拖拽还是点击
  var dragXy = {
    x: 0,
    y:0,
  }
  function dragFun() {
    $('.cardOptions').drag({
      before: function (e) {
        dragXy.x = e.pageX / window.base;
        dragXy.y = e.pageY / window.base;
        isDrag = false;
        isDragFinish = false;
        dragStyle($(this));
      },
      process: function (e) {
        isDrag = true;
        let that = $(this);
        let pageX = e.pageX / window.base;
        let pageY = e.pageY / window.base;
        for (let i = 0; i < $(".waitAddOptions").length; i++) {
          let t2 = $(".waitAddOptions").eq(i).offset().top / window.base,
            l2 = $(".waitAddOptions").eq(i).offset().left / window.base,
            r2 = ($(".waitAddOptions").eq(i).offset().left + $(".waitAddOptions").eq(i).width()) / window.base,
            b2 = ($(".waitAddOptions").eq(i).offset().top + $(".waitAddOptions").eq(i).height()) / window.base;
          if ((pageY > t2 && pageY < b2) && (pageX > l2 && pageX < r2)) { // 表示碰上
            if ($(".waitAddOptions").eq(i).hasClass('noDrag')) {
              return false;
            }
            $(".waitAddOptions").eq(i).css({
              border: '0.05rem dashed #fff601',
            }).addClass('current');
            return false;
          } else {
            $(".waitAddOptions").css({
              border: '0.05rem dashed #fff',
            }).removeClass("current");

          }
        }
      },
      end: function (e) {
        let that = $(this),
          runIntoNum = 0,
          whichBox;
        let pageX = e.pageX / window.base;
        let pageY = e.pageY / window.base;
        console.log(dragXy.y, pageY)
        //为了做移动端点击事件的兼容，通过判断距离
        if(Math.abs((dragXy.y - pageY >= 0)) && (Math.abs(dragXy.y - pageY) < 0.2) ) {
          isDrag = false;
        }
        for (let i = 0; i < $(".waitAddOptions").length; i++) {
          let t2 = $(".waitAddOptions").eq(i).offset().top / window.base,
            l2 = $(".waitAddOptions").eq(i).offset().left / window.base,
            r2 = ($(".waitAddOptions").eq(i).offset().left + $(".waitAddOptions").eq(i).width()) / window.base,
            b2 = ($(".waitAddOptions").eq(i).offset().top + $(".waitAddOptions").eq(i).height()) / window.base;
          if ((pageY > t2 && pageY < b2) && (pageX > l2 && pageX < r2)) { // 表示碰上
            runIntoNum++
            if (runIntoNum < 2) {
              whichBox = $(".waitAddOptions").eq(i).attr("data-syncactions").split('list')[1];
            }
          } else {

            if (!that.hasClass("draged")) {
              if (!$(".waitAddOptions").eq(i).hasClass("noDrag")) {
                if (whichBox === undefined) {
                  // whichBox = $(".waitAddOptions.current").attr("data-syncactions").split('list')[1];
                  whichBox = $(".waitAddOptions").eq(i).attr("data-syncactions").split('list')[1];

                }
              }
            }
          }
        }
        if (!isSync) {
          $(this).trigger('syncDragEnd', {
            runIntoNum: runIntoNum,
            whichBox: whichBox,
            isDrag: isDrag
          })
          return
        }
        SDK.bindSyncEvt({
          index: $(this).data('syncactions'),
          eventType: 'dragEnd',
          method: 'drag',
          left: $(this).data('startPos').left,
          top: $(this).data('startPos').top,
          pageX: '',
          pageY: '',
          syncName: 'syncDragEnd',
          otherInfor: {
            chooseArr: chooseArr,
            answerArr: answerArr,
            runIntoNum: runIntoNum,
            whichBox: whichBox,
            isDrag: isDrag
          },
          recoveryMode: 1
        })
      }
    })
  }
  //拖拽回调
  $('.page').on('syncDragEnd', '.card', function (e, message) {
    //console.log(answerArr, 'youlaile')
    let runIntoNum
    let whichBox
    let that = $(this)
    if (!isSync) {
      runIntoNum = message.runIntoNum
      whichBox = message.whichBox
      isDrag = message.isDrag
    } else {
      //线上拖拽不做拖拽状态恢复，只做当前页面恢复
      let obj = message.otherInfor;
      runIntoNum = obj.runIntoNum
      whichBox = obj.whichBox
      isDrag = obj.isDrag
      if (message == undefined || message.operate == 1) {

      } else {
        //恢复拖拽状态
        rebackPage(obj, 'drag')
        answerArr = obj.answerArr
        chooseArr = obj.chooseArr
        return
      }
    }
    //判定在不在盒子内  if 在 else 不在
    if (runIntoNum >= 2 || runIntoNum == 0) {
      if (that.hasClass("draged") || isDrag) {
        goBackPos(that, isDrag) //归位
        resetConLeft(that.attr("data_index"));
      } else {
        toNewPos(that, whichBox, 1, isDrag) //放入目标盒子
        resetConLeft(whichBox);
      }
    } else {
      if ($(".list").eq(whichBox).hasClass('noDrag')) {
        //置空原有答案数组对应的内容
        goBackPos(that, isDrag) // 盒子中已有卡片 归位
        resetConLeft(whichBox);

      } else {
        toNewPos(that, whichBox, 2, isDrag) //放入目标盒子
        resetConLeft(whichBox);
      }
    }
    SDK.setEventLock()
  });
  //（实时计算改变位置和宽度）
  function resetConLeft(whichBox) {
    $('.card.draged').each(function () {
      toNewPos($(this), $(this).attr('data_index') - 1, 1, isDrag) //放入目标盒子
    })
  }
  //放入目标盒子
  function toNewPos(obj, ansIndex, status, isDrag) {
    console.log("放入目标盒子了", status, isDrag)
    //获取目标盒子当前的位置
    if ($(".list").eq(ansIndex).length == 0) return;
    let t = ($(".list").eq(ansIndex).offset().top - $(".container").offset().top) / window.base;
    let l = ($(".list").eq(ansIndex).offset().left - $(".container").offset().left) / window.base + 0.15;
    let Zindex = overflowZindex(t);
    let ansBoxIndex = $(".card").eq(obj.index()).attr('data_index');
    if (!!ansBoxIndex) {
      $(".list").eq(ansBoxIndex - 1).removeClass('noDrag')
    }
    if (isDrag) {
      $(".card").eq(obj.index()).css({
        left: l + "rem",
        top: t + "rem",
      }).addClass("draged").attr("data_index", Number(ansIndex) + 1);
      isDragFinish = true;
    } else {
      $(".card").eq(obj.index()).stop().animate({
        left: l + "rem",
        top: t + "rem",
      },function() {
         isDragFinish = true;
      }).addClass("draged").attr("data_index", Number(ansIndex) + 1);
    }
    dragPutBox($(".card").eq(obj.index()),Zindex);
    $(".list").eq(ansIndex).addClass('noDrag');
    //临时
    $(".list").eq(ansIndex).css({
      width: $(".card").eq(obj.index()).outerWidth() / window.base + 0.3 + 'rem',
    })
    showDone();
    breakAns(obj, status)
  }
  //zindex 处理  选项是浮动的  为了做题干超出部分隐藏  只能判断距离改变 zindex
  function overflowZindex(t) {
    let Zindex = 100; //4.9是题干区域
    if(textType == 1 && t >= 4.9){
      Zindex = -1;
    }
    if(textType == 2 && t >= 3.9){
      Zindex = -1;
    }
    return Zindex;
  }
  //返回原位置
  function goBackPos(obj) {
    console.log("返回原位置", isDrag)
    let ansBoxIndex = $(".card").eq(obj.index()).attr('data_index');
    if (!!ansBoxIndex) {
      $(".list").eq(ansBoxIndex - 1).removeClass('noDrag');
      //临时
      $(".list").eq(ansBoxIndex - 1).css({
        width: '2rem'
      })
    }
    if (targetCardPos[obj.index()]) {
      if (isDrag) {
        $(".card").eq(obj.index()).stop().animate({
          left: targetCardPos[obj.index()].left + "rem",
          top: targetCardPos[obj.index()].top + "rem",
        },function() {
          isDragFinish = true;
       }).removeClass("draged").attr("data_index", '');
      } else {
        $(".card").eq(obj.index()).stop().animate({
          left: targetCardPos[obj.index()].left + "rem",
          top: targetCardPos[obj.index()].top + "rem",
        },function() {
          isDragFinish = true;
       }).removeClass("draged").attr("data_index", '');
      }
    }
    dragStyle($(".card").eq(obj.index()));
    showDone();
    breakAns(obj, '')
  }
  //答案放到盒子里样式
  function dragPutBox(ele,Zindex) {
    ele.removeClass("sourceCard").css({
      'zIndex': Zindex ? Zindex : 100,
    });
    $(".waitAddOptions").css({
      border: '0.05rem dashed #fff',
    }).removeClass("current");
  }
  //拖动答案时和返回原位置的样式
  function dragStyle(ele) {
    ele.addClass("sourceCard").css({
      width: 'auto',
      'zIndex': 200
    });
    $(".waitAddOptions").css({
      border: '0.05rem dashed #fff',
    }).removeClass("current");
  };
  //存储答案
  function breakAns(obj, status) {
    if (status == 1 || status == 2) {
      answerArr[$(obj).attr('data_index') - 1] = $(obj).text()
      chooseArr[$(obj).attr('data_index') - 1] = $(obj).index()
    }
    //刷新答案
    $('.list').each(function (i, arr) {
      if (!$(arr).hasClass("noDrag")) {
        answerArr[i] = ''
        chooseArr[i] = -1
      }
    })
  }
  //初始化小手位置
  function initHand() {
    $(".hand").css({
      left: answeCenterPos[0].centerL + $(".card").width() / 2 / window.base + 'rem',
      top: answeCenterPos[0].centerT + $(".card").height() / 2 / window.base + 'rem'
    })
  }

  //结束雪碧图持续时间
  function spriteFun() {
    return {
      'win': function () {
        $('.sprite').addClass("whenWin");
        $('.whenWin').css({
          "webkitAnimationDuration": staticData.spriteduration + 's',
        });
        SDK.playRudio({
          index: $('.rightAudio').get(0),
          syncName: $('.rightAudio').attr("data-syncaudio")
        });
        if(!staticData.palyTime) {
          staticData.palyTime = 1;
        }
        if(staticData.palyTime == '#') {
          staticData.palyTime = 'infinite';
        }
        $(".whenWin").on('animationend  webkitAnimationEnd', function () {
          $('.whenWin').css({"webkitAnimationDuration": '',});
          $('.whenWin').addClass("whenJump").removeClass('whenWin');
          if (staticData.palyTime != "infinite") {
            setTimeout(function () {
              $(".whenJump").removeClass("whenJump");
              $('.sprite').css({
                left: '85%',
                'background-position': '100% 0'
              })
            }, 1000 * staticData.palyTime)
          }
        })
      },
      'lose': function () {
        $('.sprite').addClass("whenLose");
        SDK.playRudio({
          index: $('.wrongAudio').get(0),
          syncName: $('.wrongAudio').attr("data-syncaudio")
        })
      }
    }
  }

  //显示done提交按钮
  function showDone() {
    if ($(".list.noDrag").length == $('.waitAddOptions').length) {
      submitBtn = true;
      $(".submitBtn img").attr("src", './image/Done.png');
    } else {
      submitBtn = false;
      $(".submitBtn img").attr("src", './image/Done1.png');
    }
  }
  //老师点击 hint 按钮
  let hintStatus = true;
  $(".hint-btn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (hintStatus) {
      hintStatus = false;
      if (!isSync) {
        $(this).trigger("btnClick");
        return;
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'btnClick',
        });
      }
    }
  })
  $(".hint-btn").on('btnClick', function (e, message) {
    $('.hand').addClass("handAnimation");
    $(".mask").show();
    $(".hand").on('animationend  webkitAnimationEnd', function () {
      $('.hand').removeClass("handAnimation");
      $(".mask").hide();
    })
    hintStatus = true;
    SDK.setEventLock();
  });

  // //提交
  let submitBtn = false;
  $(".submitBtn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    if (submitBtn) {
      submitBtn = false;
      if (!isSync) {
        $(this).trigger('submitClickSync')
        return
      }
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'submitClickSync',
        recoveryMode: 1,
        otherInfor: {
          chooseArr: chooseArr,
          answerArr: answerArr,
        }
      });
    }
  });
  $(".submitBtn").on("submitClickSync", function (e, message) {
    if (!isSync) {
      submitAns()
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      if (message == undefined || message.operate == 1) {
        submitAns(message)
      } else {
        //直接恢复页面 提交答案后出现掉线
        answerArr = obj.answerArr
        chooseArr = obj.chooseArr
        rebackPage(obj, 'submit')
        SDK.setEventLock()
      }
    }
  });
  //提交结果
  function submitAns(message) {
    $(".submitBtn").hide();
    $(".waitAddOptions").css({
      border: 'none'
    });
    $(".doneTip").hide();
    let curLength = 0,
      rightLength = 0;
    for (let i = 0; i < answerArr.length; i++) {
      if (answerArr[i]) {
        if (answerArr[i] != rightTxt[curLength]) {
          $('.card.draged').each(function () {
            if ($(this).text() == answerArr[i]) {
              $(this).css({
                color: '#cc0000',
                'text-stroke': '0.02rem #fff',
                '-webkit-text-stroke': '0.02rem #fff'
              })
            }
          })
        } else {
          rightLength++;
        }
        curLength++;
      }
    }
    if (rightLength == rightTxt.length) {
      spriteFun().win();
    } else {
      spriteFun().lose();
    }
    $('.cardOptions').removeClass("cardOptions");
    $(".sprite").on('animationend  webkitAnimationEnd', function () {
      //显示正确答案
      $(".showAns span").html(WholeText);
      $(".showAns").css({
        'zIndex': 202
      });
    })
  }

  //点击播放音频事件
  let isStartBtn = true;
  $('.audioImg').on('click touchend', function (e) {
    if (e.type == "touchend") {
      e.preventDefault()
    }
    if (isStartBtn) {
      isStartBtn = false;
      if (!isSync) {
        $(this).trigger("audioSync");
        return
      }
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data("syncactions"),
        eventType: 'click',
        method: 'event',
        syncName: 'audioSync',
      });
    }
  });

  //老师端声音播放同步到学生端
  $('.audioImg').on("audioSync", function (e, message) {
    var $audio = $(".metrialAudio");
    var $img = $(".audioImg");
    if (!isSync) {
      SDK.playRudio({
        index: $audio.get(0),
        syncName: $audio.attr("data-syncaudio")
      });
      if ($img.length != 0) {
        $img.attr("src", $img.attr("src").replace(".png", ".gif"));
        //播放完毕img状态
        $audio.get(0).onended = function () {
          $img.attr("src", $img.attr("src").replace(".gif", ".png"));
          //flag = true;//恢复点击
        }.bind(this);
      }
    } else {
      if ($(window.frameElement).attr('id') === 'h5_course_self_frame' && (message == undefined || message.operate == 1)) {
        SDK.playRudio({
          index: $audio.get(0),
          syncName: $audio.attr("data-syncaudio")
        });
        if ($img.length != 0) {
          $img.attr("src", $img.attr("src").replace(".png", ".gif"));
          //播放完毕img状态
          $audio.get(0).onended = function () {
            $img.attr("src", $img.attr("src").replace(".gif", ".png"));
            //flag = true;//恢复点击
          }.bind(this);
        }
      }
    }
    SDK.setEventLock();
    isStartBtn = true;
  });

  // //掉线页面恢复
  function rebackPage(data, style) {
    //渲染答案区结果和对应的卡片状态
    let ansIndex = data.chooseArr;
    console.log('断线重连')
    console.log(ansIndex)
    for (let i = 0; i < ansIndex.length; i++) {
      if (ansIndex[i] != -1) {
        //获取目标盒子当前的位置
        $(".list").eq(i).addClass('noDrag')
        let t = ($(".list").eq(i).offset().top - $(".container").offset().top) / window.base;
        let l = ($(".list").eq(i).offset().left - $(".container").offset().left) / window.base + 0.15;
        let Zindex = overflowZindex(t);
        //恢复卡片对应的位置
        $(".card").eq(ansIndex[i]).css({
          left: l + "rem",
          top: t + "rem",
        }).addClass("draged").attr("data_index", i + 1);

        dragPutBox($(".card").eq(ansIndex[i]), Zindex);
        //临时
        $(".list").eq(i).css({
          width: $(".card").eq(ansIndex[i]).outerWidth() / window.base + 0.3 + 'rem'
        })
        showDone();
        if (style == 'submit') {
          $(".card").eq(ansIndex[i]).removeClass('cardOptions') //撤销已选卡片的cardOptions类名
        }
      }
    }
    if (style == 'submit') {
      submitAns();
      submitBtn = false; //不可再点击
    }
    SDK.setEventLock()
  }
})
