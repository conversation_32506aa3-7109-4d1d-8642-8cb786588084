@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';

@mixin setEle($l:0rem, $t:0rem, $w:0rem, $h:0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.hide {
  display: none;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.container {
  background-size: auto 100%;
  position: relative;
}

.point {
  position: absolute;
  width: 0.05rem;
  height: 0.05rem;
  background: red;
}

.sec-middle {
  width: 100%;
  height: 6.62rem;
}

audio {
  width: 0;
  height: 0;
  opacity: 0;
  position: absolute;
}

.lookTitle {
  width: 100%;
  height: 1.5rem;
  font-size: 0.8rem;
  text-align: center;
  font-weight: 700;
  color: #333333;

  span {
    display: inline-block;
    margin-top: 0.45rem;
  }
}

.main {
  width: 19.2rem;
  height: 10.8rem;
  position: absolute;
  top: 0;
  left: 0;
  padding-top: 1.5rem;
  box-sizing: border-box;

  .title {
    width: 100%;
    height: 1rem;
    line-height: 1rem;
    text-align: center;
    font-size: 0.36rem;
  }
}

.page {
  position: absolute;
  top: 0;
  left: 0;
  width: 18rem;
  height: 100%;
  overflow: hidden;

  .conTxt {
    // width: 12rem;
    // height: 3rem;
    // margin: 2.14rem auto;
    width: 15.76rem;
    height: 3.1rem;
    position: relative;
    margin: 2rem 1.7rem;
    overflow: hidden;
    .conTxt-ul {
      width: 14.2rem;
      max-height: 3rem;
      position: absolute;
      left: 50%;
      // top:20%;
      transform: translate(-50%, -0%);
      top: 0.21rem;

      li {
        vertical-align: top;
        display: inline-block;
        padding: 0 0.1rem;
        text-align: center;
        margin-left: 0.18rem;
        margin-bottom: 0.2rem;
        font-family: 'Century Gothic';
      }

      .staticOptions {
        -webkit-text-stroke: 0.04rem #522a1d;
        text-stroke: 0.04rem #522a1d;
        color: #FFE950;
        font-weight: bolder;
        // background: rgba(255, 255, 255, 0.3);
      }

      .smallText {
        height: 1.1rem;
        line-height: 1.1rem;
        font-size: 0.8rem;
      }

      .bigText {
        height: 2rem;
        line-height: 2rem;
        font-size: 1rem;
      }
    }
  }

  .optionTxt {
    width: 16rem;
    height: 2rem;
    margin-left: 1.59rem;

    .optionTxt-ul {
      width: 100%;
      height: 2rem;
      overflow: hidden;

      li {
        display: inline-block;
        position: absolute;
        cursor: pointer;
        text-align: center;
        font-size: 0.6rem;
        -webkit-text-stroke: 0.04rem #522a1d;
        text-stroke: 0.02rem #522a1d;
        color: #ffffff;
        font-weight: bolder;
        font-family: 'Century Gothic';
        min-width: 1.6rem;
        border-radius: 0.1rem;
      }

      .sourceCard {
        background: #d98649;
        box-shadow: 0rem 0.05rem 0rem #BD7A1C;
        font-weight: bolder;
        padding: 0 0.1rem;
      }
    }
  }
}

.doneTip {
  width: 12rem;
  height: 1rem;
  border-radius: 0.2rem .2rem 0 0;
  position: absolute;
  margin-left: -6rem;
  left: 50%;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  // display: none;
  font-size: .3rem;

  p {
    height: 100%;
    width: 9.14rem;
    line-height: 1rem;
    text-align: center;
    padding-left: 0.53rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .btn {
    position: absolute;
    top: .2rem;
    padding: 0.17rem 0.26rem;
    color: #fff;
    text-align: center;

    line-height: 0.23rem;
    border-radius: .3rem;
    cursor: pointer;
  }

  .hint-btn {
    background: #f1a91e;
    left: 9.6rem;
  }
}

//小手点击动画
.hand {
  position: absolute;
  left: 1.4rem;
  top: 1.3rem;
  margin-left: -0.35rem;
  background: url('../image/hands.png');
  background-size: 7.2rem 1.8rem;
  cursor: pointer;
  opacity: 0;
  z-index: 202;
  width: 0px;
  height: 0px;
}

.handAnimation {
  opacity: 1;
  width: 1.8rem;
  height: 1.8rem;
  left: 5.5rem;
  top: 6rem;
  animation-name: handLclick, handHide;
  animation-duration: 1s, 1S;
  animation-timing-function: steps(4, end), linear;
  animation-delay: 1s, 4s;
  animation-iteration-count: 3, 1;
  animation-fill-mode: forwards, forwards;
  animation-direction: normal, normal;


  -webkit-animation-name: handLclick, handHide;
  -webkit-animation-duration: 1s, 1S;
  -webkit-animation-timing-function: steps(4, end), linear;
  -webkit-animation-delay: 1s, 4s;
  -webkit-animation-iteration-count: 3, 1;
  -webkit-animation-fill-mode: forwards, forwards;
  -webkit-animation-direction: normal, normal;
}

// 遮罩层
.mask {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 201;
  display: none;
  background: rgba(0, 0, 0, 0.3);

  .right-top-hint {
    position: absolute;
    right: 0;
    top: 0;
    width: 1.95rem;
    height: 0.67rem;
    background: rgba(70, 70, 70, 1);
    opacity: 0.63;
    border-bottom-left-radius: 0.22rem;
    color: #fff;
    line-height: 0.67rem;
    font-size: 0.43rem;

    img {
      width: 0.51rem;
      height: 0.53rem;
      margin-left: 0.22rem;
    }
  }
}

// 提交按钮
.submitBtn {
  position: absolute;
  top: 5.67rem;
  left: 8.30rem;
  width: 2.91rem;
  cursor: pointer;

  img {
    width: 100%;
  }
}

// 完整句子
.showAns {
  position: absolute;
  left: 50%;
  top: 6.07rem;
  width: 12rem;
  height: 3rem;
  transform: translate(-50%, 0);
  background: #fff;
  box-shadow: 0.04rem -0.1rem 0.01rem #cccccc;
  border: 0.01rem solid #fff;
  border-radius: 0.2rem;
  text-align: center;
  overflow: hidden;
  display: table;
  z-index: -1;

  span {
    display: table-cell;
    vertical-align: middle;
    font-size: 0.6rem;
    font-weight: 400;
    line-height: 120%;
  }

}

@keyframes handLclick {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 133% 0;
  }
}

@-webkit-keyframes handLclick {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 133% 0;
  }
}

@keyframes handHide {
  0% {
    opacity: 0;
    width: 0px;
  }

  100% {
    opacity: 0;
    width: 0px;
  }
}

@-webkit-keyframes handHide {
  0% {
    opacity: 0;
    width: 0px;
  }

  100% {
    opacity: 0;
    width: 0px;
  }
}

@keyframes shake {
  0% {
    transform: translateX(2px);

  }

  20% {
    transform: translateX(-2px);

  }

  40% {
    transform: translateX(2px);
  }

  60% {
    transform: translateX(-2px);
  }

  80% {
    transform: translateX(2px);
  }

  100% {
    transform: translateX(0px);
  }
}

@-webkit-keyframes shake {
  0% {
    transform: translateX(2px);

  }

  20% {
    transform: translateX(-2px);

  }

  40% {
    transform: translateX(2px);
  }

  60% {
    transform: translateX(-2px);
  }

  80% {
    transform: translateX(2px);
  }

  100% {
    transform: translateX(0px);
  }
}

@keyframes shakeUp {
  0% {
    transform: rotate(10deg);
  }

  20% {
    transform: rotate(-10deg);
  }

  40% {
    transform: rotate(10deg);
  }

  60% {
    transform: rotate(-10deg);
  }

  70% {
    transform: rotate(10deg);
  }

  80% {
    transform: rotate(10deg) translateY(8px);
  }

  100% {
    transform: rotate(10deg) translateY(16px);
  }
}

@-webkit-keyframes shakeUp {
  0% {
    transform: rotate(10deg);
  }

  20% {
    transform: rotate(-10deg);
  }

  40% {
    transform: rotate(10deg);
  }

  60% {
    transform: rotate(-10deg);
  }

  70% {
    transform: rotate(10deg);
  }

  80% {
    transform: rotate(10deg) translateY(8px);
  }

  100% {
    transform: rotate(10deg) translateY(16px);
  }
}

.audioList {
  position: absolute;
  width: 1.45rem;
  height: 1.34rem;
  background: url('../image/btn-audio-bg.png') no-repeat;
  background-size: 100% 100%;
  z-index: 200;
  left: 3.5rem;
  top: 1.1rem;
  transform: translateX(-50%) translateY(-50%);
  cursor: pointer;
  .audioImg {
    position: absolute;
    top: 0.3rem;
    left: 0.3rem;
    width: 0.83rem;
    height: 0.8rem;
    cursor: pointer;
  }
}



.sprite {
  position: absolute;
  width: 2.5rem;
  height: 4.6rem;
  background: url('../image/sprite5.png') no-repeat;
  background-size: 500% 100%;
  z-index: 999;
}

.whenWin {
  display: block;
  animation: goMove 5s linear 1;
}

.whenJump {
  left: 85%;
  animation: winJump 1s steps(2) infinite forwards;
}

.whenLose {
  display: block;
  animation-name: loseDown;
  animation-duration: 0.5s;
  animation-timing-function: steps(1, end);
  animation-delay: 0s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-direction: normal;
}

@keyframes goMove {
  0% {
    left: 5%;
  }

  100% {
    left: 85%;
  }
}

@keyframes winJump {
  0% {
    background-position: 75% 0;
  }

  100% {
    background-position: 125% 0;
  }
}

@keyframes loseDown {
  0% {
    background-position: 25% 0;
  }

  100% {
    background-position: 50% 0;
  }
}
