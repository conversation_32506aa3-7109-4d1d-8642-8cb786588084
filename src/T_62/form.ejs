<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>TUN0002_排序过河</title>
  <link rel="stylesheet" href="form/css/style.css">
  <link rel="stylesheet" href="form/js/QuillEditor/quill.snow.css">
  <script src='form/js/jquery-2.1.1.min.js'></script>
  <script src='form/js/vue.min.js'></script>
  <script src='form/js/QuillEditor/quill.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <div class="h-title">TUN0002_排序过河</div>

      <% include ./src/common/template/common_head_nontitle %>

      <!-- 版式的格式设置 -->
      <div class="c-group">
        <div class="c-title">版式的格式设置</div>
        <div class="c-area">
          <div class="c-well">
            <label>小字号：最多显示2行文本； 大字号：最多显示1行文本。</label>
            <span class='txt-info'>选项文本的字号 <em>*</em></span>
            <label class="inline-label"><input type="radio" name="textType" value="1"
                v-model="configData.source.textType"> 小号字</label>
            <label class="inline-label"><input type="radio" name="textType" value="2"
                v-model="configData.source.textType"> 大号字</label>
          </div>
        </div>
      </div>

      <!-- 题干内容 -->
      <div class="c-group">
        <div class="c-title">题干内容</div>
        <div class="c-area">
          <label>请按照顺序 依次填写“题干+正确选项”：</label>
          <label>使用下划线工具，标记需要填空的单词</label>
          <label>对于题干：大字号最多1行，小字号最多2行，超出时不显示</label>
          <label>请提交后预览，检查是否题干文本过长</label>
          <label>对于选项： “正确选项+干扰项” 不超过1行，超出时不显示</label>
          <label> 请提交后预览，检查是否选项文本过长</label>
          <label> 英文输入包括标点</label>
          <div class="c-well">
            <div id="QuillEditor"></div>
            <div id="QuillEditor-toolbar"></div>
          </div>
        </div>

      </div>

      <!-- 干扰选项 -->
      <div class="c-group">
        <div class="c-title">干扰选项</div>
        <div class="c-area">
          <label>干扰选项（选填）</label>
          <label>对于选项： “正确选项+干扰项” 不超过1行，超出时不显示</label>
          <label>请提交后预览，检查是否选项文本过长</label>
          <label>最多2个干扰选项</label>
          <div class="c-well">
            <label v-for="(item,index) in configData.source.interfereTxt">
              干扰选项{{index + 1}} <input type="text" class='c-input-txt short-input' placeholder="" v-model="item.text">
            </label>
          </div>
        </div>
        <button type="button" class="add-tg-btn text-add-btn" v-show="configData.source.interfereTxt.length<2"
          v-on:click="addOptions(configData.source.interfereTxt, {text:''})">+</button>
      </div>

      <!-- 题干音频 -->
      <div class="c-group">
        <div class="c-title">题干音频</div>
        <div class="c-area upload img-upload radio-group">
          <div class="c-well">
            <div class="field-wrap">
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-audio"
                v-on:change="audioUpload($event,configData.source,'audio',30)">
              题干音频（选填）<label for="content-audio" class="btn btn-show upload"
                v-if="!configData.source.audio">上传</label><em class="red">文件大小≤30KB</em>
            </div>
            <div class="audio-preview" v-show="configData.source.audio">
              <div class="audio-tools">
                <p v-show="configData.source.audio">{{configData.source.audio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="configData.source.audio"></audio>
              </span>
            </div>
            <div class="field-wrap" style="margin-top:20px;">
              <label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.audio"
                @click="configData.source.audio=''">删除</label>
              <label for="content-audio" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 结局动画提示 -->
      <div class="c-group">
        <div class="c-title"> 结局动画提示</div>
        <div class="c-area">

          <label>5帧雪碧图； 默认显示第3帧</label>
          <label>成功后，动画从起始点平移至终点，在终点显示第345帧</label>
          <label>失败后，动画显示第321帧</label>
        </div>
        <div class="c-area upload img-upload radio-group">
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label" for="">动画雪碧图5帧<em>*</em></label>
              <input type="file" v-bind:key="Date.now()" class="btn-file" id="spriteImg" size="1250*460"
                v-on:change="imageUpload($event,configData.source,'spriteImg',50)">
              <label for="spriteImg" class="btn btn-show upload"
                v-if="configData.source.spriteImg==''?true:false">上传</label>
              <label for="spriteImg" class="btn upload re-upload"
                v-if="configData.source.spriteImg!=''?true:false">重新上传</label>
              <span class='txt-info'>尺寸：1250*460。文件大小≤50KB</span>
            </div>
            <div class="img-preview" v-if="configData.source.spriteImg!=''?true:false">
              <img v-bind:src="configData.source.spriteImg" alt="" />
              <div class="img-tools">
                <span class="btn btn-delete" v-on:click="configData.source.spriteImg=''">删除</span>
              </div>
            </div>
          </div>
          <div class="c-well">
            <span>多帧图片播放次数</span>
            <input type="text" class="play-time" maxlength="2" v-model="configData.source.palyTime"
              @input="inputPlayNum"> 次 (1～20,"#" 无限重复)
          </div>
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label" for="">提示声音成功 </label>
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="successAudio"
                v-on:change="audioUpload($event,configData.source,'successAudio',20)">
              <label for="successAudio" class="btn btn-show upload" v-if="!configData.source.successAudio">上传</label><em
                class="red">*文件大小≤20KB</em>
            </div>
            <div class="audio-preview" v-show="configData.source.successAudio">
              <div class="audio-tools">
                <p v-show="configData.source.successAudio">{{configData.source.successAudio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="configData.source.successAudio"></audio>
              </span>
            </div>
            <div class="field-wrap" style="margin-top:20px;">
              <label for="successAudio" class="btn upload btn-audio-dele" v-if="configData.source.successAudio"
                @click="configData.source.successAudio=''">删除</label>
              <label for="successAudio" class="btn upload re-upload" v-if="configData.source.successAudio">重新上传</label>
            </div>
          </div>
          <div class="c-well">
            <p class="field-label" for="">成功后，从起始点移动到终止点, 共 <input type="number" class='c-input-txt short-input w20'
                placeholder="" v-model="configData.source.spriteduration"> s完成（1-5S）</p>
          </div>
          <div class="c-well">
            <div class="field-wrap">
              <label class="field-label" for="">提示声音失败</label>
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="fialAudio"
                v-on:change="audioUpload($event,configData.source,'fialAudio',20)">
              <label for="fialAudio" class="btn btn-show upload" v-if="!configData.source.fialAudio">上传</label><em
                class="red">*文件大小≤20KB</em>
            </div>
            <div class="audio-preview" v-show="configData.source.fialAudio">
              <div class="audio-tools">
                <p v-show="configData.source.fialAudio">{{configData.source.fialAudio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="configData.source.fialAudio"></audio>
              </span>
            </div>
            <div class="field-wrap" style="margin-top:20px;">
              <label for="fialAudio" class="btn upload btn-audio-dele" v-if="configData.source.fialAudio"
                @click="configData.source.fialAudio=''">删除</label>
              <label for="fialAudio" class="btn upload re-upload" v-if="configData.source.fialAudio">重新上传</label>
            </div>
          </div>
        </div>
      </div>

      <!--   结局-显示完整句子 -->
      <div class="c-group">
        <div class="c-title"> 结局-显示完整句子</div>
        <div class="c-area">
          <p class="field-label">完整句子 <em  class="red">*</em> <input type="text" class='c-input-txt short-input' placeholder="" maxlength="100"
              v-model="configData.source.WholeText"></p>
          <label for="">师生可见，请输入完整的答案，小于100个字符</label>
        </div>

      </div>

      <!--     老师面板-答案提示 -->
      <div class="c-group">
        <div class="c-title"> 老师面板-答案提示</div>
        <div class="c-area">
          <p class="field-label">答案文本<em class="red">*</em> <input type="text" class='c-input-txt short-input' placeholder="" maxlength="50"
              v-model="configData.source.answerText"></p>
          <label for="">仅老师可见，小于50个字符</label>
        </div>

      </div>
      <button class="send-btn" v-on:click="onSend">提交</button>
    </div>

  </div>
  <div class="edit-show">
    <div class="show-fixed">
      <div class="show-img">
        <img src="form/img/bg.jpg?v=<%=new Date().getTime()%>" alt="">
      </div>
      <ul class="show-txt">
        <li>
          <em>图片格式：</em>JPG/PNG/GIF</li>
        <li>
          <em>声音格式：</em>MP3/WAV</li>
        <li>
          <em>视频格式：</em>MP4</li>
        <li>带有“ * ”号为必填项</li>
      </ul>
    </div>
  </div>
  </div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
