
var configData = {
  bg: "",
  desc: "111111",
  tg: [{
      title: "",
      content: "",
  }],
  level:{
      high:[{
          title: "",
          content: ""
      }],
      low:[{
          title: "",
          content: "",
      }]
  },
  source: {
     textType: 1, //小号字 1  大号字 2
     conTxt:[{"insert":"insert"},{"insert":"insert"},{"insert":"insert"},{"insert":"insert"},{"insert":"insert"},{"insert":"insert"},{"attributes":{"underline":true},"insert":"like"},{"insert":" "},{"attributes":{"underline":true},"insert":"playing"},{"insert":" "},{"attributes":{"underline":true},"insert":"basketball"},{"insert":" "},{"attributes":{"underline":true},"insert":"after"},{"insert":" "},{"attributes":{"underline":true},"insert":"school."},{"insert":"\n"}], //题干内容
     interfereTxt: [{'text':"i"},{'text':"m"},{'text':"m"},{'text':''}], //干扰选项
     audio: "./audio/02.mp3",//题干音频
     spriteImg: './image/sprite2.png', //雪碧图
     successAudio: './audio/02.mp3', //成功声音
     spriteduration: 2,//雪碧图持续时间
     fialAudio: './audio/01.mp3',//失败声音
     WholeText: 'i am iron man i am iron man i am iron man i am iron man i am iron man i am iron man i am iron man i am iron man',//完整句子
     answerText: 'iron; man',//答案文本
     palyTime:'#',//雪碧图播放次数
     random:0.3,//随机打乱顺序的数（为保持两端一致，写在这）
  }
}
