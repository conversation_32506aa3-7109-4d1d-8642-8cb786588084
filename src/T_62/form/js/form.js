// var domain = 'http://172.16.0.107:9011/pages/10003/';
var domain = '';
var Data = {
    configData: {
        bg: "",
        desc: "",
        tg: [{
            title: "",
            content: "",
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
            textType: 1, //小号字 1  大号字 2
            conTxt: [], //题干内容
            interfereTxt: [{ 'text': '' }, { 'text': '' }], //干扰选项
            audio: '', //题干音频
            spriteImg: '', //雪碧图
            successAudio: '', //成功声音
            spriteduration: 1, //雪碧图持续时间
            fialAudio: '', //失败声音
            WholeText: '', //完整句子
            answerText: '', //答案文本
            palyTime: '1', //雪碧图播放次数
            random: Math.random(), //随机打乱顺序的数（为保持两端一致，写在这）
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
};

$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            var options = Data.configData.source.options;
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
        }
    },
    error: function(res) {
        console.log(res.data, '22')
    }
});
new Vue({
    el: '#container',
    data: Data,
    mounted: function() {
        var ths = this;
        var toolbarOptions = ['underline'];
        var quill = new Quill('#QuillEditor', {
            modules: {
                toolbar: toolbarOptions
            },
            placeholder: 'eg: I am a boy.',
            readOnly: false,
            theme: 'snow'
        });
        quill.setContents(ths.configData.source.conTxt);
        quill.on('text-change', function(delta, oldDelta, source) {
            // if (source == 'api') {
            //   console.log("An API call triggered this change.");
            // } else if (source == 'user') {
            //   console.log("A user action triggered this change.");
            // }
            console.log(quill.getContents())
            var content = $.extend({}, quill.getContents());
            ths.configData.source.conTxt = content.ops;
        });
        this.quill = quill;
        $('.ql-editor p').attr("spellcheck", "false")
    },
    methods: {
        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                e.target.value = '';
                return;
            }

            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                });
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        sourceImgCheck: function(input, data) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                alert("应上传图片大小为：" + size.join("或") + ", 上传图片尺寸为：" + data.width + "*" + data.height);
                input.value = '';
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source
            var check = true;
            var conTxt = data.conTxt;
            var spriteImg = data.spriteImg;
            var successAudio = data.successAudio;
            var fialAudio = data.fialAudio;
            var WholeText = data.WholeText;
            var answerText = data.answerText;
            var spriteduration = data.spriteduration;
            var palyTime = data.palyTime;
            var interfereTxt = data.interfereTxt;
            console.log(conTxt)
            if (!conTxt || conTxt.length == 1) {
                alert("请填写题干内容")
                return
            }
            if (!spriteImg) {
                alert("请上传5帧动画雪碧图")
                return
            }
            if (!successAudio) {
                alert("请上传成功提示声音")
                return
            }
            if (!fialAudio) {
                alert("请上传失败提示声音")
                return
            }
            if (!WholeText) {
                alert("请填写完整句子")
                return
            }
            if (!answerText) {
                alert("请填写答案文本")
                return
            }
            if (spriteduration > 5 || spriteduration < 1) {
                alert("请填写正确的秒数")
                return
            }
            if ((palyTime != '#' && palyTime > 20) || !palyTime) {
                alert("请填写正确的播放次数")
                return
            }
            return check
        },
        onSend: function() {
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            console.log(_data, 'aaa');
            var val = this.validate();
            if (val === true) {

                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                //alert('带*号的为必填项')
                // alert('图片、音频和文字不能同时为空')
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            bg = arguments.length > 2 ? arguments[2] : null;
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            var _this = this;
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },
        audioUpload: function(e, item, attr, fileSize) {
            console.log(attr)
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的声音大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                e.target.value = '';
                return;
            }
            this.postData(file, item, attr);
        },
        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addOptions: function(arr, item) {
            arr.push(item)
        },
        delOption: function(arr, item) {
            arr.remove(item)
        },
        inputPlayNum: function() {
            var reg = new RegExp("^([1-9][0-9]{0,1}|[2][0]{0,1})$");
            var regResult = reg.test(Data.configData.source.palyTime);
            if (!regResult && Data.configData.source.palyTime != "#") {
                Data.configData.source.palyTime = "";
            }
        }
    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
