"use strict";
import "../../common/js/common_1v1.js";
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import {
  resultWin,
  resultHide,
} from "../../common/template/ribbonWin/index.js";
import {
  templateScale,
  handClick,
  handHide,
} from "../../common/template/hint/index.js";
$(function () {
  var script = document.createElement("script"); //创建新的script节点
  script.setAttribute("type", "text/javascript");
  script.setAttribute(
    "src",
    // "//cdn.51talk.com/apollo/public/js/vconsole.min.3.3.0.js"
    "./vconsole.min.3.3.0.js"
  );
  document.body.appendChild(script); //添加到body节点的末尾

  script.addEventListener(
    "load",
    function () {
      var vConsole = new VConsole();
      console.log("vconsole已正常启用");
    },
    false
  );

  //是否在控制器显示功能按钮
  window.h5Template = {
    hasDemo: "1", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "2", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式
  };
  let staticData = configData.source;
  let gameList = staticData.gameList; //每轮数据
  let userType =
    window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师
  let classStatus = 1; //教室状态
  let roundNum = 0; //当前轮次
  //判断是否使用默认背景图片
  let isFirst = true;
  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bg.jpg)",
    });
  }
  let timer = null; //长时间回答不正确时 正确选项出现提示
  let isDemo = false; //是否是demo模式
  let demoScale = 1; //demo模式缩小倍数
  let optionClickStus = true; //点击选项事件
  let demoTimer; //demo模式出现小手定时器

  /**
   * 初始化
   */

  //生成像素网格
  renderPx();
  //小手位置初始化
  // initHand();
  //初始化标志物
  initLandMark();
  //判断用户角色，显示不同功能
  isShowBtn();
  //游戏初始化
  initGame();
  //进度条初始化
  initProgressBar();
  //生成像素网格
  function renderPx() {
    let liHtml = "";
    for (let i = 1; i < 801; i++) {
      liHtml += `
                <li class="pos_${i}"></li>	`;
    }
    $(".boxUl").html(liHtml);
  }

  if (SDK.getUserType() == "tea") {
    $(".buttons").show();
  }
  //初始化标志物
  function initLandMark(param_roundNum) {
    //需要标志物
    if (staticData.isNeedLandMark == 2) {
      //如用户自定义了标志物，使用用户上传；否则使用默认
      var imgsrc = staticData.landMarkImg
        ? staticData.landMarkImg
        : "./image/landMark.png";
      $(".landMarkImg").attr("src", imgsrc);
      //加载图片 获取宽高
      var preList = [
        {
          image: imgsrc,
        },
      ];
      var preImgs = [];
      $.when(preloadImg(preList, preImgs)).done(
        //图片加载完成后 才能获得宽高
        function () {
          $(".landMarkImg").css({
            backgroundSize: "100% 100%",
            width: preImgs[0].width / 100 + "rem",
            height: preImgs[0].height / 100 + "rem",
          });
        }
      );

      //如果用户选择了初始位置
      if (staticData.landMarkShowType == 2) {
        let left = staticData.landMarkPositionX / 100 + "rem";
        let top = staticData.landMarkPositionY / 100 + "rem";
        if (param_roundNum) {
          //断线重连  恢复到本轮的位置
          left = staticData.landMarkPositionIngameX / 100 + "rem";
          top = staticData.landMarkPositionIngameY / 100 + "rem";
        }
        $(".landMarkImg").css({
          left: left,
          top: top,
          animation: "",
        });
        $(".landMarkImg").removeClass("hide");
      } else {
        //未选择初始位置 则初始化时将其隐藏
        $(".landMarkImg").addClass("hide");
      }
    }
  }

  /**
   * 游戏选项初始化
   * @param {*} param_roundNum   轮次
   */

  function initGame(param_roundNum) {
    $(".optionUl").html("");
    gameList.forEach(function (item, index) {
      //正确图片位置
      let liEle = "";
      let left = item.rightPositionStartX / 100 + "rem";
      let top = item.rightPositionStartY / 100 + "rem";
      liEle = $('<li class="optionLi optionLiPic" ></li>');
      liEle.attr({
        roundNum: index + 1,
        "data-syncactions": "roundNum" + (index + 1),
      });
      var z = 10;
      if (param_roundNum && index + 1 <= param_roundNum) {
        //断线重连  已完成的轮 不显示
        z = -1;
      }
      liEle.css({
        left: left,
        top: top,
        "z-index": z,
        "background-image": "url(" + item.rightImg + ")",
      });
      //遮罩图片位置
      let liEle2 = "";
      let left2 = item.shadePositionX / 100 + "rem";
      let top2 = item.shadePositionY / 100 + "rem";
      liEle2 = $('<li  class="optionLi optionLiShade"></li>');
      liEle2.attr({
        roundNum: index + 1,
        "data-syncactions": "shadeRoundNum" + (index + 1),
      });
      liEle2.css({
        left: left2,
        top: top2,
        "background-image": "url(" + item.shadeImg + ")",
      });
      //加载图片 获取宽高
      var preList = [
        {
          image: item.rightImg,
        },
        {
          image: item.shadeImg,
        },
      ];
      var preImgs = [];
      $.when(preloadImg(preList, preImgs)).done(
        //图片加载完成后 才能获得宽高
        function () {
          liEle.css({
            backgroundSize: "100% 100%",
            width: preImgs[0].width / 100 + "rem",
            height: preImgs[0].height / 100 + "rem",
          });
          $(".optionUl").append(liEle);
          
          liEle2.css({
            "z-index": 20,
            backgroundSize: "100% 100%",
            width: preImgs[1].width / 100 + "rem",
            height: preImgs[1].height / 100 + "rem",
          });
          $(".optionUl").append(liEle2);
          //最后一轮加载完成后，即所有内容加载完毕后 添加事件
          if (index + 1 == gameList.length) {
            addEvent(); //选项添加事件
          }
        }
      );
    });
  }
  //小手曲线动画
  function handAnima() {
    let postionTop = $(".container")
    if (!postionTop.find('.handsss').length) {
      postionTop.append('<div class="handsss"></div>');
    }
   
  }
  
  // $(".container").on("click touchstart", function (e) {
  //   if (e.type == "touchstart") {
  //     e.preventDefault();
  //   }
  //   e.stopPropagation();
  //  $(".handsss").hide();
  // });
  //判断用户角色，显示不同功能(如老师的底部文字提示，学生的预习模式等)
  function isShowBtn() {
    if (isSync) {
      //同步模式
      classStatus = SDK.getClassConf().h5Course.classStatus;
      if (classStatus == 0 && userType == "stu") {
        $(".funcMask").show(); //预习模式和倒计时
        console.log("isShowBtn 预习模式和倒计时")
      }
    } else {
      //非同步模式
      var hrefParam = parseURL("http://www.example.com");
      if (top.frames[0] && top.frames[0].frameElement) {
        hrefParam = parseURL(top.frames[0].frameElement.src);
      } else {
        hrefParam = parseURL(window.location.href);
      }
      var role_num = hrefParam.params["role"];

      function parseURL(url) {
        var a = document.createElement("a");
        a.href = url;
        return {
          source: url,
          protocol: a.protocol.replace(":", ""),
          host: a.hostname,
          port: a.port,
          query: a.search,
          params: (function () {
            var ret = {},
              seg = a.search.replace(/^\?/, "").split("&"),
              len = seg.length,
              i = 0,
              s;
            for (; i < len; i++) {
              if (!seg[i]) {
                continue;
              }
              s = seg[i].split("=");
              ret[s[0]] = s[1];
            }
            return ret;
          })(),
          file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ""])[1],
          hash: a.hash.replace("#", ""),
          path: a.pathname.replace(/^([^\/])/, "/$1"),
          relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ""])[1],
          segments: a.pathname.replace(/^\//, "").split("/"),
        };
      }
      if (role_num == "1" || role_num == undefined) {
        console.log("role_num funcMask",role_num)
        $(".funcMask").show();
      } else if (role_num == "2") {
        $(".funcMask").show();
        console.log("role_num 2 funcMask",role_num)
      }
    }
  }

  /**
   *  开始游戏
   *  2种途径可开启游戏，1.老师通过控制器点击开始 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击开始
  window.SDK.actAuthorize = function (message) {
    if (isSync) {
      if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
        //老师显示下方提示条
        $(".doneTip").removeClass("hide");
      }

      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        //第一次授权才显示倒计时
        if (isFirst) {
          isFirst = false;
          $(".funcMask").show();
          console.log("第一次授权才显示倒计时")
          $(".optionUl").html("");
          threeTwoOne(); //321倒计时
        }
      }
    }
  };
  //2.非课中模式，如预习，学生点击开启
  let startBtnStatus = true; //开始按钮是否可点击(预习模式)
  $(".startBtn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (startBtnStatus) {
      startBtnStatus = false;
      threeTwoOne(); //321倒计时
    }
  });

  /**
   *  demo模式
   *  2种途径可开启demo模式，1.老师通过控制器点击提示 2.非课中模式，如预习，学生点击开启
   */
  //1.老师通过控制器点击提示
  window.SDK.actDemo = function (message) {
    demoFun(false); //demo模式
    //老师上传一份空白cldata,来覆盖以前的数据。
    if (isSync) {
      SDK.bindSyncEvt({
        index: "clear",
        eventType: "click",
        method: "event",
        syncName: "clear", //SDK.js中对其特殊处理
        recoveryMode: "1",
      });
    }
    SDK.setEventLock();
  };
  //2.非课中模式，如预习，学生点击开启
  $(".demo-btnStu").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    demoFun(true); //demo模式
  });
  $(".startBox").hide();
  // setTimeout(function() {
  //   threeTwoOne(); //321倒计时
  // },1000)
  // 3 2 1倒计时
  function threeTwoOne() {
    let q = 4;
    $(".startBox").hide().siblings(".timeChangeBox").show().find(".numberList");
    SDK.playRudio({
      index: $(".timeLowAudio_" + q).get(0),
      syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
    });
    let audioPlay = setInterval(function () {
      q++;
      q++;
      if (q > 4) {
        clearInterval(audioPlay);
        //3210倒计时结束
        SDK.setEventLock();
        // $(".funcMask").hide();
        $(".timeChangeBox").hide();
        initGame(); //初始化游戏
        currentEnd(); //当前轮结束
        gameControl(); //开启新一轮（控制轮次和音频）
      } else {
        // 播放倒计时声音
        SDK.playRudio({
          index: $(".timeLowAudio_" + q).get(0),
          syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
        });
        $(".numberList").css({
          "background-position-x": -(1.5 * (q - 1)) + "rem",
        });
      }

    }, 1000);
    setTimeout(function(){
      $(".funcMask").hide();
    },1200)
   
  }

  // 结束本轮
  function currentEnd() {
    $(".optionLiPic").removeClass("optionScale");
    if (roundNum >= gameList.length) {
      //游戏已结束
      clearTimeout(timer); //清除计时
      soundControl().pause();
      if (userType == "stu" || !isSync) {
        sendGameOver(); //游戏结束
      }
      return;
    }
    soundControl().pause(); //停止本轮音频
  }
  /**
   * 开启新一轮（控制轮次和音频）
   */
  function gameControl() {
    roundNum = roundNum + 1; //下一轮
    //N秒整个页面不点击，正确选项提醒(放大缩小)
    timer = setTimeout(() => {
      $(".optionLiPic[roundnum=" + roundNum + "]").addClass("optionScale");
    }, 10000);

    //  音频改为本轮音频
    $(".frequencyAudio").attr("src", gameList[roundNum - 1].audio);
    if (isDemo) {
      //demo模式下音频只播放一次
      $(".frequencyAudio").removeAttr("loop");
    } else {
      //正常模式 音频是循环播放
      $(".frequencyAudio").attr("loop", "loop");
    }

    //播放本轮音频
    soundControl().play(); //再调用播放
    optionClickStus = true;
  }

  //选项添加事件
  function addEvent() {
    //选项
    $(".optionLi").on("click touchstart", function (e) {
      console.log("点击了======》");
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation(); //阻止事件进一步传播
     
      $(".handsss").hide();
      if (optionClickStus) {
        optionClickStus = false;

        let self = $(this);
        if (self.hasClass("optionLiShade")) {
          //如果点击的是选项的遮罩  目标改为选项图片
          self = self.prev();
        }
        let roundNumInpic = self.attr("roundNum");

        if (!isSync) {

          $(this).trigger("syncItemClick");
          return;
        }
        console.log("bindSyncEvt============》1")
        console.log("bindSyncEvt============》1",$(".optionLi").data("syncactions"))
        console.log("bindSyncEvt============》2",$(e.currentTarget).data("syncactions"))
        SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index:$(e.currentTarget).data("syncactions"), //选项的data-syncactions值
          eventType: 'click',
          method: 'event',
          syncName: 'syncItemClick',
          otherInfor: {
            roundNum: roundNum
          },
          recoveryMode: '1'
        })
      }
    });

    $(".container").on("click touchstart", function (e) {
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation(); //阻止事件进一步传播
      $(".handsss").hide();
    });
    //点击选项
    $(".optionLi").on("syncItemClick", function (e, message) {
      //demo模式不能点击
      if (isDemo) {
        return;
      }
      console.log("syncItemClick====>1")

      let self = $(this);
      if (self.hasClass("optionLiShade")) {
        //如果点击的是选项的遮罩  目标改为选项图片
        self = self.prev();
        // 如已消除不可点击
        //  if(self.hasClass('already-hide')) {
        //   optionClickStus = true;
        //   return
        //  }
      }
      console.log("syncItemClick====>2")
      
      let roundNumInpic = self.attr("roundNum");
      if (roundNumInpic == roundNum) {
        //当前点击是本轮次的选项
        clearTimeout(timer); //答对清除计时
        $(".optionLiPic").removeClass("optionScale"); //移除无人答题时选项放大的动画
        rightWrong(self).right(); //点击正确
      } else {
        rightWrong(self).wrong(); //错误
       
      }
      self.on("animationend webkitAnimationEnd", function () {
        self.removeClass("shake");
        SDK.setEventLock();
        optionClickStus = true;
      });
      SDK.setEventLock();

      if (userType == "stu") {
        gameProcessBidSync(); //上报游戏数据
      }
    });
  }
  /**
   * 进度条初始化
   * @param
   * @returns
   */
  function initProgressBar() {
    //重置demo产生的第一个黄点
    // $('.y1').css({
    //     'opacity': 0
    // });
    var roundNumAll = gameList.length;
    if (roundNumAll == 1) {
      //仅一轮时 喇叭下移 不显示进度条
      $(".horn").css({
        bottom: "0.8rem",
      });
    } else {
      //大于一轮时 显示放大镜  显示进度条
      $(".magnifier").removeClass("hide");
      $(".proprogressBarBox").removeClass("hide");
      $(".proprogressBarBox").css(
        "width",
        `${1.8 + 0.76 * (roundNumAll - 2)}rem`
      );
      $(".proprogressBar").css("width", `${1 + 0.76 * (roundNumAll - 2)}rem`);
      //根据轮次显示暗点
      for (var i = 0; i <= roundNumAll; i++) {
        $(`.c${i}`).removeClass("hide");
      }
    }
  }

  //声音控制
  function soundControl() {
    return {
      play: function (number) {
        //正确特效时，只播放一次
        if (number == "1") {
          $(".frequencyAudio").removeAttr("loop");
        }
        setTimeout(() => {
          $(".frequencyAudio")[0].currentTime = 0; //从头播放

          SDK.playRudio({
            index: $(".frequencyAudio")[0],
            syncName: $(".frequencyAudio").attr("data-syncaudio"),
          });
          $(".horn").addClass("hornAnimation");
        }, 200);
      },
      pause: function () {
        SDK.pauseRudio({
          index: $(".frequencyAudio")[0],
          syncName: $(".frequencyAudio").attr("data-syncaudio"),
        });
        $(".horn").removeClass("hornAnimation");
      },
    };
  }

  //上报游戏数据方便重连（切换下一轮、点击选项）
  function gameProcessBidSync() {
    console.log("上报游戏数据方便重连（切换下一轮、点击选项）");
    if (isSync) {
      SDK.bindSyncEvt({
        index: "runing",
        eventType: "click",
        method: "event",
        syncName: "syncRuning",
        otherInfor: {
          roundNum: roundNum, //第几轮
        },
        recoveryMode: "1",
      });
    }
  }

  //游戏中恢复数据
  $(".runing").on("syncRuning", function (e, message) {
    if (isSync) {
      let obj = message.data[0].value.syncAction.otherInfor;
      if (message.operate == 5) {
        //直接恢复页面
        rebackPage().whenClick(obj);
      }
    }
    SDK.setEventLock();
  });

  //结束游戏 发送数据(由学生判断游戏结束，老师只接受学生的结果)
  function sendGameOver() {
    if (!isSync) {
      $(".overBtn").trigger("gameOver");
    } else {
      SDK.bindSyncEvt({
        index: "overBtn",
        eventType: "mygameevent",
        method: "event",
        syncName: "gameOver",
        otherInfor: {
          roundNum: roundNum,
        },
        recoveryMode: "1",
      });
    }
  }

  //结束反馈事件
  $(".overBtn").on("gameOver", function (e, message) {
    clearTimeout(timer); //答对清除计时
    $(".optionLiPic").removeClass("optionScale"); //移除无人答题时选项放大的动画
    if (isSync) {
      let obj = message.data[0].value.syncAction.otherInfor;
      if (message.operate == "5") {
        endGameappendImg("whenend");
        rebackPage().whenEnd(obj);
        return;
      } else {
        endGameappendImg("over");
      }
      //如果是老师 告知控制器，将授权状态改为老师控制，classstatus为6
      if (
        userType == "tea" &&
        (SDK.getClassConf().h5Course.classStatus == "5" ||
          SDK.getClassConf().h5Course.classStatus == "1")
      ) {
        SDK.bindSyncCtrl({
          type: "gameOverToCtrl",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [
              {
                key: "classStatus",
                value: "6",
                ownerUID: SDK.getClassConf().user.id,
              },
            ],
          },
        });
      }
    } else {
      endGameappendImg("over");
    }
    SDK.setEventLock();
  });

  /**
   *   终局页面去重动态插入图片  所有正确选项去重
   *   type  'over'正常结束   'whenend'断线重连结束
   */
  function endGameappendImg(type) {
    //隐藏下方提示条
    $(".doneTip").addClass("hide");

    var time = 1;
    //终局特效
    if (type == "over") {
      //有回答正确的 且不是断线重连
      //播放终局特效
      resultWin({
        WinIsLoop: false, // 答对声音是否重复播放 true/false
        Mask_Z_Index: "500", // 遮罩层z_index
      });
      var prefect = $(
        "<div class='perfectBox'><img src='./image/light.png' class='light'><img src='./image/prefect.png' class='perfect'></div>"
      );
      // var prefect_num = $(`<div class='numBox'> <img src="./image/countImg.png" alt="">  <label></label>  <span class='score'>x<span class='scoreNum'>${gameList.length}</span></span> </div>`);
      $(".resultWin").append(prefect);
      // $(".perfectBox").append(prefect_num);
      time = 5000; //5s后 隐藏终局特效
    }
    setTimeout(function () {
      resultHide();
      //显示所有正确选项
      $(".optionLiPic").css({
        "z-index": 30,
      });
      soundControl().pause();
      $(".endGame").show();
    }, time);
  }

  //掉线页面恢复（点击选项、结束）
  function rebackPage(data) {
    return {
      whenClick: function (data) {
        isFirst = false;
        if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
          //下方提示条
          $(".doneTip").removeClass("hide");
        }
        // score = data.score; //分数
        roundNum = data.roundNum; //第几轮

        initGame(roundNum); //本轮游戏选项初始化
        initLandMark(roundNum); //恢复标志物
        recoverProgressBar(roundNum); //恢复进度条
        // addEvent(); //添加事件
        currentEnd(); //当前轮结束
        gameControl(); //开启新一轮（控制轮次和音频）
        SDK.setEventLock();
      },
      whenEnd: function (data) {
        isFirst = false;
        $(".doneTip").hide();
        roundNum = data.roundNum;
        recoverProgressBar(roundNum); //恢复进度条
        endGameappendImg("whenEnd"); //结束游戏
        SDK.setEventLock();
      },
    };
  }

  //正确与错误逻辑(停止选项音、播放反馈音，改变分数、自增答对个数)
  function rightWrong(self) {
    soundControl().pause(); //停止
    return {
      right: function () {
        self.addClass("already-hide");
        rightAni(self); //正确反馈（在正确反馈中开启下一轮）
        console.log("syncItemClick====>正确")
        SDK.playRudio({
          index: $(".rightAudio")[0],
          syncName: $(".rightAudio").attr("data-syncaudio"),
        });
      },
      wrong: function () {
        self.addClass("shake");
        SDK.playRudio({
          index: $(".wrongAudio")[0],
          syncName: $(".wrongAudio").attr("data-syncaudio"),
        });
        $(".wrongAudio")[0].onended = function () {
          soundControl().play();
        };
        if (isSync) {
          SDK.bindSyncEvt({
            sendUser: "",
            receiveUser: "",
            eventType: "click",
            method: "event",
            syncName: "syncItemClick",
            otherInfor: {
              roundNum: roundNum,
              correct:false,
            },
            recoveryMode: "1",
          });
        }
      },
    };
  }

  /**
   * 正确反馈
   * @param  {object} self 需要操作的元素
   * @param  {object}  showfuncMask  是否展示预习遮罩(学生自己预习时展示)；无论true/flase，有值则为demo模式。
   * @returns
   */
  function rightAni(self, showfuncMask) {
    // var audioTime = gameList[roundNum - 1].audioTime; //音频时长，若音频时长小于1000毫秒 视为 1000毫秒
    // audioTime = audioTime > 1000 ? audioTime : 1000;
    var audioTime = 2000;
    //目标出现在正确反馈位置 且在遮罩层上方
    let left = gameList[roundNum - 1].rightPositionEndX / 100 + "rem";
    let top = gameList[roundNum - 1].rightPositionEndY / 100 + "rem";
    self.css({
      left: left,
      top: top,
      "z-index": 21,
    });
    
    //处理标志物
    if (staticData.isNeedLandMark == 2) {
      //本轮标志物位置
      let left = gameList[roundNum - 1].landMarkPositionIngameX / 100 + "rem";
      let top = gameList[roundNum - 1].landMarkPositionIngameY / 100 + "rem";
      if (staticData.landMarkShowType == 2) {
        //标志物需要飞行
        document.styleSheets[0].insertRule(
          `@keyframes landMarkMove${roundNum} {100%{ animation-timing-function: linear;left: ${left};top: ${top};}}`
        ); //写入样式
        $(".landMarkImg").css({
          animation: `landMarkMove${roundNum} 1s 1 ease-in-out`,
          "animation-fill-mode": "forwards",
        });
        $(".landMarkImg").on("webkitAnimationEnd", function () {
          //将标志物定位在最后位置(每轮动画的新起点)
          $(".landMarkImg").css({
            animation: ``,
            left: left,
            top: top,
            "z-index": 30,
          });
        });
      } else {
        //标志物原地显示
        $(".landMarkImg").css({
          left: left,
          top: top,
          "z-index": 30,
        });
        $(".landMarkImg").removeClass("hide");
      }
    }
    //处理正确特效
    let timer1 = setTimeout(() => {
      $(".blackShadow").show();
      $(".blackShadow").css({
        "z-index": 40,
      });
      //非demo模式下 每轮隐藏选项
      if (showfuncMask == undefined) {
        self.css({
          left: left,
          top: top,
          "z-index": -1,
        });
      }
      //页面中间旋转特效
      var rightLight = $(
        `<div class='rightAniBox'><img src='./image/light2.png' class='rightLight'><img src='${
          gameList[roundNum - 1].rightImg
        }' class='optionInlight'></div>`
      );
      $(".rightBox").append(rightLight);
      clearInterval(timer1);
      //播放一遍正确音频
      soundControl().play("1");
      //音频播放结束后  todo
      let timer2 = setTimeout(() => {
        //旋转特效选项先消失
        $(".optionInlight").addClass("hide");
        clearInterval(timer2);
        let timer3 = setTimeout(() => {
          //光圈再消失
          $(".rightLight").addClass("hide");
          //整体效果移除
          $(".rightBox").html("");
          $(".blackShadow").hide();
          //仅有一轮游戏 无需后续动画 直接结束游戏。
          if (gameList.length == 1) {
            if (showfuncMask != undefined) {
              demoOut(showfuncMask); //退出demo模式,恢复正常模式
            } else {
              currentEnd(); //当前轮结束
              gameControl(); //开启下一轮(进入结束流程)
            }
            return;
          }
          // ↓↓↓↓ 以下代码仅 游戏轮数＞1 时启用
          //亮点移动到进度条
          var smallLight = $(
            `<img src='./image/light0.png' class='smallLight'>`
          );
          $(".rightBox").append(smallLight);
          //获取进度条黄点位置
          let p_left =
            (($(".y" + roundNum).offset().left -
              $(".container").offset().left +
              25) /
              window.base) *
              demoScale +
            "rem";
          let p_top =
            (($(".y" + roundNum).offset().top - $(".container").offset().top) /
              window.base) *
              demoScale +
            "rem";
          //亮点飞行到黄点
          document.styleSheets[0].insertRule(
            `@keyframes lightMove${roundNum} {100%{ animation-timing-function: linear;left: ${p_left};top: ${p_top};}}`
          ); //写入样式
          $(".smallLight").css({
            animation: `lightMove${roundNum} 1s 1 ease-in-out`,
            "animation-fill-mode": "forwards",
          });
          $(".smallLight").on("webkitAnimationEnd", function () {
            $(".smallLight").css({
              animation: ``,
            });
            //显示黄点
            $(".y" + roundNum).css({
              opacity: 1,
            });
            // 发送指令

            //延长黄色进度条
            $(".yellowBar").css({
              width: `${0.8 * (roundNum - 1)}rem`,
            });
            $(".rightBox").html("");
            if (showfuncMask != undefined) {
              //demo模式
              demoOut(showfuncMask); //退出demo模式,恢复正常模式
              return;
            } else {
              console.log("答题结束当前轮次", roundNum);
              currentEnd(); //当前轮结束
              SDK.bindSyncEvt({
                sendUser: "",
                receiveUser: "",
                eventType: "click",
                method: "event",
                syncName: "syncItemClick",
                otherInfor: {
                  roundNum: roundNum,
                  correct:true,
                },
                recoveryMode: "1",
              });
            }
          });
          clearInterval(timer3);
        }, 200);
      }, audioTime); //音频播放时长
    }, 1000);
  }

  /**
   * 断线重连-恢复进度条
   * @param
   * @returns
   */
  function recoverProgressBar(roundNum) {
    var roundNumAll = gameList.length;
    if (roundNumAll == 1) {
      //仅一轮时
      return;
    } else {
      //大于一轮时
      for (var i = 1; i <= roundNum; i++) {
        //显示黄点
        $(".y" + i).css({
          opacity: 1,
        });
      }
      //延长黄色进度条
      $(".yellowBar").css({
        width: `${0.8 * (roundNum - 1)}rem`,
      });
    }
  }
  /**
   * demo模式
   * @param showfuncMask  是否显示预习模式遮罩
   * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
   */
  function demoFun(showfuncMask) {
    // 添加模版缩放模式
    templateScale({
      show: true,
      className: $(".container"),
    });
    isDemo = true;
    demoScale = 1.39;
    clearTimeout(demoTimer); //清除以前的定时器
    initGame(); //游戏选项初始化
    currentEnd(); //当前轮结束
    gameControl();
    if (showfuncMask) {
      //是否显示学生用的预习模式弹窗
      $(".funcMask").hide();
    }
    //手
    demoTimer = setTimeout(function () {
      //小手位置初始化
      // let left = (((gameList[0].rightPositionStartX) * window.base /100  +  $('.container').offset().left / window.base) + 'rem') / demoScale;
      // let top = (((gameList[0].rightPositionStartY) * window.base /100 + $('.container').offset().top / window.base) + 'rem') / demoScale ;

      let left = $(".optionLiPic").eq(0).offset().left;
      let top = $(".optionLiPic").eq(0).offset().top;
      handClick(top - 30, left - 10); //小手点击
    }, 1000);
    //小手触发正确效果
    setTimeout(function () {
      rightAni($(".optionLiPic[roundnum=1]").eq(0), showfuncMask); //正确反馈 1为正确选项
      SDK.playRudio({
        index: $(".rightAudio")[0],
        syncName: $(".rightAudio").attr("data-syncaudio"),
      });
    }, 2800);
    //隐藏小手
    setTimeout(function () {
      handHide(true);
    }, 3600); //注意调用handClick后 不低于2600
  }
  /**
   * 退出demo模式,恢复正常模式
   * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
   */
  function demoOut(showfuncMask) {
    soundControl().pause();
    clearTimeout(demoTimer); //清除以前的定时器
    clearTimeout(timer); //清除以前的定时器
    $(".optionLiPic").removeClass("optionScale"); //移除无人答题时选项放大的动画

    setTimeout(() => {
      $(".optionUl").html(""); //清空选项区

      if (showfuncMask) {
        $(".funcMask").show();
      }
      //退出demo模式
      isDemo = false;
      demoScale = 1;
      templateScale({
        className: $(".container"),
        show: false,
      });
      initGame(); //初始化游戏
      initLandMark(); //初始化标志物
      initProgressBar(); //初始化进度条
      roundNum = 0; //轮次置为默认
      console.log("demoOut", roundNum);

      //告知控制器取消demo模式
      if (userType == "tea") {
        SDK.bindSyncCtrl({
          type: "tplDemoOut",
          data: {
            CID: SDK.getClassConf().course.id + "", //教室id 字符串
            operate: "1",
            data: [],
          },
        });
      }
    }, 500);
  }

  window.generalTplData = function (message) {
    var dom = $(`.optionLiPic[roundnum=${roundNum}]`);
   
    if (message.actionType == "start") {
      roundNum = 0;
      $(".funcMask").show();
      threeTwoOne();
    }
    if (message.actionType == "fingerDome") {
      // 小手引导出现
      $(".handsss").show();
      handAnima();
    }
    if (message.actionType == "playAudio") {
      // 开启音频
      soundControl().play(); 
    }
    if (message.actionType == "pauseAudio") {
      // 暂停音频
      soundControl().pause();
    }
    

    //根据不同协议调不同方法
    if (message.actionType == "next") {
      console.log("next", roundNum);
      currentEnd(); //当前轮结束  重复了 导致轮次过多了
      gameControl(); //开启下一轮
    } else if (message.actionType == "selection") {
      $(".handsss").hide();
      //帮用户选择正确答案
      clearTimeout(timer); //答对清除计时
      $(".optionLiPic").removeClass("optionScale"); //移除无人答题时选项放大的动画
      rightWrong(dom).right(); //点击正确
      
      console.log("selection");
     
    }
    SDK.setEventLock();
  };

  //为了老师预览 临时代码
  $(".play1").on("click", function () {
    generalTplData({ actionType: "selection" });
  });
  $(".play2").on("click", function () {
    generalTplData({ actionType: "next" });
  });
});
/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
  var def = $.Deferred(),
    len = list.length;
  $(list).each(function (i, e) {
    var img = new Image();
    img.src = e.image;
    if (img.complete) {
      imgs[i] = img;
      len--;
      if (len == 0) {
        def.resolve();
      }
    } else {
      img.onload = (function (j) {
        return function () {
          imgs[j] = img;
          len--;
          if (len == 0) {
            def.resolve();
          }
        };
      })(i);
      img.onerror = function () {
        len--;
      };
    }
  });
  return def.promise();
}
