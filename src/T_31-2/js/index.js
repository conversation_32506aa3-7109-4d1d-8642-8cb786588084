"use strict"
import '../../common/js/common_1v1.js'
import './drag.js'

$(function() {
  // var script = document.createElement("script"); //创建新的script节点
  // script.setAttribute("type", "text/javascript");
  // script.setAttribute("src", "//cdn.51talk.com/apollo/public/js/vconsole.min.3.3.0.js");
  // document.body.appendChild(script); //添加到body节点的末尾
  // script.addEventListener('load', function() {
  //     var vConsole = new VConsole();
  //     console.log('vconsole已正常启用');
  // }, false);
    /**
     * 控制器调用的变量
     */
    window.h5Template = {
        hasPractice: '0'
    }

    let lock = true;
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

    if (configData.bg == '') {
        $(".container").css({ 'background-image': 'url(./image/defaultBg.jpg)' })
    }


    var currentWindowId = $(window.frameElement).attr('id');
    var cacheFrameId = "h5_course_cache_frame";

    // 填充内容
    const page = {
        options: configData.source.options,
        time: parseInt(configData.source.time),
        bomb: configData.source.bomb,
        setThemePic: function() {
            let img = '';
            if (this.options[0].themePic) {
                img = `<img class="showImg" src="${this.options[0].themePic}"/>`
            } else {
                img = `<img class="showImg" src="image/img.jpg"/>`
            }
            $(".pic").append(img);
        },
        setAudio: function() {
            let audio = '';
            audio = `<audio class="showAudio" webkit-playsinline controls preload="preload" src="" data-syncaudio="audioShow"></audio>`
            $(".source-audio").append(audio);
        },
        setBombAudio: function() {
            $('#bombAudio').attr('src', this.bomb)
        },
        setTime: function() {
            $('.count-down').text(this.time + 's')
        },
        showBtn: function() {
            let btn = '';
            for (let i = 0; i < this.options.length; i++) {
                btn += `<span class="changeBtn" data-syncactions="btn-${i}">${i+1}</span>`;
            }
            $(".pic-btn").append(btn);
        },
        judgeSource: function() {
            $(".changeBtn").eq(0).addClass('active');
            if (this.options[0].themePic != '') {
                $(".showImg").attr('src', this.options[0].themePic);
            }
            if (this.options[0].audio != '') {
                $(".showAudio").attr('src', this.options[0].audio);
            } else {
                $(".source-audio").css({
                    display: "none"
                })
            }
        },
        init: function() {
            this.setThemePic()
            this.setAudio()
            this.setTime()
            this.setBombAudio()
            this.showBtn()
            this.judgeSource()
            if (this.options.length < 2) {
                $(".leftBtnBg").css({ display: 'none' })
                $(".rightBtnBg").css({ display: 'none' })
            }
        }
    }
    page.init();

    // 同步拖拽
    // 倒计时
    let start = false
    let time = page.time
    let timer = null
    // 更新设备信息
    let u = navigator.userAgent;
    if ((u.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
        //学生端老师不提供拖拽功能
    } else {
        //仅PC端老师提供拖拽功能
        $('.bomb').drag({
            before: function(e) {
                // $audioStart ? $audioStart.pause() : '';
                SDK.pauseRudio({
                    index: $audioStart,
                    syncName: $('#start').attr("data-syncaudio")
                })
                if (!isSync) {
                    $('.bomb').trigger('syncDragBefore', {
                        left: $('.bomb').data('startPos').left,
                        top: $('.bomb').data('startPos').top,
                        pageX: '',
                        pageY: '',
                    })
                    return
                }
                if (window.frameElement.getAttribute('user_type') == 'tea') {
                    SDK.bindSyncEvt({
                        index: $(e.currentTarget).data('syncactions'),
                        eventType: 'dragBefore',
                        method: 'drag',
                        left: $('.bomb').data('startPos').left,
                        top: $('.bomb').data('startPos').top,
                        pageX: '',
                        pageY: '',
                        syncName: 'syncDragBefore'
                    })
                }
            },
            process: function(e) {
                // $audioStart ? $audioStart.pause() : '';
                SDK.pauseRudio({
                    index: $audioStart,
                    syncName: $('#start').attr("data-syncaudio")
                })
                if (!isSync) {
                    $('.bomb').trigger('syncDragProcess', {
                        left: $('.bomb').attr('data-left'),
                        top: $('.bomb').attr('data-top'),
                        pageX: '',
                        pageY: '',
                    })
                    return
                }
                if (lock && window.frameElement.getAttribute('user_type') == 'tea') {
                    lock = false
                    setTimeout(function() {
                        SDK.bindSyncEvt({
                            index: $('.bomb').data('syncactions'),
                            eventType: 'dragProcess',
                            method: 'drag',
                            left: $('.bomb').attr('data-left'),
                            top: $('.bomb').attr('data-top'),
                            pageX: '',
                            pageY: '',
                            syncName: 'syncDragProcess'
                        })
                        lock = true
                    }.bind(this), 300)
                }
            },
            end: function() {
                if (!isSync) {
                    $('.bomb').trigger('syncDragEnd', {
                        left: $('.bomb').attr('data-left'),
                        top: $('.bomb').attr('data-top'),
                        pageX: '',
                        pageY: '',
                    })
                    return
                }
                if (window.frameElement.getAttribute('user_type') == 'tea') {
                    setTimeout(function() {
                        SDK.bindSyncEvt({
                            index: $('.bomb').data('syncactions'),
                            eventType: 'dragEnd',
                            method: 'drag',
                            left: $('.bomb').attr('data-left'),
                            top: $('.bomb').attr('data-top'),
                            pageX: '',
                            pageY: '',
                            syncName: 'syncDragEnd',
                        })
                    }, 300)
                }
            }
        })
    }


    $('.bomb').on("syncDragBefore", function(e, pos) {
        $(this).data('startPos', {
            left: pos.left,
            top: pos.top
        })
        SDK.setEventLock()
    })

    $('.bomb').on("syncDragProcess", function(e, pos) {
        $(this).css({
            'left': pos.left,
            'top': pos.top
        })
        SDK.setEventLock()
    })

    $('.bomb').on('syncDragEnd', function(e, pos) {
        $(this).css({
            'left': pos.left,
            'top': pos.top
        })
        $(this).addClass('drop-down')
        SDK.setEventLock()
    })
    let startTime = true;
    $(".btn").on('click', function(e) {
        // console.log(start)

        //console.log(start)
        if (startTime == true) {
            startTime = false;
            if (!isSync) {
                $(this).trigger('syncCountDown')
                return
            }
            if (window.frameElement.getAttribute('user_type') == 'tea') {
                SDK.bindSyncEvt({
                    index: $(e.currentTarget).data('syncactions'),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'syncCountDown'
                })
            }
        }
    })
    let size = 0;
    let times = parseInt(configData.source.time) * 2;
    let $audioStart = document.getElementById('start');
    let $audioEnd = document.getElementById('end');
    $('.btn').on('syncCountDown', function(e) {
        if (start == true) {
            start = false
        } else {
            start = true
        }
        // if ($(window.frameElement).attr('id') == 'h5_course_self_frame') {
        if (start == true) {
            $('.btn').addClass('stop')
            $('.btn').removeClass('start')
        } else {
            $('.btn').addClass('start')
            $('.btn').removeClass('stop')
        }
        // }

        if (start) {
            start = true
            //========= reset time
            time = page.time
            $('.count-down').text(time + 's')
            //========= reset time
            if (timer) {
                clearInterval(timer)
            }
            // if ($(window.frameElement).attr('id') == 'h5_course_self_frame') {
            timer = setInterval(function() {
                size += parseFloat(1 / times);

                $('.bomb-img').css({
                    transition: '0.5s cubic-bezier(0.2, 1.84, 0.2, -0.32)',
                    'transform': 'scale(' + (1 + size) + ')',
                    'transform-origin': 'bottom'
                })
                time--
                isBom(time)
                $('.count-down').text(time + 's')

            }, 1000);
            // }
            $audioStart.currentTime = 0;
            // $audioStart ? $audioStart.play() : '';
            SDK.playRudio({
                index: $audioStart,
                syncName: $("#start").attr("data-syncaudio")
            })
        } else {
            console.log("结束")

            if (timer) {
                clearInterval(timer)
                // $audioStart ? $audioStart.pause() : '';
                SDK.pauseRudio({
                    index: $audioStart,
                    syncName: $('#start').attr("data-syncaudio")
                })
                console.log('111111111111')
            }
            time = page.time
            $('.count-down').text(time + 's')
            start = false
            $('.bomb').removeClass('drop-down')
            // $('.bomb img').removeClass('beat')
            size = 0;
            $('.bomb-img').css({
                'transition': 'none',
                'transform': 'scale(1)',
                'width': '1.2rem',
                'height': "1.45rem"
            })
            $(".bomb-img img").css({
                'display': 'block'
            })
            $(".bomb .count-down").css({
                'display': 'block'
            })
        }
        SDK.setEventLock()
        startTime = true;
    })

    function isBom(time) {
        if (time < 0) {

            $('.bomb-img').css({
                'transition': 'none'
            })
            $('.bomb-img img').css({
                'transition': 'none',
                'display': 'none'
            })
            // $audioStart ? $audioStart.pause() : '';
            SDK.pauseRudio({
                index: $audioStart,
                syncName: $('#start').attr("data-syncaudio")
            })

            if (!isSync || $(window.frameElement).attr('user_id') == SDK.getClassConf().user.id) {
                $audioEnd.currentTime = 0;
                // $audioEnd ? $audioEnd.play() : '';
                SDK.playRudio({
                    index: $audioEnd,
                    syncName: $("#end").attr("data-syncaudio")
                })
            }

            // let img='<img src="image/ball.gif" alt="" class="bomb-gif">'
            // $(".bomb").append(img)
            time = 0
            clearInterval(timer)
            start = false
            $('.bomb-gif').attr('src', 'image/ball.gif').removeClass('hide')
            // $('.bomb').removeClass('drop-down')
            // $('.bomb-img img').removeClass('beat')


            $(".bomb .count-down").css({
                'display': 'none'
            })
            $('.bomb-img').css({
                'width': (size * 1.8) + 'rem',
                'height': (size * 1.8) * 1.45 / 1.2 + 'rem'
            })
            $(".bomb .bomb-gif").css({
                'width': (size * 2) + 'rem',
                'height': (size * 2) * 1.45 / 1.2 + 'rem'
            })
            size = 0;

            SDK.bindSyncEvt({
              index: 'clear',
              eventType: 'click',
              method: 'event',
              syncName: 'clear', //SDK.js中对其特殊处理
              recoveryMode: '1',
              otherInfor: {
                gameover: 1,
            },

          });
            setTimeout(function() {
                $('.bomb-gif').attr('src', '').addClass('hide')
                // $('.bomb-gif').remove()
                $('.count-down').text(page.time + 's')
                $('.bomb').css({
                    'left': '15.2rem',
                    'top': '6.2rem',
                    'transition': 'none',
                    'transform': 'scale(1)',
                    'width': '1.2rem',
                    'height': "1.45rem"
                }).removeAttr("data-left").removeAttr("data-top");
                $('.bomb-img').css({
                    'width': '1.2rem',
                    'height': "1.45rem",
                    'transform': 'scale(1)'
                })
                $(".bomb-img img").css({
                    'display': 'block',
                    'width': '100%',
                    'height': '100%'
                })
                $(".bomb .count-down").css({
                    'display': 'block'
                })
                $('.btn').addClass('start')
                $('.btn').removeClass('stop')

            }, 1000)
            // if(page.bomb){
            // 	document.querySelector('#bombAudio').play()
            // }
        }


    }


    // 音频播放
    const audioEle = document.querySelector('.showAudio')
    let play = false
    let audioPlay = function(message) {
        if (!isSync) {
            // audioEle.play()
            SDK.playRudio({
                index: audioEle,
                syncName: $("audio.showAudio").attr("data-syncaudio")
            })
            $('.source-audio img').attr('src', './image/btn-audio.gif')
        } else {
            // if ($(window.frameElement).attr('id') === 'h5_course_self_frame') {
            if (message == undefined || message.operate == 1) {
                // audioEle.play()
                SDK.playRudio({
                    index: audioEle,
                    syncName: $("audio.showAudio").attr("data-syncaudio")
                })
                $('.source-audio img').attr('src', './image/btn-audio.gif')
            }
            // }
        }
    }
    let audioPause = function(message) {
        if (!isSync) {
            // audioEle.pause()
            SDK.pauseRudio({
                index: audioEle,
                syncName: $('audio.showAudio').attr("data-syncaudio")
            })
        } else {
            if (message == undefined || message.operate == 1) {
                // audioEle.pause()
                SDK.pauseRudio({
                    index: audioEle,
                    syncName: $('audio.showAudio').attr("data-syncaudio")
                })
            }
        }
        $('.source-audio img').attr('src', './image/btn-audio.png')
    }

    let audioClick = true
    $('.source-audio').on('click touchend', function(e) {
        if (e.type == "touchend") {
            e.preventDefault()
        }
        if (audioClick) {
            audioClick = false
            if (!isSync) {
                $(this).trigger('syncAudioClick')
                return
            }
            if (window.frameElement.getAttribute('user_type') == 'tea') {
                SDK.bindSyncEvt({
                    index: $(e.currentTarget).data('syncactions'),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'syncAudioClick',
                    funcType: 'audio'
                })
            } else {
                $(this).trigger('syncAudioClick')
            }
        }

    })

    $('.source-audio').on('syncAudioClick', function(e, message) {
        if (play) {
            audioPause(message)
        } else {
            audioPlay(message)
            audioEle.onended = function() {
                rightClick = true
                leftClick = true
                pageBtn = true
                // play = true
                $('.source-audio img').attr('src', './image/btn-audio.png')
            }
        }
        SDK.setEventLock()
        audioClick = true
    })

    //点击右切换按钮
    let options = configData.source.options;
    let rightClick = true;
    let currentPage = 0;
    $(".rightBtnBg").on('click touchstart', function(e) {
        $('.source-audio img').attr('src', './image/btn-audio.png')
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        if (currentPage < options.length - 1) {
            currentPage++;
        } else {
            currentPage = options.length - 1;
            return
        }


        if (rightClick) {
            rightClick = false;
            if (!isSync) {
                $(this).trigger('syncRightClick');
                return
            }

            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                syncName: 'syncRightClick',
                recoveryMode: '1',
                otherInfor: {
                    currentPage: currentPage
                }
            });
        }
    })

    $(".rightBtnBg").on('syncRightClick', function(e, message) {

        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            currentPage = obj.currentPage;
            if (message.operate == 5) {
                resetAc(currentPage)
            }
        } else {
            currentPage = currentPage;
        }
        console.log(currentPage,'currentPage111++++++++++ rightBtnBg')
        isShow(currentPage);
        SDK.setEventLock();
        rightClick = true;
    })

    //点击左切换按钮
    let leftClick = true;
    $(".leftBtnBg").on('click touchstart', function(e) {
        $('.source-audio img').attr('src', './image/btn-audio.png')
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        if (currentPage > 0) {
            currentPage--;

        } else {
            currentPage = 0;
            return
        }

        if (leftClick) {
            leftClick = false;
            if (!isSync) {
                $(this).trigger('syncLeftClick');
                return
            }

            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                syncName: 'syncLeftClick',
                recoveryMode: '1',
                otherInfor: {
                    currentPage: currentPage
                }
            });
        }
    })

    $(".leftBtnBg").on('syncLeftClick', function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            currentPage = obj.currentPage;
            if (message.operate == 5) {
                resetAc(currentPage)
            }
        } else {
            currentPage = currentPage;
        }

        isShow(currentPage);
        SDK.setEventLock();
        leftClick = true;
    })

    //点击标识页码按钮
    let pageBtn = true;
    $('.changeBtn').on('click touchstart', function(e) {
        $('.source-audio img').attr('src', './image/btn-audio.png')
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        currentPage = $(this).text() - 1;
        console.log( currentPage = $(this).text() - 1)
        if (pageBtn) {
            pageBtn = false;
            if (!isSync) {
                $(this).trigger('syncPagebtnClick');
                return
            }

            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                syncName: 'syncPagebtnClick',
                recoveryMode: '1',
                otherInfor: {
                    currentPage: currentPage
                }
            });
        }
    })

    $('.changeBtn').on('syncPagebtnClick', function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            currentPage = obj.currentPage;
            if (message.operate == 5) {
                resetAc(currentPage)
            }
        } else {
            currentPage = currentPage;
        }
        isShow(currentPage);
        SDK.setEventLock();
        pageBtn = true;

    })
    //页面展示内容
    function isShow(currentPage) {
      console.log(currentPage,'currentPage111++++++++++ isShow')

        if (options[currentPage].themePic != '') {
            $(".pic img").css({
                display: "block"
            })
            $(".showImg").attr('src', options[currentPage].themePic);
        } else {
            $(".showImg").attr('src', 'image/img.jpg');
        }
        if (options[currentPage].audio != '') {
            $(".source-audio").css({
                display: "block"
            })
            $(".showAudio").attr('src', options[currentPage].audio);
        } else {
            $(".source-audio").css({
                display: "none"
            })
        }


        let btns = $(".changeBtn ");
        for (let i = 0; i < btns.length; i++) {
            if (i == currentPage) {
                btns.eq(i).addClass('active')
            } else {
                btns.eq(i).removeClass('active')
            }
        }

        if (currentPage == options.length - 1) {
            setBtnStyle(0.5, 1)
        } else if (currentPage == 0) {
            setBtnStyle(1, 0.5)
        } else {
            setBtnStyle(1, 1)
        }
    }

    function setBtnStyle(num1, num2) {
        let rightBtn = $(".rightBtnBg")
        let leftBtn = $(".leftBtnBg")
        rightBtn.css({
            'opacity': num1
        })
        leftBtn.css({
            'opacity': num2
        })
    }



    function resetAc(currentPage) {
        $(".showImg").attr('src', options[currentPage].themePic);
        if (options[currentPage].audio != '') {
            $(".source-audio").css({
                display: "block"
            })
            $(".showAudio").attr('src', options[currentPage].audio);
        }
    }
    window.generalTplData = function (message) {
      if(message.actionType == "pagAdd") {
        
          $('.source-audio img').attr('src', './image/btn-audio.png')
          let btns = $(".changeBtn ");
          currentPage = currentPage +=1;
          if (currentPage >= btns.length) {
            currentPage = options.length - 1;
          }
          if (pageBtn) {
              pageBtn = false;
              // $('.changeBtn').trigger('syncPagebtnClick');
              SDK.bindSyncEvt({
                index:$('.changeBtn').data('syncactions'),
                eventType: 'click',
                method: 'event',
                syncName: 'syncPagebtnClick',
                recoveryMode: '1',
                otherInfor: {
                    currentPage: currentPage
                }
            });
          }
     
      }
      
    }
})
