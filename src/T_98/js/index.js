"use strict"
import '../../common/js/common_1v1.js'
import "../../common/template/multyDialog/index.js";
import "../../common/js/teleprompter.js"
import "../../common/template/avatarUpWall/index.js";
import {feedbackAnimation} from "../../common/template/feedbackAnimation/index.js";
import {USER_TYPE, CLASS_STATUS, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE} from "../../common/js/constants.js";  // 导入常量

$(function () {
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      item: configData.source.options.reduce((sum, group) => sum + (group.cliclElement.length - 1), 0) || 0
    },
    teaData: {
      teacher_type: TEACHER_TYPE.TEACHING_INPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.LISTEN
    },
  })
  // 全局配置
  window.h5Template = {
    hasPractice: '1'
  }

  // 同步状态检查
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  // 设置背景图
  if (configData.bg == '') {
    $(".container").css({'background-image': 'url(./image/bg.png)'})
  }

  // 获取配置选项
  const options = configData.source.options;

  // 存储所有Lottie动画实例
  const lottieInstances = {};

  /**
   * 页面管理器
   * 负责管理页面元素、动画和交互
   */
  const pageManager = {
    // 记录已加载的元素
    loadedElements: {},

    // 结束标志
    isEnded: false,

    /**
     * 初始化页面
     */
    init() {
      this.initFirstElement();
      // 初始化第二组开始的每组第一个元素
      this.initOtherGroupFirstElements();
    },

    /**
     * 初始化第一个元素
     * 创建并显示页面上的第一个交互元素
     */
    async initFirstElement() {
      // 创建第一组第一个元素的所有组件
      const {status1El, status2El, textPicEl} = await this.createElementSet(0, 0, options[0].cliclElement[0]);

      // 添加到页面
      $('.mainArea').append(status1El, status2El, textPicEl);

      // 初始化元素并播放序列
      this.initAndPlayElementSequence(0, 0);
    },

    /**
     * 初始化第二组开始的每组第一个元素
     * 直接展示每组第一个元素的状态1
     */
    async initOtherGroupFirstElements() {
      // 从第二组开始循环
      for (let groupIndex = 1; groupIndex < options.length; groupIndex++) {
        // 只处理每组的第一个元素
        const elementIndex = 0;
        const element = options[groupIndex].cliclElement[elementIndex];

        // 创建元素集合
        const {status1El, textPicEl} = await this.createElementSet(groupIndex, elementIndex, element);

        // 添加到页面
        $('.mainArea').append(status1El, textPicEl);

        // 立即显示状态1
        status1El.show();

        // 如果状态1是JSON动画，先初始化再自动播放
        if (element.status1 && this.isJsonFile(element.status1)) {
          const status1AnimId = `status1_${groupIndex}_${elementIndex}`;
          // 确保动画已初始化
          if (!lottieInstances[status1AnimId]) {
            await this.initLottieAnimation(status1El, element.status1, true);
          }
          lottieAnimations.play(lottieInstances[status1AnimId]);
        }

        // 标记为已加载
        const elementKey = `${groupIndex}_${elementIndex}`;
        this.loadedElements[elementKey] = true;
      }
    },

    /**
     * 加载下一个元素 - 按需加载模式
     * @param {number} groupIndex - 当前组索引
     * @param {number} elementIndex - 当前元素索引
     */
    async loadNextElement(groupIndex, elementIndex) {
      // 计算下一个元素的位置
      let nextElement = elementIndex + 1;
      let nextGroup = groupIndex;

      // 如果当前组中没有更多元素，移动到下一组的第一个元素
      if (nextElement >= options[groupIndex].cliclElement.length) {
        nextElement = 0;
        nextGroup++;

        // 如果没有更多组，则结束
        if (nextGroup >= options.length) {
          return;
        }
      }

      // 检查元素是否已经加载过
      const elementKey = `${nextGroup}_${nextElement}`;
      if (this.loadedElements[elementKey]) {
        return;
      }

      // 标记为已加载
      this.loadedElements[elementKey] = true;

      // 获取下一个元素的配置
      const nextElementConfig = options[nextGroup].cliclElement[nextElement];

      // 创建元素
      const {status1El, status2El, textPicEl} = await this.createElementSet(
        nextGroup, nextElement, nextElementConfig
      );

      // 添加到页面，但先隐藏起来
      const fragment = document.createDocumentFragment();
      fragment.appendChild(status1El[0]);
      fragment.appendChild(status2El[0]);
      fragment.appendChild(textPicEl[0]);

      $('.mainArea').append(fragment);
    },

    /**
     * 初始化并播放元素序列
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {Object} destroyInfo - 需要销毁的元素信息（可选）
     */
    async initAndPlayElementSequence(groupIndex, elementIndex, destroyInfo = null) {
      // 获取相关数据和元素
      const group = options[groupIndex];
      const element = group.cliclElement[elementIndex];
      const status1El = $(`[data-syncactions="status1_${groupIndex}_${elementIndex}"]`);
      const status2El = $(`[data-syncactions="status2_${groupIndex}_${elementIndex}"]`);
      // 初始化喇叭动画（如果有内容音频）
      if (element.contentAudio) {
        const labaAnimKey = `laba${groupIndex}_${elementIndex}`;
        if (!lottieInstances[labaAnimKey]) {
          lottieInstances[labaAnimKey] = await lottieAnimations.init(
            null,
            './image/laba.json',
            `[data-syncactions="contentAudio${groupIndex}_${elementIndex}"]`,
            false
          );
        }
      }
      // 初始化状态1（如果是JSON）
      if (element.status1 && this.isJsonFile(element.status1)) {
        await this.initLottieAnimation(status1El, element.status1, true);
      }

      // 判断是否有状态2或状态2音频
      const hasStatus2 = element.status2;
      const hasStatus2Audio = element.status2Audio
      console.log(element)
      if (hasStatus2 || hasStatus2Audio) {
        console.log('有状态2或状态2音频')
        // 在开始显示新元素前，销毁旧元素（如果有销毁信息）

        // 如果有状态2，初始化并显示状态2动画
        if (hasStatus2) {
          const status2AnimId = `status2_${groupIndex}_${elementIndex}`;
          await this.initLottieAnimation(status2El, element.status2);
          status2El.show();
          setTimeout(() => {
            if (destroyInfo) {
              this.destroyElements(
                destroyInfo.destroyGroup,
                destroyInfo.destroyElement,
                destroyInfo.destroyAll
              );
            }
          }, 200)
          // 播放状态2动画
          lottieAnimations.play(lottieInstances[status2AnimId]);
        }
        // 开始状态2到状态1的转换过程
        this.transitionToStatus1(groupIndex, elementIndex, destroyInfo);
      } else {
        // 如果没有状态2和状态2音频，直接显示状态1和文字图片
        // 旧元素已在上方销毁，这里直接显示新元素
        await this.showStatus1AndContent(groupIndex, elementIndex, destroyInfo);
      }

      // 预加载下一个元素
      this.loadNextElement(groupIndex, elementIndex);
    },

    /**
     * 从状态2转换到状态1
     * 该函数负责监听状态2动画和音频的完成事件，然后触发状态1的显示
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {Object} destroyInfo - 需要销毁的元素信息（可选）
     */
    transitionToStatus1(groupIndex, elementIndex, destroyInfo = null) {
      // 获取相关元素和配置
      const status2AnimId = `status2_${groupIndex}_${elementIndex}`;
      const element = options[groupIndex].cliclElement[elementIndex];

      // 判断状态2 JSON动画和音频的存在情况
      const hasStatus2Json = element.status2 && lottieInstances[status2AnimId];
      const hasStatus2Audio = element.status2Audio;

      // 根据不同情况处理
      if (hasStatus2Json && hasStatus2Audio) {
        // 情况1: 同时存在状态2 JSON动画和音频，需要等待两者都结束
        let animCompleted = false;
        let audioCompleted = false;

        // 创建检查函数，当两者都完成时调用showStatus1AndContent
        const checkBothCompleted = () => {
          if (animCompleted && audioCompleted) {
            this.stopLottieAnimation(status2AnimId);
            this.showStatus1AndContent(groupIndex, elementIndex, destroyInfo);
          }
        };

        // 监听动画完成事件
        lottieInstances[status2AnimId].addEventListener("complete", () => {
          animCompleted = true;
          checkBothCompleted();
        });

        // 播放音频并监听完成事件
        this.playAudio(element.status2Audio, `status2Audio_${groupIndex}_${elementIndex}`, true)
          .then(() => {
            audioCompleted = true;
            checkBothCompleted();
          });
      } else if (hasStatus2Json) {
        // 情况2: 只有状态2 JSON动画，等待动画完成
        lottieInstances[status2AnimId].addEventListener("complete", () => {
          this.stopLottieAnimation(status2AnimId);
          this.showStatus1AndContent(groupIndex, elementIndex, destroyInfo);
        });
      } else if (hasStatus2Audio) {
        if (destroyInfo) {
          this.destroyElements(
            destroyInfo.destroyGroup,
            destroyInfo.destroyElement,
            destroyInfo.destroyAll
          );
        }
        // 情况3: 只有状态2音频，播放并等待音频完成
        this.playAudio(element.status2Audio, `status2Audio_${groupIndex}_${elementIndex}`, true)
          .then(() => {
            this.showStatus1AndContent(groupIndex, elementIndex, destroyInfo);
          });
      } else {
        // 情况4: 没有状态2动画和音频，直接显示状态1和内容
        // 此情况应该在initAndPlayElementSequence中处理，但为了健壮性这里也保留
       this.showStatus1AndContent(groupIndex, elementIndex, destroyInfo);
      }
    },

    /**
     * 显示状态1和内容
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {Object} destroyInfo - 需要销毁的元素信息（可选）
     */
    async showStatus1AndContent(groupIndex, elementIndex, destroyInfo = null) {
      // 获取相关数据和元素
      const group = options[groupIndex];
      const element = group.cliclElement[elementIndex];
      const status1El = $(`[data-syncactions="status1_${groupIndex}_${elementIndex}"]`);
      const textPicEl = $(`[data-syncactions="textPic_${groupIndex}_${elementIndex}"]`);
      $(`[data-syncactions="status2_${groupIndex}_${elementIndex}"]`).hide();
      // 显示状态1
      status1El.show();
      setTimeout(() => {
        if (destroyInfo) {
          this.destroyElements(
            destroyInfo.destroyGroup,
            destroyInfo.destroyElement,
            destroyInfo.destroyAll
          );
        }
      }, 200)

      // 判断是否为第二组之后的每组第一个元素（初始化时已播放）
      const isFirstElementOfLaterGroup = groupIndex >= 1 && elementIndex === 0;

      // 如果状态1是JSON动画且不是已初始化的组第一个元素，则播放它
      if (element.status1 && this.isJsonFile(element.status1) && !isFirstElementOfLaterGroup) {
        const status1AnimId = `status1_${groupIndex}_${elementIndex}`;
        lottieAnimations.play(lottieInstances[status1AnimId]);
      }

      // 显示文字图片（如果有）
      if (element.textPic) {
        textPicEl.show();
        // 播放文字图片喇叭动画（如果同时存在contentAudio）
        if (element.contentAudio) {
          const labaAnimKey = `laba${groupIndex}_${elementIndex}`;
          if (lottieInstances[labaAnimKey]) {
            lottieAnimations.play(lottieInstances[labaAnimKey]);
          }
        }
      }

      // 播放内容音频（如果有）
      if (element.contentAudio) {
        await this.playAudio(element.contentAudio, `contentAudio_${groupIndex}_${elementIndex}`, true);
        SDK.reportTrackData({
          action:'CK_FT_INTERACTION_AUDIOPLAY', //事件名称
          data:{}, // 老师和学生  都需要上报的数据
          teaData:{
            roundid:groupIndex+1,
            audio:elementIndex+1
          },
          stuData:{},  // 只有学生端会上报的数据
        },USER_TYPE.TEA)
        // 音频播放结束后停止喇叭动画（如果有）
        const labaAnimKey = `laba${groupIndex}_${elementIndex}`;
        if (lottieInstances[labaAnimKey]) {
          lottieAnimations.stop(lottieInstances[labaAnimKey]);
        }
      }

      // 判断是否需要结束课程
      const isLastElement = (groupIndex === options.length - 1 &&
        elementIndex === options[groupIndex].cliclElement.length - 1);
      const shouldEndNow = isLastElement || element.afterclick === '2';

      if (shouldEndNow) {
        this.end();
        return;
      }

      // 判断当前元素是否是组的最后一个元素
      const isLastElementInGroup = elementIndex === options[groupIndex].cliclElement.length - 1;
      // 判断是否是最后一组
      const isLastGroup = groupIndex === options.length - 1;

      // 如果是组的最后一个元素但不是最后一组的最后一个元素，直接进入下一组
      if (isLastElementInGroup && !isLastGroup) {
        // 计算下一组第一个元素的位置
        const nextGroup = groupIndex + 1;
        const nextElement = 0;

        // 获取当前元素的afterclick值
        const currentElement = options[groupIndex].cliclElement[elementIndex];
        const afterclick = currentElement.afterclick;

        // 根据afterclick值决定销毁策略
        const destroyAll = afterclick === '0';

        // 直接初始化并播放下一组第一个元素
        this.initNextElement(nextGroup, nextElement, {
          destroyGroup: groupIndex,
          destroyElement: elementIndex,
          destroyAll: destroyAll
        }, true);
      } else {
        // 设置状态1的点击事件（仅对非最后一个元素或最后一组最后一个元素）
        this.setupStatus1ClickEvent(status1El);

        // 显示小手（如果需要且不是组内最后一个元素）
        if (group.showHand === '1' && !isLastElementInGroup) {
          this.setHand(groupIndex, elementIndex);
        } else {
          // 确保隐藏小手
          this.setHand(null, null);
        }
      }
    },

    /**
     * 设置状态1点击事件
     * @param {Object} status1El - 状态1元素jQuery对象
     */
    setupStatus1ClickEvent(status1El) {
      const self = this; // 保存对pageManager的引用
      let hasClicked = false;

      status1El.syncbind("click touchstart", function (dom, next) {
        if (hasClicked) {
          return;
        }
        hasClicked = true;
        if (isSync) {
          next();
        } else {
          next(false);
        }
      }, function () {
        //小手隐藏
        self.setHand(null, null);
        // 从点击的元素中获取组下标和元素下标信息
        const syncActionAttr = status1El.attr('data-syncactions') || '';
        // 处理点击事件
        self.handleStatus1Click(syncActionAttr, false);
      });
    },

    /**
     * 处理状态1点击后逻辑
     * @param {string} syncActionAttr - 同步动作属性
     * @param {boolean} isAutoClick - 是否为自动点击
     */
    async handleStatus1Click(syncActionAttr, isAutoClick = false) {
      // 获取当前点击元素的位置
      let prevGroup;
      let prevElement;

      // 从属性值中解析出组下标和元素下标
      if (syncActionAttr) {
        const parts = syncActionAttr.split('_');
        if (parts.length >= 3) {
          prevGroup = parseInt(parts[1]);
          prevElement = parseInt(parts[2]);
        }
      }
      console.log('组',prevGroup+1,'元素',prevElement+1)
      SDK.reportTrackData({
        action:'CK_FT_INTERACTION_ITEM', //事件名称
        data:{}, // 老师和学生  都需要上报的数据
        teaData:{},  // 只有老师端会上报的数据
        stuData:{
          roundid: prevGroup+1,
          item: prevElement+1
        },
      },USER_TYPE.STU)

      // 获取当前元素的afterclick值
      const currentElement = options[prevGroup].cliclElement[prevElement];
      const afterclick = currentElement.afterclick;

      // 根据afterclick值决定销毁策略
      switch (afterclick) {
        case '0':
          // 不再立即销毁，而是保存需要销毁的元素信息
          const nextPosition = this.calculateNextElementPosition(prevGroup, prevElement);
          // 初始化并播放下一个元素（传入当前元素信息，用于在合适时机销毁）
          await this.initNextElement(nextPosition.nextGroup, nextPosition.nextElement, {
            destroyGroup: prevGroup,
            destroyElement: prevElement,
            destroyAll: true
          }, isAutoClick);
          break;

        case '1':
          // 不再立即销毁，保存需要销毁的元素信息
          const nextPos = this.calculateNextElementPosition(prevGroup, prevElement);
          // 销毁当前元素status1的点击事件
          const status1El = $(`[data-syncactions="status1_${prevGroup}_${prevElement}"]`);
          status1El.off('click touchstart');
          // 初始化并播放下一个元素（传入当前元素信息，用于在合适时机销毁）
          await this.initNextElement(nextPos.nextGroup, nextPos.nextElement, {
            destroyGroup: prevGroup,
            destroyElement: prevElement,
            destroyAll: false
          }, isAutoClick);
          break;

        case '2':
          //先把状态2、状态1和内容图隐藏
          const status2El = $(`[data-syncactions="status2_${prevGroup}_${prevElement}"]`);
          const status1 = $(`[data-syncactions="status1_${prevGroup}_${prevElement}"]`);
          const textPicEl = $(`[data-syncactions="textPic_${prevGroup}_${prevElement}"]`);
          status2El.hide();
          status1.hide();
          textPicEl.hide();
          // 只有非自动点击才播放点击音效
          if (!isAutoClick) {
            await $('#clickAudio').playAudioSync()
          }
          // 重新初始化并播放当前元素
          await this.initAndPlayElementSequence(prevGroup, prevElement);
          break;
      }

      SDK.setEventLock();
    },

    /**
     * 计算下一个元素的位置
     * @param {number} currentGroup - 当前组索引
     * @param {number} currentElement - 当前元素索引
     * @returns {Object} 下一个元素的位置信息
     */
    calculateNextElementPosition(currentGroup, currentElement) {
      // 计算下一个元素的位置
      let nextElement = currentElement + 1;
      let nextGroup = currentGroup;

      // 如果当前组中没有更多元素，移动到下一组的第一个元素
      if (nextElement >= options[currentGroup].cliclElement.length) {
        nextElement = 0;
        nextGroup++;

        // 如果没有更多组，可以循环回到第一组或者处理结束逻辑
        if (nextGroup >= options.length) {
          nextGroup = -1; // 表示没有下一个元素
          nextElement = -1;
        }
      }

      return {
        nextGroup,
        nextElement
      };
    },

    /**
     * 初始化并播放下一个元素
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {Object} destroyInfo - 需要销毁的元素信息（可选）
     * @param {boolean} isAutoClick - 是否为自动点击
     */
    async initNextElement(groupIndex, elementIndex, destroyInfo = null, isAutoClick = false) {
      // 检查是否有下一个元素
      if (groupIndex < 0 || elementIndex < 0 || groupIndex >= options.length) {
        this.destroyElements(
          destroyInfo.destroyGroup,
          destroyInfo.destroyElement,
          destroyInfo.destroyAll
        );
        return;
      }

      // 只有非自动点击才播放点击音效
      if (!isAutoClick) {
        await $('#clickAudio').playAudioSync()
      }

      try {
        // 初始化并播放元素序列，传入销毁信息
        await this.initAndPlayElementSequence(groupIndex, elementIndex, destroyInfo);
      } catch (error) {
        console.error(`初始化下一个元素失败: ${error.message}`);
      }
    },

    /**
     * 所有轮次结束
     * 显示结束反馈动画
     */
    async end() {
      // 如果已经结束，不再执行
      if (this.isEnded) {
        return;
      }

      // 设置结束标志
      this.isEnded = true;

      await feedbackAnimation('feedKey1');
      SDK.reportTrackData({
        action:'CK_FT_INTERACTION_COMPLETE', //事件名称
        data:{}, // 老师和学生  都需要上报的数据
        teaData:{result:'success'},  // 只有老师端会上报的数据
        stuData:{},  // 只有学生端会上报的数据
      },USER_TYPE.TEA)
      SDK.setEventLock();
    },

    /**
     * 销毁元素
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {boolean} destroyAll - 是否销毁所有元素
     */
    destroyElements(groupIndex, elementIndex, destroyAll = true) {
      try {
        // 清理状态2资源
        this.cleanupElementResources(groupIndex, elementIndex, 'status2');

        // 如果需要销毁所有元素
        if (destroyAll) {
          this.cleanupElementResources(groupIndex, elementIndex, 'status1');
          this.cleanupElementResources(groupIndex, elementIndex, 'textPic');
        }
      } catch (error) {
        console.error(`销毁元素失败 - 组:${groupIndex}, 元素:${elementIndex}`, error);
      }
    },

    /**
     * 清理元素资源（辅助方法）
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {string} elementType - 元素类型 (status1, status2, textPic)
     */
    cleanupElementResources(groupIndex, elementIndex, elementType) {
      // 根据元素类型定义相关ID和选择器
      const elementConfigs = {
        status1: {
          domSelector: `[data-syncactions="status1_${groupIndex}_${elementIndex}"]`,
          animId: `status1_${groupIndex}_${elementIndex}`,
          audioSelector: `.mainArea audio[data-syncaudio="contentAudio_${groupIndex}_${elementIndex}"]`,
          audioSyncName: `contentAudio_${groupIndex}_${elementIndex}`
        },
        status2: {
          domSelector: `[data-syncactions="status2_${groupIndex}_${elementIndex}"]`,
          animId: `status2_${groupIndex}_${elementIndex}`,
          audioSelector: `.mainArea audio[data-syncaudio="status2Audio_${groupIndex}_${elementIndex}"]`,
          audioSyncName: `status2Audio_${groupIndex}_${elementIndex}`
        },
        textPic: {
          domSelector: `[data-syncactions="textPic_${groupIndex}_${elementIndex}"]`,
          labaAnimId: `laba${groupIndex}_${elementIndex}`
        }
      };

      const config = elementConfigs[elementType];
      if (!config) return;

      // 清理动画
      if (config.animId && lottieInstances[config.animId]) {
        lottieAnimations.stop(lottieInstances[config.animId]);
        delete lottieInstances[config.animId];
      }

      // 清理喇叭动画（仅对textPic元素）
      if (config.labaAnimId && lottieInstances[config.labaAnimId]) {
        lottieAnimations.stop(lottieInstances[config.labaAnimId]);
        delete lottieInstances[config.labaAnimId];
      }

      // 停止并移除音频
      if (config.audioSelector) {
        const audioEl = $(config.audioSelector);
        if (audioEl.length > 0) {
          // 先停止音频播放
          SDK.pauseRudio({
            index: audioEl.get(0),
            syncName: config.audioSyncName
          });
          audioEl.remove();
        }
      }

      // 移除DOM元素
      const element = $(config.domSelector);
      if (element.length > 0) {
        element.remove();
      }
    },

    /**
     * 播放音频
     * @param {string} audioSrc - 音频源URL
     * @param {string} audioId - 音频元素ID
     * @param {boolean} waitForComplete - 是否等待播放完成
     * @returns {Object} 音频元素jQuery对象
     */
    async playAudio(audioSrc, audioId, waitForComplete = false) {
      // 先检查是否已有相同ID的音频元素
      let audioEl = $(`.mainArea audio[data-syncaudio="${audioId}"]`);

      // 如果没有找到现有音频元素，才创建新的
      if (audioEl.length === 0) {
        audioEl = $(`<audio src="${audioSrc}" data-syncaudio="${audioId}"></audio>`);
        $('.mainArea').append(audioEl);
      }

      if (waitForComplete) {
        // 播放并等待完成
        await audioEl.playAudioSync();
      } else {
        // 播放但不等待
        audioEl.playAudioSync();
      }

      return audioEl;
    },

    /**
     * 初始化Lottie动画
     * @param {Object} element - 元素jQuery对象
     * @param {string} jsonUrl - JSON文件URL
     * @returns {Object} 动画实例
     */
    async initLottieAnimation(element, jsonUrl, loop = false) {
      try {
        // 生成唯一的动画ID
        const animId = element.attr('data-syncactions');

        // 如果实例已存在，直接返回
        if (lottieInstances[animId]) {
          return lottieInstances[animId];
        }

        // 使用lottieAnimations辅助对象初始化动画
        lottieInstances[animId] = await lottieAnimations.init(
          null,
          jsonUrl,
          `[data-syncactions="${animId}"]`,
          loop
        );

        return lottieInstances[animId];
      } catch (error) {
        console.error('Lottie动画加载失败:', error);
        return null;
      }
    },

    /**
     * 停止指定动画
     * @param {string} animId - 动画ID
     * @returns {boolean} 是否成功停止
     */
    stopLottieAnimation(animId) {
      if (lottieInstances[animId]) {
        lottieAnimations.stop(lottieInstances[animId]);
        return true;
      }
      return false;
    },

    /**
     * 小手控制
     * 设置小手位置和显示状态
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     */
    setHand(groupIndex, elementIndex) {

      const element = $(`[data-syncactions="status1_${groupIndex}_${elementIndex}"]`);
      if (element.length) {
        // 获取元素配置
        const elementConfig = options[groupIndex].cliclElement[elementIndex];

        // 使用配置中的handLocation
        if (elementConfig && elementConfig.handLocation &&
          elementConfig.handLocation.x && elementConfig.handLocation.y) {
          $('.hands').css({
            left: elementConfig.handLocation.x / 100 + 'rem',
            top: elementConfig.handLocation.y / 100 + 'rem',
            display: 'block'
          });
        } else {
          // 如果没有设置handLocation，则默认使用元素位置
          const position = element.position();
          const width = element.outerWidth();
          const height = element.outerHeight();
          $('.hands').css({
            left: position.left + width / 2 + 'px',
            top: position.top + height / 2 + 'px',
            display: 'block'
          });
        }
      } else {
        $('.hands').hide();
      }
    },

    /**
     * 创建元素集合
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     * @param {Object} element - 元素配置对象
     * @returns {Object} 创建的元素集合
     */
    async createElementSet(groupIndex, elementIndex, element) {
      // 创建元素
      const status1El = $('<div>').addClass('status1').attr('data-syncactions', `status1_${groupIndex}_${elementIndex}`);
      const status2El = $('<div>').addClass('status2').attr('data-syncactions', `status2_${groupIndex}_${elementIndex}`);
      const textPicEl = $('<div>').addClass('textPic').attr('data-syncactions', `textPic_${groupIndex}_${elementIndex}`);


      // 为同一个点击元素的三个部分设置相同的z-index
      // 从元素配置中获取zIndex值，如果没有则默认为0
      const zIndexValue = element.zIndex || 0;
      status1El.css('z-index', zIndexValue);
      status2El.css('z-index', zIndexValue);
      textPicEl.css('z-index', zIndexValue);

      // 设置位置和缩放
      const scale = element.scale / 100;
      const statusLocation = element.statusLocation;
      const textPicLocation = element.textPicLocation;

      // 设置状态1元素样式
      this.setElementStyle(status1El, element.status1, statusLocation, scale);

      // 设置状态2元素样式（初始隐藏）
      this.setElementStyle(status2El, element.status2, statusLocation, scale);

      // 设置文本图片样式（初始隐藏）
      this.setElementStyle(textPicEl, element.textPic, textPicLocation, scale);

      // 如果同时有内容音频和文本图片，添加喇叭图标
      if (element.contentAudio && element.textPic) {
        const contentAudioWrapper = $(`<div data-syncactions="contentAudio${groupIndex}_${elementIndex}"></div>`);
        textPicEl.append(contentAudioWrapper);

        // 设置喇叭样式
        contentAudioWrapper.css({
          transform: 'translate(-50%, -50%)',
          cursor: 'pointer',
          width: '1.08rem',
          height: '1.08rem'
        });

        // 设置喇叭点击事件
        this.setupLabaClickEvent(contentAudioWrapper, element.contentAudio, groupIndex, elementIndex);
      }

      return {status1El, status2El, textPicEl};
    },

    /**
     * 设置喇叭点击事件
     * @param {Object} contentAudioWrapper - 喇叭包装元素jQuery对象
     * @param {string} contentAudio - 内容音频URL
     * @param {number} groupIndex - 组索引
     * @param {number} elementIndex - 元素索引
     */
    setupLabaClickEvent(contentAudioWrapper, contentAudio, groupIndex, elementIndex) {
      const self = this; // 保存对pageManager的引用
      contentAudioWrapper.syncbind('click touchstart', function (dom, next) {
        if (!isSync) {
          next(false);
        } else {
          console.log('喇叭点击组===',groupIndex,'元素===',elementIndex)
          SDK.reportTrackData({
            action:'CK_FT_INTERACTION_AUDIOPLAY', //事件名称
            data:{}, // 老师和学生  都需要上报的数据
            teaData:{
              roundid:groupIndex+1,
              audio:elementIndex+1
            },  // 只有老师端会上报的数据
            stuData:{},  // 只有学生端会上报的数据
          },USER_TYPE.TEA)
          next();
        }
      }, async function () {
        const labaAnimKey = `laba${groupIndex}_${elementIndex}`;
        // 播放喇叭动画
        if (lottieInstances[labaAnimKey]) {
          lottieAnimations.play(lottieInstances[labaAnimKey]);

          // 获取已存在的音频元素ID
          const audioId = `contentAudio_${groupIndex}_${elementIndex}`;

          // 播放内容音频
          await self.playAudio(contentAudio, audioId, true);

          // 音频播放结束后停止喇叭动画
          lottieAnimations.stop(lottieInstances[labaAnimKey]);

          SDK.setEventLock();
        }
      });
    },

    /**
     * 设置元素样式
     * @param {Object} element - 元素jQuery对象
     * @param {string} url - 资源URL
     * @param {Object} location - 位置信息
     * @param {number} scale - 缩放比例
     */
    setElementStyle(element, url, location, scale) {
      if (!url) {
        return;
      }

      const isJson = this.isJsonFile(url);

      // 基础样式设置
      const styleObject = {
        position: 'absolute',
        left: location.x / 100 + 'rem',
        top: location.y / 100 + 'rem',
        display: 'none'
      };

      // 对于非JSON文件，设置背景图片
      if (!isJson) {
        Object.assign(styleObject, {
          backgroundImage: `url(${url})`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center'
        });
      }

      // 应用样式
      element.css(styleObject);

      // 获取并设置尺寸
      this.getImageDimensions(url)
        .then(dimensions => {
          element.css({
            width: dimensions.width * scale / 100 + 'rem',
            height: dimensions.height * scale / 100 + 'rem'
          });
        })
        .catch(error => console.error('获取图片尺寸失败:', error));
    },

    /**
     * 获取图片尺寸
     * @param {string} url - 图片URL
     * @returns {Promise} 包含宽高信息的Promise对象
     */
    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        if (!url) {
          reject(new Error('无效的图片URL'));
          return;
        }

        if (this.isJsonFile(url)) {
          $.getJSON(url)
            .done(data => {
              const width = data.width || data.w;
              const height = data.height || data.h;
              resolve({width, height});
            })
            .fail(() => reject(new Error('JSON文件加载失败')));
        } else {
          const img = new Image();
          img.onload = () => resolve({width: img.width, height: img.height});
          img.onerror = () => reject(new Error('图片加载失败'));
          img.src = url;
        }
      });
    },

    /**
     * 判断是否为JSON文件
     * @param {string} url - 文件URL
     * @returns {boolean} 是否为JSON文件
     */
    isJsonFile(url) {
      if (!url) return false;
      const extension = url.split('.').pop().toLowerCase();
      return extension === 'json';
    }
  };

  // 初始化页面
  pageManager.init();
});




