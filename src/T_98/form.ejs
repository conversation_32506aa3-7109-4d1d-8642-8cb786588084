<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>CJBBB0001FT_超级变变变FT</title>
    <link rel="stylesheet" href="form/css/style.css">
    <script src='form/js/jquery-2.1.1.min.js'></script>
    <script src='form/js/vue.min.js'></script>
</head>

<body>
    <div id="container">
        <div class="edit-form">
            <div class="h-title">CJBBB0001FT_超级变变变FT</div>
            <% include ./src/common/template/common_head %>
                <!-- 交互提示标签 -->
                <% include ./src/common/template/dynamicInstruction/form.ejs %>
                    <% include ./src/common/template/multyDialog/form.ejs %>
                        <div class="c-group">
                            <div class="c-title">设置点击内容(最多12组)</div>
                            <div class="c-area upload img-upload" style="padding-top: 0">
                                <ul>
                                    <li v-for="(group, groupIndex) in configData.source.options"
                                        style="margin-top: 20px">
                                        <div class="group-header">
                                            <span class="group-title">第{{groupIndex + 1}}组</span>
                                            <span class="dele-tg-btn" v-show="configData.source.options.length > 1 && !(groupIndex === 0)"
                                                v-on:click="deleGroup(group)">
                                            </span>
                                        </div>
                                        <div style="background-color: #f1f1f1;padding: 0 10px;">
                                            <span>显示动态小手<em style="color: red;">*</em></span>
                                            <label class="inline-label" :for="'showHand'+groupIndex"
                                                style="display: inline-block;margin-left: 10px">
                                                <input type="radio" :name="'showHand'+groupIndex" value="0"
                                                    v-model="group.showHand">&nbsp;不展示
                                            </label>
                                            <label class="inline-label" :for="'showHand'+groupIndex"
                                                style="display: inline-block;margin-left: 10px">
                                                <input type="radio" :name="'showHand'+groupIndex" value="1"
                                                    v-model="group.showHand">&nbsp;展示
                                            </label>
                                        </div>
                                        <div v-for="(item, index) in group.cliclElement">
                                            <div style="padding: 10px;background-color: #f1f1f1;"
                                                :style="index === group.cliclElement.length - 1 ? 'border-radius: 0 0 8px 8px;' : ''">
                                                <div class="c-well"
                                                    style="box-shadow: 1px 1px 10px #ccc;margin-bottom: 0">
                                                    <div class="field-wrap">
                                                        <div style="display: flex;justify-content: space-between;">
                                                            <div style="color: black;">
                                                                点击元素{{index + 1}}
                                                            </div>
                                                            <span class="dele-tg-btn"
                                                                v-show="group.cliclElement.length > 1 && !(groupIndex === 0 && index === 0)"
                                                                v-on:click="confirmDelete(item, groupIndex)">
                                                            </span>
                                                        </div>
                                                        <label class="field-label">
                                                            状态1<em>*</em>
                                                        </label>
                                                        <span class="field-wrap">
                                                            <label :for="'content-pic-status1'+index+groupIndex"
                                                                class="btn btn-show upload"
                                                                v-if="!item.status1">上传</label>
                                                            <label :for="'content-pic-status1'+index+groupIndex"
                                                                class="btn upload re-upload"
                                                                v-if="item.status1">重新上传</label>
                                                        </span>
                                                        <div class='txt-info'>
                                                            <em>JPG、PNG、json格式，大小≤240Kb</em>
                                                        </div>
                                                        <input type="file" v-bind:key="Date.now()" class="btn-file"
                                                            :id="'content-pic-status1'+index+groupIndex" size=""
                                                            accept=".jpg,.png,.json"
                                                            @change="feedbackUpload($event,item,'status1',240)">
                                                    </div>

                                                    <div class="img-preview" v-if="item.status1">
                                                        <img :src="item.status1.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.status1"
                                                            alt="" />
                                                        <div class="img-tools">
                                                            <span class="btn btn-delete"
                                                                @click="delPrew(item,'status1')">删除</span>
                                                        </div>
                                                    </div>


                                                    <div class="field-wrap" style="margin-top: 20px" v-if="!(index === 0)">
                                                        <label class="field-label">状态2</label>
                                                        <span class="field-wrap">
                                                            <label :for="'content-pic-status2'+index+groupIndex"
                                                                class="btn btn-show upload"
                                                                v-if="!item.status2">上传json</label>
                                                            <label :for="'content-pic-status2'+index+groupIndex"
                                                                class="btn upload re-upload"
                                                                v-if="item.status2">重新上传</label>
                                                        </span>
                                                        <div class='txt-info'>
                                                            <em>非必填，json格式，大小≤240Kb<br>
                                                                播放一次，播放完成后展示状态1</em>
                                                        </div>
                                                        <input type="file" v-bind:key="Date.now()+'status2'"
                                                            class="btn-file"
                                                            :id="'content-pic-status2'+index+groupIndex" size=""
                                                            accept=".json"
                                                            @change="feedbackUpload($event,item,'status2',240)">
                                                    </div>

                                                    <div class="img-preview" v-if="item.status2">
                                                        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                                                            alt="" />
                                                        <div class="img-tools">
                                                            <span class="btn btn-delete"
                                                                @click="delPrew(item,'status2')">删除</span>
                                                        </div>
                                                    </div>

                                                    <div class="field-wrap-item" style="margin-top: 20px;">
                                                        <span>显示位置<em>*</em> &nbsp;&nbsp;</span>
                                                        X:<input type="number" class="c-input-txt"
                                                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                            oninput="if(value>1920)value=1920;if(value<0)value=0"  @change="item.copyPosition = false"
                                                            v-model="item.statusLocation.x">
                                                        Y:<input type="number" class="c-input-txt" @change="item.copyPosition = false"
                                                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                            oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                            v-model="item.statusLocation.y">
                                                        <label class="inline-label"style="display: inline-block;margin: 0;" v-if="index > 0">
                                                            <input type="checkbox"
                                                                @change="copyPositionChange(group.cliclElement, item, $event)"
                                                                v-model="item.copyPosition">&nbsp;继承上一个点击元素位置
                                                        </label>

                                                        <br>
                                                        <span style="color: red;">对状态1和状态2同时生效</span>
                                                    </div>


                                                    <div class="field-wrap" style="margin-top: 20px">
                                                        <span>缩放<em style="color: red;">*</em> &nbsp;&nbsp;</span>
                                                        <input type="text" class='c-input-txt'
                                                            oninput="if(value>200)value=200;if(value<0)value=0"
                                                            style="margin: 0 10px;width: 100px!important;display: inline-block;"
                                                            placeholder="" v-model="item.scale">
                                                        <span>1%-200%</span>
                                                    </div>

                                                    <div class="field-wrap" style="margin-top: 20px">
                                                        <label class="field-label">文字图片{{index + 1}}</label>
                                                        <span class="field-wrap">
                                                            <label :for="'content-pic-textPic'+index+groupIndex"
                                                                class="btn btn-show upload"
                                                                v-if="!item.textPic">上传</label>
                                                            <label :for="'content-pic-textPic'+index+groupIndex"
                                                                class="btn upload re-upload"
                                                                v-if="item.textPic">重新上传</label>
                                                        </span>
                                                        <div class='txt-info'>
                                                            <em>JPG、PNG格式，小于等于80Kb<br>
                                                                状态2播放完成后，从目标位置渐显</em>
                                                        </div>
                                                        <input type="file" v-bind:key="Date.now()+'textPic'"
                                                            class="btn-file"
                                                            :id="'content-pic-textPic'+index+groupIndex" size=""
                                                            accept=".jpg,.png"
                                                            @change="imageUpload($event,item,'textPic',80)">
                                                    </div>

                                                    <div class="img-preview" v-if="item.textPic">
                                                        <img :src="item.textPic" alt="" />
                                                        <div class="img-tools">
                                                            <span class="btn btn-delete"
                                                                @click="delPrew(item,'textPic')">删除</span>
                                                        </div>
                                                    </div>

                                                    <div class="field-wrap-item" style="margin-top: 20px;">
                                                        <span>文字图片位置<em v-if="item.textPic">*</em>&nbsp;&nbsp;</span>
                                                        X:<input type="number" class="c-input-txt"
                                                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                            oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                            v-model="item.textPicLocation.x">
                                                        Y:<input type="number" class="c-input-txt"
                                                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                            oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                            v-model="item.textPicLocation.y">
                                                        <br>
                                                    </div>

                                                    <!-- 状态2音频 -->
                                                    <div class="field-wrap" style="margin-top: 20px" v-if="!(index === 0)">
                                                        <label class="field-label" for="">状态2音频</label>
                                                        <span>
                                                            <label :for="'audio-upload-status2Audio'+index+groupIndex"
                                                                class="btn btn-show upload"
                                                                v-if="item.status2Audio==''?true:false">上传音频</label>
                                                            <label :for="'audio-upload-status2Audio'+index+groupIndex"
                                                                class="btn upload re-upload mar"
                                                                v-if="item.status2Audio!=''?true:false">重新上传</label>
                                                        </span>
                                                        <div style="color: red">mp3、wav格式，小于等于50Kb</div>

                                                        <div class="audio-preview"
                                                            v-show="item.status2Audio!=''?true:false">
                                                            <div class="audio-tools">
                                                                <p v-show="item.status2Audio!=''?true:false">
                                                                    {{item.status2Audio}}</p>
                                                            </div>
                                                            <span class="play-btn" v-on:click="play($event)">
                                                                <audio v-bind:src="item.status2Audio"></audio>
                                                            </span>
                                                        </div>
                                                        <span class="btn btn-audio-dele"
                                                            v-show="item.status2Audio!=''?true:false"
                                                            v-on:click="item.status2Audio=''">删除</span>
                                                        <input type="file"
                                                            :id="'audio-upload-status2Audio'+index+groupIndex"
                                                            class="btn-file upload" size="" accept=".mp3,.wav"
                                                            v-on:change="audioUpload($event,item,'status2Audio',50)"
                                                            v-bind:key="Date.now()+'status2Audio'">
                                                    </div>

                                                    <!-- 内容音频 -->
                                                    <div class="field-wrap" style="margin-top: 20px" v-if="!(groupIndex === 0 && index === 0)">
                                                        <label class="field-label" for="">内容音频</label>
                                                        <span>
                                                            <label :for="'audio-upload-contentAudio'+index+groupIndex"
                                                                class="btn btn-show upload"
                                                                v-if="item.contentAudio==''?true:false">上传音频</label>
                                                            <label :for="'audio-upload-contentAudio'+index+groupIndex"
                                                                class="btn upload re-upload mar"
                                                                v-if="item.contentAudio!=''?true:false">重新上传</label>
                                                        </span>
                                                        <div style="color: red">mp3、wav格式，小于等于50Kb</div>

                                                        <div class="audio-preview"
                                                            v-show="item.contentAudio!=''?true:false">
                                                            <div class="audio-tools">
                                                                <p v-show="item.contentAudio!=''?true:false">
                                                                    {{item.contentAudio}}</p>
                                                            </div>
                                                            <span class="play-btn" v-on:click="play($event)">
                                                                <audio v-bind:src="item.contentAudio"></audio>
                                                            </span>
                                                        </div>
                                                        <span class="btn btn-audio-dele"
                                                            v-show="item.contentAudio!=''?true:false"
                                                            v-on:click="item.contentAudio=''">删除</span>
                                                        <input type="file"
                                                            :id="'audio-upload-contentAudio'+index+groupIndex"
                                                            class="btn-file upload" size="" accept=".mp3,.wav"
                                                            v-on:change="audioUpload($event,item,'contentAudio',50)"
                                                            v-bind:key="Date.now()+'contentAudio'">
                                                    </div>

                                                    <div class="field-wrap-item" style="margin-top: 20px;">
                                                        <span>本组内容层级</span>
                                                       <input type="number" class="c-input-txt"
                                                                 style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                                 oninput="if(value>999)value=999;if(value<0)value=0"
                                                                 v-model="item.zIndex">
                                                                 <div style="color: red;">层级越高的元素会覆盖层级低的元素（0~999）</div>
                                                    </div>

                                                    <div class="field-wrap-item" style="margin-top: 20px;"
                                                    v-if="group.showHand === '1'" >
                                                        <span>小手位置&nbsp;&nbsp;</span>
                                                        X:<input type="number" class="c-input-txt"
                                                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                            oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                            v-model="item.handLocation.x">
                                                        Y:<input type="number" class="c-input-txt"
                                                            style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                            oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                            v-model="item.handLocation.y">
                                                        <br>
                                                        <div style="color: red" v-if="groupIndex === configData.source.options.length - 1
                                                        && index === group.cliclElement.length - 1">后一个点击元素，小手不会展示</div>
                                                    </div>


                                                    <div class="field-wrap" style="margin-top: 20px">
                                                        <label class="field-label">点击后<em>*</em> &nbsp;&nbsp;</label>
                                                        <select v-model="item.afterclick" style="width: 200px">
                                                            <option value="0">点击后隐藏元素</option>
                                                            <option value="1">点击后不隐藏元素</option>
                                                            <option value="2">点击后状态2再次播放一次（不显示下一个元素）</option>
                                                        </select>
                                                        <div style="color: red;">选择"点击后状态2再次播放一次"时游戏将结束！</div>
                                                    </div>
                                                    <div style="text-align: center;">
                                                        <button class="add-tg-btn"
                                                            style="margin-top: 20px;margin-left: 0;"
                                                            v-on:click="addCliclElement(groupIndex)"
                                                            v-if="index < 11 && index === group.cliclElement.length -1">+
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 组的添加按钮 -->
                                        <div style="text-align: center; margin-top: 20px;"
                                            v-show="groupIndex < 11 && groupIndex === configData.source.options.length -1">
                                            <button class="btn upload" style="padding: 8px;letter-spacing: 4px;"
                                                @click="addGroup">
                                                添加一组点击内容
                                            </button>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <!-- 头像上墙 -->
                        <% include ./src/common/template/avatarUpWall/form %>
                        <!-- 反馈动画添加 -->
                        <% include ./src/common/template/feedbackAnimation/form %>
                            <button class="send-btn" v-on:click="onSend">提交</button>
        </div>

        <div class="edit-show">
            <div class="show-fixed">
                <div class="show-img">
                    <img src="form/img/preview.png?v=<%= new Date().getTime() %>" alt="">
                </div>
                <ul class="show-txt">
                    <li><em>图片格式：</em>JPG/PNG/GIF</li>
                    <li><em>声音格式：</em>MP3/WAV</li>
                    <li><em>视频格式：</em>MP4</li>
                    <li>带有" * "号为必填项</li>
                </ul>
            </div>
        </div>
    </div>
</body>
<script src='form/js/form.js?v=<%= new Date().getTime() %>'></script>

</html>
