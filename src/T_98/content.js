var configData = {
	bg: '',
	desc: '',
	title:'',
	tg: [
		{
			content: "c",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}
	],
	level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
				content: "eegeg appy family. My father i appy family. My father i",
				title: "weqwf appy family. My father i"
		}]
	},
  source: {
      options: [
          {
            showHand: '1', // 0不显示小手 1显示小手
            cliclElement: [
              {
                status1: './assets/images/shuazi.json', // 状态1
                status2: '', // 状态2
                statusLocation: {
                  x: '200',
                  y: '200'
                }, // 状态位置
                copyPosition: false, // 是否继承上一个点击元素位置
                scale:'100', // 缩放比例
                textPic: './assets/images/duihua1.png', // 文本图片
                textPicLocation: {
                  x: '100',
                  y: '100'
                }, // 文本位置
                handLocation: {
                  x: '100',
                  y: '100'
                }, // 小手位置
                status2Audio: '', // 状态2音频
                contentAudio: './assets/audios/01.mp3', // 内容音频
                zIndex: 1, // 元素z-index
                afterclick: '0' //0隐藏本组 1不隐藏本组 2循环本组
              },
              {
                status1: './assets/images/shuazi.json', // 状态1
                status2: './assets/images/Doll_turn1.json', // 状态2
                statusLocation: {
                  x: '800',
                  y: '800'
                }, // 状态位置
                copyPosition: false, // 是否继承上一个点击元素位置
                scale:'80', // 缩放比例
                textPic: './assets/images/clock.png', // 文本图片
                textPicLocation: {
                  x: '900',
                  y: '900'
                }, // 文本位置
                status2Audio: './assets/audios/01.mp3', // 状态2音频
                contentAudio: './assets/audios/01.mp3', // 内容音频
                handLocation: {
                  x: '100',
                  y: '100'
                }, // 小手位置
                afterclick: '1' //0隐藏本组 1不隐藏本组 2循环本组
              }
            ]
          },
          {
            showHand: '1', // 0不显示小手 1显示小手
            cliclElement: [
              {
                status1: './assets/images/Doll_turn1.json', // 状态1
                status2: '', // 状态2
                statusLocation: {
                  x: '900',
                  y: '200'
                }, // 状态位置
                copyPosition: false, // 是否继承上一个点击元素位置
                scale:'100', // 缩放比例
                textPic: './assets/images/duihua1.png', // 文本图片
                textPicLocation: {
                  x: '1200',
                  y: '100'
                }, // 文本位置
                status2Audio: '', // 状态2音频
                contentAudio: '', // 内容音频
                zIndex: 3, // 元素z-index
                handLocation: {
                  x: '1200',
                  y: '100'
                }, // 小手位置
                afterclick: '0' //0隐藏本组 1不隐藏本组 2循环本组
              },
              {
                status1: './assets/images/clock.png', // 状态1
                status2: './assets/images/shuazi.json', // 状态2
                statusLocation: {
                  x: '900',
                  y: '200'
                }, // 状态位置
                copyPosition: false, // 是否继承上一个点击元素位置
                scale:'100', // 缩放比例
                textPic: './assets/images/table.png', // 文本图片
                textPicLocation: {
                  x: '1200',
                  y: '100'
                }, // 文本位置
                status2Audio: './assets/audios/01.mp3', // 状态2音频
                contentAudio: './assets/audios/01.mp3', // 内容音频
                zIndex: 4, // 元素z-index
                handLocation: {
                  x: '1200',
                  y: '100'
                }, // 小手位置
                afterclick: '2' //0隐藏本组 1不隐藏本组 2循环本组
              }
            ]
          },
      ],
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "",
          audio: "",
        },
      ],
      messageLocationX: "", // 消息内容位置x
      messageLocationY: "", // 消息内容位置y
      roleLocationX: "100", // 角色位置x
      roleLocationY: "600", // 角色位置y
      roleImg: "", // 角色图片
      playAfterStauts: "2", // 播放完之后状态
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框
      hiddenStatus: "1", // 播放完是否应藏的状态
    }
  },
  dynamicEffect:'./assets/images/shuazi.json',
  feedbackLists:[
    {
      positiveFeedback: '2',
      feedbackList:[
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./image/prefect.json', mp3:'./audio/prefect.mp3' },
        { id:'1', json:'./image/goldCoin.json', mp3:'./audio/goldCoin.mp3' },
        { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
        { id:'9', json: './image/feedback.json', mp3: './audio/feedback01.mp3' },
      ],
      feedbackObj: { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
      feedback:'',
      feedbackAudio:'',
      feedbackName:'整体反馈',
      key:'feedKey1',
      loop: false,
    },
  ]
};
