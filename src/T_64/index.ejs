<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="TOR0002_语音答题"; %>
  <%include ./src/common/template/index_head %>
  <script src="js/lottie_svg.min.js"></script>
</head>

<body>
  <div class="container" id="container">
    <section class="commom">
      <div class="desc"></div>
    </section>
    <section class="main">
      <!-- 开始按钮 -->
      <div class="startBtn" data-syncactions="startBtnSync">
        <div class="startContent">Start Oral Test</div>
      </div>
      <!-- 网格布局 -->
      <div class="boxList">
        <ul class="boxUl"></ul>
      </div>
      <!-- 音频 -->
      <div class="audioDiv" data-syncactions="audioDIV">
        <img id="audioImg" src="image/btn-audio.png" />
        <audio id="audioExample" class="audioExample"  src=""  data-syncactions="audio"></audio>
      </div>
      <!-- 麦克风图标 -->
      <img id="microphoneImg" src="image/microphone.png" />
      <!-- 麦克风动画 -->
      <div class="animation-con">
        <div id="tea-recording" class="record-con-tea">Recording Stu′s Voice</div>
        <div id="showRecord" class="record-con showRecord"></div>
        <div id="recording" class="record-con"></div>
        <div id="hideRecord" class="record-con"></div>
        <div id="processAni" class="record-con processAni"></div>
        <!-- 等待中动画 -->
        <div id="wait-container" class="record-con">
          <div id="wait" class="waitAni"></div>
        </div>
        <!-- 播放录音动画 -->
        <div id="play-record-container" class="record-con">
          <div id="play-record" class="playAni"></div>
        </div>
      </div>
      <!-- 教师专用评价面板 -->
      <div class="evaluateTip">
        <div class="tip"> click to judge Stu's work</div>
        <div class="evaluateBtn great-btn" data-syncactions="greatBtnSync">Great</div>
        <div class="evaluateBtn goodjob-btn" data-syncactions="goodJobBtnSync">Good Job</div>
      </div>
      <!-- great动画 -->
      <div id="great" class="finialAni"></div>
      <!-- goodJob动画 -->
      <div id="goodJob" class="finialAni"></div>
      <!-- 虚拟按钮 用于开始播放通信 -->
      <div id="playSyncbtn" data-syncactions="playSync"></div>
      <!-- 虚拟按钮 用于结束播放通信 -->
      <div id="stopPlaySyncbtn" data-syncactions="stopPlaySync"></div>
      <!-- 虚拟按钮 用于通知老师开启评价 -->
      <div id="showEvaluateSyncbtn" data-syncactions="showEvaluateSync"></div>
      <!-- audio音频 -->
      <audio src="audio/startAudio.mp3" id="startAudio" data-syncaudio="startAudio"></audio>
      <audio src="audio/great.mp3" id="greatAudio" data-syncaudio="greatAudio"></audio>
      <audio src="audio/goodjob.mp3" id="goodJobAudio" data-syncaudio="goodJobAudio"></audio>
    </section>
  </div>
  <%include ./src/common/template/index_bottom %>
</body>

</html>
