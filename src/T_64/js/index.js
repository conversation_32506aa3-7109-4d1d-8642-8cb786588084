"use strict"
import '../../common/js/common_1v1.js'
import './drag.js'

import {
    showRecord,
    recording,
    hideRecord,
    greatAnimation,
    goodJobAnimation,
    processAnimation,
    waitAnimation,
    playAnimation
} from './lottie_json.js';

$(function() {
    window.h5Template = {
        hasPractice: '0'
    }
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
    let staticData = configData.source;
    const userType = SDK.getUserType();

    let isSDKPlay = true; // 是否启用SDK播放。仅学生端使用
    let timeIntStu = null; //进度条倒计时动画
    let waitTimeIntStu = null; //等待中动画倒计时（N+5秒未收到通知,结束等待动画）
    let stopPlayTimer = null; //音频播放倒计时
    let textType = staticData.textType; //单词 or 句子
    let textContent = staticData.textContent; //文本
    let duration = staticData.duration; //时间
    let isRecordresult = false; //是否已收到录音结果
    let processStatus = 0; //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
    let wavfile = ''; //录音URL
    //生成像素表格
    renderPx();

    function renderPx() {
        let liHtml = '';
        for (let i = 1; i < 801; i++) {
            liHtml += `
				<li class="pos_${i}"></li>	`
        }
        $('.boxUl').html(liHtml);
    }
    //初始化音频
    initAudio()
    //初始化麦克风图标位置
    initPosition();


    function initAudio() {
        if (configData.source.audioPosition && configData.source.audio) {
            $("#audioExample").attr("src", configData.source.audio)
            let startPos = configData.source.audioPosition;
            let left = ($('.pos_' + startPos).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + startPos).offset().top - $('.container').offset().top) / window.base + 'rem';
            $('.audioDiv').css({
                left: left,
                top: top
            });
            $('.audioDiv').show();
        }
    }

    function initPosition() {
        if (configData.source.microphonePosition) {
            let startPos = configData.source.microphonePosition;
            let left = ($('.pos_' + startPos).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + startPos).offset().top - $('.container').offset().top) / window.base + 'rem';
            $('#microphoneImg').css({
                left: left,
                top: top
            });
            $('#microphoneImg').show();
        }
    }
    if (userType === 'tea') {
        $(".startBtn").show();
    }
    // 点击示范音图标
    $(".audioDiv").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (isSync) {
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'audioClick',
                recoveryMode: '2',
            });
        } else {
            $(this).trigger("audioClick");
        }
    })
    $(".audioDiv").on('audioClick', function(e, message) {
        let $audio = $("#audioExample")[0];
        let $img = $("#audioImg");
        SDK.playRudio({
            index: $audio,
            syncName: $("#audioExample").attr("data-syncactions")
        })
        $("#audioImg").attr("src", "./image/btn-audio.gif");
        //播放完毕img状态
        $audio.onended = function() {
            $img.attr("src", "./image/btn-audio.png");
        }.bind(this);
        SDK.setEventLock();
    })
    /**
     * 动画部分
     */
    // 举起麦克风
    var showRecordInit = lottie.loadAnimation({
        container: document.getElementById("showRecord"), // 容器
        renderer: "svg",
        loop: 0,
        autoplay: false,
        animationData: showRecord(), //如果使用的是JSON
    });
    // 话筒两侧线条跳动
    var recordingInit = lottie.loadAnimation({
        container: document.getElementById("recording"), // 容器
        renderer: "svg",
        loop: true,
        autoplay: false,
        animationData: recording(), //如果使用的是JSON
    });
    // 录音进度条
    var processInit = lottie.loadAnimation({
        container: document.getElementById("processAni"), // 容器
        renderer: "svg",
        loop: 0,
        autoplay: false,
        animationData: processAnimation(), //如果使用的是JSON
    });
    // 放下麦克风
    var hideRecordInit = lottie.loadAnimation({
        container: document.getElementById("hideRecord"), // 容器
        renderer: "svg",
        loop: 0,
        autoplay: false,
        animationData: hideRecord(), //如果使用的是JSON
    });
    // 等待中动画  等待打分结果时使用
    var waitInit = lottie.loadAnimation({
        container: document.getElementById("wait"), // 容器
        renderer: "svg",
        loop: true,
        autoplay: false,
        animationData: waitAnimation(), //如果使用的是JSON
    });
    // 播放动画
    var playInit = lottie.loadAnimation({
        container: document.getElementById("play-record"), // 容器
        renderer: "svg",
        loop: true,
        autoplay: false,
        animationData: playAnimation(), //如果使用的是JSON
    });
    var greatInit = lottie.loadAnimation({
        container: document.getElementById("great"), // 容器
        renderer: "svg",
        loop: 0,
        autoplay: false,
        animationData: greatAnimation(), //如果使用的是JSON
    });
    var goodJobInit = lottie.loadAnimation({
        container: document.getElementById("goodJob"), // 容器
        renderer: "svg",
        loop: 0,
        autoplay: false,
        animationData: goodJobAnimation(), //如果使用的是JSON
    });
    // 点击开始录音按钮
    $(".startBtn").on("click touchstart", function(e) {
        if (userType !== 'tea') return;
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (isSync) {
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'startBtnClick',
                otherInfor: {
                    supporth5audioperation: SDK.getClassConf().course.supporth5audioperation, //老师端是否支持语音播放功能  此信息会传递给学生端
                },
                recoveryMode: '2', //不用做断线重连
            });
        } else {
            $(this).trigger("startBtnClick");
        }
    })
    $(".startBtn").on('startBtnClick', function(e, message) {
        if (userType === 'stu') {
            //老师端或学生端任何一个不支持语音播放功能 假录音模式(不启动录音功能,但有动画，老师从视频语音里听学生发音)。N秒后自动结束录音
            if (!isSync || (SDK.getClassConf().course.supporth5audioperation !== 3) || (message.data[0].value.syncAction.otherInfor.supporth5audioperation !== 3)) {
                isSDKPlay = false;
            } else {
                startRecordSync(); //发送"开始录音"协议
            }
        }
        $(".startBtn").hide();
        // if (startRecordTimer) {
        //     clearTimeout(startRecordTimer)
        //     startRecordTimer = null;
        // }
        // startRecordTimer = setTimeout(function() {
        startRecord(); //开始录音动画(录音功能有一定时间延迟，因此动画较晚)
        // }, 1500)
        SDK.setEventLock();
    })
    // 举起麦克风
    function startRecord() {
        if (userType === 'tea') {
            $(".record-con-tea").show(); // 老师端显示"recording Stu‘s voice" 学生录音中
        }
        $("#showRecord").show();
        showRecordInit.play();
        SDK.playRudio({
            index: $('#startAudio').get(0),
            syncName: $('#startAudio').attr("data-syncaudio")
        })
        showRecordInit.addEventListener('complete', function() {
            setTimeout(function() {
                $("#showRecord").hide();
                $("#recording").show();
                showRecordInit.stop();
                recordingInit.play();
                processStart();
            }, 500)
        });
        console.log("提前绑定放下麦克风动画的事件")
        hideRecordListener();//提前绑定放下麦克风动画的事件
    }

    //进度条动画
    function processStart() {
        processStatus = 1;
        $("#processAni").show();
        processInit.setSpeed(1)
        if (duration > 20) {
            processInit.setSpeed(4.5 / duration)
        } else {
            if (duration < 5) {
                processInit.setSpeed(6 / duration)
            } else {
                processInit.setSpeed(5 / duration)
            }
        }
        processInit.play(); //开始进度条动画
        processEnd() //录音倒计时
    }

    /**
     * 录音倒计时
     */
    function processEnd() {
        if (timeIntStu) {
            clearTimeout(timeIntStu);
            timeIntStu = null;
        }
        timeIntStu = setTimeout(function() {
            if (processStatus != 2 && processStatus != 3) {
                console.log("processStatus", processStatus)
                finishRecord(); //结束进度条 放下麦克风
            }
        }, duration * 1000);
    };

    // 结束进度条 放下麦克风
    function finishRecord() {
        processInit.stop();
        $("#recording").hide();
        console.log("进度条结束，准备放下麦克风")
        $("#hideRecord").show();
        hideRecordInit.play();
        $("#processAni").fadeOut(100);
        processStatus = 2; //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
    }
    //放下麦克风 监听事件。
    function hideRecordListener(){
        console.log("放下麦克风 已绑定事件")
        hideRecordInit.addEventListener('complete', function() {
            console.log("麦克风已放下")
            //移除进度条，播放退出动画
            processStatus = 3;
            $("#hideRecord").hide();
            $(".record-con-tea").hide();
            hideRecordInit.stop();
            if (userType === 'stu') {
                console.log("isSync && isSDKPlay", isSync, isSDKPlay)
                if (isSync && isSDKPlay) { //设备支持播放
                    if (!isRecordresult) { // 【麦克风放下依然没收到录音结果， 显示等待动画，等待结果返回】
                        console.log("显示等待中")
                        $("#wait-container").show();
                        waitInit.play(); //显示等待中动画
                        //N+5秒后未收到录音结束通知
                        if (waitTimeIntStu) {
                            clearTimeout(waitTimeIntStu);
                            waitTimeIntStu = null;
                        }
                        waitTimeIntStu = setTimeout(function() { //N+5后容错处理
                            if (!isRecordresult) { //仍未收到录音结果
                                showEvaluate(); //告知老师需要显示评价
                                $("#wait-container").remove();
                                waitInit.stop();
                            }
                        }, (duration + 5) * 1000);
                    } else { //【 如已收到录音结果， 则无需播放等待动画，启用播放方法】
                        startPlaySync(wavfile) //调用播放方法
                    }
                } else {
                    showEvaluate(); //告知老师需要显示评价
                }
            }
        })
    }

    // 学生已收到开始播放成功通知，开启播放动画 同时告知老师（只有学生可收到）
    function PlayBySDK() {
        console.log("发出播放动画")
        //告知老师显示播放动画
        SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: 'playSync',
            eventType: 'click',
            method: 'event',
            syncName: 'playSyncName',
            recoveryMode: '2', //不用做断线重连
        });
    }
    //开始播放动画
    $("#playSyncbtn").on('playSyncName', function(e, message) {
        console.log("开始播放动画")
        $("#play-record-container").show();
        playInit.play();
        SDK.setEventLock();
    })

    //学生已收到结束播放通知，结束播放动画  同时告知老师结束播放动画（只有学生可收到）
    function stopBySDK() {
        SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: 'stopPlaySync',
            eventType: 'click',
            method: 'event',
            syncName: 'stopPlaySyncName',
            recoveryMode: '2', //不用做断线重连
        });
    }

    //结束播放动画 (学生是直接调用 老师是收到通知调用)
    $("#stopPlaySyncbtn").on('stopPlaySyncName', function(e, message) {
        console.log("结束播放动画")
        playInit.stop();
        $("#play-record-container").hide();
        SDK.setEventLock();
        showEvaluate(); //告知老师需要显示评价
    })
    //告知老师显示评价
    function showEvaluate() {
        SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: 'showEvaluateSync',
            eventType: 'click',
            method: 'event',
            syncName: 'showEvaluateSyncName',
            recoveryMode: '2', //不用做断线重连
        });
    }
    // 老师显示评价
    $("#showEvaluateSyncbtn").on('showEvaluateSyncName', function(e, message) {
        SDK.setEventLock();
        if (processStatus != 6) {
            if (userType !== 'tea' || processStatus == 0) return;
            processStatus = 6
            $(".evaluateTip").show();
        }
    })
    // 点击great按钮
    $(".great-btn").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (processStatus == 7) return;
        $(".evaluateBtn").addClass("disableBtn").attr('disabled', 'disabled');
        processStatus = 7 //播放评价动画
        console.log("great")
        if (isSync) {
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'greatBtnClick',
                recoveryMode: '2', //不用做断线重连
            });
        } else {
            $(this).trigger("greatBtnClick");
        }
    })
    $(".great-btn").on('greatBtnClick', function(e, message) {
        greatAni();
        SDK.setEventLock();
    })
    // 点击goodjob按钮
    $(".goodjob-btn").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (processStatus == 7) return;
        $(".evaluateBtn").addClass("disableBtn").attr('disabled', 'disabled');
        processStatus = 7
        if (isSync) {
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'goodJobBtnClick',
                recoveryMode: '2', //不用做断线重连
            });
        } else {
            $(this).trigger("goodJobBtnClick");
        }
    })
    $(".goodjob-btn").on('goodJobBtnClick', function(e, message) {
        goodJobAni();
        SDK.setEventLock();
    })
    //great动画
    function greatAni() {
        var timeIntFinish = setInterval(function() {
            $("#great").show();
            SDK.playRudio({
                index: $('#greatAudio').get(0),
                syncName: $('#greatAudio').attr("data-syncaudio")
            })
            greatInit.play();
            greatInit.addEventListener('complete', function() {
                greatInit.stop();
                $("#great").hide();
                $(".evaluateBtn").removeClass("disableBtn").removeAttr('disabled');
                processStatus = 6
            });
            clearInterval(timeIntFinish);
            timeIntFinish = null;
        }, 500)
    }

    //goodJob动画
    function goodJobAni() {
        var timeIntFinish = setInterval(function() {
            $("#goodJob").show();
            SDK.playRudio({
                index: $('#goodJobAudio').get(0),
                syncName: $('#goodJobAudio').attr("data-syncaudio")
            })
            goodJobInit.play();
            goodJobInit.addEventListener('complete', function() {
                goodJobInit.stop();
                $("#goodJob").hide();
                $(".evaluateBtn").removeClass("disableBtn").removeAttr('disabled');
                processStatus = 6
            });
            clearInterval(timeIntFinish);
            timeIntFinish = null;
        }, 500)
    }

    //启动录音协议
    function startRecordSync() {
        SDK.bindSyncNormalData({
            'type': 'js2cpp_startspeechgrading',
            'data': {
                "text": textContent,
                "engine": "2",
                "mode": textType,
                "timeout": duration,
                "otherInfor": {
                    pageRandom: SDK.getClassConf().h5Course.pageRandom
                }
            }
        });
    }
    //播放音频协议前置方法 用于检查当前状态
    function startPlayPre(wavfile) {
        //如果还有进度条，则取消进度条 播放麦克风放下动画
        console.log("当前processStatus", processStatus)
        if (processStatus == 1) { //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
            console.log("当前处于进度条动画，结束进度条")
            processInit.stop();
            $("#recording").hide();
            console.log("准备放下麦克风")
            $("#hideRecord").show();
            hideRecordInit.play(); //麦克风放下后将自动触发播放方法
            processStatus = 2
            $("#processAni").fadeOut(100);
        } else if (processStatus == 2) { //2 已开始播放放下麦克风动画 麦克风放下后将自动触发播放方法
            console.log("当前已开始播放放下麦克风动画 麦克风放下后将自动触发播放方法")
        } else { //直接调用播放方法
            console.log("当前录音动画已全部播放完毕，直接调用播放方法")
            startPlaySync(wavfile) //调用播放方法
        }
    }

    //发送播放音频协议
    function startPlaySync(wavfile) {
        console.log("播放音频", wavfile)
        SDK.bindSyncNormalData({
            'type': 'js2cpp_startplayaudio',
            'data': {
                'playmode': '3',
                'wavfile': wavfile,
                'otherInfor': {
                    pageRandom: SDK.getClassConf().h5Course.pageRandom
                }
            }
        })
        //不论是否已收到播放结束通知，N+5秒后都会尝试一次停止播放动画。如后续逻辑中判断已停止，则不继续执行。
        if (stopPlayTimer) {
            clearTimeout(stopPlayTimer)
            stopPlayTimer = null;
        }
        stopPlayTimer = setTimeout(function() {
            stopBySDK();
            showEvaluate();
        }, duration * 1000 + 5000)
    }
    //供SDK调用
    window.classTempObj = {
        //收到录音结果通知
        cpp2js_onspeechgradedData: function(message) {
            //返回录音结果
            if (message.cbtype === 'speechresult') {
                isRecordresult = true;
                console.log("移除等待中")
                $("#wait-container").remove(); //移除等待动画
                waitInit.stop(); //结束等待中动画
                if (message.code == 0 && message.wavfile != "") {
                     wavfile = message.wavfile
                    startPlayPre(message.wavfile); //发出播放协议
                } else {
                    showEvaluate(); //录音失败,告知老师需要显示评价
                }
            }
        },
        //收到播放结果通知
        cpp2js_onplayaudioresultData: function(message) {
            console.log("收到播放通知")
            //返回开始播放结果
            if (message.cbtype === 'start') {
                if (message.code == 0) {
                    PlayBySDK(); //播放动画
                } else {
                    showEvaluate(); //录音播放失败,告知老师需要显示评价
                }
            }

            //返回结束播放结果
            if (message.cbtype === 'stop') {
                stopBySDK();
            }
        },
        recoverTpl: function() { //有断线重连数据，说明游戏在进行中 隐藏开始录音按钮
            $(".startBtn").hide();
        }
    }

    /**
     * 断线后另一端的处理
     */
    window.SDK.memberChange = function(message) {
        if (isSync) {
            if (message.state == 'enter') { // 进入教室

            } else if (message.state == 'out') { // 退出教室
                console.log("有人断线")
                if (userType === 'tea' && message.role == 'stu') { //老师检测到学生掉线。移除一切动画，老师显示评价面板
                    console.log("学生掉线了")
                    $(".startBtn").hide();
                    $("#showRecord").hide();
                    $("#recording").hide();
                    $("#hideRecord").hide();
                    $(".record-con-tea").hide();
                    $("#play-record-container").hide();
                    processStatus = 6
                    $(".evaluateTip").show();
                }
            }
        }
    }


})
