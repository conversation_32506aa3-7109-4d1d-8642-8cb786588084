<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>TOR0002_语音答题</title>
  <link rel="stylesheet" href="form/css/style.css">
  <link rel="stylesheet" href="form/js/QuillEditor/quill.snow.css">
  <script src='form/js/jquery-2.1.1.min.js'></script>
  <script src='form/js/vue.min.js'></script>
  <script src='form/js/QuillEditor/quill.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <div class="h-title">TOR0002_语音答题</div>

      <% include ./src/common/template/common_head_nontitle %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
      <!-- 麦克风图标位置 -->
      <div class="c-group">
        <div class="c-title">提示录音文本的图标</div>
        <div class="c-area c-well">
          <div class="c-well">
            <p>- 该图标的作用是，告诉用户应该读那一部分的内容。</p>
            <p>- 结合背景图，设置“图标位置”</p>
            <p>- 背景图内，在需要录音的文本下，请使用黄色下划线标记。</p>
            <span>提示图标的位置
              <em>*</em>
            </span>
            <input class='c-input-txt2' placeholder="" maxlength="100" type="number"
              v-model="configData.source.microphonePosition">
            数字，1～800
          </div>
        </div>
      </div>
      <!-- 题干音频 -->
      <div class="c-group">
        <div class="c-title">题干音频</div>
        <div class="c-area upload img-upload radio-group">
          <div class="c-well">
            <div class="field-wrap">
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-audio"
                v-on:change="audioUpload($event,configData.source,'audio',40)">
              题干音频<label for="content-audio" class="btn btn-show upload" v-if="!configData.source.audio">上传</label><em
                class="red">文件大小≤40KB 非必填</em>
            </div>
            <div class="audio-preview" v-show="configData.source.audio">
              <div class="audio-tools">
                <p v-show="configData.source.audio">{{configData.source.audio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="configData.source.audio"></audio>
              </span>
            </div>
            <div class="field-wrap" style="margin-top:20px;">
              <label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.audio"
                @click="configData.source.audio=''">删除</label>
              <label for="content-audio" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
            </div>
            <div>
              <span>图标位置
              </span>
              <input class='c-input-txt2' placeholder="" maxlength="100" type="number"
                v-model="configData.source.audioPosition">
              数字，1～800
            </div>
          </div>
        </div>
      </div>



      <!-- 题目信息 -->
      <div class="c-group">
        <div class="c-title">语音答题</div>
        <div class="c-area user-input">
          <p>-以下内容仅供语音识别使用，不直接在页面上显示</p>
          <div class="c-well">
            <div class="well-con">
              <label class="distinguish-text">文本类型：<em>*&nbsp;&nbsp;</em></label>
              <label class="inline-label" for="textType"><input type="radio" name="textType" value="1"
                  v-model="configData.source.textType"> 单词</label>&nbsp;&nbsp;
              <label class="inline-label" for="textType"><input type="radio" name="textType" value="3"
                  v-model="configData.source.textType"> 词组/短语/句子</label>
            </div>
            <div class="well-con distinguish">
              <label class="distinguish-text">识别文本：<em>*&nbsp;&nbsp;</em>≤50个字符（一句话，仅限英文文本、标点）</label>
              <textarea class="textarea-input" name="" cols="56" rows="2" placeholder="请在此输入文本内容"
                v-model="configData.source.textContent" maxlength="50"></textarea>
            </div>
            <div class="well-con">
              <p class="distinguish-text">录音时间：<em>*&nbsp;&nbsp;</em>
                <lable v-if="configData.source.textType == 1">2s ～10s</lable>
                <lable v-if="configData.source.textType == 3">2s ～30s</lable>
              </p>
              <input class='c-input-txt2 text-input' placeholder="" type="number"
                v-model="configData.source.duration">秒
                <em class="red">请设置合适的录音时间，不要过长。</em>
            </div>
          </div>
        </div>
      </div>


      <button class="send-btn" v-on:click="onSend">提交</button>
    </div>

  </div>
  <div class="edit-show">
    <div class="show-fixed">
      <div class="show-img">
        <img src="form/img/bg.jpg?v=<%=new Date().getTime()%>" alt="">
      </div>
      <ul class="show-txt">
        <li>
          <em>图片格式：</em>JPG/PNG/GIF</li>
        <li>
          <em>声音格式：</em>MP3/WAV</li>
        <li>
          <em>视频格式：</em>MP4</li>
        <li>带有“ * ”号为必填项</li>
      </ul>
    </div>
  </div>
  </div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
