@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';

html {
    touch-action: manipulation;
}

@mixin setEle($l:0rem, $t:0rem, $w:0rem, $h:0rem, $mt:0rem) {
    position: absolute;
    left: $l;
    top: $t;
    width: $w;
    height: $h;
    margin-top: $mt;
}

@mixin setEle1($l:0rem, $w:0rem) {
    position: absolute;
    left: $l;
    width: $w;
}


audio {
    width: 0;
    height: 0;
    opacity: 0;
    position: absolute;
}

.container {
    margin-top: -5.4rem;
}

.tg {
    z-index: 100;
}

.desc {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.title {
    height: 1.85rem;
    margin-top: 0;
}

.title h3 {
    margin: 0.3rem 0;
}

.main {
    width: 100%;
    height: 100%;
    position: relative;

    // 网格系统
    .boxList {
        position: absolute;
        left: 1.08rem;
        top: .3rem;
        width: 17rem;
        height: 9.8rem;
        z-index: -1;

        .boxUl {
            position: absolute;
            left: .5rem;
            top: .7rem;
            width: 16rem;
            height: 8.4rem;
            display: flex;
            flex-wrap: wrap;

            li {
                // background: red;
                margin-left: .01rem;
                width: .39rem;
                height: .39rem;
            }
        }
    }

    .audioDiv {
        display: none;
        position: absolute;
        width: 1.45rem;
        height: 1.34rem;
        background: url('../image/btn-audio-bg.png') no-repeat;
        background-size: 100% 100%;
        z-index: 10;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
        cursor: pointer;

        #audioImg {
            position: absolute;
            top: 0.3rem;
            left: 0.3rem;
            width: 0.83rem;
            height: 0.8rem;
        }

        #audioExample {
            width: 0;
            height: 0;
            opacity: 0;
            position: absolute;
        }
    }

    #microphoneImg {
        display: none;
        position: absolute;
        width: 0.86rem;
        height: 1.34rem;
    }

    //开始按钮
    .startBtn {
        display: none;
        position: absolute;
        bottom: 0.1rem;
        left: 50%;
        width: 2.7rem;
        height: 0.76rem;
        background: rgba(255, 255, 255, 1);
        border-radius: 0.38rem;
        transform: translate(-50%, 0px);
        cursor: pointer;

        .startContent {
            width: 2.54rem;
            height: 0.6rem;
            background: rgba(229, 171, 66, 1);
            border-radius: 0.3rem;
            margin: 0 auto;
            position: relative;
            top: 50%;
            transform: translate(0, -50%);
            font-size: 0.3rem;
            font-family: Century Gothic;
            font-weight: bold;
            color: rgba(255, 255, 255, 1);
            line-height: 0.46rem;
            line-height: .6rem;
            text-align: center;
        }
    }

    // 麦克风动画
    .animation-con {
        position: absolute;
        bottom: 0px;
        width: 100%;

        .record-con-tea {
            display: none;
            text-align: center;
            position: absolute;
            bottom: 2.51rem;
            width: 4rem;
            height: 0.88rem;
            left: 50%;
            transform: translate(-50%, 0px);
            background: rgba(0, 0, 0, 0.6);
            border-radius: 16px;
            font-size: 0.3rem;
            font-weight: 400;
            line-height: 0.88rem;
            color: #fff;
        }

        .record-con {
            display: none;
            position: absolute;
            bottom: 0;
            width: 4rem;
            height: 2.33rem;
            left: 50%;
            transform: translate(-50%, 0px);
            background: rgba(0, 0, 0, 0.6);
            border-radius: 40px 40px 0px 0px;
        }

        .processAni {
            background: transparent;
        }

        .waitAni {
            width: 3.48rem;
            height: 1.74rem;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0px);
            bottom: 0.3rem;
        }

        .playAni {
            width: 3.48rem;
            height: 1.74rem;
            position: absolute;
            left: 50%;
            transform: translate(-50%, 0px);
            bottom: 0.3rem;
        }
    }

    // 评价动画
    .finialAni {
        position: absolute;
        width: 100%;
        height: 100%;
        display: none;
    }

    // 评价
    .evaluateTip {
        display: none;
        width: 9.1rem;
        height: 1rem;
        border-radius: 0.16rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0px);
        bottom: 0;
        background: rgba(255, 255, 255, 0.6);
        font-size: .3rem;

        .tip {
            height: 100%;
            width: 4.6rem;
            line-height: 1rem;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .evaluateBtn {
            position: absolute;
            top: .12rem;
            color: #fff;
            text-align: center;
            line-height: 0.6rem;
            border-radius: .38rem;
            border: #fff solid 0.08rem;
            height: 0.6rem;
            cursor: pointer;
        }



        .great-btn {
            background: #ED6E22;
            right: 2.68rem;
            width: 1.78rem;
        }

        .goodjob-btn {
            background: #f1a91e;
            right: 0.46rem;
            width: 1.84rem;
        }
        .disableBtn {
          background:#DDDDDD;
          cursor: not-allowed;
        }
    }

    #playSyncbtn,
    #stopPlaySyncbtn,
    #showEvaluateSyncbtn {
        display: none;
    }
}
