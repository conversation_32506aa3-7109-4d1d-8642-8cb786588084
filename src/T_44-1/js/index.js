"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/drag.js";
$(function () {
  window.h5Template = {
    hasPractice: "1",
  };
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  const msg = configData.source;
  const dis = 0.3;

  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/defaultBg.png)" });
  }

  /**
   * 添加入场动画
   */

  window.onload = function () {
    $(".train").addClass("moveIn");
    if (isSync) {
      var current_user_id = SDK.getClassConf().user.id;
      var frame_id = $(window.frameElement).attr("id");
      var frame_user_id = $(window.frameElement).attr("user_id");
      if (
        frame_id != "h5_course_cache_frame" &&
        frame_user_id == current_user_id &&
        frame_id === "h5_course_self_frame"
      ) {
        // $('.inOutAudio').get('0').play();
        SDK.playRudio({
          index: $(".inOutAudio").get("0"),
          syncName: $(".inOutAudio").attr("data-syncaudio"),
        });
      }
    } else {
      $(".inOutAudio").get("0").play();
    }
  };

  /**
   * 创建元素
   */
  let cardListHtml = "";
  let trainHtml = "";
  let listArr = [];
  $.each(msg.distracterList, function (index, val) {
    listArr.push(val);
  });
  let nowTrueNum = 0; // 答对的个数

  //文字的尺寸控制
  let textSize = (function textClass() {
    if (msg.type != "img") {
      for (let i = 0; i < msg.trainMsg.length; i++) {
        if (msg.trainMsg[i].font.length > 2) {
          return "cs";
        }
      }
      for (let i = 0; i < msg.distracterList.length; i++) {
        if (msg.distracterList[i].font.length > 2) {
          return "cs";
        }
      }
    }
    return "fs";
  })();

  // 创建火车车厢
  for (let i = 0; i < msg.trainMsg.length; i++) {
    if (msg.type == "img") {
      if (msg.trainMsg[i].isEmpty == "true") {
        listArr.push(msg.trainMsg[i]);
      } else {
        nowTrueNum++;
      }
      trainHtml += `
                <div class="trainB_evl pos_${msg.trainMsg[i].pos} ${
        msg.trainMsg[i].isEmpty == "true" ? "hasMask" : ""
      }">
                    <div class="mask"></div>
                    <div class="box">
                        <img src="${msg.trainMsg[i].img}" class="isShow ${
        msg.trainMsg[i].isEmpty == "true" ? "hide" : ""
      }">
                        <img src="./image/wenhao.png" class="nullpic ${
                          msg.trainMsg[i].isEmpty == "true" ? "" : "hide"
                        }">
                    </div>
                </div>
            `;
    } else {
      if (msg.trainMsg[i].isEmpty == "true") {
        listArr.push(msg.trainMsg[i]);
      } else {
        nowTrueNum++;
      }
      trainHtml += `
                <div id="pos_${msg.trainMsg[i].pos}" class="trainB_evl pos_${
        msg.trainMsg[i].pos
      } ${msg.trainMsg[i].isEmpty == "true" ? "hasMask" : ""}">
                    <div class="mask"></div>
                    <div class="box">
                        <div class="isShow ${textSize} ${
        msg.trainMsg[i].isEmpty == "true" ? "wenhao" : ""
      }">
                            <span class="${
                              msg.trainMsg[i].isEmpty == "true" ? "hide" : ""
                            }">${msg.trainMsg[i].font}</span>
                        </div>
                    </div>
                </div>
            `;
    }
  }
  trainHtml += `<div class="trainWBox"><div class="trainW"></div></div>`;
  $(".trainB").html(trainHtml);

  if (configData.source.audio) {
    $(".audioBg")
      .show()
      .append(
        `<audio src="${configData.source.audio}" class="audio" data-syncaudio="audio1"></audio>`
      );
  }

  /**
   * 打乱顺序
   */

  let seleList = []; // 打乱顺序后的数组
  let cardObj = {};
  if (listArr.length > 0) {
    if (isSync) {
      if (msg.random) {
        let pages = SDK.getClassConf().h5Course.localPage; //页码打乱顺序
        let seleListOne = window.reSort(listArr, pages);
        seleList = window.reSort(seleListOne, msg.random);
      } else {
        let pages = SDK.getClassConf().h5Course.localPage; //页码打乱顺序
        seleList = window.reSort(listArr, pages);
      }
    } else {
      if (msg.random) {
        seleList = window.reSort(listArr, msg.random);
      } else {
        seleList = window.reSort(listArr, Math.round(Math.random() * 100));
      }
    }
  }
  let initPOsLe = 0;
  switch (listArr.length) {
    case 1:
      initPOsLe = 7;
      break;
    case 2:
      initPOsLe = 5.2;
      break;
    case 3:
      initPOsLe = 4.2;
      break;
    case 4:
      initPOsLe = 3;
      break;
    case 5:
      initPOsLe = 1.5;
      break;
    case 6:
      initPOsLe = 0;
      break;
  }

  // 创建拖拽选项
  for (let i = 0; i < seleList.length; i++) {
    cardObj["card_ele_" + seleList[i].pos] = false;
    if (msg.type == "img") {
      cardListHtml += `
                <div class="card_ele imgC card_ele_${
                  seleList[i].pos
                }" data-syncactions="item_${i}" data-pos="${
        seleList[i].pos
      }" data-id="card_ele_${seleList[i].pos}" data-item="pos_${
        seleList[i].pos
      }" data-initT = "6.95" data-initL = ${
        (dis + 2.5) * i + initPOsLe
      } style="left:${(dis + 2.5) * i + initPOsLe}rem" >
                    <div class="cardBox"><img src="${seleList[i].img}"></div>
                </div>
             `;
    } else {
      cardListHtml += `
                <div class="card_ele imgF card_ele_${
                  seleList[i].pos
                }" data-syncactions="item_${i}" data-pos="${
        seleList[i].pos
      }" data-id="card_ele_${seleList[i].pos}" data-item="pos_${
        seleList[i].pos
      }"  data-initT = "6.95" data-initL = "${
        (dis + 2.5) * i + initPOsLe
      }" style="left:${(dis + 2.5) * i + initPOsLe}rem">
                    <div class="cardBox  ${textSize}">${seleList[i].font}</div>
                </div>
            `;
    }
  }
  $(".cardList").html(cardListHtml);

  $(".card_ele").hover(
    function () {
      $(this).css({ transform: "scale(1.05)" });
    },
    function () {
      $(this).css({ transform: "scale(1)" });
    }
  );

  /**
   * 碰撞  与  拖拽
   */

  let boxHalf = 1.2;
  let moveHalf = 1.25;
  let breakAre = 1.5; //碰撞的有效面积
  let canDrag = true;
  $(".train").on("animationend webkitAnimationEnd", function () {
    // $('.inOutAudio').get('0').pause();
    SDK.pauseRudio({
      index: $(".inOutAudio").get("0"),
      syncName: $(".inOutAudio").attr("data-syncaudio"),
    });
    $(".card_ele").drag({
      before: function (e) {
        $(".hasMask .mask").fadeIn(500);
        $(this).css({
          transition: "",
          "z-index": 21,
          transform: "scale(.8)",
        });
      },
      process: function (e) {},
      end: function (e) {
        $(".hasMask .mask").fadeOut(500);
        let moveLeft =
          ($(this).offset().left - $(".container").offset().left) /
            window.base +
          moveHalf; //拖拽体的中心left
        let moveTop =
          ($(this).offset().top - $(".container").offset().top) / window.base +
          moveHalf; //拖拽体的中心top
        let tarinBoxTop =
          ($(".pos_1").offset().top - $(".container").offset().top) /
            window.base +
          boxHalf; //火车区域的高度
        if ($(this).attr("data-pos") == "#") {
          // 干扰项
          $(this).css({
            left: $(this).attr("data-initL") + "rem",
            top: $(this).attr("data-initT") + "rem",
            transition: "all 0.5s",
            transform: "scale(1)",
            "z-index": 20,
          });
          var name = $(this).attr("data-syncactions");
          if (Math.abs(moveTop - tarinBoxTop) <= breakAre) {
            console.log("干扰项已拖拽到火车高度，回答错误", name);
            SDK.playRudio({
              index: $(".trueAudiofill").get("0"),
              syncName: $(".trueAudiofill").attr("data-syncaudio"),
            });
            if (isSync) {
              SDK.bindSyncEvt({
                eventType: "click",
                method: "event",
                syncName: "syncDragEnd",
                otherInfor: {
                  name: name,
                  isWrong: true,
                  tag: "useless",
                  type: "interference",
                },
                recoveryMode: "1",
              });
            }
          } else {
            console.log("干扰项未拖拽到火车， 本次行为无效", name);
          }
        } else {
          let className = $(this).attr("data-item");
          // 获取正确车厢中心位置
          let boxLeft =
            ($("." + className).offset().left - $(".container").offset().left) /
              window.base +
            boxHalf; //拖拽坑的left位置
          let boxTop =
            ($("." + className).offset().top - $(".container").offset().top) /
              window.base +
            boxHalf; //拖拽坑的位置
          let sameLeft = Math.abs(moveLeft - boxLeft);
          let sameTop = Math.abs(moveTop - boxTop);
          var name = $(this).attr("data-syncactions");
          //判断中心点是否在检测区域
          if (sameLeft <= breakAre && sameTop <= breakAre) {
            console.log("答对正确", name);
            nowTrueNum++;
            cardObj[$(this).attr("data-id")] = true;
            console.log(cardObj);
            if (canDrag) {
              canDrag = false;
              if (!isSync) {
                $(this).trigger("syncDragEnd");
                return;
              }
              SDK.bindSyncEvt({
                index: $(this).data("syncactions"),
                eventType: "click",
                method: "event",
                syncName: "syncDragEnd",
                name: name,
                otherInfor: {
                  cardObj: cardObj,
                  nowTrueNum: nowTrueNum,
                  goOut: false,
                  type: "option",
                },
                recoveryMode: "1",
              });
            }
          } else {
            //答错
            $(this).css({
              left: $(this).attr("data-initL") + "rem",
              top: $(this).attr("data-initT") + "rem",
              transition: "all 0.5s",
              transform: "scale(1)",
              "z-index": 20,
            });
            var name = $(this).attr("data-syncactions");

            if (Math.abs(moveTop - tarinBoxTop) <= breakAre) {
              console.log("选项已拖拽到火车高度，回答错误", name);
              SDK.playRudio({
                  index: $(".trueAudiofill").get("0"),
                  syncName: $(".trueAudiofill").attr("data-syncaudio"),
              });
              if (isSync) {
                SDK.bindSyncEvt({
                  index: $(this).data("syncactions"),
                  eventType: "click",
                  method: "event",
                  syncName: "syncDragEnd",
                  otherInfor: {
                    name: name,
                    isWrong: true,
                    tag: "useless",
                    type: "option",
                  },
                  recoveryMode: "1",
                });
              }

            } else {
              console.log("选项未拖拽到火车，本次行为无效", name);
            }
          }
        }
      },
    });
  });
  $(".card_ele").on("syncDragEnd", function (e, message) {
    if (isSync) {
      let otherInfor = message.data[0].value.syncAction.otherInfor;
      cardObj = otherInfor.cardObj;
      nowTrueNum = otherInfor.nowTrueNum;
      if (message.operate == "5") {
        callbackFn(cardObj, otherInfor.goOut);
        return;
      }
    }
    let className = $(this).attr("data-item");
    if (msg.type == "img") {
      $("." + className)
        .find(".isShow")
        .show();
      $("." + className)
        .find(".nullpic")
        .hide();
    } else {
      $("." + className)
        .find(".isShow span")
        .show();
      $("." + className)
        .find(".isShow")
        .removeClass("wenhao");
    }
    $("." + className).removeClass("hasMask");
    $(this).hide();
    checkOver(); //检测游戏是否结束
    SDK.setEventLock();
  });

  let isGameover = false;
  let isGameoverSet = false;

  //检测游戏是否结束
  function checkOver(){
    if (nowTrueNum >= msg.trainMsg.length) {
      $(".redLight").hide();
      $(".greenLight").show();
      $(".trainH .cloud1").fadeIn(1000);
      $(".trainH .cloud2").fadeIn(1500);
      $(".trainH .cloud3").fadeIn(2000);
      if (!isGameoverSet) {
        isGameover = true;
        isGameoverSet = true;
      }
      if (isSync) {
        // 火车可发车，上报已全部答完
        SDK.bindSyncEvt({
          eventType: "click",
          method: "event",
          syncName: "syncDragEnd",
          otherInfor: {
            gameover: isGameover ? true : false,
            tag: "useless",
          },
          recoveryMode: "1",
        });
        console.log("发送游戏结束 ====>")

        if (window.frameElement.getAttribute("user_type") == "tea") {
          $(".alertBox").css("opacity", "1");
        }
      } else {
        $(".alertBox").css("opacity", "1");
      }
      canDrag = false;
      setTimeout(function () {
        // $('.trueAudio').get('0').play();
        // SDK.playRudio({
        //   index: $(".trueAudio").get("0"),
        //   syncName: $(".trueAudio").attr("data-syncaudio"),
        // });
        // $(".trueAudio").get("0").onended = function () {
          // setTimeout(function () {
          //   SDK.playRudio({
          //     index: $(".trueAudio1").get("0"),
          //     syncName: $(".trueAudio1").attr("data-syncaudio"),
          //   });
          //   // $('.trueAudio1').get('0').play();
          // }, 500);
        // };
        starFun();
      }, 800);
      $(".Allmask").show();
    } else {
      canDrag = true;
    }
  }
  //答题结束触发发送星星
  function starFun() {
    if (!isSync) {
      return false;
    }
    var classStatus =
      parent.window.h5SyncActions.classConf.h5Course.classStatus;
    var support1v1h5star =
      parent.window.h5SyncActions.classConf.serverData.objCourseInfo
        .support1v1h5star;
    var device = parent.window.h5SyncActions.classConf.h5Course.device;
    if (
      window.frameElement.getAttribute("user_type") == "stu" &&
      classStatus == 2
    ) {
      if ((device == "pc" && support1v1h5star == 1) || device != "pc") {
        console.log("学生拖动正确后发星星");
        SDK.bindSyncStart({
          type: "newH5StarData",
          num: 1,
        });
      }
    }
  }

  /**
   * 老师提示框
   */

  $(".alertBox").drag();
  let canClick = true;
  $(".btn").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (canClick) {
      canClick = false;
      if (!isSync) {
        $(this).trigger("clickBtn");
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "clickBtn",
          otherInfor: {
            cardObj: cardObj,
            nowTrueNum: nowTrueNum,
            goOut: true,
          },
          recoveryMode: "1",
        });
      }
    }
  });
  $(".btn").on("clickBtn", function (e, message) {
    if (isSync) {
      let otherInfor = message.data[0].value.syncAction.otherInfor;
      cardObj = otherInfor.cardObj;
      nowTrueNum = otherInfor.nowTrueNum;
      if (message.operate == "5") {
        callbackFn(cardObj, otherInfor.goOut);
        return;
      }
    }
    $(this).css({
      background: "#bcbcbc",
      cursor: "not-allowed",
    });
    // $('.inOutAudio').get('0').play();
    SDK.playRudio({
      index: $(".inOutAudio").get("0"),
      syncName: $(".inOutAudio").attr("data-syncaudio"),
    });
    $(".train").addClass("moveOut");
    SDK.setEventLock();
  });

  /**
   * 点击声音按钮
   */

  let audioBgClick = true;
  $(".audioBg").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (audioBgClick) {
      audioBgClick = false;
      // 判断幕布是否开启
      if (!isSync) {
        $(this).trigger("audioBgClick");
        return;
      }
      if (window.frameElement.getAttribute("user_type") == "tea") {
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          funcType: "audio",
          syncName: "audioBgClick",
          otherInfor: {
            cardObj: cardObj,
            nowTrueNum: nowTrueNum,
            goOut: true,
          },
          recoveryMode: "1",
        });
      }
    }
  });
  $(".audioBg").on("audioBgClick", function (e, message) {
    if (isSync) {
      let otherInfor = message.data[0].value.syncAction.otherInfor;
      cardObj = otherInfor.cardObj;
      nowTrueNum = otherInfor.nowTrueNum;
      if (message.operate == "5") {
        callbackFn(cardObj, otherInfor.goOut);
        return;
      }
    }
    $(".audioInit").hide();
    $(".audioGif").show();
    // $('.audio').get(0).play()
    SDK.playRudio({
      index: $(".audio").get("0"),
      syncName: $(".audio").attr("data-syncaudio"),
    });

    $(".audio").get(0).onended = function () {
      $(".audioInit").show();
      $(".audioGif").hide();
    };
    audioBgClick = true;
    SDK.setEventLock();
  });

  /**
   * 重新进入教室
   */

  function callbackFn(obj, status) {
    $.each(obj, function (key, val) {
      if (val) {
        let className = $("." + key).attr("data-item");
        if (msg.type == "img") {
          $("." + className)
            .find(".isShow")
            .show();
          $("." + className)
            .find(".nullpic")
            .hide();
        } else {
          $("." + className)
            .find(".isShow span")
            .show();
          $("." + className)
            .find(".isShow")
            .removeClass("wenhao");
        }
        $("." + className).removeClass("hasMask");
        $("." + key).hide();
      }
    });
    if (nowTrueNum >= msg.trainMsg.length) {
      $(".Allmask").show();
      $(".redLight").hide();
      $(".greenLight").show();
      $(".trainH .cloud1").fadeIn(1000);
      $(".trainH .cloud2").fadeIn(1500);
      $(".trainH .cloud3").fadeIn(2000);
      if (isSync) {
        if (window.frameElement.getAttribute("user_type") == "tea") {
          $(".alertBox").css("opacity", "1");
        }
      } else {
        $(".alertBox").css("opacity", "1");
      }
      canDrag = false;
    } else {
      canDrag = true;
    }
    if (status) {
      $(".train").addClass("moveOut");
      $(".btn").css({
        background: "#bcbcbc",
        cursor: "not-allowed",
      });
    }
    SDK.setEventLock();
  }
  if (SDK.getUserType() == "tea") {
    $(".buttons").show();
  }

  // num代表火车车厢。比如火车6节车厢 第124为空位，356有货物，则此处num为 124。


  window.generalTplData = function (message) {
    console.log("【generalTplData】", message);
    //根据不同协议调不同方法
    if (message.actionType == "drag") {
      let num = message.num; //火车上第几个车厢
      nowTrueNum++;
      cardObj[`card_ele_${num}`] = true;
      console.log(cardObj);
      let className = `pos_${num}`;
      //获取货品对应的正确车厢的坐标     货品飞往车厢
      var option = $(`.card_ele[data-item=${className}]`);
      var position = getRelativePosition(`.pos_${num}`, `#main`);
      option.animate(
        {
          left: position.left - 50 + "px",
          top: position.top + "px",
        },
        1000
      );
      $("#container").css("pointer-events", "none");

      setTimeout(() => {
        // 火车里显示货品
        if (msg.type == "img") {
          $("." + className)
            .find(".isShow")
            .show();
          $("." + className)
            .find(".nullpic")
            .hide();
        } else {
          $("." + className)
            .find(".isShow span")
            .show();
          $("." + className)
            .find(".isShow")
            .removeClass("wenhao");
        }
        $("." + className).removeClass("hasMask");
        //隐藏待拖动物体
        $(`.card_ele[data-item=${className}]`).hide();
        checkOver(); //检测游戏是否结束
        $("#container").css("pointer-events", "auto");

      }, 1000);

    }
    if (message.actionType == "fingerDome") {
      $(".handsss").show();
      handAnima();
    }
    
    SDK.setEventLock();
  };
  $(".container").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
   $(".handsss").hide();
   stopAnimation();
  });
  // 引导小手
  function handAnima() {
    let postionTop = $(".card_ele").eq($(".card_ele").length - 1);
    if (!postionTop.find('.handsss').length) {
      postionTop.append('<div class="handsss"></div>');
    }
    // 获取新添加的 div 元素
    var slideDiv = postionTop.find('.handsss');
    let target = $('.hasMask').eq($(".hasMask").length - 1); // 最终的元素
    moveToElement(
      slideDiv.get(0),
      target.get(0),
      800, // 动画持续时间，单位毫秒
      t => t * t * t, // 简单的三次方缓动函数
      () => {
       setTimeout(function(){
        handAnima();
       },800)
      }
    );    
  }
  // 开启小手指动画

  let animationRequestId; // 用于存储 requestAnimationFrame 的返回值

  function moveToElement(startElement, targetElement, duration, easingFunc, callback) {
    const startTime = performance.now();
    const startPosition = startElement.getBoundingClientRect();
    const targetPosition = targetElement.getBoundingClientRect();
  
    function animate(timestamp) {
      const progressTime = timestamp - startTime;
      const progress = Math.min(progressTime / duration, 1);
      const easedProgress = easingFunc(progress);
  
      startElement.style.transform = `translate(${easedProgress * (targetPosition.left - startPosition.left)}px, ${easedProgress * (targetPosition.top - startPosition.top)}px)`;
  
      if (progress < 1) {
        // requestAnimationFrame(animate);
        animationRequestId = requestAnimationFrame(animate); 

      } else {
        if (callback) {
          callback();
        }
      }
    }
  
    requestAnimationFrame(animate);
  }
  // 暂定小手指动画
  function stopAnimation() {
    if (animationRequestId !== undefined) {
      cancelAnimationFrame(animationRequestId); 
      animationRequestId = undefined; 
    }
  }

  //为了老师预览 临时代码
  $(".play1").on("click", function () {
    generalTplData({ actionType: "drag", num: 1 });
  });
  $(".play2").on("click", function () {
    generalTplData({ actionType: "drag", num: 2 });
  });
  $(".play3").on("click", function () {
    generalTplData({ actionType: "drag", num: 3 });
  });
  $(".play4").on("click", function () {
    generalTplData({ actionType: "drag", num: 4 });
  });
  $(".play5").on("click", function () {
    generalTplData({ actionType: "drag", num: 5 });
  });
  $(".play6").on("click", function () {
    generalTplData({ actionType: "drag", num: 6 });
  });

  function getRelativePosition(div1, div2) {
    var $div1 = $(div1);
    var $div2 = $(div2);

    // 初始相对位置
    var position = { top: 0, left: 0 };

    // 递归计算 div1 相对于 div2 的位置
    while ($div1[0] !== $div2[0] && $div1.length) {
      position.top += $div1.position().top;
      position.left += $div1.position().left;
      $div1 = $div1.offsetParent();
    }

    return position;
  }
});
