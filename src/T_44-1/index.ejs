<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TPD0001AI_小火车轮船AI"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult='show-result-1'>
    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>

    <section class="main" id="main">
              <!-- 触发按钮 仅老师 -->
              <div class="buttons hide">
                <div class="">只能点击需要填充的车厢</div>
                <div class="play1">填充第1个车厢</div>
                <div class="play2">填充第2个车厢</div>
                <div class="play3">填充第3个车厢</div>
                <div class="play4">填充第4个车厢</div>
                <div class="play5">填充第5个车厢</div>
                <div class="play6">填充第6个车厢</div>
              </div>
        <div class="stage">
          
            <audio src="./audio/come.mp3" class="inOutAudio" data-syncaudio="audio2"></audio>
            <!-- <audio src="./audio/come.mp3" class="trueAudio" data-syncaudio="audio3"></audio>
            <audio src="./audio/come.mp3" class="trueAudio1" data-syncaudio="audio4"></audio> -->
            <audio src="./audio/fail.mp3" class="trueAudiofill" data-syncaudio="audio5"></audio>
            <div class="audioBg" data-syncactions="audioBtn">
                <img src="./image/btn-audio.png" class="audioInit"/>
                <img src="./image/btn-audio.gif" class="audioGif"/>
            </div>
            <div class="light hide">
                <img src="./image/red.png" class="redLight"/>
                <img src="./image/green.png" class="greenLight"/>
            </div>
            <div class="train">
                <div class="trainH">
                    <span class="cloud1"></span>
                    <span class="cloud2"></span>
                    <span class="cloud3"></span>
                </div>
                <div class="trainB"></div>
            </div>
            <div class="cardList"></div>
            <div class="alertBox">
                <p>click “GO” and the train will leave. </p>
                <span class="btn" data-syncactions="btn">GO</span>
            </div>
        </div>
        <div class="Allmask"></div>
    </section>

</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
