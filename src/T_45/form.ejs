<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>TMI0001_飞天盗贼</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="module-title">TMI0001_飞天盗贼</div>

			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">设置卡片内容</div>
				<div class="c-areAtation img-upload">
					<font>注:卡片数量2～6个，显示顺序从左至右，从上之下。</font>
				</div>
				<div class="c-area upload img-upload">
					<div class="c-well" v-for="(item, index) in configData.source.cards">
						<span class="dele-tg-btn" @click="delCards(item)" v-show="configData.source.cards.length>2"></span>
						<div class="field-wrap">
							<label class="field-label"  for="">图片</label>
							<span class='txt-info'><em>文件大小≤50KB,尺寸450x340 * </em></span> 
							<input type="file"  v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size="450*340" @change="imageUpload($event,item,'pic',50)">
						</div>

						<div class="field-wrap">
							<label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.pic">上传</label>
							<label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.pic">重新上传</label>
						</div>
						<div class="img-preview" v-if="item.pic">
							<img :src="item.pic" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(item)">删除</span>
							</div>
						</div>
						<label>答案 <em>字符：≤20 * </em></label>
						<input type="text" class='c-input-txt' placeholder="请在此输入答案" v-model="configData.source.cards[index].text" maxlength="20">
					</div>
					<button v-if="configData.source.cards.length<6" type="button" class="add-tg-btn" @click="addCards" >+</button>	
				</div> 
			</div>
			<div class="c-group">
				<div class="c-title">环境素材：盗窃／归还者</div>
				<div class="c-areAtation img-upload">
					<font>注:仅上传盗窃者资源时，系统自动将图片左右镜像后作为归还者。</font>
				</div>
				<div class="c-area upload img-upload">
					<div class="c-well" v-for="(item, index) in configData.source.thief">
						<span class="dele-tg-btn" @click="delThiefs(item)" v-show="configData.source.thief.length>0"></span>
						<div class="field-wrap">
							<label class="field-label" for="">{{index===0?"窃贼":"归还者"}}</label>
							<span class='txt-info'><em>文件大小≤50KB，尺寸282x172 </em></span> 
							<input type="file"  v-bind:key="Date.now()" class="btn-file" :id="'thief-pic-'+index" size="282*172" @change="imageUpload($event,item,'pic',50)">
						</div> 
						<div class="field-wrap">
							<label :for="'thief-pic-'+index" class="btn btn-show upload" v-if="!item.pic">上传</label>
							<label :for="'thief-pic-'+index" class="btn upload re-upload" v-if="item.pic">重新上传</label>
						</div>
						<div class="img-preview" v-if="item.pic">
							<img :src="item.pic" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(item)">删除</span>
							</div>
						</div> 
					</div>
					<button v-if="configData.source.thief.length<2" type="button" class="add-tg-btn" @click="addThiefs" >+</button>	
				</div> 
			</div>
			<div class="c-group">
				<div class="c-title">光效</div>
				<div class="c-area upload img-upload">
					<div class="c-well"> 
						<div class="field-wrap">
							<label class="field-label" for="">光效</label>
							<span class='txt-info'><em>文件大小≤50KB，尺寸631x172 </em></span> 
							<input type="file"  v-bind:key="Date.now()" class="btn-file" id="effect-pic-0" size="631*172" @change="imageUpload($event,configData.source.effect,'pic',50)">
						</div> 
						<div class="field-wrap">
							<label for="effect-pic-0" class="btn btn-show upload" v-if="!configData.source.effect.pic">上传</label>
							<label for="effect-pic-0" class="btn upload re-upload" v-if="configData.source.effect.pic">重新上传</label>
						</div>
						<div class="img-preview" v-if="configData.source.effect.pic">
							<img :src="configData.source.effect.pic" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(configData.source.effect)">删除</span>
							</div>
						</div> 
					</div>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>