var configData = {
	bg: './image/defaultBg.jpg',
	desc: 'Drag and say.',
	title:'Use may to make polite requests Use may to make polite requestsUse may to make polite requests Use may to make',
	tg: [
		{
			content: "TOV0001_记忆翻牌eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}
	],
	level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
				content: "eegeg appy family. My father i appy family. My father i",
				title: "weqwf appy family. My father i"
			}]
	},	
	source: {
		type: 'font',
		random:'',
		audio: './assets/audio/1.mp3',
		trainMsg: [
			{
				isEmpty: 'true',
				pos:'1',
				img:'./assets/images/1.png',
				font: 'c'
			},
			{
				isEmpty: 'false',
				pos:'2',
				img:'./assets/images/1.png',
				font: 'r'
			},
			{
				isEmpty: 'false',
				pos:'3',
				img:'./assets/images/1.png',
				font: 'ab'
			},
			{
				isEmpty: 'false',
				pos:'4',
				img:'./assets/images/2.png',
				font: 'i'
			},
			{
				isEmpty: 'true',
				pos:'5',
				img:'./assets/images/1.png',
				font: 'n'
			},
		],
		distracterList: [
			{
				pos:'#',
				img: './assets/images/1.png',
				font: 'd'
			}
		]
	}
};