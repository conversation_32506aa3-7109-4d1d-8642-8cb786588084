<!DOCTYPE html>
<html lang="en">
  <head>
    <% var title="FTG0002_听音点选分类"; %> <%include
    ./src/common/template/index_head %>
  </head>
  <body>
    <div class="container" id="container" data-syncresult="show-result-1">
      <!-- 反馈组件 -->
      <%include  ./src/common/template/feedbackAnimation/index.ejs %>
      <section class="commom">
        <div class="desc"></div>
        <div class="title">
          <h3></h3>
        </div>
      </section>

      <section class="main">
        <audio
          id="effect-audio"
          class="audioSrc"
          webkit-playsinline
          controls
          src=""
          data-syncaudio="effectAudio"
          loop
        ></audio>
        <audio id="click-audio" class="audioSrc" webkit-playsinline controls src="" data-syncaudio="effectAudio"></audio>
        <div class="game-container" data-syncactions='syncInitRule'>
          <div class="experience">
            <div class="experience-bar">
                <div class="experience-bar-fill"></div>
              </div>
              <div class="chest"></div>
            <div class="chest-half"></div>
            <div class="horn"></div>
          </div>
        </div>
      </section>

      <!-- 功能遮罩层 -->
      <div class="funcMask">
        <!-- 预习模式操作区 -->
        <div class="startBox">
          <div class="demoTextBox">
            <img
              src="./image/4dfeec15c2cd5d502bf53095ec38b9ea.png"
              class="startMsg"
            />
          </div>
          <div class="demoBtnBox">
            <!-- <img
              src="./image/57f60d04ee91d3178a98cc81d7b08001.png"
              class="demo-btnStu"
            /> -->
            <img
              src="./image/0250983386ad8a2c2226cac7b83be49e.png"
              class="startBtn"
              data-syncactions="sycnStartBtnClick"
            />
          </div>
        </div>
        <!-- 倒计时 -->
        <div class="timeChangeBox hide">
          <div class="timeBg">
            <div class="numberList"></div>
            <audio
              src="./audio/timeLow.mp3"
              class="timeLowAudio_1"
              data-syncaudio="timeLowAudio_1"
            ></audio>
            <audio
              src="./audio/timeLow.mp3"
              class="timeLowAudio_2"
              data-syncaudio="timeLowAudio_2"
            ></audio>
            <audio
              src="./audio/timeLow.mp3"
              class="timeLowAudio_3"
              data-syncaudio="timeLowAudio_3"
            ></audio>
            <audio
              src="./audio/timehigh.mp3"
              class="timeLowAudio_4"
              data-syncaudio="timeLowAudio_4"
            ></audio>
          </div>
        </div>
      </div>
    </div>
    <%include ./src/common/template/index_bottom %> <%include
    ./src/common/template/lottie %>
  </body>
</html>
