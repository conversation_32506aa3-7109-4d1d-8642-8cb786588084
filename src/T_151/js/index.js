"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";
$(function () {
  window.h5Template = {
    hasDemo: "0",
    hasPractice: "2",
  };
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  let source = configData.source;
  let classStatus = "0"; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }
  let options = configData.source.options;

  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/bg.png)" });
  }
  let isFirst = true;

  const reportItemLength = configData.targets.reduce((acc, cur) => {
    if (cur.arr && cur.arr.length > 0) {
      return acc + cur.arr.reduce((locAcc, imgObj) => locAcc + (imgObj.locations ? imgObj.locations.length : 0), 0);
    }
    return acc;
  }, 0);
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      item: reportItemLength || 0,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.PRACTICE_INPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.LISTEN
    },
  })

  const page = {
    musicConfig: {
      right: "./audio/right-change.mp3",
      wrong: "./audio/wrong.mp3",
    },

    defaultConfig: {
      horn: "./image/laba.json",
    },

    hornAnimation: null,

    // todo 总数量
    total: 0,

    // todo 当前包播放的是哪个音频
    currentIndex: 0,

    // todo 音频播放数组
    audioList: [],
    // todo 当前经验值
    currentValue: 0,

    targetsAllowClick: false,

    //交互锁
    isClickLocked: false,

    init: function () {
      this.isShowMask();
      this.initAnimation();
      this.initPage();
      // this.initAudio()
      this.initRule();
      this.initHalfChest();
    },

    isShowMask: function () {
      if (isSync) {
        const userType = window.frameElement
        ? window.frameElement.getAttribute("user_type")
        : "";
        const classStatus = SDK.getClassConf().h5Course.classStatus;
        if (classStatus == 0 && userType == "stu") {// not in class && student
            $(".funcMask").show();
        }
      }else {
        $(".funcMask").show();
      }
      // const userType = window.frameElement
      //   ? window.frameElement.getAttribute("user_type")
      //   : "";
      // if (userType == "stu") {
      //   // todo 学生端展示
      //   $(".funcMask").show()
      // }
      
    },

    initAnimation: function () {
      this.createHornAnimation();
    },

    initPage: function () {
      const { targets = [], destination = [] } = configData;
      console.log("targets", targets);

      $(".game-container").on("syncInitRule", function (e) {
        console.log(
          "recover ganme-container sdk",
          page.result,
          SDK.syncData.result
        );
        page.audioList = [...SDK.syncData.audioList];
        SDK.setEventLock();
      });

      if (targets.length > 0) {
        for (let i = 0; i < targets.length; i++) {
          const target = targets[i];
          const { audio, arr = [], value } = target || {};
          
          if (arr && arr.length > 0) {
            for (let k = 0; k < arr.length; k++) {
              const imgObj = arr[k];
              const { img, locations = [] } = imgObj || {};
              
              if (locations.length > 0 && img) {
                getImageSize(img, (width, height) => {
                  for (let j = 0; j < locations.length; j++) {
                    const location = locations[j];
                    const { x = 0, y = 0 } = location;
                    console.log(i, "-----i");
                    console.log(j, "-----j");
                    target.audio && (page.total += 1);
                    const imgElement = $(
                      `<img class="target-btn" data-syncactions="syncTargetBtnClick-${i}-${k}-${j}" />`
                    )
                      .attr("src", img)
                      .css({
                        position: "absolute",
                        // zIndex: 10,
                        zIndex: 5,
                        left: x / 100 + "rem",
                        top: y / 100 + "rem",
                        width: width / 100 + "rem",
                        height: height / 100 + "rem",
                        cursor: "pointer",
                      })
                      .attr("data-desvalue", value);
                    $(".game-container").append(imgElement);
                    imgElement.addClass("breathe");

                    // console.log("target-btn", $(".target-btn"));
                    imgElement.on("click touchstart", function (e) {
                      console.log(i, j, "my click");
                      if (e.type === "touchstart") {
                        e.preventDefault();
                      }
                      e.stopPropagation();
                      console.log("jf click", $(this), this);
                      if (page.isClickLocked) {
                        console.log("<span style='color: red;'>点击锁定中</span>");
                        return
                      }
                      page.isClickLocked = true
   
                      if (!isSync) {
                        $(this).trigger("syncTargetBtnClick");
                        return;
                      }

                      SDK.bindSyncEvt({
                        index: $(e.currentTarget).data("syncactions"),
                        eventType: "click",
                        method: "event",
                        syncName: "syncTargetBtnClick",
                        recoveryMode: "1",
                      });
                    });

                    imgElement.on("syncTargetBtnClick", function () {
                  

                      // palying music not allow click
                      // if (!page.targetsAllowClick) {
                      //   console.log("palying music not allow click");
                      //   page.isClickLocked = false; // 释放锁，因为这里提前返回了
                      //   return SDK.setEventLock();
                      // }

                      // 游戏结束后，剩余的点击没有反应
                      if (page.currentIndex >= page.audioList.length) {
                        page.isClickLocked = false; // 释放锁，因为这里提前返回了
                        return SDK.setEventLock();
                      }
                      $(this).css({
                        transition: "left 0.5s ease-in-out,top 0.5s ease-in-out",
                      });
                      const desValue = $(this).data("desvalue");
                      console.log(page.currentIndex);
                      console.log(
                        page.audioList[page.currentIndex].value,
                        desValue,
                        "---"
                      );
                      if (page.audioList[page.currentIndex].value === desValue) {
                        page.pauseTargetMusic()
                        page.targetsAllowClick = false;
                        // todo 移动到目标位置
                        console.log("对了，执行动画");
                        $(this).removeClass("shake breathe");
                        // todo 播放音效
                        page.playClickAudio(true);
                        page.currentIndex < page.audioList.length &&
                          page.currentIndex++;
                        // todo 执行动画
                        const ele = destination.find(
                          (item) =>
                            item.answer.indexOf(desValue) > -1 ||
                            item.answer.indexOf(Number(desValue)) > -1
                        );
                        console.log("ele", ele, destination, desValue);
                        $(this).css({
                          left: ele.centerLocation.x,
                          top: ele.centerLocation.y,
                        });
                        SDK.reportTrackData({
                          action: 'CK_FT_ANSWER_RESULT',
                          data: {
                            result: 'right',
                          }
                        },USER_TYPE.STU)
                        // 在动画结束时释放点击锁，而不是用setTimeout
                        $(this).one('transitionend', function() {
                          page.isClickLocked = false;
                        });
                      } else {
                        console.log( $("#effect-audio").attr("src"),'jf')   
                        // todo pause target music
                        page.pauseTargetMusic() 
                        $(this).css({ transition: "none" });
                        // todo 播放音效
                        console.log("错了， 不播放音频");
                        page.playClickAudio(false);
                        // todo 动效
                        $(this).removeClass("shake breathe");
                        // 使用setTimeout确保动画被移除后再添加
                        setTimeout(() => {
                          $(this).addClass("shake");
                          // 监听shake动画结束事件
                          $(this).one("animationend", function () {
                            $(this).removeClass("shake").addClass("breathe");
                            // 错误动画结束后释放锁
                            page.isClickLocked = false;
                          });
                        }, 10);
                        SDK.reportTrackData({
                          action: 'CK_FT_ANSWER_RESULT',
                          data: {
                            result: 'wrong',
                          }
                        },USER_TYPE.STU)
                      }
                      SDK.setEventLock();
                    });

                    // imgElement.on('transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd',function(){
                    //   console.log('动画结束了')
                    //   page.currentIndex++
                    //   $(this).hide()
                    //   // todo 进度条
                    //   page.dealExperienceValue()
                    //   // todo 篮子换图片

                    //   setTimeout(() => {
                    //     page.playAudio(page.currentIndex)
                    //   }, 1000);
                    // })

                    imgElement.on(
                      "transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd",
                      function (e) {
                        $(this).css({ transition: "none" });
                        const { destination = [] } = configData;

                        // 确保只处理left和top属性的过渡结束事件
                        if (
                          e.originalEvent.propertyName === "left" ||
                          e.originalEvent.propertyName === "top"
                        ) {
                          console.log("动画结束了", e.originalEvent.propertyName);
                          // 只有当两个属性都过渡完成时才执行后续操作
                          if (e.originalEvent.propertyName === "left") {
                            // 记录left过渡已完成
                            $(this).data("leftTransitionComplete", true);
                          } else if (e.originalEvent.propertyName === "top") {
                            // 记录top过渡已完成
                            $(this).data("topTransitionComplete", true);
                          }

                          // 检查两个过渡是否都已完成
                          if (
                            $(this).data("leftTransitionComplete") &&
                            $(this).data("topTransitionComplete")
                          ) {
                            // 重置标记
                            $(this).data("leftTransitionComplete", false);
                            $(this).data("topTransitionComplete", false);

                            // 执行后续操作
                            // page.currentIndex++
                            $(this).hide();
                            // todo 进度条
                            page.dealExperienceValue();
                            // todo 篮子换图片
                            const desValue = $(this).data("desvalue");
                            const ele = destination.find(
                              (item) =>
                                item.answer.indexOf(desValue) > -1 ||
                                item.answer.indexOf(Number(desValue)) > -1
                            );
                            ele.dom.attr("src", ele.fullImg);

                            console.log(
                              page.currentIndex,
                              "page.currentIndex",
                              "123"
                            );
                            // if(page.currentIndex < page.audioList.length) {
                            //   let timer = setTimeout(() => {
                            //     if(page.currentIndex < page.audioList.length) {
                            //       page.playAudio(page.currentIndex)
                            //       clearTimeout(timer)
                            //       timer = null
                            //     }
                            //   }, 1000);
                            // }
                          }
                        }
                      }
                    );
                  }
                  // todo 初始化音频
                  // console.log('zhixing')
                  // if(i === targets.length - 1) {
                  //   page.initAudio()
                  // }
                });
              }
            }
          }
        }
      }
      if (destination.length > 0) {
        destination.forEach((item) => {
          const {
            img,
            imgWithTarget,
            answer,
            location = { x: 0, y: 0 },
          } = item || {};
          if (img) {
            getImageSize(img, (width, height) => {
              const imgElement = $("<img>")
                .attr("src", img)
                .css({
                  position: "absolute",
                  // zIndex: 10,
                  zIndex:4,
                  left: location.x / 100 + "rem",
                  top: location.y / 100 + "rem",
                  width: width / 100 + "rem",
                  height: height / 100 + "rem",
                });
              $(".game-container").append(imgElement);
              item.centerLocation = {
                // x: (location.x + width / 2) / 100 + "rem",
                // y: (location.y - 200) / 100 + "rem",
                x: (location.x + width / 2) / 100 + "rem",
                y: (location.y - 50) / 100 + "rem",
              };
              item.dom = imgElement;
            });
          }
        });
      }

      $(".experience-bar-fill").on(
        "transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd",
        function (e) {
          console.log("经验值动画结束", e.originalEvent.propertyName);
          // const currentValue = page.currentValue
          // if(currentValue >= totalValue) {
          //   console.log('经验值大于等于总经验值，开宝箱！')
          //   $('.chest').css({
          //     animation:'shake 0.5s'
          //   })
          // }else{
          //   console.log('经验值小于总经验值，继续游戏！')
          //   page.carouselAutoNext()
          // }
          console.log(page.currentIndex, "page.currentIndex", "456");
          const { feedbackLists = [] , halfFeedbackCount } = configData
          const halfFeedback = feedbackLists.find(item => {
            return item.key === 'halfFeedback'
          })
          const finalFeedback = feedbackLists.find(item => {
            return item.key === 'finalFeedback'
          })
          // const { halfFeedback = {} } = configData;
          // const { count = 0, feedback = "" } = halfFeedback;
          // todo 小宝箱
          if (page.currentIndex === halfFeedbackCount && halfFeedbackCount > 0) {
            console.log("中途奖励经验条触发完成");

            // todo 执行中途奖励动画
            $(".chest-half").css({
              animation: "none",
            });
            setTimeout(() => {
              $(".chest-half").css({
                animation: "shake 0.5s",
              });
            }, 10);
            // debugger
            // ? 触发中途Json动画
            halfFeedback && page.execHalfFeedback()


            
            if (page.currentIndex < page.audioList.length) {
              let timer = setTimeout(() => {
                if (page.currentIndex < page.audioList.length) {
                  page.targetsAllowClick = true;
                  page.playAudio(page.currentIndex);
                  clearTimeout(timer);
                  timer = null;
                }
              }, 4000);
            }
          } else if (page.currentIndex === page.audioList.length) {
            console.log("最终奖励经验条触发完成");
            $(".chest").css({
              animation: "none",
            });
            setTimeout(() => {
              $(".chest").css({
                animation: "shake 0.5s",
              });
            }, 10);

            // ? 触发最终动效
            finalFeedback && page.execFinalFeedback()

            SDK.reportTrackData({
              action: 'CK_FT_INTERACTION_COMPLETE',
              data: {
                result: 'success',
              }
            },USER_TYPE.TEA)
          } else {
            if (page.currentIndex < page.audioList.length) {
              let timer = setTimeout(() => {
                if (page.currentIndex < page.audioList.length) {
                  page.targetsAllowClick = true;
                  page.playAudio(page.currentIndex);
                  clearTimeout(timer);
                  timer = null;
                }
              }, 0);
            }
          }
        }
      );

      $(".startBtn").on("click touchstart", function (e) {
        console.log("jf startBtn click");
        if (e.type == "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation();

        // page.initTragetClick()

        // const userType = window.frameElement
        //   ? window.frameElement.getAttribute("user_type")
        //   : "";
        // if (userType === "stu") {
        //   return; // 学生不允许点击
        // }
        if (!isSync) {
          $(this).trigger("sycnStartBtnClick");
          return;
        }
        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "sycnStartBtnClick",
          recoveryMode: "1",
        });
      });

      $(".startBtn").on("sycnStartBtnClick", function () {
        console.log("jf sycnStartBtnClick");
        page.initCommonData();
        page.threeTwoOne();
        SDK.setEventLock();
      });
    },

    initRule: function () {
      const userType = window.frameElement
        ? window.frameElement.getAttribute("user_type")
        : "";
      const classStatus = SDK.getClassConf && SDK.getClassConf().h5Course.classStatus;
      if (!isSync || (classStatus == 0 && userType == "stu")) {
        page.initAudio();
      } else {
        if (userType === "tea") {
          // todo tea coming computed result
          if (
            page.audioList.length === 0 &&
            (!SDK.syncData.audioList || SDK.syncData.audioList.length === 0)
          ) {
            page.initAudio();
            SDK.syncData.audioList = [...page.audioList];
            SDK.bindSyncEvt({
              index: $(".game-container").data("syncactions"),
              eventType: "sendmessage",
              method: "event",
              syncName: "syncInitRule",
            });
          }
        }
      }
    },

    initAudio: function () {
      const { audioSetting = {}, targets = [] } = configData;
      const { firstAudio = "" } = audioSetting;
      if (targets.length <= 0) return;
      const temp = [];
      targets
        .filter((item) => item.audio)
        .forEach((target) => {
          if (target.arr && target.arr.length > 0) {
            target.arr.forEach(imgObj => {
              const { locations = [] } = imgObj;
              locations.forEach(() => {
                // temp.push(target.audio)
                temp.push({
                  audio: target.audio,
                  value: target.value,
                });
              });
            });
          }
        });
      console.log("temp", temp);
      page.audioList = page.shuffleArray(temp);
      console.log("page.audioList", page.audioList);
      const target = targets.find((item) => item.value == firstAudio);
      if (target) {
        const audio = target.audio;
        const firstAudioIndex = page.audioList.findIndex(
          (item) => item.audio == audio
        );
        console.log("firstAudioIndex", firstAudioIndex);
        if (firstAudioIndex > -1 && firstAudioIndex !== 0) {
          const tempItem = page.audioList[0];
          page.audioList[0] = page.audioList[firstAudioIndex];
          page.audioList[firstAudioIndex] = tempItem;
          console.log("final result", page.audioList);
        }
      }
    },

    initCommonData: function () {
      if (
        page.audioList.length > 0 &&
        (!SDK.syncData.audioList || SDK.syncData.audioList.length === 0)
      ) {
        console.log("sdk start page.audioList 有数据");
        SDK.syncData.audioList = [...page.audioList];
      } else if (
        SDK.syncData.audioList &&
        SDK.syncData.audioList.length > 0 &&
        page.audioList.length === 0
      ) {
        page.audioList = [...SDK.syncData.audioList];
        console.log("sdk start SDK 有数据");
      }
    },

    initHalfChest: function () {
      const { halfFeedbackCount = 0 ,targets = [],audioSetting = {}} = configData;
      const { firstAudio = "" } = audioSetting;

      const temp = [];
      targets
        .filter((item) => item.audio)
        .forEach((target) => {
          if (target.arr && target.arr.length > 0) {
            target.arr.forEach(imgObj => {
              const { locations = [] } = imgObj;
              locations.forEach(() => {
                // temp.push(target.audio)
                temp.push({
                  audio: target.audio,
                  value: target.value,
                });
              });
            });
          }
        });

      // todo 小宝箱
      if (halfFeedbackCount > 0) {
        console.log(
          "halfFeedbackCount",
          halfFeedbackCount,
          temp.length
        );halfFeedbackCount
        $(".chest-half").show();
        $(".chest-half").css(
          "left",
          `calc(${
            (halfFeedbackCount / temp.length) * 100
          }% - 0.25rem)`
        );
      }
    },

    // initTragetClick: function () {
    //   console.log('target-btn',$('.target-btn'))
    //   $('.target-btn').on('click touchstart', function (e) {
    //     if (e.type === "touchstart") {
    //       e.preventDefault();
    //     }
    //     e.stopPropagation();
    //     console.log('jf click', $(this),this)

    //     if(!isSync) {
    //       $(this).trigger("syncTargetBtnClick");
    //       return;
    //     }

    //     SDK.bindSyncEvt({
    //       index: $(e.currentTarget).data('syncactions'),
    //       eventType: 'click',
    //       method: 'event',
    //       syncName: 'syncTargetBtnClick',
    //       recoveryMode: '1'
    //     });
    //   })

    //   $('.target-btn').on('syncTargetBtnClick',function(){
    //     console.log('jf syncClick',$(this) ,this)
    //     console.log('syncTargetBtnClick',page.currentIndex,page.audioList.length,page.targetsAllowClick,page,SDK.syncData)
    //     // palying music not allow click
    //     if(!page.targetsAllowClick) {
    //       console.log('palying music not allow click')
    //       return SDK.setEventLock();
    //     }

    //     // 游戏结束后，剩余的点击没有反应
    //     if(page.currentIndex >= page.audioList.length) {
    //       return  SDK.setEventLock();
    //     }
    //     $(this).css({transition: 'left 0.5s ease-in-out,top 0.5s ease-in-out'})
    //     const desValue = $(this).data('desvalue')
    //     console.log(page.currentIndex)
    //     console.log(page.audioList[page.currentIndex].value,desValue,'---')
    //     if(page.audioList[page.currentIndex].value === desValue) {
    //       page.targetsAllowClick = false
    //       // todo 移动到目标位置
    //       console.log('对了，执行动画')
    //       $(this).removeClass('shake breathe')
    //       // todo 播放音效
    //       page.playClickAudio(true)
    //       page.currentIndex < page.audioList.length && page.currentIndex++
    //       // todo 执行动画
    //       const ele =  destination.find(item => (item.answer.indexOf(desValue) > -1 || item.answer.indexOf(Number(desValue)) > -1))
    //       console.log('ele',ele,destination,desValue)
    //       $(this).css({
    //         left:ele.centerLocation.x,
    //         top:ele.centerLocation.y
    //       })
    //     }else{
    //       $(this).css({transition: 'none'})
    //       // todo 播放音效
    //       console.log('错了， 不播放音频')
    //       page.playClickAudio(false)
    //       // todo 动效
    //       $(this).removeClass('shake breathe')
    //       // 使用setTimeout确保动画被移除后再添加
    //       setTimeout(() => {
    //         $(this).addClass('shake')
    //         // 监听shake动画结束事件
    //         $(this).one('animationend', function() {
    //           $(this).removeClass('shake').addClass('breathe')
    //         })
    //       }, 10);
    //     }
    //     SDK.setEventLock();
    //   })

    //   // imgElement.on('transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd',function(){
    //   //   console.log('动画结束了')
    //   //   page.currentIndex++
    //   //   $(this).hide()
    //   //   // todo 进度条
    //   //   page.dealExperienceValue()
    //   //   // todo 篮子换图片

    //   //   setTimeout(() => {
    //   //     page.playAudio(page.currentIndex)
    //   //   }, 1000);
    //   // })

    //   $('.target-btn').on('transitionend webkitTransitionEnd mozTransitionEnd oTransitionEnd',function(e){
    //     $(this).css({transition: 'none'})
    //     const {destination = []} = configData

    //     // 确保只处理left和top属性的过渡结束事件
    //     if (e.originalEvent.propertyName === 'left' || e.originalEvent.propertyName === 'top') {
    //       console.log('动画结束了', e.originalEvent.propertyName)
    //       // 只有当两个属性都过渡完成时才执行后续操作
    //       if (e.originalEvent.propertyName === 'left') {
    //         // 记录left过渡已完成
    //         $(this).data('leftTransitionComplete', true);
    //       } else if (e.originalEvent.propertyName === 'top') {
    //         // 记录top过渡已完成
    //         $(this).data('topTransitionComplete', true);
    //       }

    //       // 检查两个过渡是否都已完成
    //       if ($(this).data('leftTransitionComplete') && $(this).data('topTransitionComplete')) {
    //         // 重置标记
    //         $(this).data('leftTransitionComplete', false);
    //         $(this).data('topTransitionComplete', false);

    //         // 执行后续操作
    //         // page.currentIndex++
    //         $(this).hide()
    //         // todo 进度条
    //         page.dealExperienceValue()
    //         // todo 篮子换图片
    //         const desValue = $(this).data('desvalue')
    //         const ele = destination.find(item => (item.answer.indexOf(desValue) > -1 || item.answer.indexOf(Number(desValue)) > -1))
    //         ele.dom.attr('src',ele.fullImg)

    //         console.log(page.currentIndex,'page.currentIndex','123')
    //         // if(page.currentIndex < page.audioList.length) {
    //         //   let timer = setTimeout(() => {
    //         //     if(page.currentIndex < page.audioList.length) {
    //         //       page.playAudio(page.currentIndex)
    //         //       clearTimeout(timer)
    //         //       timer = null
    //         //     }
    //         //   }, 1000);
    //         // }
    //       }
    //     }
    //   })
    // },

    gameBegin: function () {
      console.log("gameBegin");
      page.targetsAllowClick = true;
      this.playAudio(0);
      // this.currentIndex++
    },

    // todo 处理经验值
    dealExperienceValue: function () {
      console.log("dealExperienceValue", page.currentIndex, page.total);
      const percent = (page.currentIndex / page.total) * 100;


      // todo 播放经验值音效
      // setTimeout(() => {
      //   $('#effect-audio').attr('src', page.musicConfig.experience)
      //   if ($(window.frameElement).attr("id") === "h5_course_self_frame" || !isSync) {
      //       SDK.playRudio({
      //       index: $('#effect-audio').get(0),
      //       syncName: $('#effect-audio').attr("data-syncaudio"),
      //     });
      //   }
      // }, 500);
      const width = `${percent > 100 ? 100 : percent}%`;
      $(".experience-bar-fill").css({
        width,
      });
    },

    shuffleArray: function (array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    },

    playAudio: function (index) {
      // page.targetsAllowClick = false;
      const audio = $("#effect-audio").get(0);
      audio.currentTime = 0;
      if (page.hornAnimation) {
        lottieAnimations.stop(page.hornAnimation);
        lottieAnimations.play(page.hornAnimation);
      }
      $("#effect-audio").attr("src", page.audioList[index].audio);
      SDK.playRudio({
        index: audio,
        syncName: $("#effect-audio").attr("data-syncaudio"),
      });
      // $("#effect-audio").one("ended", function () {
      //    page.targetsAllowClick = true;
      // });
    },

    pauseTargetMusic:function() {
      const audio = $("#effect-audio").get(0);
      // todo pause target music
      SDK.pauseRudio({
        index: audio,
        syncName: $("#effect-audio").attr("data-syncaudio"),
      })
      audio.currentTime = 0;
    },

    playClickAudio: function (isRight) {
      const audio = $("#click-audio").get(0);
      audio.currentTime = 0;
      $("#click-audio").attr(
        "src",
        isRight ? page.musicConfig.right : page.musicConfig.wrong
      );
      SDK.playRudio({
        index: audio,
        syncName: $("#click-audio").attr("data-syncaudio"),
      });

      if(!isRight) {
        $("#click-audio").off('ended').one("ended", function () {
          // todo go on playing target music
          console.log('click-audio ended')
          const audio = $("#effect-audio").get(0);
          audio.currentTime = 0;
          SDK.playRudio({
            index: audio,
            syncName: $("#effect-audio").attr("data-syncaudio"),
          });
        });
      }
    },
    threeTwoOne: function () {
      console.log("jf threeTwoOne");
      let q = 1;
      $('.funcMask').show()
      $(".startBox")
        .hide()
        .siblings(".timeChangeBox")
        .show()
        .find(".numberList");
      SDK.playRudio({
        index: $(".timeLowAudio_" + q).get(0),
        syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
      });
      let audioPlay = setInterval(function () {
        q++;
        if (q > 4) {
          clearInterval(audioPlay);
          SDK.setEventLock();
          $(".funcMask").hide();
          $(".timeChangeBox").hide();
          setTimeout(() => {
            page.gameBegin();
          }, 500);
        } else {
          SDK.playRudio({
            index: $(".timeLowAudio_" + q).get(0),
            syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
          });
          $(".numberList").css({
            "background-position-x": -(1.5 * (q - 1)) + "rem",
          });
        }
      }, 1000); // @WARNING
    },

    createHornAnimation: function () {
      const { horn } = this.defaultConfig;
      getImageSizeFromJSON(horn, async (width, height) => {
        this.hornAnimation = await lottieAnimations.init(
          this.hornAnimation,
          horn,
          ".horn",
          false
        );
        // $(`.horn`).css({
        //   width: width / 100 + "rem",
        //   height: height / 100 + "rem",
        // })
        // this.hornAnimation.addEventListener("complete", () => {
        //   console.log("喇叭动效结束")
        //   lottieAnimations.stop(this.hornAnimation);
        // });
      });
    },

    execFinalFeedback: async function() {
      console.log('最终动画执行完成！')
      await feedbackAnimation('finalFeedback')
    },

    execHalfFeedback: async function() {
      console.log('阶段动画执行完成！')
      await feedbackAnimation('halfFeedback')
    }
  };
  page.init();

  //断线重连页面恢复
  SDK.recover = function (data) {
    // console.log('-------SDK-RECOVER',data)
    // const { result , currentIndex , betweenStartAndGreat, gameStart, currentTime , currentValue} = data
    // const { content = [] } = configData.carousel
    // console.log('re-data',data,page)
    // page.currentIndex = currentIndex || 0
    // $('.carousel-inner').css('transform', 'translateX(-' + (page.currentIndex * 100) + '%)');
    // $('.carousel-counter').text(`${page.currentIndex + 1}/${content.length}`)
    // if(betweenStartAndGreat) {
    //   $('.start').fadeOut()
    //   const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
    //   if(!isSync || userType === 'tea') {
    //     $('.great').fadeIn()
    //   }
    //   $('.mic-json').slideDown(1000)
    //   page.micJsonAnimation && lottieAnimations.play(page.micJsonAnimation);
    //   console.log('re-netween 执行动画')
    // }else {
    //   $('.start').fadeIn()
    //   $('.great').fadeOut()
    // }
    // page.gameStart = gameStart
    // if(gameStart) {
    //   // $('.dialog').hide()
    //   $(".carousel-control.prev").hide()
    //   $(".carousel-control.next").hide()
    // }else{
    //   $(".carousel-control.prev").show()
    //   $(".carousel-control.next").show()
    // }
    // if(page.currentIndex === 0) {
    //   $(".carousel-control.prev").addClass('not-allowed')
    //   $(".carousel-control.next").removeClass('not-allowed')
    // }else if(page.currentIndex === page.times - 1) {
    //   $(".carousel-control.prev").removeClass('not-allowed')
    //   $(".carousel-control.next").addClass('not-allowed')
    // }else {
    //   $(".carousel-control.prev").removeClass('not-allowed')
    //   $(".carousel-control.next").removeClass('not-allowed')
    // }
    // if(Array.isArray(data.result) && data.result.length > 0) {
    //   page.result = result
    // }
    // if(Array.isArray(data.result) && data.result.length > 0 && data.currentTime !== undefined) {
    //   page.currentTime = currentTime
    //   $('.pannel-wheel').css('transition', 'none')
    //   const targetDegree = result[currentTime - 1]
    //   $('.pannel-wheel').css('transform', `rotate(${targetDegree}deg)`)
    //   setTimeout(() => {
    //     $('.pannel-wheel').css('transition', 'transform 5s ease-in-out')
    //   }, 10);
    // }
    // if(data.currentValue !== undefined) {
    //   page.currentValue = currentValue
    //   const percent = currentValue / page.timesConfig[page.times].total * 100
    //   const width = `${percent > 100 ? 100 : percent}%`
    //   $('.experience-bar-fill').css({
    //     width
    //   })
    // }
    // SDK.setEventLock();
  };

  let gamePauseFlag = false;
  let stuStatus, teaStatus; //检测老师或学生在教室的状态
  // SDK.memberChange = function (message) {
  //   console.log("jf memberChange", message);
  //   if (isSync) {
  //     if (message.state == "enter") {
  //       if (message.role == "tea") {
  //         console.log("jf memberChange tea");
  //         $(".funcMask").hide();
  //       }
  //     }
  //   }
  // };

  SDK.actAuthorize = function (message) {
    console.log("jf actAuthorize", message, isSync);
    if (isSync) {
      // if (userType == "tea" && SDK.getClassConf().h5Course.classStatus == 5) {
      //   $(".doneTip").removeClass("hide");
      // }

      if (message && message.operate == 5) {
        isFirst = false;
      }
      if (message && message.type == "practiceStart") {
        // $(".startBtn").trigger("click");
        // page.threeTwoOne();
        // SDK.setEventLock();


        // if (message && message.type == "practiceStart") {
        //   if (isFirst) {
        //     isFirst = false;
        //     $(".funcMask").show();
        //     page.threeTwoOne();
        //   }
        // }

        const userType = window.frameElement
          ? window.frameElement.getAttribute("user_type")
          : "";
        console.log("jf userType",userType);
        if (userType == "tea") {
          // console.log("jf userType===tea");
          // page.threeTwoOne();
          SDK.setEventLock();
          $(".startBtn").trigger("click");
          SDK.setEventLock();
        }
      }
      // $(".startBtn").trigger("click");
      // setTimeout(() => {
      //   SDK.bindSyncCtrl({
      //     type: "tplDemoOut",
      //     data: {
      //       CID: SDK.getClassConf().course.id + "", //教室id 字符串
      //       operate: "1",
      //       data: [],
      //     },
      //   });
      // }, 500);
    }
  };

  // 从JSON文件中获取图片尺寸
  function getImageSizeFromJSON(jsonUrl, callback) {
    $.getJSON(jsonUrl, function (data) {
      const width = data.width || data.w;
      const height = data.height || data.h;
      callback(width, height);
    }).fail(function () {
      console.error("JSON 文件加载失败");
    });
  }

  // 显示图片信息，播放音频
  function getImageSize(url, callback) {
    const img = new Image();
    img.src = url;
    // 确保图片加载完成后获取宽高
    img.onload = function () {
      const width = img.width;
      const height = img.height;
      callback(width, height);
    };
    // 处理加载错误
    img.onerror = function () {
      console.error("图片加载失败");
    };
  }
});
