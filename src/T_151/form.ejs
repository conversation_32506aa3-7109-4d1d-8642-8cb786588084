<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>FTG0002_听音点选分类</title>
    <link rel="stylesheet" href="form/css/style.css" />
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
  </head>
  <body>
    <div id="container">
      <div class="edit-form">
        <div class="h-title">FTG0002_听音点选分类</div>
        <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <div class="c-group">
          <div class="c-title">上传点击元素（最多6组）</div>
          <div class="c-area upload img-upload">
            <div
              class="c-well"
              v-for="(item, index) in configData.targets"
              v-bind:key="index"
            >
              <div>
                  <span style="font-size: 16px;font-weight: bold;color: #000;">第{{index+1}}组</span>
                  <span style="font-size: 14px;color: #999;">（最多8张图片）</span>
                <span
                  class="dele-tg-btn"
                  style="position: relative; z-index: 10"
                  @click="delTragets(item)"
                  v-show="configData.targets.length>1"
                ></span>

                <div class="field-wrap">
                  <label class="field-label" for="">音频</label>
                  <span class="txt-info"
                  ><em>音频大小≤50KB * 不传则为干扰项</em></span
                  >
                  <input
                          type="file"
                          v-bind:key="Date.now()"
                          class="btn-file"
                          :id="'tarets-audio-'+index"
                          accept=".mp3"
                          @change="audioUpload($event,item,'audio',50)"
                  />
              </div>

              <div class="field-wrap">
                  <label
                          :for="'tarets-audio-'+index"
                          class="btn btn-show upload"
                          v-if="!item.audio"
                  >上传</label
                  >
                  <label
                          :for="'tarets-audio-'+index"
                          class="btn upload re-upload"
                          v-if="item.audio"
                  >重新上传</label
                  >
                  <label
                          class="btn upload btn-audio-dele"
                          v-if="item.audio"
                          @click="delAudio(item)"
                  >删除</label
                  >
                  <!-- 这里还要删除热区的答案， 以及在删除整个点击源色的时候，也要删除热区答案 -->
              </div>
              <div class="audio-preview" v-show="item.audio">
                  <div class="audio-tools">
                      <p v-show="item.audio">{{ item.audio }}</p>
                  </div>
                  <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="item.audio"></audio>
              </span>
              </div>


              <div  style="box-shadow: rgb(204, 204, 204) 1px 1px 10px; 
              margin-bottom: 0px;padding:  0 10px 10px 10px;border-radius: 5px;
              margin-top: 10px;"
              v-for="(sub, subIndex) in item.arr"
              v-bind:key="subIndex"
              >
                <div class="field-wrap">
                  <label
                    class="field-label"
                    for=""
                    >{{`图片${subIndex+1}* `}}</label
                  >
                  <span class="txt-info"
                    ><em
                      >宽最大510
                      ，高最大340,大小≤50KB,格式.jpg.png *</em
                    ></span
                  >
                  <span
                  class="dele-tg-btn"
                  style="position: relative; z-index: 10;margin-top: 10px;"
                  v-if="item.arr.length > 1"
                  @click="deleteItem(subIndex,index)"
                  
                ></span>
                  <input
                    type="file"
                    class="btn-file"
                    v-bind:key="Date.now()"
                    :id="`target-${index}-${subIndex}`"
                    size="510*340"
                    :max="true"
                    accept=".jpg,.jpeg,.png"
                    @change="imageUpload($event,sub,'img',50)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    :for="`target-${index}-${subIndex}`"
                    class="btn btn-show upload"
                    v-if="!sub.img"
                    >上传</label
                  >
                  <label
                    :for="`target-${index}-${subIndex}`"
                    class="btn upload re-upload"
                    v-if="sub.img"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="sub.img">
                  <img :src="sub.img" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delTargetsPrew(sub, 'img')"
                      >删除</span
                    >
                  </div>
                </div>

                <div
                  class="field-wrap target-location"
                  v-for="(location, locationIndex) in sub.locations"
                  v-bind:key="locationIndex"
                  style="margin-top: 10px"
                >
                  <span
                    class="dele-tg-btn"
                    style="position: relative; z-index: 10"
                    @click="delTargetsLocation(sub.locations,locationIndex)"
                    v-show="sub.locations.length>1"
                  ></span>
                  <label
                    class="field-label"
                    for=""
                    >{{`展示位置${locationIndex+1}`}}</label
                  >
                  <br />
                  X:<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                    v-model="location.x"
                  />
                  Y:<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                    v-model="location.y"
                  />
                  <span class="txt-info"
                  ><em class="game_em">数字,0<=x<=1920,0<=y<=1080 *</em></span
                >
                </div>
                <button
                  v-if="sub.locations.length<6"
                  type="button" style="width: 89px;
                  height: 30px;
                  border: 1px solid #fcc800;
                  line-height: 30px;
                  color: #000;
                  letter-spacing: 4px;
                  margin: 10px auto 0;
                  border-radius: 5px;
                  background-color: #fcc800;
                  display: block;"
                  @click="addTargetsLocation(sub)"
                >
                  添加位置
                </button>
              </div>

              <button type="button" class="add-tg-btn" @click="addImg(index)" v-if="item.arr.length<8">+</button>


              </div>
            </div>
            <button
              v-if="configData.targets.length<6"
              type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog"
              @click="addTargets"
            >
              添加元素
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">设置热区（最多4个）</div>

          <div class="c-area upload img-upload">
            <div
              class="c-well"
              v-for="(item, index) in configData.destination"
              v-bind:key="item.value"
            >
              <div>
                <span
                  class="dele-tg-btn"
                  style="position: relative; z-index: 10"
                  @click="delDestination(item)"
                  v-show="configData.destination.length>1"
                ></span>
                <div class="field-wrap">
                  <label
                    class="field-label"
                    for=""
                    >{{`图片${index+1}* `}}</label
                  >
                  <span class="txt-info"
                    ><em
                      >尺寸：宽度最大510
                      ，高度最大340,文件大小≤50KB,格式.jpg.png *</em
                    ></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    :id="`destination-content-${index}`"
                    size="510*340"
                    :max="true"
                    accept=".jpg,.jpeg,.png"
                    @change="imageUpload($event,item,'img',50)"
                  />
                </div>
                <div class="field-wrap">
                  <label
                    :for="`destination-content-${index}`"
                    class="btn btn-show upload"
                    v-if="!item.img"
                    >上传</label
                  >
                  <label
                    :for="`destination-content-${index}`"
                    class="btn upload re-upload"
                    v-if="item.img"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="item.img">
                  <img :src="item.img" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delDestinationPrew(item, 'img')"
                      >删除</span
                    >
                  </div>
                </div>

                <div class="field-wrap">
                  <label class="field-label" for="">填充后的效果图片</label>
                  <span class="txt-info"
                    ><em
                      >尺寸：宽度最大510
                      ，高度最大340,文件大小≤50KB,格式.jpg.png *</em
                    ></span
                  >
                  <input
                    type="file"
                    class="btn-file"
                    v-bind:key="Date.now()"
                    :id="`destination-content-full-${index}`"
                    size="510*340"
                    :max="true"
                    accept=".jpg,.jpeg,.png"
                    @change="imageUpload($event,item,'fullImg',50)"
                  />
                </div>
                <div class="field-wrap">
                  <label
                    :for="`destination-content-full-${index}`"
                    class="btn btn-show upload"
                    v-if="!item.fullImg"
                    >上传</label
                  >
                  <label
                    :for="`destination-content-full-${index}`"
                    class="btn upload re-upload"
                    v-if="item.fullImg"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="item.fullImg">
                  <img :src="item.fullImg" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delDestinationPrew(item, 'fullImg')"
                      >删除</span
                    >
                  </div>
                </div>
                <br />
                <div class="field-wrap">
                  <label class="field-label" for="">展示位置</label><br />
                  X:<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                    v-model="item.location.x"
                  />
                  Y:<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                    v-model="item.location.y"
                  />
                  <span class="txt-info"
                    ><em class="game_em">数字,0<=x<=1920,0<=y<=1080 *</em></span
                  >
                </div>

                <div class="field-wrap answer-wrap">
                  <label class="field-label" for="">设置答案</label>
                  <label
                    :class="{'cursor-not-allowed': calculateAnswer(option,index)}"
                    v-for="(option,subIndex) in effectiveTargets"
                    :key="option.value"
                  >
                    <input
                      type="checkbox"
                      v-model="item.answer"
                      :value="option.value"
                      :disabled="calculateAnswer(option,index)"
                    />
                    {{ option.name }}
                  </label>
                </div>
              </div>
            </div>
            <button
              v-if="configData.destination.length<4"
              type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog"
              @click="addDestination"
            >
              添加热区
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">音频播放顺序</div>
          <div class="c-area upload img-upload">
            <div class="c-well">
              <div class="field-wrap">
                <label class="field-label" for="">音频播放顺序*:</label>
                <select
                  id="teachTime"
                  v-model="configData.audioSetting.order"
                  style="width: 170px"
                >
                  <option name="optive" value="1">随机顺序</option>
                </select>
              </div>
              <div class="field-wrap">
                <label class="field-label" for="">首次音频*:</label>
                <select
                  id="teachTime"
                  v-model="configData.audioSetting.firstAudio"
                  style="width: 170px"
                >
                  <option
                    v-for="item in effectiveTargets"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="c-group">
          <!-- <div v-if="effectiveTragetLocationsLength > 1"> -->
            <div >
            <div class="c-title">阶段反馈次数</div>
            <div class="c-area upload img-upload">
              <div class="c-well feedback-animation-custom">
                <div class="field-wrap">
                  <label class="field-label" for="">阶段反馈</label>
                  <span class="txt-info"
                    ><em class="game_em"
                      >设置次数，正整数，不能超过最大次数，到进度既可播放阶段反馈，可以不设置（设置为0），不设置则不显示顶部的小奖杯图标</em
                    ></span
                  >
                  <br />
                  达到次数<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    @input="halfFeedBackOnInput($event.target.value)"
                    v-model="configData.halfFeedbackCount"
                  />次
                </div>

              </div>
            </div>

          </div>
        </div>

        <% include ./src/common/template/feedbackAnimation/form %>
        <button class="send-btn" v-on:click="onSend">提交</button>
      </div>
      <div class="edit-show">
        <div class="show-fixed">
          <div class="show-img">
            <img
              src="form/img/preview.jpg?v=<%=new Date().getTime()%>"
              alt=""
            />
          </div>
          <ul class="show-txt">
            <li><em>图片格式：</em>JPG/PNG/GIF</li>
            <li><em>声音格式：</em>MP3/WAV</li>
            <li><em>视频格式：</em>MP4</li>
            <li>带有" * "号为必填项</li>
          </ul>
        </div>
      </div>
    </div>
  </body>
  <script src="form/js/form.js?v=<%=new Date().getTime()%>"></script>
</html>
