@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/multyDialog/style.scss';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.container{
	// background-image: url(../image/1.jpg);
	position: relative;
    // font-family:"ARLRDBD";

}
audio{
	width: 0;
	height: 0;
	opacity: 0;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    .audioSrc {
        position: absolute;
    }
}
.mainArea{
	width: 16.4rem;
	height: 7.45rem;
	// position: relative;
	top:2.15rem;
	left: 50%;
	margin-left: -8.2rem;
	.list{
		width: 1.64rem;
		height: 1.49rem;
		// float: left;
		box-sizing: border-box;
		// position: relative;
        position: absolute;
        z-index: 10;
		.audioList{
            position: absolute;
            width: 1.45rem;
            height: 1.34rem;
            background: url('../image/btn-audio-bg.png') no-repeat;
            background-size: 100% 100%;
            z-index: 10;
			left: 50%;
			top: 50%;
            transform: translateX(-50%) translateY(-50%);
            cursor: pointer;
            img{
                position: absolute;
                top: 0.3rem;
                left: 0.3rem;
                width: 0.83rem;
                height: 0.8rem;
            }
            audio{
                width:0;
                height: 0;
                opacity: 0;
                position:absolute;
            }
        }
	}
}

.game-container {
    width: 100%;
    height: 100%;
    position: relative;

    .experience {
        position: absolute;
        z-index: 7;
        left: 50%;
        top: 0.4rem;
        transform: translateX(-50%);
        width: 5.36rem;
        height: 0.4rem;
        .chest {
            position: absolute;
            // left: 0;
            // top: 1rem;
            width: 0.87rem;
            height: 0.8rem;
            background: url('../image/chest.png') no-repeat  center;
            background-size: cover;
            left: 91%;
            bottom: -20%;
            z-index: 10;
        }
        .chest-half {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 0.51rem;
            height: 0.47rem;
            background: url('../image/chest.png') no-repeat  center;
            background-size: cover;
            z-index: 10;
            // left: 33.33%;
            display: none;
        }
        .horn {
            position: absolute;
            z-index: 10;
            left: 0;
            top: -30%;
            transform: translate(-100%,-50%);
            // background-color:red;
            width: 0.6rem;
            height: 0.6rem;
        }
    }
    .experience-bar {
        // position: absolute;
        // z-index: 7;
        // left: 50%;
        // top: 1rem;
        // transform: translateX(-50%);
        // width: 5.36rem;
        // height: 0.4rem;
        width: 100%;
        height: 100%;
        background-color: RGBA(126, 78, 59, 1);
        border-radius: .24rem;
        overflow: hidden;
        padding: 2px;
        border: 0.06rem solid #fff;

        .experience-bar-fill {
            height: 100%;
            width: 0;
            transition: width 1s ease;
            background: url('../image/experience.png') repeat  center;
            background-size: cover;
            border-radius: .24rem;
        }

    }
}

.funcMask {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 21;
    display: none;

    .startBox {
     position: absolute;
     left: 0;
     top: 0;
     bottom: 0;
     right: 0;
     margin: auto;
     height: 7.02rem;
     width: 10.5rem;
     background: rgba(0, 0, 0, 0.7);
     border-radius: 0.5rem;

     .demoTextBox {
         width: 9.26rem;
         height: 3.9rem;
         position: absolute;
         left: 50%;
         top: 0.64rem;
         transform: translate(-50%);
         border-radius: 0.3rem;
         background: rgba(0, 0, 0, 0.2);
         text-align: center;

         .startMsg {
             width: 6.75rem;
             height: 2.88rem;
             margin-top: 0.4rem;
         }
     }

     .demoBtnBox {
         width: 5.72rem;
         height: 1.14rem;
         transform: translate(-50%);
         // background: rgba(0, 0, 0, 0.2);
         text-align: center;
         position: absolute;
         left: 50%;
         bottom: 0.75rem;

         .demo-btnStu {
           width: 2.14rem;
           height: auto;
           cursor: pointer;
           display: inline-block;
         }

         .startBtn {
         width: 2.03rem;
         height: auto;
         cursor: pointer;
         display: inline-block;
         margin-right: 0;
        //  margin-left: 1.55rem;
       }
     }
    }

    .timeChangeBox {
       position: absolute;
       left: 0;
       top: 0;
       bottom: 0;
       right: 0;
       margin: auto;
       height: 4.8rem;
       width: 7.2rem;
       background: rgba(0, 0, 0, 0.8);
       border-radius: 0.5rem;

       .timeBg {
         width: 3.79rem;
         height: 3.84rem;
         position: absolute;
         top: 1rem;
         background: url(../image/timeBg.png) no-repeat;
         background-size: 100% 100%;
         left: 50%;
         margin-left: -1.9rem;
         top: 50%;
         margin-top: -1.92rem;

         .numberList {
           width: 1.5rem;
           height: 1.5rem;
           position: absolute;
           left: 0;
           bottom: 0;
           right: 0;
           top: 0;
           margin: auto;
           background: url(../image/number1.png) no-repeat;
           background-size: 6rem 100%;
           background-position-x: 0.1rem;
         }
       }
    }
}

.timeChangeBox {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    height: 4.8rem;
    width: 7.2rem;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.5rem;

    .timeBg {
      width: 3.79rem;
      height: 3.84rem;
      position: absolute;
      top: 1rem;
      background: url(../image/timeBg.png) no-repeat;
      background-size: 100% 100%;
      left: 50%;
      margin-left: -1.9rem;
      top: 50%;
      margin-top: -1.92rem;

      .numberList {
        width: 1.5rem;
        height: 1.5rem;
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        top: 0;
        margin: auto;
        background: url(../image/number1.png) no-repeat;
        background-size: 6rem 100%;
        background-position-x: 0.1rem;
      }
    }
 }




@keyframes shake {
    0%, 100% { transform: translateX(0) rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px) rotate(-5deg); }
    20%, 40%, 60%, 80% { transform: translateX(10px) rotate(5deg); }
}


@keyframes breathe {
    // 0% {
    //     transform: scale(1);
    // }
    // 12.5% {
    //     transform: scale(1.02);
    // }
    // 25% {
    //     transform: scale(1.04);
    // }
    // 37.5% {
    //     transform: scale(1.06);
    // }
    // 50% {
    //     transform: scale(1.08);
    // }
    // 62.5% {
    //     transform: scale(1.06);
    // }
    // 75% {
    //     transform: scale(1.04);
    // }
    // 87.5% {
    //     transform: scale(1.02);
    // }
    // 100% {
    //     transform: scale(1);
    // }
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

// 添加动画类
.breathe {
    animation: breathe 3s ease-in-out infinite;
    will-change: transform;
}

.shake {
    animation: shake 0.5s;
    will-change: transform;
}
