//var domain = 'http://172.16.0.107:9011/pages/159/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
import {
  feedbackAnimationSend,
  feedbackData,
  delWheelGamePrew,
  initializeFeedback,
  feedBackChange
} from "../../../common/template/feedbackAnimation/form.js";
var domain = "";
var halfFeedbackObjData = feedbackData({
  key:'halfFeedback',
  name:'阶段反馈'
})
var feedbackObjData = feedbackData({
  key:'finalFeedback',
  name:'整体反馈'
})
// 对话框初始化数据
// const dialogsInitData = {
//   // 对话框信息列表
//   messages: [
//     {
//       text: "",
//       audio: "",
//     },
//   ],
//   messageLocationX: "", // 消息内容位置x
//   messageLocationY: "", // 消息内容位置y
//   roleLocationX: "100", // 角色位置x
//   roleLocationY: "600", // 角色位置y
//   roleImg: "", // 角色图片
//   playAfterStauts: "2", // 播放完之后状态
//   scale: 100, //缩放比例  1-500
//   autoNext: "1", // 是否自动播放下一条对话框
//   hiddenStatus: "1", // 播放完是否应藏的状态
// };

// todo 转盘初始化数据
// const pannelInitData = {
//   // 转盘图片
//   pannelBottomBg: "",
//   pannelBottonLocationX: 102,
//   pannelBottonLocationY: 151,
//   pannelWheelBg: "",
//   contentBg: "",
//   pipeTopJson: "",
//   pipeBottomJson: "",
//   coinJson: "",
//   hasRotateMusic: "1",
// };

// todo 初始化轮播图内容数据
// const carouselInitData = {
//   content: [
//     {
//       img: "",
//     },
//     {
//       img: "",
//     },
//     {
//       img: "",
//     },
//   ],
// };

// todo 初始化数据
const targetsInitData = [
  // {
  //   img:'./image/apple.png',
  //   audio:"./audio/rotate.mp3",
  //   locations:[
  //     {
  //       x:300,
  //       y:300
  //     },
  //     {
  //       x:500,
  //       y:400
  //     }
  //   ],
  //   value:1
  // },
  // {
  //   img:'./image/banana.png',
  //   audio:"./audio/pipe.mp3",
  //   locations:[
  //     {
  //       x:1200,
  //       y:400
  //     }
  //   ],
  //   value:2
  // },
  // {
  //   img:'./image/chest.png',
  //   audio:"",
  //   locations:[
  //     {
  //       x:1500,
  //       y:400
  //     }
  //   ],
  //   value:3
  // },
  // {
  //   img:'./image/chest.png',
  //   audio:"",
  //   locations:[
  //     {
  //       x:1500,
  //       y:400
  //     }
  //   ],
  //   value:3
  // },
  // {
  //   img:'./image/chest.png',
  //   audio:"",
  //   locations:[
  //     {
  //       x:1500,
  //       y:400
  //     }
  //   ],
  //   value:3
  // },

  {

    audio: "",
    arr:[
      {
        img: "",
        locations: [
          {
            x: 0,
            y: 0,
          }
        ],
      }

    ],
    value: new Date().getTime(),
  },
];

// todo 初始化热区
const destinationInitData = [
  // {
  //   img:'./image/basket.png',
  //   fullImg:'./image/banana-full.png',
  //   imgWithTarget:'',
  //   answer:[2,3],
  //   location:{
  //     x:300,
  //     y:800,
  //   }
  // },
  // {
  //   img:'./image/basket.png',
  //   fullImg:'./image/apple-full.png',
  //   imgWithTarget:'',
  //   answer:[1],
  //   location:{
  //     x:1200,
  //     y:800,
  //   }
  // }
  {
    img:'',
    fullImg:'',
    imgWithTarget:'',
    answer:[],
    location:{
      x:0,
      y:0,
    }
  },
];

var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    instructions: [{
      commandId: '-1'
    }],
    tg: [
      {
        title: "",
        content: "",
      },
    ],
    level: {
      high: [
        {
          title: "",
          content: "",
        },
      ],
      low: [
        {
          title: "",
          content: "",
        },
      ],
    },
    source: {
      // dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
    },
    // todo 转盘相关的数据
    // pannel: JSON.parse(JSON.stringify(pannelInitData)),
    // carousel: JSON.parse(JSON.stringify(carouselInitData)),

    // todo 数据
    targets: JSON.parse(JSON.stringify(targetsInitData)),

    // todo 热区
    destination: JSON.parse(JSON.stringify(destinationInitData)),

    // todo 音频设置
    audioSetting: {
      order: "1", // 音频播放顺序 1是随机
      firstAudio: "", // 首次音频
    },
    // todo 阶段反馈次数
    halfFeedbackCount:0,
    //
    feedbackLists:[
      ...halfFeedbackObjData,
      ...feedbackObjData
    ]
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [], //交互提示标签

};
Data.configData.feedbackLists.push(halfFeedbackObjData)
Data.configData.feedbackLists.push(feedbackObjData)


$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }

      // todo IP组件初始化数据
      // if (!Data.configData.source.dialogs) {
      //   Data.configData.source.dialogs = JSON.parse(
      //     JSON.stringify(dialogsInitData)
      //   );
      // }
      // if (!Data.configData.source.dialogs.scale) {
      //   Data.configData.source.dialogs.scale = 100;
      // }
      // if (!Data.configData.source.dialogs.autoNext) {
      //   Data.configData.source.dialogs.autoNext = "2";
      // }
      // if (!Data.configData.source.dialogs.hiddenStatus) {
      //   Data.configData.source.dialogs.hiddenStatus = "1";
      // }
      // if (
      //   Data.configData.source.dialogs.roleLocationX === "" ||
      //   Data.configData.source.dialogs.roleLocationX === undefined
      // ) {
      //   Data.configData.source.dialogs.roleLocationX = "100";
      // }
      // if (
      //   Data.configData.source.dialogs.roleLocationY === "" ||
      //   Data.configData.source.dialogs.roleLocationY === undefined
      // ) {
      //   Data.configData.source.dialogs.roleLocationY = "600";
      // }

      // todo 转盘游戏数据初始化
      // if (!Data.configData.pannel) {
      //   Data.configData.pannel = JSON.parse(JSON.stringify(pannelInitData));
      // }
      // if (!Data.configData.carousel) {
      //   Data.configData.carousel = JSON.parse(JSON.stringify(carouselInitData));
      // }
      // if (
      //   Data.configData.pannel.pannelBottonLocationX === "" ||
      //   Data.configData.pannel.pannelBottonLocationX === undefined
      // ) {
      //   Data.configData.pannel.pannelBottonLocationX = "102";
      // }
      // if (
      //   Data.configData.pannel.pannelBottonLocationY === "" ||
      //   Data.configData.pannel.pannelBottonLocationY === undefined
      // ) {
      //   Data.configData.pannel.pannelBottonLocationY = "151";
      // }
      // if (
      //   Data.configData.pannel.hasRotateMusic === "" ||
      //   Data.configData.pannel.hasRotateMusic === undefined
      // ) {
      //   Data.configData.pannel.hasRotateMusic = "1";
      // }

      // todo 初始化targets
      if (!Data.configData.targets) {
        Data.configData.targets = JSON.parse(JSON.stringify(targetsInitData));
      }

      // todo 初始化destination
      if (!Data.configData.destination) {
        Data.configData.destination = JSON.parse(JSON.stringify(destinationInitData));
      }

      // todo feedback 初始化
      if (!Data.configData.feedback) {
        Data.configData.feedback = "";
      }

      // todo 初始化音频设置
      if(!Data.configData.audioSetting) {
        Data.configData.audioSetting = {
          order:1,
          firstAudio:'',
        }
      }

      // todo 阶段反馈次数
      if(!Data.configData.halfFeedbackCount) {
        Data.configData.halfFeedbackCount = 0
      }
      if(!Data.configData.instructions){
        Data.configData.instructions = [{
            commandId: '-1'
        }]
      }

      //判断下数据 如果是老的格式转换下
      if(Data.configData.targets && Data.configData.targets.length) {
        Data.configData.targets = Data.configData.targets.map(item => {
          // 检查是否为旧格式数据（有img属性但没有arr属性）
          if(item.img && !item.arr) {
            return {
              audio: item.audio || "",
              arr: [
                {
                  img: item.img,
                  locations: item.locations || [{ x: 0, y: 0 }]
                }
              ],
              value: item.value || new Date().getTime()
            };
          }
          return item;
        });
      }

      // todo 反馈组件
      initializeFeedback(Data)
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
  el: "#container",
  data: Data,
  mounted: function() {
    this.getDynamicInstructions();
  },
  methods: {
    deleteItem(subIndex,index){
      this.configData.targets[index].arr.splice(subIndex,1)
    },
    getDynamicInstructions: function() {
      var that = this;
      getDynamicInstructions(function(res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function() {
        addInstruction(this.configData);
    },
    removeInstruction: function(index) {
        removeInstruction(index, this.configData);
    },
    validateInstructions: function() {
        return validateInstructions(this.configData);
    },
    // todo 反馈动画组件相关
    delWheelGamePrew(item, key){
      delWheelGamePrew(item, key)
    },
    feedBackChange(item){
      feedBackChange(item)
    },
    feedbackUpload:function(e, item, attr, fileSize) {
      console.log(e, item, attr, fileSize);
      const file = e.target.files[0];
      if (file.type === "image/png") {
        this.imageUpload(e, item, attr, fileSize);
      } else {
        this.lottieUpload(e, item, attr, fileSize);
      }
    },
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e);
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && Number(height) + 1 > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
      }
      return checkSize;
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(",");
      var max = dom.attr("max");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (max) {
          if (width >= data.width && height >= data.height) {
            return true;
          }
        } else {
          if (width == data.width && height == data.height) {
            return true;
          }
        }

        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
        input.value = "";
      }
      return checkSize;
    },
    validate: function () {
      // 验证 IP组件，如果传了json文件，就需要设置对话
      // const dialogs = this.configData.source.dialogs;
      // if (dialogs.roleImg) {
      //   for (let index = 0; index < dialogs.messages.length; index++) {
      //     const item = dialogs.messages[index];
      //     const { text, audio } = item;
      //     if (!text || !audio) {
      //       return alert("请上传对话内容");
      //     }
      //   }
      // }

      // todo 验证用户跟读内容
      // const { content } = this.configData.carousel || [];
      // if (content.length < 3 || content.length > 6) {
      //   return alert("用户跟读内容，最少3张，最多6张");
      // }
      // for (let index = 0; index < content.length; index++) {
      //   const item = content[index];
      //   const { img } = item;
      //   if (!img) {
      //     return alert("请上传跟读内容");
      //   }
      // }

      // todo 验证targets
      if (this.configData.targets.length) {
        for (let index = 0; index < this.configData.targets.length; index++) {
          const item = this.configData.targets[index];
          if (!item.arr || !item.arr.length) {
            return alert("点击元素缺少图片数据");
          }

          for (let j = 0; j < item.arr.length; j++) {
            const imgObj = item.arr[j];
            if (!imgObj.img) {
              return alert(`请上传第${index+1}组中第${j+1}个点击元素图片`);
            }
          }
        }
      }

      // todo 验证destination
      if (this.configData.destination.length) {
        for (let index = 0; index < this.configData.destination.length; index++) {
          const item = this.configData.destination[index];
          if (!item.img) {
            return alert("请上传热区图片");
          }
          if (!item.fullImg) {
            return alert("请上传热区填充后的效果图片");
          }
          if (!item.answer || !item.answer.length) {
            return alert("请设置热区答案");
          }
        }
      }

      // todo 验证音频设置
      if (!this.configData.audioSetting.firstAudio) {
        return alert("请设置首次音频");
      }

      const totalAnswer = this.configData.destination.reduce((acc,item) => acc + item.answer.length,0)
      if(totalAnswer < this.effectiveTargets) {
        return alert("所有点击元素必须设置答案")
      }

      return true;
    },
    onSend: function () {
      var data = this.configData;
      // todo 反馈组件
      let feedbackStatus = feedbackAnimationSend(data);
      if(!feedbackStatus){
        return;
      }
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == "-2") {
        //其他
        data.log.teachTime =
          data.log_editor.TeachTimeOtherM * 60 +
          data.log_editor.TeachTimeOtherS +
          "";
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长");
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther;
      }
      var val = this.validate();
      if (val === true && this.validateInstructions()) {
        var _data = JSON.stringify(data);
        $.ajax({
          url: domain + "content?_method=put",
          type: "POST",
          data: {
            content: _data,
          },
          success: function (res) {
            window.parent.postMessage("close", "*");
          },
          error: function (err) {
            console.log(err);
          },
        });
      } else {
        // alert("带有“*”号为必填项！");
      }
    },
    postData: function (file, item, attr) {
      var FILE = "file";
      var bg = arguments.length > 2 ? arguments[2] : null;
      var oldImg = item[attr];
      var data = new FormData();
      var _this = this;
      data.append("file", file);
      if (oldImg != "") {
        data.append("key", oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: "post",
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          console.log(res.data.key);
          var options = _this.configData.source.options;

          item[attr] = domain + res.data.key;
          console.log(Data.configData);
        },
        error: function (err) {
          console.log(err);
        },
      });
    },
    studyAudioUpload: function (e, item, index, attr, fileSize) {
      //校验规则
      //var _type = this.rules.audio.sources.type;

      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      // if (!_type.test(type)) {
      //     alert("您上传的文件类型错误，请检查后再上传！");
      //     return;
      // }

      this.checkTotalAudio(index, (size / 1024).toFixed(2)); //存储所有音频的文件大小

      if ((size / 1024).toFixed(2) > 500) {
        console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      } else {
        console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      }
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error(
          "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
        );
        alert(
          "您上传的声音大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      this.postData(file, item, attr);
    },
    checkTotalAudio: function (index, size) {
      var options = this.configData.source.options;
      for (var i = 0; i < options.length; i++) {
        if (i == index) {
          options[i].audioSize = size;
        }
      }
    },
    addSele: function () {
      this.configData.source.options.push({
        audio: "",
        // pos: "",
        x: 0,
        y: 0,
        audioSize: 0,
      });
    },
    delSele: function (item) {
      this.configData.source.cardList.remove(item);
    },
    addTg: function (item) {
      this.configData.tg.push({ title: "", content: "" });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    delOption: function (item) {
      this.configData.source.options.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({ title: "", content: "" });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: "", content: "" });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    delPrew: function (item) {
      item.image = "";
    },
    // 添加对话框
    addDialog: function () {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: "",
      });
    },
    // 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },
    // 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },
    // 添加动效
    // addAnimation: function() {
    //   this.configData.source.multyAnimation.push({
    //     roleLocationX: '', // 角色位置x
    //     roleLocationY: '', // 角色位置y
    //     roleImg: "", // 角色图片,
    //     scale: 100, //缩放比例  1-500
    //   });
    // },
    // 删除动效
    delAnimation: function (item) {
      this.configData.source.multyAnimation.remove(item);
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          // img = null;
        } else {
          // img = null;
        }
      };
      reader.readAsText(file);
    },
    //ip组件音频上传
    audioUpload: function (e, item, attr, fileSize = 500) {
      console.log("audioUpload", item);
      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      var that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error(
          "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
        );
        alert(
          "您上传的音频大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      var url = URL.createObjectURL(file); //获取录音时长
      var audioElement = new Audio(url);
      var duration;
      audioElement.addEventListener("loadedmetadata", function (_event) {
        duration = audioElement.duration ? audioElement.duration : "";
        var check = that.sourceAudioCheck(e.target, {
          duration: duration,
        });
        if (check) {
          item[attr] = "./form/img/loading.jpg";
          that.postData(file, item, attr);
          audioElement = null;
        } else {
          audioElement = null;
        }
      });
    },
    //音频长度校检
    sourceAudioCheck: function (input, data) {
      let dom = $(input),
        time = dom.attr("time");
      if (time == "" || time == undefined || data.duration == "") return true;
      let checkSize = false;
      if (data.duration <= time) {
        checkSize = true;
      } else {
        alert(
          `您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`
        );
      }
      return checkSize;
    },

    // todo 元素相关的方法
    // 删除已上传的图片
    delTargetsPrew: function (item, key) {
      item[key] = "";
    },

    delTragets: function (data) {
      this.configData.targets.remove(data);
      const { value } = data;

      // todo 删除热区
      this.configData.destination.forEach(function (item) {
        if (item.answer.includes(value)) {
          item.answer = item.answer.filter(function (sub) {
            return sub !== value;
          });
        }
      });

      // todo 删除首次音频
      if (this.configData.audioSetting.firstAudio == value) {
        this.configData.audioSetting.firstAudio = "";
      }

      console.log(this.configData.targets, this.configData.destination,this.configData.audioSetting.firstAudio);
    },

    // todo 计算答案选项
    calculateAnswer: function (item, index) {
      const { value } = item;
      const temp = this.configData.destination.filter(function (sub, subIndex) {
        return subIndex !== index;
      });
      const result = temp.some((item) => item.answer.includes(value));
      return result;
    },

    // 添加跟读内容图片
    addTargets: function () {
      if (this.configData.targets.length >= 6) return;
      this.configData.targets.push({
        audio: "",
        arr:[
          {
            img: "",
            locations: [{ x: 0, y: 0 }]
          }
        ],
        value: new Date().getTime(),
      });
      console.log(new Date().getTime());
    },

    delTargetsLocation: function (locations, locationIndex) {
      // 直接从传入的locations数组中删除指定索引位置的元素
      locations.splice(locationIndex, 1);
      console.log('delTargetsLocation',locations)
    },

    addTargetsLocation: function (item) {
      item.locations.push({
        x: 0,
        y: 0,
      });
    },
    addImg(index){
      console.log('addImg',index)
      this.configData.targets[index].arr.push({
        img: "",
        locations: [{ x: 0, y: 0 }],
        value: new Date().getTime(),
      });
    },

    // todo 热区相关的方法
    addDestination: function () {
      if (this.configData.destination.length >= 4) return;
      this.configData.destination.push({
        img: "",
        fullImg: "",
        imgWithTarget: "",
        answer: [],
        location: {
          x: 0,
          y: 0,
        },
      });
    },

    delDestination: function (item) {
      this.configData.destination.remove(item);
    },

    delDestinationPrew: function (item, key) {
      item[key] = "";
    },
    halfFeedBackOnInput(value){
      console.log('halfFeedBackOnInput',Number(value))
      if(value >= this.effectiveTragetLocationsLength - 1) {
        this.configData.halfFeedbackCount = this.effectiveTragetLocationsLength - 1
      }
      if(value < 0) {
        this.configData.halfFeedbackCount = 0
      }
    },
    delAudio(item){
      item.audio=''
      this.configData.destination.forEach(function (sub) {
        sub.answer = sub.answer.filter(function (x) {
          return x !== item.value;
        });
      });
    }
  },
  computed: {
    effectiveTargets() {
      const temp = this.configData.targets.map((item,index) => ({...item,name:`第${index+1}组`}));
      return temp.filter((item) => item.audio);
    },
    effectiveTragetLocationsLength() {
      if(this.effectiveTargets && this.effectiveTargets.length) {
        return this.effectiveTargets.reduce((acc,item) => acc + item.locations.length,0)
      }
      return 0
    }
  },
  watch: {
    effectiveTargets: {
      handler(newVal) {
        console.log('effectiveTargets',newVal,this.configData.audioSetting.firstAudio)
        if(newVal.length && !this.configData.audioSetting.firstAudio) {
          this.configData.audioSetting.firstAudio = newVal[0].value
        }
      },
      immediate: true,
    },
    // effectiveTragetLocationsLength: {
    //   handler(newVal) {
    //     if(this.configData.halfFeedbackCount >= newVal - 1 || newVal == 1) {
    //       this.configData.halfFeedbackCount = 0
    //     }
    //   },
    // }
  },
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
