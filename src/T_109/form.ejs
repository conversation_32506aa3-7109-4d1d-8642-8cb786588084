<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>画画猜猜</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">画画猜猜</div>
			
			<% include ./src/common/template/common_head_nondesc %>

			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<!-- 五官 -->
				<div class="c-area upload img-upload" style="background: #ccc; margin-bottom: 10px;">
					<label>五官图片 <em>( 图片必填 )</em></label>
					<div class="c-well" v-for="(item,index) in configData.source.list">
						<div class="well-title">
							<p>选项{{index+1}}</p>
							<span class="dele-tg-btn" v-on:click="delSele(item,'list')" v-show='configData.source.list.length>1?true:false'></span>
						</div>
						<div class="well-con">
							<!-- 位置2 -->
							<div class="field-wrap">
								<label class="field-label"  for="">位置2图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.eyeImg==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.eyeImg!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.eyeImg!=''?true:false">
								<img v-bind:src="item.eyeImg" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.eyeImg=''">删除</span>
								</div>
							</div>
							<!-- 位置4 -->
							<div class="field-wrap">
								<label class="field-label"  for="">位置4图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.noseImg==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.noseImg!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'eyeImg',50)">
							</div>
							<div class="img-preview" v-if="item.noseImg!=''?true:false">
								<img v-bind:src="item.noseImg" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.noseImg=''">删除</span>
								</div>
							</div>
							<!-- 位置5 -->
							<div class="field-wrap">
								<label class="field-label"  for="">位置5图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.earImg==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.earImg!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'earImg',50)">
							</div>
							<div class="img-preview" v-if="item.earImg!=''?true:false">
								<img v-bind:src="item.earImg" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.earImg=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele('list')" v-show='configData.source.list.length<3?true:false'>+</button>
				</div>
				<!-- 身体 -->
				<div class="c-area upload img-upload" style="background: #ccc; margin-bottom: 10px;">
					<label>身体图片 <em>( 图片必填 )</em></label>
					<div class="c-well" v-for="(item,index) in configData.source.bodyList">
						<div class="well-title">
							<p>选项{{index+1}}</p>
							<span class="dele-tg-btn" v-on:click="delSele(item,'bodyList')" v-show='configData.source.bodyList.length>1?true:false'></span>
						</div>
						<div class="well-con">
							<!-- 位置2 -->
							<div class="field-wrap">
								<label class="field-label"  for="">位置3图片</label><label v-bind:for="'img-uploadBody'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-uploadBody'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span class='txt-info'>尺寸：895x765（大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-uploadBody'+index" size="895*765" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
						</div>
						<div class="color">
							<span>身体颜色</span><input type="text" v-model = "item.color"/><em :style="{background:item.color}"></em>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele('bodyList')" v-show='configData.source.bodyList.length<4?true:false'>+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.png?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
