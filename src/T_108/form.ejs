<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>找你妹</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">找你妹</div>
			
			<% include ./src/common/template/common_head_nondesc %>

			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>动物图片 <em>( 图片必填 )</em></label>
					<span style="display: inline-block">要选择图片的名字<em style="color: red;">*</em></span><input type="text" v-model="configData.source.itemName" style="margin-left: 20px;"/><br>
					<span style="display: inline-block;margin-top: 10px;margin-bottom: 10px;">提示的问题<em style="color: red;">*</em></span><input type="text" v-model="configData.source.text" style="margin-left: 20px;"/>
					<div class="c-well" v-for="(item,index) in configData.source.list">
						<div class="well-title">
							<p>选项{{index+1}}</p>
							<span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.list.length>3?true:false'></span>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span class='txt-info'>（尺寸：300X300，大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="300*300" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
								<span style="display: inline-block; margin-top: 10px;">动物名字</span><input type="text" v-model="item.name" style="margin-left: 20px;"/>
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele" >+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
