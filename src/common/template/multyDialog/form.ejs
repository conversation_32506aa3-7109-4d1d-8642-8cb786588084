<!-- 对话框 -->
<div class="c-group">
    <div class="c-title">对话（未填不校验，填一则校验）</div>
    <div class="c-areAtation img-upload">
    </div>
    <div class="c-area upload img-upload">
      <div class="c-well">
        <div>
          <div class="field-wrap">
            <label class="field-label"  for="">IP对象</label>
            <span class='txt-info'><em>文件大小≤2000KB,格式.json * </em></span>
            <input type="file" v-bind:key="Date.now()" class="btn-file" id="dialog-user-img" size="" accept=".json" @change="lottieUpload($event,configData.source.dialogs,'roleImg',2000)">
          </div>

          <div class="field-wrap">
            <label for="dialog-user-img" class="btn btn-show upload" v-if="!configData.source.dialogs.roleImg">上传</label>
            <label for="dialog-user-img" class="btn upload re-upload" v-if="configData.source.dialogs.roleImg">重新上传</label>
          </div>
          <div class="img-preview" v-if="configData.source.dialogs.roleImg">
            <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
            <div class="img-tools">
              <span class="btn btn-delete" @click="delDialogPrew(configData.source.dialogs, 'roleImg')">删除</span>
            </div>
          </div>
        </div>
        <div class="field-wrap">
          <label class="field-label"  for="">IP对象位置</label><br/>
          X:<input type="number" class="c-input-txt c-input-txt-inline "
            oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="configData.source.dialogs.roleLocationX">
          Y:<input type="number" class="c-input-txt c-input-txt-inline "
            oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="configData.source.dialogs.roleLocationY">
            <span class='txt-info'><em class="game_em">数字,0<=x<=1920,0<=y<=1080 *</em></span>
        </div>
        <div class="field-wrap">
            <label class="field-label"  for="">缩放:</label>
            <input type="number" class="c-input-txt c-input-txt-inline "
            oninput="if(value>500)value=500;if(value<1)value=1" v-model="configData.source.dialogs.scale">% <span class='txt-info'><em>数字,范围1-500,100为原尺寸;等比例缩放</em></span>
        </div>
      </div>
    </div>
    <div class="c-area upload img-upload" >
      <div class="c-well" v-for="(item, index) in configData.source.dialogs.messages">
        <span>{{`对话 ${index+1}`}}</span>
        <span class="dele-tg-btn" @click="delDialog(item)" v-show="configData.source.dialogs.messages.length>1"></span>
        <!-- 对话框内容信息 -->
        <div>
          <div class="field-wrap">
            <label class="field-label"  for="">对话框内容</label>
            <span class='txt-info'><em>文件大小≤50KB * </em></span>
            <input type="file"  v-bind:key="Date.now()" class="btn-file" size="" :id="'content-message-text-'+index" accept=".gif,.jpg,.jpeg,.png" @change="imageUpload($event,item,'text',50)">
          </div>

          <div class="field-wrap">
            <label :for="'content-message-text-'+index" class="btn btn-show upload" v-if="!item.text">上传</label>
            <label :for="'content-message-text-'+index" class="btn upload re-upload" v-if="item.text">重新上传</label>
          </div>
          <div class="img-preview" v-if="item.text">
            <img :src="item.text" alt=""/>
            <div class="img-tools">
              <span class="btn btn-delete" @click="delDialogPrew(item, 'text')">删除</span>
            </div>
          </div>
        </div>
        <!-- 对话框音频 -->
        <div>
          <div class="field-wrap">
            <label class="field-label"  for="">对话框音频</label>
            <span class='txt-info'><em>音频大小≤200KB * </em></span>
            <input type="file"  v-bind:key="Date.now()" class="btn-file" :id="'content-message-audio-'+index" accept=".mp3" @change="audioUpload($event,item,'audio',200)">
          </div>

          <div class="field-wrap">
            <label :for="'content-message-audio-'+index" class="btn btn-show upload" v-if="!item.audio">上传</label>
            <label :for="'content-message-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
            <label class="btn upload btn-audio-dele" v-if="item.audio" @click="item.audio=''">删除</label>
          </div>
          <div class="audio-preview" v-show="item.audio">
            <div class="audio-tools">
              <p v-show="item.audio">{{item.audio}}</p>
            </div>
            <span class="play-btn" v-on:click="play($event)">
              <audio v-bind:src="item.audio"></audio>
            </span>
          </div>
        </div>
      </div>
      <button type="button" class="text-add-btn add-tg-btn add-tg-btn-dialog" @click="addDialog">添加对话</button>
    </div>
    <div class="c-area upload img-upload">
      <div class="c-well">
        <div class="field-wrap">
          <label class="field-label"  for="">对话位置</label><br/>
          X:<input type="number" class="c-input-txt c-input-txt-inline "
            oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="configData.source.dialogs.messageLocationX">
          Y:<input type="number" class="c-input-txt c-input-txt-inline "
            oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="configData.source.dialogs.messageLocationY">
            <span class='txt-info'><em class="game_em">数字,0<=x<=1920,0<=y<=1080 *</em></span>
        </div>
        <div class="field-wrap">
            <label class="field-label"  for="">手动触发下一组对话:</label>
            <select id="teachTime" v-model="configData.source.dialogs.autoNext" style="width: 170px;">
                <option name="optive" value="1">是,显示左右切换按钮</option>
                <option name="optive" value="2">否,不显示左右切换按钮</option>
            </select>
        </div>
        <div class="field-wrap">
          <span class="fixed-width">结束后状态</span>
          <!-- <label class="inline-label" for="playAfterStauts"><input type="radio" name="playAfterStauts" value="1"
              v-model="configData.source.dialogs.playAfterStauts"> 显示</label>
          <label class="inline-label" for="playAfterStauts"><input type="radio" name="playAfterStauts" value="2"
              v-model="configData.source.dialogs.playAfterStauts"> 隐藏</label> -->
         <select id="teachTime" v-model="configData.source.dialogs.hiddenStatus" style="width: 170px;">
                <option name="optive" value="1">不隐藏</option>
                <option name="optive" value="2">气泡隐藏</option>
                <option name="optive" value="3">IP和气泡都隐藏</option>
         </select>
        </div>
      </div>
    </div>
  </div>
