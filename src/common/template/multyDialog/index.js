// IP 多伦 对话框
"use strict";
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
require("../../js/commonFunctions.js");
$(function () {
  const data = {
    dialogs: configData.source.dialogs,
    dialogIndex: 0, // 当前对话索引
    roleAnimations: 0, // 角色动画图
    prevOrNextBtnClicked: false, // 是否点击过上一条或下一条按钮
    // 对话框元素初始化
    initDialogEl: function () {
      const dialogEl = `<audio src="" class="dialog-audio" data-syncaudio="dialog-audio"></audio>
        <div class="dialog">
          <div class="dialog-role"></div>
          <div class="dialog-text"></div>
        </div>`;
      $(".container").append(dialogEl);
    },
    // 角色信息初始化
    initDialogRole: function () {
      if(this.roleAnimations) {
        // todo 已经创建好元素后，就不用创建了
        console.log('已经创建过动画了')
        lottieAnimations.play(this.roleAnimations);
        return 
      }
      this.getImageSizeFromJSON(this.dialogs.roleImg, async (width, height) => {
        this.roleAnimations = await lottieAnimations.init(
          this.roleAnimations,
          this.dialogs.roleImg,
          ".dialog-role"
        );
        $(".dialog-role").css({
          left: this.dialogs.roleLocationX / 100 + "rem",
          top: this.dialogs.roleLocationY / 100 + "rem",
          width: width / 100 + "rem",
          height: height / 100 + "rem",
          transform: `scale(${this.dialogs.scale / 100})`
        });
        lottieAnimations.play(this.roleAnimations);
      });
    },
    // 显示文字信息，播放音频
    initDialogAudio: function () {
      const dialogMessage = this.dialogs.messages || []
      const autoNext = this.dialogs.autoNext;
      const message = this.dialogs.messages[this.dialogIndex];
      const dialogText = $(".dialog-text");
      const dialogAudio = $(".dialog-audio");
      this.getImageSize(message.text, (width, height) => {
        dialogText.html(
          `<img src="${message.text}"/>
          ${(dialogMessage.length > 1 && autoNext === '1') ? `<span id='dialog-text-prev-btn' data-syncactions='dialogPrevBtn' class='dialog-text-prev-btn ${this.dialogIndex === 0 ? 'dialog-text-prev-btn-disabled' : ''}'></span><span id='dialog-text-next-btn' data-syncactions='dialogNextBtn' class='dialog-text-next-btn  ${this.dialogIndex === dialogMessage.length - 1 ? 'dialog-text-next-btn-disabled' : ''}'></span>` : ''}
          `
        );
        dialogText.css({
          left: this.dialogs.messageLocationX / 100 + "rem",
          top: this.dialogs.messageLocationY / 100 + "rem",
          width: width / 100 + "rem",
          height: height / 100 + "rem",
        });
        dialogAudio.attr("src", message.audio);
        if (
          $(window.frameElement).attr("id") === "h5_course_self_frame" ||
          !isSync
        ) {
          // lottieAnimations.play(this.roleAnimations);
          this.initDialogRole()
          SDK.playRudio({
            index: dialogAudio.get("0"),
            syncName: dialogAudio.attr("data-syncaudio"),
          });
        }
        this.onDialogAudioEnd(dialogText, dialogAudio);

        $("#dialog-text-prev-btn").on("click touchstart", function (e) {
          console.log("prev click")
          data.prevOrNextBtnClicked = true;
          if (e.type == "touchstart") {
            e.preventDefault()
          }
          e.stopPropagation(); // 阻止事件传播
          if (!isSync) {
            $(this).trigger("syncDialogPrevBtnClick");
            return;
          }
          
          SDK.bindSyncEvt({
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            // funcType: 'audio',
            syncName: 'syncDialogPrevBtnClick',
            recoveryMode: '1'
          });
        });

        $("#dialog-text-prev-btn").on("syncDialogPrevBtnClick", function (e) {
          console.log("syncDialogPrevBtnClick执行")
          e.stopPropagation(); // 阻止事件传播
          if(data.dialogIndex > 0) {
            data.dialogIndex--
            data.initDialogAudio()
          }
          SDK.setEventLock();
        });

        $("#dialog-text-next-btn").on("click touchstart", function (e) {
          console.log("next click");
          console.log(data.dialogIndex)
          data.prevOrNextBtnClicked = true;
          if (e.type == "touchstart") {
            e.preventDefault()
          }
          e.stopPropagation(); // 阻止事件传播
          
          if (!isSync) {
            $(this).trigger("syncDialogNextBtnClick");
            return;
          }
          SDK.bindSyncEvt({
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            // funcType: 'audio',
            syncName: 'syncDialogNextBtnClick',
            recoveryMode: '1'
          });
        });

        $("#dialog-text-next-btn").on("syncDialogNextBtnClick", function (e) {
          console.log("syncDialogNextBtnClick执行")
          e.stopPropagation(); // 阻止事件传播
          if(data.dialogIndex < dialogMessage.length - 1) {
            data.dialogIndex++
            data.initDialogAudio()
          }
          SDK.setEventLock();
        });
      });
    },
    // 显示图片信息，播放音频
    getImageSize(url, callback) {
      const img = new Image();
      img.src = url;
      // 确保图片加载完成后获取宽高
      img.onload = function () {
        const width = img.width;
        const height = img.height;
        callback(width, height);
      };
      // 处理加载错误
      img.onerror = function () {
        console.error("图片加载失败");
      };
    },
    // 从JSON文件中获取图片尺寸
    getImageSizeFromJSON(jsonUrl, callback) {
      $.getJSON(jsonUrl, function (data) {
        const width = data.width || data.w;
        const height = data.height || data.h;
        callback(width, height);
      }).fail(function () {
        console.error("JSON 文件加载失败");
      });
    },
    // 音频顺序播放
    onDialogAudioEnd: function (dialogText, dialogAudio) {
      const { messages, autoNext, hiddenStatus} = this.dialogs;
      // 监听音频播放完毕
      dialogAudio.off("ended").on("ended", () => {
        // 音频一停止播放就结束动画
        lottieAnimations.stop(this.roleAnimations)
        if (this.dialogIndex < messages.length - 1) {
          if(autoNext === "2" && !this.prevOrNextBtnClicked) {
            this.dialogIndex++;
            this.initDialogAudio();
          }
        } else {
          // 播放完毕处理
          // if (hiddenStatus == "2") {
          //   lottieAnimations.stop(this.roleAnimations);
          //   dialogText.hide();
          // } else {
          //   lottieAnimations.stop(this.roleAnimations);
          // }
          switch (hiddenStatus) {
            case "1":
              // IP和气泡都不隐藏
              break;
            case "2":
              // 气泡隐藏   
              dialogText.hide() 
              break;
            case "3":
              // IP和气泡都隐藏
              $('.dialog-role') && $('.dialog-role').hide()
              dialogText.hide() 
              break;
            default:
              break;
          }
        }
      });
    },
    //对话框初始化
    init: function () {
      // 如果角色没有上传图片，则不显示对话框
      if (!this.dialogs.roleImg) return;
      this.initDialogEl();
      // this.initDialogRole();
      this.initDialogAudio();

      // const tempImgs = dialogMessage.map(item => ({image:item.text}))
      // $.when(preloadImg(tempImgs)).done(function(res) {
      // 
      // })
    },
  };
  data.init();
});



/**
 *
 * @param {*} list 需要加载的图片数组
 * @returns
 */
function preloadImg(list) {
  var imgs = [];
  var def = $.Deferred(),
    len = list.length;
  $(list).each(function (i, e) {
    var img = new Image();
    img.src = e.image;
    if (img.complete) {
      imgs[i] = img;
      len--;
      if (len == 0) {
        def.resolve(imgs);
      }
    } else {
      img.onload = (function (j) {
        return function () {
          imgs[j] = img;
          len--;
          if (len == 0) {
            def.resolve(imgs);
          }
        };
      })(i);
      img.onerror = function () {
        len--;
      };
    }
  });
  return def.promise();
}
