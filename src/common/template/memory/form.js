let memoryForm = [
  {
    id: '1',
    name: '',
  },
  {
    id: '2',
    name: '',
  },
  {
    id: '3',
    name: '',
  },
  {
    id: '4',
    name: '',
  },
  {
    id: '5',
    name: '',
  },
  {
    id: '6',
    name: '',
  },
  {
    id: '7',
    name: '',
  },
  {
    id: '8',
    name: '',
  },
  {
    id: '9',
    name: '',
  },
  {
    id: '10',
    name: '',
  }
]

/**
 * 从URL中获取表单ID
 * @returns {string} 表单ID，如果未找到则返回空字符串
 */
const getFormId = () => {
  try {
    const url = window.location.href;
    const matches = url.match(/lesson_id=(\d+)/);
    return matches ? matches[1] : '';
  } catch (error) {
    console.error('获取表单ID失败:', error);
    return '';
  }
}

/**
 * 获取记忆表单数据
 * @param {Object} Data - 配置数据对象
 */
const getMemoryForm = (Data) => {
  // 检查是否已有记忆表单数据
  if (!Data.configData.source.memoryForm || Data.configData.source.memoryForm.length === 0) {
    // 获取本地存储的表单数据
    const storageKey = `ccs_form_${getFormId()}`;
    const memoryData = localStorage.getItem(storageKey);
    // 如果本地存储有数据则使用，否则使用默认值
    Data.configData.source.memoryForm = memoryData ? JSON.parse(memoryData) : memoryForm;
  }
}
/**
 * 将表单数据保存到localStorage
 * @param {Object} data - 包含source.memoryForm的数据对象
 */
const setMemoryForm = (data) => {
  if (data.source.memoryForm && data.source.memoryForm.length > 0) {
    localStorage.setItem(`ccs_form_${getFormId()}`, JSON.stringify(data.source.memoryForm))
  }
}

export {
  getMemoryForm,
  setMemoryForm
}

