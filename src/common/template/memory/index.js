import { USER_TYPE, CLASS_STATUS } from "../../js/constants.js";

const h5SyncActions = parent.window.h5SyncActions;
const h5Course = h5SyncActions && h5SyncActions.classConf.h5Course;
const course = h5SyncActions && h5SyncActions.classConf.course;
const isSync = h5SyncActions && h5SyncActions.isSync;
const isStudent =
  window.frameElement &&
  window.frameElement.getAttribute("user_type") === USER_TYPE.STU;
const isTeacher =
  window.frameElement &&
  window.frameElement.getAttribute("user_type") === USER_TYPE.TEA;

/**
 * 获取缓存的键名
 * @returns {string} 缓存键名
 */
const getCacheKey = () => {
  let cacheKey = "";
  if (isSync) {
    //教材id
    const textbookId = h5Course.textbook_id || h5Course.innovation_id;
    cacheKey = "ccs_" + textbookId + "_" + course.id;
  } else {
    const urlComment = window.frameElement ? window.frameElement.getAttribute('src') : window.location.href;
    const idRegex = /\/l\/(\d+)(?:\/|\/index\.html|\?|$)/;
    const match = urlComment.match(idRegex);
    const urlId = match && match[1] ? match[1] : "";
    if (urlId) {
      cacheKey = "ccs_" + urlId;
    }
  }
  return cacheKey;
};

/**
 * 创建初始缓存数据并保存到localStorage
 * @param {string} key - 缓存键名
 * @param {boolean} isCleared - 是否设置为已清除状态，默认为true
 */
const initAndSaveCache = (key, isCleared = true) => {
  const initialCache = {
    selectedArr: [], // 初始化空选择数组
    cleared: isCleared, // 标记为已清除
  };
  localStorage.setItem(key, JSON.stringify(initialCache));
};
/**
 * 获取记忆卡片相关的缓存数据
 * 根据不同场景（教室模式或预览模式）获取相应的缓存
 * @returns {Object} 处理后的缓存数据
 */
const getMemory = () => {
  let storedCache = {
    selectedArr: [], // 选择数组
    cleared: false, // 标记为已清除状态
  };
  //在教室
  const cacheKey = getCacheKey();
  if (isSync) {
    //学生
    if (isStudent) {
      //课中状态
      if (h5Course && h5Course.classStatus != CLASS_STATUS.NOT) {
        // 获取并处理localStorage缓存数据
        let cacheData = localStorage.getItem(cacheKey);
        // 如果缓存数据存在
        if (cacheData) {
          cacheData = JSON.parse(cacheData);
          console.log("cacheData应爱情", cacheData);
          // 如果缓存cleared不为true
          if (!cacheData.cleared) {
            initAndSaveCache(cacheKey);
          }
        } else {
          // 如果缓存数据不存在
          initAndSaveCache(cacheKey);
        }
      } else {
        // 预习状态 cleared为false
        let cacheData = localStorage.getItem(cacheKey);
        if (!cacheData) {
          initAndSaveCache(cacheKey, false);
        }
      }
    } else {
      //老师暂时不做处理
    }
  } else {
    //不在教室 预览模式
    if (cacheKey) {
      let cacheData = localStorage.getItem(cacheKey);
      if (!cacheData) {
        initAndSaveCache(cacheKey);
      }
    }
  }

  // 获取并处理缓存数据
  try {
    if (localStorage.getItem(cacheKey)) {
      storedCache = JSON.parse(localStorage.getItem(cacheKey));
    }
  } catch (error) {
    console.error("获取存数据时出错:", error);
  }
  console.log("缓存数据----", storedCache);
  return storedCache;
};

const setMemory = (memoryData) => {
  const cacheKey = getCacheKey();
  if (cacheKey) {
    localStorage.setItem(cacheKey, JSON.stringify(memoryData));
  }
};
export { getMemory, setMemory };
