import '../../js/snow.js'
//显示动画
export function resultWin(msg) { // 成功
    let WinL = msg.WinIsLoop == true ? true : false;
    if (WinL) {
        $('.resultWinMp3').attr('loop', 'loop');
    };


    if (SDK && SDK.playRudio) {
        SDK.playRudio({
            index: $('.resultWinMp3').get(0),
            syncName: $('.resultWinMp3').attr("data-syncaudio")
        })
    }
    $('.resultMask').css({
        'z-index': msg.Mask_Z_Index ? msg.Mask_Z_Index : '10'
    })
    $('.resultMask').show().find('.resultWin').show();
    let aniTNum = 1;
    let aniTNum2 = 1;
    //添加动画
    $('.resultAPos_' + aniTNum).css({
        animation: 'resultAPosAn 4s infinite linear'
    });
    let aniT = null;
    clearInterval(aniT);
    aniT = setInterval(function() {
        let mad = Math.random() * 3 + 2; //随机数
        if (aniTNum < 19) {
            aniTNum = aniTNum + 2;
            if ($('.resultAPos_' + aniTNum).hasClass('rotate')) { //需要转圈的元素
                $('.resultAPos_' + aniTNum).css({
                    animation: `resultAPosAn ${mad}s infinite linear,rotateAni 1s linear infinite`
                });
            } else { //无需转圈的元素
                $('.resultAPos_' + aniTNum).css({
                    animation: `resultAPosAn ${mad}s infinite ease-in`
                });
            }
            aniTNum2 = aniTNum + 1
            if ($('.resultAPos_' + aniTNum2).hasClass('rotate')) { //需要转圈的元素
                $('.resultAPos_' + aniTNum2).css({
                    animation: `resultAPosAn ${mad}s infinite linear,rotateAni 1s linear infinite`
                });
            } else { //无需转圈的元素
                $('.resultAPos_' + aniTNum2).css({
                    animation: `resultAPosAn ${mad}s infinite ease-out`
                });
            }
        } else {
            clearInterval(aniT);
        }
    }, 300);
};
export function resultHide() {
    $('.resultAPos').css('animation', '');
    if (SDK && SDK.pauseRudio) {
        SDK.pauseRudio({
            index: $('.resultWinMp3').get('0'),
            syncName: $('.resultWinMp3').attr("data-syncaudio")
        })
    }
    $('.resultMask').hide();
    $('.resultLose').hide();
    $('.resultWin').hide();
}
