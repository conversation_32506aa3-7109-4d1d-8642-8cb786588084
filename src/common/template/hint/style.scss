body {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: fixed;
}
//缩放
.hint-container {
    z-index: 5;
    transform: scale(0.72, 0.72);
    border-radius: .3rem;
    border: .01rem #cfd7e3 solid;
    box-sizing: border-box;
    margin: -5.55rem 0 0 -10rem;
    overflow: hidden;
    position: relative;
}


// 手机外壳
.hint-content {
    display: none;
    overflow: hidden;

    .content-img {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 19.2rem;
        height: 10.8rem;
        transform: translate(-50%, -50%);
        // background: url("//cdn.51talk.com/apollo/images/6eff39de45208b5ee5df69a60151ac15.png");
        background: url("../image/6eff39de45208b5ee5df69a60151ac15.png");
        background-size: 100% 100%;
        background-position: center center;
        background-repeat: no-repeat;
        z-index: 1;
    }

    //小手点击动画
    .hand {
        position: absolute;
        z-index: 10;
        left: 100%;
        top: 100%;
        // background: url('https://cdn.51talk.com/apollo/images/58a0a77378d5c105e6fff66f8c1159b6.png');
        // background: url('https://cdn.51talk.com/apollo/images/4c9d69948633e85fb12b97b899080ac1.png');
        background: url('../image/4c9d69948633e85fb12b97b899080ac1.png');
        background-size: 36.54rem 10.64rem;
        cursor: pointer;
        opacity: 1;
        z-index: 99;
        width: 12.18rem;
        height: 10.64rem;
    }
    .hide{
      display: none;
    }
}

@keyframes handClick {
    0% {
        background-position: 0% 0;
    }

    100% {
        background-position: 300% 0;
    }
}

@keyframes moveOut {
    0% {

    }

    100% {
        left: 100%;
        top: 100%;
    }
}

.mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba($color: #000000, $alpha: 0.1);
    z-index: 1000;
    display: none;
}
