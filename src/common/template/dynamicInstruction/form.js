
// 添加动态指令
const addInstruction = (configData) => {
  if (configData.instructions && configData.instructions.length < 7) {
    configData.instructions.push({ commandId: '-1' });
  }
}
// 删除动态指令
const removeInstruction = (index, configData) => {
  configData.instructions.splice(index, 1);
}
// 验证动态指令
const validateInstructions = (configData) => {
  const instructions = configData.instructions;

  // 如果只有一条且是默认的"请选择"，则通过
  if (instructions && instructions.length === 1 && instructions[0].commandId === '-1') {
    return true;
  }
  // 如果有多条，检查是否所有选项都不是"请选择"
  const isValid = instructions.every(instruction => instruction.commandId !== '-1');

  if (!isValid) {
    alert('请选择动态指令');
    return false;
  }

  return true;
}

const getDynamicInstructions = (callback, instructions) => {
  let newIstructions = JSON.parse(JSON.stringify(instructions));
  $.ajax({
    type: "get",
    url: '//acisg.51talkjr.com/Ac/AcConf/getInstructList',
    async: true,
    dataType: 'json',
    xhrFields: {
      withCredentials: true
    },
    crossDomain: true,
    success: function(res) {
      if (res.res != "") {
        let commandIdAll = [];
        let ccsInstructList = [];
        res.res.instructList && res.res.instructList.forEach(item => {
          if (item.is_ccs_show == 1) {
            ccsInstructList.push(item);
            commandIdAll.push(item.commandId);
          }
        });
        newIstructions.forEach(item => {
          if (!commandIdAll.includes(item.commandId)) {
            item.commandId = -1;
          }
        });
        callback(ccsInstructList, newIstructions);
        // window.dynamicInstructions = res.res && res.res.instructList;
      }
    },
    error: function(res) {
        console.error('获取指令列表失败:', res);
    }
  });
}
  // getDynamicInstructions();
  // window.dynamicInstructions = [
  //           {
  //               "desc":"请大声跟着老师一起说吧！",
  //               "descEn":"Please repeat after the teacher and speak loudly!",
  //               "commandId":"speak",
  //               "simplifyWord":"Speak",
  //               "pic":"https://cdn.51talk.com/apollo/images/e486d3144ea27efb92d53b256f7e30ee.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/b9882934b3ad98c60b015ca7e2633613.mp3",
  //               "type":"Instruction"
  //           },
  //           {
  //               "desc":"请跟着老师一起做动作吧！",
  //               "descEn":"Please follow the teacher and make the movements together!",
  //               "commandId":"move",
  //               "simplifyWord":"Move",
  //               "pic":"https://cdn.51talk.com/apollo/images/cf89f5c30e961cbd061771def34bef61.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/c62f07f4a21730aca22fc4354a3a6b15.mp3",
  //               "type":"Instruction"
  //           },
  //           {
  //               "desc":"请点击正确答案哦！",
  //               "descEn":"Please click on the correct answer!",
  //               "commandId":"click",
  //               "simplifyWord":"Click",
  //               "pic":"https://cdn.51talk.com/apollo/images/257f04310e757c92b98c114b487d160f.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/eee01e53836370b45613b5610644179e.mp3",
  //               "type":"Instruction"
  //           },
  //           {
  //               "desc":"请把它拖拽到正确的位置！",
  //               "descEn":"Please drag it to the correct position!",
  //               "commandId":"drag",
  //               "simplifyWord":"Drag",
  //               "pic":"https://cdn.51talk.com/apollo/images/2d18256c6b8a01f3655a6db5e4cfc062.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/d63b270264c25086d079f8b2a7c3302f.mp3",
  //               "type":"Instruction"
  //           },
  //           {
  //               "desc":"请认真看着屏幕哦！",
  //               "descEn":"Please pay close attention to the screen!",
  //               "commandId":"pay_attention",
  //               "simplifyWord":"Pay Attention",
  //               "pic":"https://cdn.51talk.com/apollo/images/136718db83cceffd61c7569623683902.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/c430a2910546b6765d8e2413a102ce94.mp3",
  //               "type":"Instruction"
  //           },
  //           {
  //               "desc":"请喊你的家长过来吧！",
  //               "descEn":"Please call your parents over!",
  //               "commandId":"parents",
  //               "simplifyWord":"Parents",
  //               "pic":"https://cdn.51talk.com/apollo/images/0c7caad2a6e1c84231448560e788e8b2.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/59e3e6bfaf03617cae8ca7ff8a518fe0.mp3",
  //               "type":"Instruction"
  //           },
  //           {
  //               "desc":"别担心，慢慢来，你一定可以的！",
  //               "descEn":"Don't worry, take your time, you can do it!",
  //               "commandId":"take_your_time",
  //               "simplifyWord":"Take Your Time",
  //               "pic":"https://cdn.51talk.com/apollo/images/bbe58acdbaf36a0af18a32343800ed41.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/d3f881de1de05450fb18998e47e9653f.mp3",
  //               "type":"Encourage"
  //           },
  //           {
  //               "desc":"没关系，加油，再试一次！",
  //               "descEn":"It's okay, keep going, try again!",
  //               "commandId":"try_again",
  //               "simplifyWord":"Try Again",
  //               "pic":"https://cdn.51talk.com/apollo/images/b3e855ce96d10e8ff174be52e0f3252a.png",
  //               "audio":"https://cdn.51talk.com/apollo/public/mp3/2894c8baa01d558b0241ac54fc10786e.mp3",
  //               "type":"Encourage"
  //           }
  //       ]


export { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions };

