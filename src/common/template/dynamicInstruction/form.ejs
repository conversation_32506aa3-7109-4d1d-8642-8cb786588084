<!-- 交互提示标签 -->
<div class="c-group">
  <div class="c-title">交互提示标签</div>
  <div class="c-area">
    <span class="instruction-desc">智能混动课堂根据此标签生成给用户的提示内容，教师可决定是否给学员展示，最多7个，要注意标签的顺序与学员的操作顺序一致！</span>
    <div v-for="(instruction, index) in configData.instructions" :key="index + '_' + new Date().getTime()" class="instruction-item">
      <div class="field-wrap">
        <label class="field-label" :for="'instruction-select-' + index">选择指令{{index + 1}}</label>
        <select v-model="instruction.commandId" :id="'instruction-select-' + index">
          <option value="-1">请选择</option>
          <option v-for="item in dynamicInstructions" :key="item.commandId" :value="item.commandId">{{item.simplifyWord}}</option>
        </select>
        <button v-if="configData.instructions && configData.instructions.length > 1" @click="removeInstruction(index)" class="btn btn-audio-dele">删除</button>
      </div>
    </div>
    <button v-if="configData.instructions && configData.instructions.length < 7" @click="addInstruction" class="add-tg-btn">+</button>
  </div>
</div>
