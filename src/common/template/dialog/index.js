// 对话框
"use strict"
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
require('../../js/commonFunctions.js');
$(function(){
	const data = {
		dialogs: configData.source.dialogs,
    dialogIndex: 0, // 当前对话索引
    roleAnimations: 0, // 角色动画图
    // 对话框元素初始化
    initDialogEl: function () {
      const dialogEl =
        `<audio src="" class="dialog-audio" data-syncaudio="dialog-audio"></audio>
        <div class="dialog">
          <div class="dialog-role"></div>
          <div class="dialog-text"></div>
        </div>`;
      $(".container").append(dialogEl);
    },
    // 角色信息初始化
    initDialogRole: function () {
      this.getImageSizeFromJSON(this.dialogs.roleImg, async (width, height) => {
        this.roleAnimations = await lottieAnimations.init(this.roleAnimations, this.dialogs.roleImg, ".dialog-role");
        $(".dialog-role").css({
          left: this.dialogs.roleLocationX / 100 + 'rem',
          top: this.dialogs.roleLocationY / 100 + 'rem',
          width: width / 100 + 'rem',
          height: height / 100 + 'rem'
        });
        lottieAnimations.play(this.roleAnimations);
      });
    },
    // 显示文字信息，播放音频
    initDialogAudio: function() {
      const message = this.dialogs.messages[this.dialogIndex];
      const dialogText = $(".dialog-text");
      const dialogAudio = $(".dialog-audio");
      this.getImageSize(message.text, (width, height) => {
        dialogText.html(`<img src="${message.text}"/>`)
        dialogText.css({
          left: this.dialogs.messageLocationX / 100 + 'rem',
          top: this.dialogs.messageLocationY / 100 + 'rem',
          width: width / 100 + 'rem',
          height: height / 100 + 'rem'
        });
        dialogAudio.attr("src", message.audio);
        if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
          SDK.playRudio({
            index: dialogAudio.get("0"),
            syncName: dialogAudio.attr("data-syncaudio"),
          });
        }
        this.onDialogAudioEnd(dialogText, dialogAudio);
      })
    },
    // 显示图片信息，播放音频
    getImageSize(url, callback) {
      const img = new Image();
      img.src = url;
      // 确保图片加载完成后获取宽高
      img.onload = function() {
        const width = img.width;
        const height = img.height;
        callback(width, height);
      };
      // 处理加载错误
      img.onerror = function() {
        console.error('图片加载失败');
      };
    },
    // 从JSON文件中获取图片尺寸
    getImageSizeFromJSON(jsonUrl, callback) {
      $.getJSON(jsonUrl, function(data) {
        const width = data.width || data.w;
        const height = data.height || data.h;
        callback(width, height);
      }).fail(function() {
        console.error('JSON 文件加载失败');
      });
    },
    // 音频顺序播放
    onDialogAudioEnd: function(dialogText, dialogAudio) {
      // 监听音频播放完毕
      dialogAudio.on('ended', () => {
        if(this.dialogIndex < this.dialogs.messages.length - 1) {
          this.dialogIndex++;
          this.initDialogAudio();
        } else {
          // 播放完毕处理
          if(this.dialogs.playAfterStauts == '2') {
            lottieAnimations.stop(this.roleAnimations);
            dialogText.hide();
          } else {
            lottieAnimations.stop(this.roleAnimations);
          }
        }
      });
    },
    //对话框初始化
		init: function() {
      // 如果角色没有上传图片，则不显示对话框
      if(!this.dialogs.roleImg) return;
      this.initDialogEl();
      this.initDialogRole();
      this.initDialogAudio();
		}
	}
	data.init()
})
