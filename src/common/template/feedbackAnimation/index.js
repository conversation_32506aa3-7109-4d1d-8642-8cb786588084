import "../../js/lottie.js";
//播放反馈动画
let feedbackObj = {
  status: false,
};
let customize = "9"; //自定义正反馈id
let customizeDefault = "10"; //自定义+默认正反馈id
let roteAnimations = null;
let feedbackJsonAnimation = null;
let feedbackImg = null;
export async function feedbackAnimation(key,loop = false) {
  console.log(key)
  if (!configData.feedbackLists) {
    configData.feedbackLists = [
      {
        positiveFeedback: configData.positiveFeedback || "-1",
        feedbackList: configData.feedbackList,
        feedbackObj: configData.feedbackObj || {},
        feedback: configData.feedback || "",
        feedbackAudio: configData.feedbackAudio || "",
        key: key,
        background: "0",
        loop: false,
      },
    ];
  }
  console.log(configData.feedbackLists, "888");
  var feedbackObjData = {};
  for (let i = 0; i < configData.feedbackLists.length; i++) {
    if (configData.feedbackLists[i].key == key) {
      feedbackObjData = configData.feedbackLists[i];
    }
  }
  console.log(feedbackObjData);
  if(feedbackObjData.loop){
    loop = feedbackObjData.loop;
  }

  if (feedbackObjData.background && feedbackObjData.background == "1") {
    console.log("隐藏透明蒙层");
    $(".feedback").css("background", "");
  }else{
    console.log("显示透明蒙层");
    $(".feedback").css("background", "rgba(0, 0, 0, 0.6)");
  }
  // var feedbackObjData = configData.feedbackObj;
  console.log("全部正确结束，执行反馈动画");
  $(".feedback").css("display", "block"); //显示悬浮层
  if (feedbackObjData.feedbackObj.id == customizeDefault) {
    console.log("播放自定义+固定动画");
    //播放音频
    // var audioPromise = feedbackDefaultAudio(feedbackObjData.feedbackObj.mp3);
    //播放自定义音频
    var customFeedbackAudioPromise = feedbackCustomizeAudio(feedbackObjData);
    loop = false;
    //播放动画
    var animationPromise = await feedbackDefaultJson(feedbackObjData.feedbackObj.json,loop);
    loop = feedbackObjData.loop;
    //播放自定义动画
    var customFeedbackPromise = feedbackCustomizeJson(feedbackObjData, loop);

  } else if(feedbackObjData.feedbackObj.id == customize){
    //播放动画
    var animationPromise = feedbackDefaultJson(feedbackObjData.feedbackObj.json,loop);
    //播放自定义动画
    var customFeedbackPromise = feedbackCustomizeJson(feedbackObjData, loop);
    //播放自定义音频
    var customFeedbackAudioPromise = feedbackCustomizeAudio(feedbackObjData);
  }else {
    //播放动画
    var animationPromise = feedbackDefaultJson(feedbackObjData.feedbackObj.json,loop);
    //播放音频
    var audioPromise = feedbackDefaultAudio(feedbackObjData.feedbackObj.mp3);
  }

  // 等待所有动画音频均完成
  await Promise.all([
    animationPromise,
    audioPromise,
    customFeedbackPromise,
    customFeedbackAudioPromise,
  ]);
  console.log("1111");
  if (loop == false) {
    $(".feedback").css("display", "none");
    $(".customFeedback-back").css("display", "none");
  }
  return feedbackObj;
}
async function feedbackDefaultJson(data, loop) {
  //播放动画
  if (data) {
    console.log("播放默认动画");
    roteAnimations = await lottieAnimations.init(
      roteAnimations,
      data,
      "#feedback",
      loop
    );
    lottieAnimations.play(roteAnimations);
    // 封装动画结束为 Promise
    return new Promise((resolve, reject) => {
      try {
        roteAnimations.addEventListener("complete", () => {
          console.log("反馈动画结束");
          // lottieAnimations.stop(roteAnimations);
          // $(".customFeedback").css("display", "none");
          lottieAnimations.destroy(roteAnimations); //销毁上一轮的烟雾动画
          feedbackObj.status = true;
          resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
async function feedbackDefaultAudio(data) {
  //播放音频
  if (data) {
    console.log("播放默认音频");
    $(".feedbackAudio").attr("src", data);
    SDK.playRudio({
      index: $(".feedbackAudio")[0],
      syncName: $(".feedbackAudio").attr("data-syncaudio"),
    });
    // 封装音频结束为 Promise
    return new Promise((resolve, reject) => {
      try {
        $(".feedbackAudio").one("ended", () => {
          console.log("反馈音频结束");
          $(".feedbackAudio")[0].pause();
          $(".feedbackAudio")[0].currentTime = 0;
          $(".feedbackAudio").attr("src", "");
          feedbackObj.status = true;
          resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
async function feedbackCustomizeAudio(data) {
  const { feedbackAudio } = data;
  const effectiveFeedbackAudio = feedbackAudio;
  console.log(effectiveFeedbackAudio, "66");
  if (effectiveFeedbackAudio) {
    console.log("播放自定义音频");
    $(".feedbackAudio").attr("src", effectiveFeedbackAudio);
    SDK.playRudio({
      index: $(".feedbackAudio")[0],
      syncName: $(".feedbackAudio").attr("data-syncaudio"),
    });
    // 封装音频结束为 Promise
    return new Promise((resolve, reject) => {
      try {
        $(".feedbackAudio").one("ended", () => {
          console.log("自定义反馈音频结束");
          $(".feedbackAudio")[0].pause();
          $(".feedbackAudio")[0].currentTime = 0;
          $(".feedbackAudio").attr("src", "");
          feedbackObj.status = true;
          resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }
}
async function feedbackCustomizeJson(data, loop) {
  const { feedback } = data;
  const effectiveFeedback = feedback;
  console.log(effectiveFeedback, "66");
  if (effectiveFeedback) {
    console.log("播放自定义动画");
    $(".customFeedback-back").css("display", "block"); //显示悬浮层
    const extension = effectiveFeedback.split(".").pop().toLowerCase();
    return new Promise((resolve, reject) => {
      try {
        if (extension === "json") {
          // todo 上传的是json
          getImageSizeFromJSON(effectiveFeedback, async (width, height) => {
            feedbackJsonAnimation = await lottieAnimations.init(
              feedbackJsonAnimation,
              effectiveFeedback,
              "#customFeedback",
              loop
            );
            lottieAnimations.play(feedbackJsonAnimation);
            $(`.customFeedback`).css({
              width: width / 100 + "rem",
              height: height / 100 + "rem",
            });
            // if(loop == false){
            //   setTimeout(() => {
            //     $(".customFeedback").css("display", "none");
            //     resolve();
            //   }, 2000);
            // }

            feedbackJsonAnimation.addEventListener("complete", () => {
              console.log("反馈动画完成");
              // $(".customFeedback").css("display", "none");
              // lottieAnimations.stop(feedbackJsonAnimation);
              lottieAnimations.destroy(feedbackJsonAnimation);
              resolve();
            });
          });
        } else {
          // todo 上传的是图片
          feedbackImg = effectiveFeedback;
          getImageSize(feedbackImg, (width, height) => {
            $(".customFeedback").css({
              background: `url(${feedbackImg}) no-repeat center`,
              "background-size": "cover",
              width: width / 100 + "rem",
              height: height / 100 + "rem",
            });
            // if(loop == false){
            setTimeout(() => {
            // $(".customFeedback").css("display", "none");
              resolve();
            }, 2000);
            // }
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  }
}
// 显示图片信息，播放音频
function getImageSize(url, callback) {
  const img = new Image();
  img.src = url;
  // 确保图片加载完成后获取宽高
  img.onload = function () {
    const width = img.width;
    const height = img.height;
    callback(width, height);
  };
  // 处理加载错误
  img.onerror = function () {
    console.error("图片加载失败");
  };
}
// 从JSON文件中获取图片尺寸
function getImageSizeFromJSON(jsonUrl, callback) {
  $.getJSON(jsonUrl, function (data) {
    const width = data.width || data.w;
    const height = data.height || data.h;
    callback(width, height);
  }).fail(function () {
    console.error("JSON 文件加载失败");
  });
}
