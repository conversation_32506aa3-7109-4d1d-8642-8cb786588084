var feedbackList = [
      { id: "-1", json: "", mp3: "" },
      { id: "0", json: "./image/prefect.json", mp3: "./audio/prefect.mp3" },
      { id: "1", json: "./image/coinTC3.json", mp3: "./audio/goldCoin.mp3" },
      { id: "2", json: "./image/FKJB.json", mp3: "./audio/resultWin.mp3" },
      { id: "9", json: "./image/guang.json", mp3: "" },
      { id: "10", json: "./image/chest-change.json", mp3: "./audio/chest-change.mp3" },
    ]
export function feedbackAnimationSend(data) {
  let status = true
  for (let i = 0; i < data.feedbackLists.length; i++) {
    if(data.feedbackLists[i].positiveFeedback == "9" || data.feedbackLists[i].positiveFeedback == "10"){
    if(data.feedbackLists[i].feedback == ""){
      alert("请上传正反馈"+ (i+1) +"自定义反馈动效")
      // return;
      status = false
    }
    if(data.feedbackLists[i].feedbackAudio == ""){
      alert("请上传正反馈"+ (i+1) +"自定义反馈音效")
      // return;
      status = false
    }
  }

  }

  //反馈动效数据
  console.log(data.feedbackLists, "反馈动效数据");
  for (let i = 0; i < data.feedbackLists.length; i++) {
    for (let t = 0; t < data.feedbackLists[i].feedbackList.length; t++) {
      if (
        data.feedbackLists[i].positiveFeedback ==
        data.feedbackLists[i].feedbackList[t].id
      ) {
        data.feedbackLists[i].feedbackObj = {}
        Object.assign(
          data.feedbackLists[i].feedbackObj,
          data.feedbackLists[i].feedbackList[t]
        );
      }
    }
  }
  return status
}
export function feedbackData(keyObj) {
  let data = {
    key: keyObj.key,
    feedbackName: keyObj.name,
    feedback: "",
    feedbackAudio: "",
    positiveFeedback: "-1",
    background: "0",
    loop: false,
    feedbackList: feedbackList,
    feedbackObj: {},
  };
  console.log(data, "35");
  return data;
}

export function initializeFeedback(Data) {
  console.log(Data, "40");
  // todo feedback 初始化
  if (!Data.configData.feedbackLists) {
    if(Data.configData.feedbackList){
      Data.configData.feedbackList.push({ id: "9", json: "./image/guang.json", mp3: "" },)
    }else{
      Data.configData.feedbackList = feedbackList;
    }    Data.configData.feedbackLists = [
      {
        positiveFeedback: Data.configData.positiveFeedback || "-1",
        feedbackList: Data.configData.feedbackList,
        feedbackObj: Data.configData.feedbackObj || {},
        feedback: Data.configData.feedback || "",
        feedbackAudio: Data.configData.feedbackAudio || "",
        key:'feedKey1',
        feedbackName:'整体反馈',
        background: "0",
        loop: false,
      },
    ];
  }else{
    let target = JSON.stringify(feedbackList);
    for (let i = 0; i < Data.configData.feedbackLists.length; i++) {
      let status = JSON.stringify(Data.configData.feedbackLists[i].feedbackList) === target
      if(!status){
        Data.configData.feedbackLists[i].feedbackList = feedbackList;
      }
      if(!Data.configData.feedbackLists[i].background){
        Data.configData.feedbackLists[i].background = "0"
      }
      if(!Data.configData.feedbackLists[i].loop){
        Data.configData.feedbackLists[i].loop = false
      }
    }
  }
  console.log(Data, "55");
}
export function feedBackChange(item) {
  console.log(item.positiveFeedback, "73");
  if (item.positiveFeedback != "9" || item.positiveFeedback != "10") {
    item.feedback = "";
    item.feedbackAudio = "";
  }
}
