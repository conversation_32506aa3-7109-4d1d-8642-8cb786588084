<!-- 整体反馈 -->
<div class="c-group" v-for="(item,index) in configData.feedbackLists" :key="index">
  <div class="c-title">{{item.feedbackName}}</div>
  <div class="c-area">
    <div class="field-wrap">
      <label class="field-label" style="width: 130px">整体反馈动效</label>
      <select id="feedBack" v-model="item.positiveFeedback" @change="feedBackChange(item)" style="width: 180px">
        <option name="optive" value="-1">请选择</option>
        <option name="optive" value="0">prefect 动效</option>
        <option name="optive" value="1">撒金币动效</option>
        <option name="optive" value="2">奖杯动效</option>
        <option name="optive" value="9">自定义</option>
        <option name="optive" value="10">开宝箱+自定义奖励内容</option>
      </select>
    </div>
  </div>
  <div class="c-area" v-if="item.positiveFeedback == '9' || item.positiveFeedback == '10'">
    <div class="field-wrap">
      <label class="field-label" style="width: 130px">是否展示半透明遮罩</label>
      <label for="twomodule" class="inline-label">
        <input type="radio" :name="'background' + index" value="0" v-model="item.background">显示
      </label>
      <label for="twomodule" class="inline-label">
        <input type="radio" :name="'background' + index" value="1" v-model="item.background">隐藏
      </label>
    </div>
  </div>
  <div v-if="item.positiveFeedback == '9' || item.positiveFeedback == '10'" class="c-area upload img-upload">
    <div>
      <div class="field-wrap">
        <label class="field-label" style="width: 130px" :for="'feedback-'+index">奖励道具<em>*</em></label>

        <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'feedback-'+index" size="" accept=".json,.png"
          @change="feedbackUpload($event,item,'feedback',250)" />
        <label :for="'feedback-'+index" class="btn btn-show upload" v-if="!item.feedback">上传</label>
        <label :for="'feedback-'+index" class="btn upload re-upload" v-if="item.feedback">重新上传</label>
      </div>


      <div class="img-preview" v-if="item.feedback">
        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
        <div class="img-tools">
          <span class="btn btn-delete" @click="item.feedback=''">删除</span>
        </div>
      </div>
      <div class="field-wrap" style="padding-left: 74px;margin: 10px 0;">
        <span class="txt-info" style="font-size: 12px;"><em>文件大小≤250KB,格式.json.png</em></span>
      </div>
    </div>

    <div class="field-wrap">
      <label class="field-label" style="width: 130px">音效<em>*</em></label>
      <input type="file" accept=".mp3" :id="'content-feedbackAudio-'+index" volume="50" v-bind:key="Date.now()"
        class="btn-file" v-on:change="audioUpload($event,item,'feedbackAudio',50)">
      <label :for="'content-feedbackAudio-'+index" class="btn btn-show upload" v-if="!item.feedbackAudio">上传音频</label>
      <div class="audio-preview" v-show="item.feedbackAudio">
        <div class="audio-tools">
          <p v-show="item.feedbackAudio">{{item.feedbackAudio}}</p>
        </div>
        <span class="play-btn" v-on:click="play($event)">
          <audio v-bind:src="item.feedbackAudio"></audio>
        </span>
      </div>
      <label :for="'content-feedbackAudio-'+index" class="btn upload btn-audio-dele" v-if="item.feedbackAudio"
        @click="item.feedbackAudio=''">删除</label>
      <label :for="'content-feedbackAudio-'+index" class="btn upload re-upload" v-if="item.feedbackAudio">重新上传</label>
      <div class="audio-tips">
        <label>
          <span><em>支持mp3格式，小于等于50Kb</em></span>
        </label>
      </div>
    </div>
  </div>
  <div class="c-area">
    <div class="field-wrap">
      <label class="field-label" style="width: 130px">播放完成后奖励隐藏</label>
      <label for="twomodule" class="inline-label">
        <input type="radio" :name="'loop' + index" :value="true" v-model="item.loop">不隐藏
      </label>
      <label for="twomodule" class="inline-label">
        <input type="radio" :name="'loop' + index" :value="false" v-model="item.loop">隐藏
      </label>
    </div>
  </div>
</div>
