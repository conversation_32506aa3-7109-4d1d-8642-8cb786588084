<div class="resultMask resultHide">
  <!-- <audio src="//cdn.51talk.com/apollo/public/mp3/93aa98305366baee034214e0bb36300f.mp3" class="resultWinMp3" data-syncaudio="resultWinMp3"></audio> -->
  <audio src="./audio/93aa98305366baee034214e0bb36300f.mp3" class="resultWinMp3" data-syncaudio="resultWinMp3"></audio>
  <!-- 胜利 -->
  <div class="resultWin resultHide">
    <div class="resultAPos_1 resultAPos"></div>
    <div class="resultAPos_2 resultAPos"></div>
    <div class="resultAPos_3 resultAPos"></div>
    <div class="resultAPos_4 resultAPos"></div>
    <div class="resultAPos_5 resultAPos rotate"></div>
    <div class="resultAPos_6 resultAPos rotate"></div>
    <div class="resultAPos_7 resultAPos"></div>
    <div class="resultAPos_8 resultAPos rotate"></div>
    <div class="resultAPos_9 resultAPos rotate"></div>
    <div class="resultAPos_10 resultAPos rotate"></div>
    <div class="resultAPos_11 resultAPos rotate"></div>
    <div class="resultAPos_12 resultAPos"></div>
    <div class="resultAPos_13 resultAPos rotate"></div>
    <div class="resultAPos_14 resultAPos rotate"></div>
    <div class="resultAPos_15 resultAPos rotate"></div>
    <div class="resultAPos_16 resultAPos rotate"></div>
    <div class="resultAPos_17 resultAPos"></div>
    <div class="resultAPos_18 resultAPos"></div>
  </div>

</div>
<!-- 图片预加载 -->
<!-- 彩带 绿 红 黄 紫 -->
<!-- <div id="preload-win-01" style="background: url('https://cdn.51talk.com/apollo/images/0133c7d335214aff167fa59bccd7d252.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-02" style="background: url('https://cdn.51talk.com/apollo/images/789580576986280f61536a23fbe1ed76.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-03" style="background: url('https://cdn.51talk.com/apollo/images/82c3e8ef7d45f1297865f28458fbee6a.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-04" style="background: url('https://cdn.51talk.com/apollo/images/185dead200974e3ff8816a6b08cbc796.png') no-repeat -9999px -9999px;"></div> -->
<div id="preload-win-01" style="background: url('./image/0133c7d335214aff167fa59bccd7d252.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-02" style="background: url('./image/789580576986280f61536a23fbe1ed76.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-03" style="background: url('./image/82c3e8ef7d45f1297865f28458fbee6a.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-04" style="background: url('./image/185dead200974e3ff8816a6b08cbc796.png') no-repeat -9999px -9999px;"></div>
<!-- 五角星 粉 黄 -->
<!-- <div id="preload-win-05" style="background: url('https://cdn.51talk.com/apollo/images/17f38c6ef4c53bde50a1b8162be33cd6.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-06" style="background: url('https://cdn.51talk.com/apollo/images/bae72969dfb55417dceae43e3027481c.png') no-repeat -9999px -9999px;"></div> -->
<div id="preload-win-05" style="background: url('./image/17f38c6ef4c53bde50a1b8162be33cd6.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-06" style="background: url('./image/bae72969dfb55417dceae43e3027481c.png') no-repeat -9999px -9999px;"></div>
<!-- 圆 -->
<!-- <div id="preload-win-07" style="background: url('https://cdn.51talk.com/apollo/images/bcb3bce0e853146ec027e7d7af028268.png') no-repeat -9999px -9999px;"></div> -->
<div id="preload-win-07" style="background: url('./image/bcb3bce0e853146ec027e7d7af028268.png') no-repeat -9999px -9999px;"></div>
<!-- 三角形 红 绿 -->
<!-- <div id="preload-win-08" style="background: url('https://cdn.51talk.com/apollo/images/4aca4238d3789daa140bde85fb2a0d26.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-09" style="background: url('https://cdn.51talk.com/apollo/images/07ba1a844dfd26882f1f28d28fa1a277.png') no-repeat -9999px -9999px;"></div> -->
<div id="preload-win-08" style="background: url('./image/4aca4238d3789daa140bde85fb2a0d26.png') no-repeat -9999px -9999px;"></div>
<div id="preload-win-09" style="background: url('./image/07ba1a844dfd26882f1f28d28fa1a277.png') no-repeat -9999px -9999px;"></div>
