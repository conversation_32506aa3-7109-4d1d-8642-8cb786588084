// 多JSON动画
"use strict";
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
require("../../js/commonFunctions.js");

$(function () {
    class MultyAnimation {
        constructor() {
            this.animations = configData.source.multyAnimation || [];
            this.roleAnimations = 0
        }

        init() {
            const ulDom = document.createElement('ul')
            $(ulDom).addClass('multy-animation')
            $('.container').append(ulDom)
            if(this.animations.length) {
                for(let i = 0; i < this.animations.length; i++) {
                    const animation = this.animations[i];
                    const { roleImg, roleLocationX, roleLocationY, scale } = animation
                    if(roleImg) {
                        const animationLiDom = document.createElement('li')
                        $(animationLiDom).addClass(`animation-li-${i}`)
                        $('.multy-animation').append(animationLiDom)
                        this.getImageSizeFromJSON(roleImg, async (width, height) => {
                            this.roleAnimations = await lottieAnimations.init(this.roleAnimations,roleImg,`.animation-li-${i}`)
                            $(`.animation-li-${i}`).css({
                                position: 'absolute',
                                zIndex:9,
                                left: (roleLocationX || 0)/ 100 + "rem",
                                top: (roleLocationY || 0) / 100  + "rem",
                                width: width / 100 + "rem",
                                height: height / 100 + "rem",
                                transform: `scale(${scale / 100})`,
                                transformOrigin: '0 0'
                            })
                            lottieAnimations.play(this.roleAnimations);
                        })
                    }
                }
            }
        }
        // 从JSON文件中获取图片尺寸
        getImageSizeFromJSON(jsonUrl, callback) {
            $.getJSON(jsonUrl, function (data) {
                const width = data.width || data.w;
                const height = data.height || data.h;
                callback(width, height);
            }).fail(function () {
                console.error("JSON 文件加载失败");
            });
        }
    }

    var multyAnimation = new MultyAnimation();
    multyAnimation.init()
})
