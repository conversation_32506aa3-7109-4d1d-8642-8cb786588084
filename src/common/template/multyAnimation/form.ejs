<!-- 多Json 动画 -->
<div class="c-group">
  <div class="c-title">添加动效</div>
  <div class="c-areAtation img-upload"></div>
  <div class="c-area upload img-upload">
    <div
      class="c-well"
      v-for="(item, index) in configData.source.multyAnimation"
      v-bind:key="index"
    >
      <div>
        <span>{{`动效 ${index+1}`}}</span>
        <span
          class="dele-tg-btn"
          @click="delAnimation(item)"
          v-show="configData.source.multyAnimation.length>1"
        ></span>
        <div class="field-wrap">
          <label class="field-label" for="">动图</label>
          <span class="txt-info"><em>文件大小≤2000KB,格式.json</em></span>
          <input
            type="file"
            class="btn-file"
            :id="`multy-animation-${index}`"
            size=""
            accept=".json"
            @change="lottieUpload($event,item,'roleImg',2000)"
          />
        </div>

        <div class="field-wrap">
          <label
            :for="`multy-animation-${index}`"
            class="btn btn-show upload"
            v-if="!item.roleImg"
            >上传</label
          >
          <label
            :for="`multy-animation-${index}`"
            class="btn upload re-upload"
            v-if="item.roleImg"
            >重新上传</label
          >
        </div>
        <div class="img-preview" v-if="item.roleImg">
          <!-- <img
            src="//cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
            alt=""
          /> -->
          <img
            src="form/img/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
            alt=""
          />
          <div class="img-tools">
            <span class="btn btn-delete" @click="delDialogPrew(item, 'roleImg')"
              >删除</span
            >
          </div>
        </div>
      </div>
      <div class="field-wrap">
        <label class="field-label" for="">位置</label><br />
        X:<input
          type="number"
          class="c-input-txt c-input-txt-inline"
          oninput="if(value>1920)value=1920;if(value<0)value=0"
          v-model="item.roleLocationX"
        />
        Y:<input
          type="number"
          class="c-input-txt c-input-txt-inline"
          oninput="if(value>1080)value=1080;if(value<0)value=0"
          v-model="item.roleLocationY"
        />
        <span class="txt-info"
          ><em class="game_em">数字,0<=x<=1920,0<=y<=1080 </em></span
        >
      </div>
      <div class="field-wrap">
        <label class="field-label" for="">缩放:</label>
        <input
          type="number"
          class="c-input-txt c-input-txt-inline"
          oninput="if(value>500)value=500;if(value<1)value=1"
          v-model="item.scale"
        />%
        <span class="txt-info"
          ><em>数字,范围1-500,100为原尺寸;等比例缩放</em></span
        >
      </div>
    </div>
    <button
      type="button"
      class="text-add-btn add-tg-btn add-tg-btn-dialog"
      @click="addAnimation"
    >
      添加动效
    </button>
  </div>
</div>
