<!-- 简版辅助提示 -->
<div class="c-group">
    <div class="c-title">添加简版辅助提示（非必填，仅教师可见）</div>

    <div class="c-area">
        <div class="c-well">
            <div class="well-con img-upload">
                <!-- 上传图片 -->
                <div class="field-wrap">
                    <span class="fixed-width">上传图片</span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" id="tImg" size="550*400"
                           v-on:change="tImageUpload($event,'tImg',50)">
                    <label for="tImg" class="btn btn-show upload"
                           v-if="!configData.tImg">上传</label>
                    <label for="tImg" class="btn upload re-upload"
                           v-if="!!configData.tImg">重新上传</label>
                    <br>
                    <em class="game_em">宽度550，高度自适应，最高400，大小：≤50KB </em>
                    <div class="img-preview" v-if="!!configData.tImg">
                        <img v-bind:src="configData.tImg" alt=""/>
                        <div class="img-tools">
                            <span class="btn btn-delete" v-on:click="configData.tImg=''">删除</span>
                        </div>
                    </div>
                </div>
                <!-- 设置位置 -->
                <label>
                    <span class="game_span">初始位置</span>
                    X:<input type="number" class="c-input-txt "
                             style="margin: 0 10px;width: 60px!important;display: inline-block;"
                             oninput="if(value>1920)value=1920;if(value<0)value=0"
                             v-model="configData.tImgX">
                    Y:<input type="number" class="c-input-txt "
                             style="margin: 0 10px;width: 60px!important;display: inline-block;"
                             oninput="if(value>1080)value=1080;if(value<0)value=0"
                             v-model="configData.tImgY">
                    <br>
                    <em class="game_em" style="color:red">数字,0<=x<=1920,0<=y<=1080</em>
                </label>

            </div>
        </div>
    </div>
</div>
