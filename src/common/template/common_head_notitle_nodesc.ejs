<!-- 背景/描述 -->
<div class="c-group">
  <div class="c-title">背景/标题</div>
  <!-- 上传图片 -->
  <div class="c-area upload img-upload radio-group">
    <div class="field-wrap">
      <label class="field-label" for="">上传背景</label>
      <span class='txt-info'><em>尺寸：1920X1080。文件大小≤100KB</em></span>
      <input type="file" v-bind:key="Date.now()" class="btn-file" id="bg" size="1920*1080"
        v-on:change="imageUpload($event,configData,'bg',100)">
    </div>
    <div class="field-wrap">
      <label for="bg" class="btn btn-show upload" v-if="configData.bg==''?true:false">上传</label>
      <label for="bg" class="btn upload re-upload" v-if="configData.bg!=''?true:false">重新上传</label>
    </div>
    <div class="img-preview" v-if="configData.bg!=''?true:false">
      <img v-bind:src="configData.bg" alt="" />
      <div class="img-tools">
        <span class="btn btn-delete" v-on:click="configData.bg=''">删除</span>
      </div>
    </div>
  </div>
  <!-- 描述 -->
  <div class="c-area">
    <label>一级标题 字符：≤40<em> 不支持编辑</em></label>
    <input type="text" class='c-input-txt' placeholder="不支持编辑" v-model="configData.desc" maxlength='40' readonly>

    <label>二级标题 字符：≤100 <em> 不支持编辑</em></label>
    <input type="text" class='c-input-txt' placeholder="不支持编辑" v-model="configData.title" maxlength='100' readonly>
  </div>
</div>
<!-- 基础埋点 -->
<div class="c-group">
  <div class="c-title">基础埋点</div>
  <div class="c-area ">
    <div class="field-wrap" style="display: none;">
      <label class="field-label" style="width: 100px;">教学环节</label>
      <select id="teachPart" v-model="configData.log.teachPart" style="width: 150px;">
        <option name="optive" value="-1">请选择</option>
        <option v-for="item in teachInfo" name="optive" :value="item.id">{{item.process_name}}</option>
      </select>
    </div>
    <div class="field-wrap">
      <label class="field-label" style="width: 100px;">建议教学时长</label>
      <select id="teachTime" v-model="configData.log_editor.isTeachTimeOther" style="width: 150px;">
        <option name="optive" value="-1">请选择</option>
        <option name="optive" value="30">0.5分钟</option>
        <option name="optive" value="60">1分钟</option>
        <option name="optive" value="90">1.5分钟</option>
        <option name="optive" value="120">2分钟</option>
        <option name="optive" value="150">2.5分钟</option>
        <option name="optive" value="180">3分钟</option>
        <option name="optive" value="-2">其他</option>
      </select>
      <br>
      <div class="field-wrap" v-show="configData.log_editor.isTeachTimeOther=='-2'">
        <label class="field-label" style="width: 100px;"></label>
        <input type="number" v-model="configData.log_editor.TeachTimeOtherM" style="width: 50px;"> 分
        <input type="number" v-model="configData.log_editor.TeachTimeOtherS" style="width: 50px;"> 秒(整数)
      </div>
    </div>
    <div class="field-wrap" style="display: none;">
      <label class="field-label" style="width: 100px;">题目属性</label>
      <label class="field-label" style="width: 150px;" v-if="configData.log.tplQuestionType=='2'">客观判断正误</label>
      <select style="width: 150px;" v-model="configData.log.tplQuestionType" v-else>
        <option name="optive" value="-1">请选择</option>
        <option name="optive" value="0">无题目</option>
        <option name="optive" value="1">主观判断正误</option>
      </select>
    </div>
  </div>
</div>
<!-- TG -->
<div class="c-group">
  <div class="c-title">添加TG</div>
  <div class="c-area">
    <div class="c-well" v-for="(item,index) in configData.tg">
      <div class="well-title">
        <p>TG {{index+1}}</p>
        <span class="dele-tg-btn" v-on:click="deleTg(item)" v-show="configData.tg.length>1"></span>
      </div>
      <div class="well-con">
        <label>标题</label>
        <input type="text" class='c-input-txt' placeholder="请在此输入TG标题" v-model="item.title">
        <label>内容 <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em></label>
        <textarea name="" cols="56" rows="2" placeholder="请在此输入TG内容" v-model="item.content"></textarea>
      </div>
    </div>
    <button type="button" class="add-tg-btn" v-on:click="addTg">+</button>
  </div>
</div>
<!-- level -->
<div class="c-group">
  <div class="c-title">添加分层 H 教学</div>
  <div class="c-area">
    <div class="c-well" v-for="(item,index) in configData.level.high">
      <div class="well-title">
        <p>H {{index+1}}</p>
        <span class="dele-tg-btn" v-on:click="deleH(item)" v-show="configData.level.high.length>1"></span>
      </div>
      <div class="well-con">
        <label>标题</label>
        <input type="text" class='c-input-txt' placeholder="请在此输入 H 标题" v-model="item.title">
        <label>内容
          <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
        </label>
        <textarea name="" cols="56" rows="2" placeholder="请在此输入 H 内容" v-model="item.content"></textarea>
      </div>
    </div>
    <button type="button" class="add-tg-btn" v-on:click="addH">+</button>
  </div>
</div>

<div class="c-group">
  <div class="c-title">添加分层 L 教学</div>
  <div class="c-area">
    <div class="c-well" v-for="(item,index) in configData.level.low">
      <div class="well-title">
        <p>L {{index+1}}</p>
        <span class="dele-tg-btn" v-on:click="deleL(item)" v-show="configData.level.low.length>1"></span>
      </div>
      <div class="well-con">
        <label>标题</label>
        <input type="text" class='c-input-txt' placeholder="请在此输入 L 标题" v-model="item.title">
        <label>内容
          <em>&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
        </label>
        <textarea name="" cols="56" rows="2" placeholder="请在此输入 L 内容" v-model="item.content"></textarea>
      </div>
    </div>
    <button type="button" class="add-tg-btn" v-on:click="addL">+</button>
  </div>
</div>

<script>
  //从CMS系统获取教学环节
  function getTeachInfo() {
    $.ajax({
      type: "get",
      // url: "http://***********:7300/mock/5bf11fb71d2cb328eddca64f/SmartCc/grabLeads",
      url: "https://studybag.51talkjr.com/api/cms/get_teach_process",
      async: false,
      success: function (res) {
        var res = JSON.parse(res)
        if (res.error_code == 0) {
          window.teachInfo = res.ret.data;
        }
      },
      error: function (res) {
        console.log(res)
      }
    });
  }
  getTeachInfo();

</script>
