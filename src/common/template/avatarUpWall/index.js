const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import { USER_TYPE, CLASS_STATUS } from "../../js/constants.js";
require("../../js/commonFunctions.js");
$(function () {
  const data = {
    init: function () {

      avatarUpWallInitialization();
      // 头像上墙
      $(".up-wall").on("click touchstart", function (e) {
        if (e.type == "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation(); // 阻止事件传播
        if (!isSync) {
          // $(this).trigger("syncUpWallClick");
          return;
        }

        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          // funcType: 'audio',
          syncName: "syncUpWallClick",
          recoveryMode: "1",
        });
      });
      $(".up-wall").on("syncUpWallClick", function (e) {
        console.log("syncUpWallClick");
        if ($(e.currentTarget).data("identity") == "tea") {
          if (
            !isSync ||
            $(window.frameElement).attr("id") !== "h5_course_self_frame"
          ) {
          } else {
            SDK.bindSyncNormalData({
              type: "js2cpp_video",
              data: {
                video_status: "up",
                video_msg: [
                  {
                    role: "tea",
                    left: Number(configData.avatarUpWallData.teacherPositionX),
                    top: Number(configData.avatarUpWallData.teacherPositionY),
                    width: 558,
                    height: 418,
                  },
                ],
              },
            });
          }
        }
        if ($(e.currentTarget).data("identity") == "stu") {
          if (
            !isSync ||
            $(window.frameElement).attr("id") !== "h5_course_self_frame"
          ) {
          } else {
            SDK.bindSyncNormalData({
              type: "js2cpp_video",
              data: {
                video_status: "up",
                video_msg: [
                  {
                    role: "stu",
                    left: Number(configData.avatarUpWallData.studentPositionX),
                    top: Number(configData.avatarUpWallData.studentPositionY),
                    width: 558,
                    height: 418,
                  },
                ],
              },
            });
          }
        }
        SDK.setEventLock();
      });
      // // 头像下墙
      // $(".close-btn").on("click touchstart", function (e) {
      //   if (e.type == "touchstart") {
      //     e.preventDefault();
      //   }
      //   e.stopPropagation(); // 阻止事件传播
      //   if (!isSync) {
      //     // $(this).trigger("syncDownWallClick");
      //     return;
      //   }

      //   SDK.bindSyncEvt({
      //     index: $(e.currentTarget).data("syncactions"),
      //     eventType: "click",
      //     method: "event",
      //     // funcType: 'audio',
      //     syncName: "syncDownWallClick",
      //     recoveryMode: "1",
      //   });
      // });
      // $(".close-btn").on("syncDownWallClick", function (e) {
      //   console.log("syncDownWallClick");
      //   if ($(e.currentTarget).data("identity") == "tea") {
      //     SDK.bindSyncNormalData({
      //       type: "js2cpp_video",
      //       data: {
      //         video_status: "down",
      //         video_msg: [
      //           {
      //             role: "tea",
      //           },
      //         ],
      //       },
      //     });
      //   }
      //   if ($(e.currentTarget).data("identity") == "stu") {
      //     SDK.bindSyncNormalData({
      //       type: "js2cpp_video",
      //       data: {
      //         video_status: "down",
      //         video_msg: [
      //           {
      //             role: "stu",
      //           },
      //         ],
      //       },
      //     });
      //   }
      //   SDK.setEventLock();
      // });
      // 交换位置
      $(".exchange-button").on("click touchstart", function (e) {
        if (e.type == "touchstart") {
          e.preventDefault();
        }
        e.stopPropagation(); // 阻止事件传播
        if (!isSync) {
          // $(this).trigger("syncExchangeClick");
          return;
        }

        SDK.bindSyncEvt({
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          // funcType: 'audio',
          syncName: "syncExchangeClick",
          recoveryMode: "1",
        });
      });
      $(".exchange-button").on("syncExchangeClick", async function (e) {
        console.log("syncExchangeClick");
        await swapTeacherStudent(configData.avatarUpWallData);
        avatarUpWallInitialization();
        SDK.setEventLock();
      });
    },
  };
  data.init();
});

function swapTeacherStudent(data) {
  // 临时存储teacher属性
  let temp = {
    status: data.teacherStatus,
    positionX: data.teacherPositionX,
    positionY: data.teacherPositionY,
    img: data.teacherImg,
  };

  // 将student属性赋给teacher
  data.teacherStatus = data.studentStatus;
  data.teacherPositionX = data.studentPositionX;
  data.teacherPositionY = data.studentPositionY;
  data.teacherImg = data.studentImg;

  // 将临时存储的teacher属性赋给student
  data.studentStatus = temp.status;
  data.studentPositionX = temp.positionX;
  data.studentPositionY = temp.positionY;
  data.studentImg = temp.img;

  return data;
}
function avatarUpWallInitialization() {
  if (configData.avatarUpWallData &&configData.avatarUpWallData.teacherStatus == "1") {
    if (configData.avatarUpWallData.teacherImg) {
      $(".tea-avatar-box").css({
        backgroundImage: `url(${configData.avatarUpWallData.teacherImg})`,
      });
    } else {
      $(".tea-avatar-box").css({
        backgroundImage: "url('./image/tea.png')",
      });
    }
    $(".tea-avatar-box").css({
      left: configData.avatarUpWallData.teacherPositionX / 100 + "rem",
      top: configData.avatarUpWallData.teacherPositionY / 100 + "rem",
      display: "block",
    });
    if (
      !isSync ||
      $(window.frameElement).attr("id") !== "h5_course_self_frame"
    ) {
    } else {
      SDK.bindSyncNormalData({
        type: "js2cpp_video",
        data: {
          video_status: "up",
          video_msg: [
            {
              role: "tea",
              left: Number(configData.avatarUpWallData.teacherPositionX),
              top: Number(configData.avatarUpWallData.teacherPositionY),
              width: 558,
              height: 418,
            },
          ],
        },
      });
    }
  }

  if (configData.avatarUpWallData && configData.avatarUpWallData.studentStatus == "1") {
    if (configData.avatarUpWallData.studentImg) {
      $(".stu-avatar-box").css({
        backgroundImage: `url(${configData.avatarUpWallData.studentImg})`,
      });
    } else {
      $(".stu-avatar-box").css({
        backgroundImage: "url('./image/stu.png')",
      });
    }
    $(".stu-avatar-box").css({
      left: configData.avatarUpWallData.studentPositionX / 100 + "rem",
      top: configData.avatarUpWallData.studentPositionY / 100 + "rem",
      display: "block",
    });
    if (
      !isSync ||
      $(window.frameElement).attr("id") !== "h5_course_self_frame"
    ) {
    } else {
      SDK.bindSyncNormalData({
        type: "js2cpp_video",
        data: {
          video_status: "up",
          video_msg: [
            {
              role: "stu",
              left: Number(configData.avatarUpWallData.studentPositionX),
              top: Number(configData.avatarUpWallData.studentPositionY),
              width: 558,
              height: 418,
            },
          ],
        },
      });
    }
  }
  if (isSync && window.frameElement.getAttribute("user_type") == USER_TYPE.TEA) {
    if (configData.avatarUpWallData &&
      configData.avatarUpWallData.teacherStatus == "1" &&
      configData.avatarUpWallData.studentStatus == "1"
    ) {
        $(".exchange").css({
        left:
          (Number(configData.avatarUpWallData.teacherPositionX) +
            558 / 2 -
            50) /
            100 +
          "rem",
        top:
          (Number(configData.avatarUpWallData.teacherPositionY) + 418) / 100 +
          "rem",
          display: "block",
      });
    }
  } else {
    $(".exchange").css({
      display: "none",
    });
  }
}
