.tea-avatar-box {
  position: absolute;
  width: 5.58rem;
  height: 4.18rem;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
  display: none;
}

.stu-avatar-box {
  position: absolute;
  width: 5.58rem;
  height: 4.18rem;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
  display: none;
}

// .tea-avatar-box,
// .stu-avatar-box {
//   .close-btn {
//     position: absolute;
//     display: block;
//     width: 0.4rem;
//     height: 0.4rem;
//     top: 0rem;
//     right: -0.4rem;
//     cursor: pointer;
//   }
// }
.exchange {
  position: absolute;
  width: 3.6rem;
  height: 1rem;
  z-index: 10;
  display: none;
}

.exchange-button {
  position: absolute;
  display: inline-block;
  width: 1rem;
  height: 1rem;
  top: 0rem;
  left: 0rem;
  background-image: url('../image/exchange.png');
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  cursor: pointer;
}

.exchange-tips {
  position: absolute;
  width: 2.4rem;
  height: 1rem;
  top: 0rem;
  right: 0rem;
  font-size: 0.3rem;
  line-height: 1.1rem;
  display: inline-block;

}
