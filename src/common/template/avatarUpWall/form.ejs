<!-- 头像上墙组件 -->
<div class="c-group">
  <div class="c-title">头像上墙组件（配置后自动上墙）</div>
  <div class="c-area">
    <!-- 教师头像上墙 -->
    <div class="field-wrap">
      <label class="field-label" style="width: 100px;">教师头像上墙<em>*</em></label>
      <select class="select-c" id="teachTime" v-model="configData.avatarUpWallData.teacherStatus" @change="teacherChange(configData.avatarUpWallData)">
        <option name="optive" value="1">开启</option>
        <option name="optive" value="2">关闭</option>
      </select>
    </div>
    <br>
    <div class="rules-content" v-if="configData.avatarUpWallData.teacherStatus == '1'">
      <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label>
      <label class="rules-field-label">X：</label>
      <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
        v-model="configData.avatarUpWallData.teacherPositionX" placeholder="请输入" />
      <label class="rules-field-label">Y：</label>
      <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
        v-model="configData.avatarUpWallData.teacherPositionY" placeholder="请输入" />
      <label class="rules-field-label"><em>数字，0<=x<=1920，0<=y<=1080</em></label>
    </div>
    <br v-if="configData.avatarUpWallData.teacherStatus == '1'">
    <!-- 学生头像上墙 -->
    <div class="field-wrap">
      <label class="field-label" style="width: 100px;">学生头像上墙<em>*</em></label>
      <select class="select-c" id="teachTime" v-model="configData.avatarUpWallData.studentStatus" @change="studentChange(configData.avatarUpWallData)">
        <option name="optive" value="1">开启</option>
        <option name="optive" value="2">关闭</option>
      </select>
    </div>
    <br>
    <div class="rules-content" v-if="configData.avatarUpWallData.studentStatus == '1'">
      <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label>
      <label class="rules-field-label">X：</label>
      <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
        v-model="configData.avatarUpWallData.studentPositionX" placeholder="请输入" />
      <label class="rules-field-label">Y：</label>
      <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
        v-model="configData.avatarUpWallData.studentPositionY" placeholder="请输入" />
      <label class="rules-field-label"><em>数字，0<=x<=1920，0<=y<=1080</em></label>
    </div>
  </div>

  <div class="c-area upload img-upload">
    <div v-if="configData.avatarUpWallData.teacherStatus == '1'">
      <div class="field-wrap">
        <label class="field-label" style="width: 80px" for="teacherImg">教师头像框</label>
        <input type="file" v-bind:key="Date.now()" class="btn-file" id="teacherImg" size="558*418"
          accept=".jpg,.jpeg,.png" @change="imageUpload($event,configData.avatarUpWallData,'teacherImg',60)" />
        <label for="teacherImg" class="btn btn-show upload" v-if="!configData.avatarUpWallData.teacherImg">上传</label>
        <label for="teacherImg" class="btn upload re-upload" v-if="configData.avatarUpWallData.teacherImg">重新上传</label>
      </div>
      <div class="img-preview" v-if="configData.avatarUpWallData.teacherImg">
        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
        <div class="img-tools">
          <span class="btn btn-delete" @click="configData.avatarUpWallData.teacherImg=''">删除</span>
        </div>
      </div>
    </div>
    <br v-if="configData.avatarUpWallData.teacherStatus == '1'">
    <div v-if="configData.avatarUpWallData.studentStatus == '1'">
      <div class="field-wrap">
        <label class="field-label" style="width: 80px" for="studentImg">学生头像框</label>
        <input type="file" v-bind:key="Date.now()" class="btn-file" id="studentImg" size="558*418"
          accept=".jpg,.jpeg,.png" @change="imageUpload($event,configData.avatarUpWallData,'studentImg',60)" />
        <label for="studentImg" class="btn btn-show upload" v-if="!configData.avatarUpWallData.studentImg">上传</label>
        <label for="studentImg" class="btn upload re-upload" v-if="configData.avatarUpWallData.studentImg">重新上传</label>
      </div>
      <div class="img-preview" v-if="configData.avatarUpWallData.studentImg">
        <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
        <div class="img-tools">
          <span class="btn btn-delete" @click="configData.avatarUpWallData.studentImg=''">删除</span>
        </div>
      </div>
    </div>
    <div class="field-wrap" style="padding-left: 74px;margin: 10px 0;" v-if="configData.avatarUpWallData.studentStatus == '1' || configData.avatarUpWallData.teacherStatus == '1'">
      <span class="txt-info" style="font-size: 12px;"><em>JPG/PNG格式，尺寸558*418，文件大小≤60KB,不上传显示默认样式</em></span>
    </div>
  </div>
</div>
