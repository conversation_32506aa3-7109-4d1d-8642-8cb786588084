export function avatarUpWallData() {
  let data = {
      teacherStatus: "2",
      teacherPositionX: "",
      teacherPositionY: "",
      teacherImg: "",

      studentStatus: "2",
      studentPositionX: "",
      studentPositionY: "",
      studentImg: "",
    }

  console.log(data, "14");
  return data;
}
export function avatarUpWallSend(data) {
  if (data.avatarUpWallData.teacherStatus == "") {
    alert("请选择教师上墙是否开启");
    return;
  }

  if (data.avatarUpWallData.teacherStatus == "1") {
    if (data.avatarUpWallData.teacherPositionX === "") {
      alert("请选择教师上墙位置");
      return;
    }
    if (data.avatarUpWallData.teacherPositionY === "") {
      alert("请选择教师上墙位置");
      return;
    }
  }
  if (data.avatarUpWallData.studentStatus == "") {
    alert("请选择学生上墙是否开启");
    return;
  }
  if (data.avatarUpWallData.studentStatus == "1") {
    if (data.avatarUpWallData.studentPositionX === "") {
      alert("请选择学生上墙位置");
      return;
    }
    if (data.avatarUpWallData.studentPositionY === "") {
      alert("请选择学生上墙位置");
      return;
    }
  }
}
export function teacherChange(data) {
  if (data.teacherStatus == "2") {
    data.teacherImg = "";
    data.teacherPositionX = "";
    data.teacherPositionY = "";
  }
}
export function studentChange(data) {
  if (data.studentStatus == "2") {
    data.studentImg = "";
    data.studentPositionX = "";
    data.studentPositionY = "";
  }
}
export function initializeAvatarUpWall(Data){
  console.log(Data, "63");
  if (!Data.configData.avatarUpWallData) {
    Data.configData.avatarUpWallData = avatarUpWallData();
    console.log(Data.configData.avatarUpWallData, "66");
  }
}
