/**
 * lottie动画封装
 * @param animations 动画对象
 * @param json 动画路径
 * @param el 容器元素
 * 使用示例
 * roteAnimations = await lottieAnimations.init(roteAnimations, item, `.role`);
      lottieAnimations.play(roteAnimations);
      lottieAnimations.stop(roteAnimations);
      lottieAnimations.destroy(roteAnimations);
*/

$(function(){
  let lottieAnimations = {
    lottieArr: [],
    // 动画初始化
    init: async function(animations, json, el, loop = true, autoplay = false) {
      console.log("animations",animations)
      try {
        const res = await $.get(json); // 使用 await 等待 $.get 完成
        if (res.w) {
          console.log("宽高", res.w, res.h);animations = lottie.loadAnimation({
            container: document.querySelector(el),
            renderer: "svg",
            loop,
            autoplay,
            path: json,
          });
          lottieAnimations.lottieArr.push(animations);

          return animations; // 成功时返回 animations
        } else {
          throw new Error("Failed to load animation");
        }
      } catch (error) {
        console.error(error); // 捕获错误
        throw error; // 如果需要，可以在调用方处理
      }
    },
    // 播放动画
    play: function(animations) {
      console.log("播放动画", animations);
      if(animations) {
        animations.play(); //播放动画
      } else {
        console.log("动画不存在");
      }
    },
    // 销毁动画
    destroy: function(animations) {
      if(animations) {
        lottieAnimations.lottieArr = lottieAnimations.lottieArr.filter(item => item !== animations);
        animations.destroy(); //销毁动画
        animations = null;
      } else {
        console.log("动画不存在");
      }
    },
    // 停止动画
    stop: function(animations) {
      if(animations) {
        animations.stop(); //停止动画
      } else {
        console.log("动画不存在");
      }
    },
    // 销毁所有动画
    destroyAll: function() {
      lottieAnimations.lottieArr.forEach(item => {
        if(item) {
          lottieAnimations.destroy(item);
        } else {
          console.log("动画不存在");
        }
      });
      lottieAnimations.lottieArr = [];
      console.log('destroyAll==end==', lottieAnimations);
    }
  }
  window.lottieAnimations = lottieAnimations;
  // 监听到离开页面处理内存释放
  $(window).on('beforeunload', function () {
    console.log('destroyAll==start==', lottieAnimations);
    lottieAnimations && lottieAnimations.destroyAll();
    window.lottieAnimations = null;
  })
})
