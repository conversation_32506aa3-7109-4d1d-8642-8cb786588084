// 用户类型
const USER_TYPE = {
  STU: 'stu',
  TEA: 'tea'
}
// 目前课中状态
const CLASS_STATUS = {
  NOT: 0, // 未上课，老师和学生预览模式
  IN_CLASS: 1, // 上课中，老师学生正在上课
  AUTH: 2 // 已经授权
}

// 教学行为类型
const TEACHER_TYPE = {
  TEACHING_INPUT:'teachinginput',
  PRACTICE_INPUT:'practiceinput',
  PRACTICE_OUTPUT:'practiceoutput'
}

const INTERACTION_TYPE = {
  CLICK:'click',
  CHOOSE:'choose',
  DRAG:'DRAG',
  PRESENT:'present'
}

const USERACTION_TYPE = {
  SPEAK:'speak',
  LISTEN:'listen',
  LOOK:'look'
}

export { USER_TYPE, CLASS_STATUS, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE }
