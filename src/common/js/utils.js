/**
 * 获取当前页面 URL 中指定的查询参数值
 * @param {string} paramName - 参数名
 * @returns {string|null} - 参数值，如果不存在则返回 null
 */
function getUrlParameter(paramName) {
  const url = window.location.href;
  const urlObj = new URL(url);
  const params = new URLSearchParams(urlObj.search);
  return params.get(paramName);
}

/**
 * 执行教师相关的操作
 * @param {Function} callback - 需要执行的操作
 */
function executeForTeacher(callback) {
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
  const role = getUrlParameter('role');

  if (isSync) {
    if (userType === 'tea') {
      if (typeof callback === 'function') {
        callback(); // 执行回调函数
      }
    } else {
      console.warn('当前用户不是教师，无法执行操作');
    }
  } else {
    if (role === '1') { // 代表老师 iframe
      if (typeof callback === 'function') {
        callback(); // 执行回调函数
      }
    } else {
      console.warn('当前角色不是教师，无法执行操作');
    }
  }
}

/**
 * 执行学生相关的操作
 * @param {Function} callback - 需要执行的操作
 */
function executeForStudent(callback) {
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
  const role = getUrlParameter('role');

  if (isSync) {
    if (userType === 'stu') {
      if (typeof callback === 'function') {
        callback(); // 执行回调函数
      }
    } else {
      console.warn('当前用户不是学生，无法执行操作');
    }
  } else {
    if (role === '2') { // 代表学生 iframe
      if (typeof callback === 'function') {
        callback(); // 执行回调函数
      }
    } else {
      console.warn('当前角色不是学生，无法执行操作');
    }
  }
}

export {
  getUrlParameter,
  executeForTeacher,
  executeForStudent
}


