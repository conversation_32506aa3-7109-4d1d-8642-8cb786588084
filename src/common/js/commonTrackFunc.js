import { USER_TYPE } from './constants'

/**
 * 模板SDK  埋点上报
 * @param {*} message 埋点信息对象  action: 埋点类型 data: 不区分角色埋点数据 teaData: 老师端需要上报数据 stuData: 学生端需要上报数据
 * @param {string} role 角色（老师/学生）  指定的角色才会触发埋点上报  如果不传表示所有角色都需要上报
 */
export function reportTrackData (message,role)  {
    const h5SyncActions = parent.window.h5SyncActions;
    const { data = {}, teaData = {}, stuData = {}, action } = message
    if (h5SyncActions &&  h5SyncActions.reportTrackData) {
        if($(window.frameElement).attr("id") !== "h5_course_self_frame") {
            return 
        }
        const userType = window.frameElement ? window.frameElement.getAttribute('user_type') : '';
        let postData = {}
        if(userType === USER_TYPE.TEA) {
            postData = {...postData,...teaData}
        } else if (userType === USER_TYPE.STU){
            postData = {...postData,...stuData}
        }
        console.log(role,'role',typeof role)
        if(role) {
            if( role && role === userType) {
                h5SyncActions.reportTrackData(action, {...postData,...data});
            }
            return
        }
        h5SyncActions.reportTrackData(action, {...postData,...data});
     } else {
        // console.log('');
        console.log('%c此控制器版本无埋点上报sdk，请升级控制器版本', 'color: red')
     }
}
