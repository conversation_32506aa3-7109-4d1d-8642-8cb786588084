import {executeForTeacher} from './utils'

$(function () {
    let contentType = 'image'; // 内容类型参数
    let text = configData.teleprompter; // 文本内容
    let imageUrl = configData.tImg; // 图片的 URL
    executeForTeacher(() => {
        createTeleprompter(contentType, text, imageUrl, configData.tImgX, configData.tImgY);
    })

    // 创建提词器的DOM结构
    function createTeleprompter(contentType, text, imageUrl, x = 1340, y = 15) {
        const width = 1920, height = 1080, imgWidth = 550, imgHeightMax = 400;
        if (contentType === 'text' && text) {
            // 创建显示文本的div
            const teleprompter = $('<div></div>', {
                id: 'teleprompter',
                css: {
                    position: 'absolute',
                    left: `${x / height * 100}%`,
                    top: `${y / width * 100}%`,
                    width: `${imgWidth / width * 100}%`, // 固定宽度
                    maxHeight: `${imgHeightMax / height * 100}%`,
                    backgroundColor: 'rgba(255, 255, 255, 0.6)',
                    borderRadius: '0.2rem',
                    padding: '0.2rem',
                    overflowY: 'auto',
                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
                    zIndex: 9999,
                }
            });

            // 创建显示文本的div
            const textDiv = $('<div></div>', {
                id: 'text',
                text: text,
                css: {
                    fontSize: '0.24rem',
                    lineHeight: '1.5'
                }
            });
            teleprompter.append(textDiv);
            // 将提词器添加到body中
            $('#container').append(teleprompter);
            enableDragging(teleprompter); // 添加拖动功能

            // 双击手势以隐藏提词器
            // addDoubleClickHide(teleprompter);
        } else if (contentType === 'image' && imageUrl) {
            // 直接使用 img 标签作为提词器
            const img = $('<img>', {
                src: imageUrl,
                css: {
                    position: 'absolute',
                    left: `${x / width * 100}%`,
                    top: `${y / height * 100}%`,
                    width: `${imgWidth / width * 100}%`, // 固定宽度
                    maxHeight: `${imgHeightMax / height * 100}%`, // 最大高度与文本一致
                    objectFit: 'cover', // 保持图片比例
                    zIndex: 9999, // 确保在最上层
                },
                alt: '提词器图片' // 添加图片替代文本
            });
            $('#container').append(img); // 直接将 img 添加到容器中

            // 添加拖动功能
            enableDragging(img);

            // 双击手势以隐藏提词器
            // addDoubleClickHide(img);
        }
    }

    // 双击隐藏的功能
    function addDoubleClickHide(element) {
        element.on('dblclick touchend', function (e) {
            if (e.type === 'touchend') {
                const touch = e.originalEvent.touches[0];
                const now = Date.now();
                if (now - (this.lastTouchTime || 0) < 300) {
                    $(this).hide(); // 隐藏提词器
                }
                this.lastTouchTime = now;
            } else {
                $(this).hide(); // 隐藏提词器
            }
        });
    }

    // 节流函数
    function throttle(func, delay) {
        let lastTime = 0;
        return function (...args) {
            const now = Date.now();
            if (now - lastTime >= delay) {
                lastTime = now;
                return func.apply(this, args);
            }
        };
    }

    // 拖动功能
    function enableDragging(element) {
        let isDragging = false;
        let offsetX, offsetY;

        element.on('mousedown', function (e) {
            isDragging = true;
            offsetX = e.clientX - $(this).offset().left + $('#container').offset().left;
            offsetY = e.clientY - $(this).offset().top + $('#container').offset().top;
            $(this).css('cursor', 'move');
            e.preventDefault(); // 阻止默认事件
            e.stopPropagation(); // 阻止事件冒泡
        });

        // 使用节流函数来处理鼠标移动
        $(document).on('mousemove', throttle(function (e) {
            if (isDragging) {
                element.css({
                    left: e.clientX - offsetX,
                    top: e.clientY - offsetY
                });
            }
        }, 16)); // 大约60帧每秒

        $(document).on('mouseup', function () {
            isDragging = false;
            element.css('cursor', '');
        });
    }
});
