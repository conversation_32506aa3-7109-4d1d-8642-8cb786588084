*{
	margin: 0;
	padding: 0;
	-webkit-tap-highlight-color: transparent;
	user-select: none;
	-webkit-user-select: none;
}
::selection{
	background: transparent;
}
html,body{
	width: 100%;
	height: 100%;
	font-family: Arial;
}
ul,li{
  	list-style: none;
}
a{
	text-decoration: none;
}
.fl{
	float: left;
}
.fr{
	float: right;
}
.clearfix:after{
	content: '';
	display: block;
	clear: both;
}
.layer{
	position: absolute;
	width: 100%;
	height: 100%;
	line-height: 5rem;
	background: rgba(0,0,0,.7);
	font-size: 36px;
	color: #fff;
	z-index: 9999;
	top: 0;
	text-align: center;
}
.hide{
	display: none;
}
/*commen container*/
.container{
	height: 7.2rem;
	width: 12.8rem;
	position: relative;
	overflow: hidden;
	left: 50%;
	top: 50%;
	margin-left: -6.4rem;
	margin-top: -3.6rem;
	background-size: 100% 100%;
	background-position: center center;
	background-repeat: no-repeat;
}
/*commen description*/
/*.sec-desc{
	height: 0.76rem;
}*/
/*.desc{
	width: 100%;
	height: 0.76rem;
	background: #fff;
	box-shadow: 0px 0.02rem 0.08rem 0 rgba(0, 0, 0, 0.1);
	font-family: Arial;
	font-size: 0.36rem;
	font-weight: bold;
	color: #333333;
	line-height: 0.76rem;
	text-align: left;
	text-indent: 0.6rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	list-style:disc inside none;
}*/
/*commen title*/
/*.title{
	width: 100%;
	height: 1.2rem;
	margin-top: 0.36rem;
	line-height: 1rem;
	text-align: center;
}*/
/*.title h3{
	display: inline-block;
	height: 1.2rem;
	line-height: 1.2rem;
	padding: 0 0.68rem;
	background: rgba(0,0,0,.5);
	border-radius: 0.6rem;
	text-align: center;
	font-size: 0.72rem;
	color: #fff;
	font-weight: bold;
	max-width: 15.2rem;
	min-width: 11.2rem;
}*/
.lookTitle{
	width:19.2rem;
	height:1.38rem;
	text-align:center;
	font-weight:700;
	color:#333333;
	font-size:0.8rem;
	display: table-cell;
	vertical-align: bottom;
	color: #fff;
}
/*commom tg*/
.tg{
	position: absolute;
	z-index: 999;
	left: 0;
	top: 0.96rem;
	width: 0.72rem;
	height: 0.96rem;
	/* -webkit-transition: transform 0.3s;
	transition: transform 0.3s; */
}
.tg-btn{
	position: absolute;
	z-index: 999;
	left: 0;
	top: 0.96rem;
	width: 0.72rem;
	height: 0.96rem;
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg-content{
	box-sizing: border-box;
	position: absolute;
	z-index: 999;
	left: 0;
	top: 0.42rem;
	z-index: 999;
	width: 8.24rem;
	height:　9.63rem;
	overflow-x: hidden;
	overflow-y: auto;
	background: rgba(255,255,255,0.85);
	border-radius: 0 0.1rem 0.1rem 0;
	padding: 0.1rem 0.25rem 1rem;
	-webkit-transform: translateX(-8.24rem);
	transform: translateX(-8.24rem);
	/* -webkit-transition: transform 0.3s;
	transition: transform 0.3s; */
}
.tg-content li{
	padding-bottom: 0.18rem;
	border-bottom: 0.02rem #e4e4e4 solid;
}
.tg-list-tit{
	height: 0.76rem;
	line-height: 0.76rem;
}
.tg-list-num{
	width: 0.36rem;
	height: 0.36rem;
	text-align: center;
	line-height: 0.36rem;
	border-radius: 100%;
	background: #ff7200;
	color: #fff;
	font-size: 0.24rem;
	font-weight: bold;
	font-family: Arial;
	margin-top: 0.2rem;
}
.tg-list-tit h3{
	margin-left: .22rem;
    color: #000;
    font-size: .36rem;
    font-weight: 400;
    height: 0.76rem;
    width: 6.8rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.tg-list-des{
	padding-left: 0.58rem;
	color: #666;
	line-height: 0.37rem;
	font-size: 0.22rem;
	font-family: 'Microsoft YaHei'
}
.tg-content.active{
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg-content.re-active{
	-webkit-transform: translateX(-8.24rem);
	transform: translateX(-8.24rem);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg.active{
	-webkit-transform: translateX(8.23rem);
	transform: translateX(8.23rem);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg.re-active{
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.shadow{
	box-shadow: 0.04rem 0.09rem 0.3rem 0.02rem rgba(10,10,10,.3);
}
/* tea-show-result */
.show-result{
	width: 3.5rem;
	position:absolute;
	z-index: 999;
	left:0;
	top: 2.4rem;
	cursor: pointer;
	border-radius: 0.3rem;
	font-family: Arial;
	overflow:hidden;
	background: #ccc;
}
.show-result .banner{
	width: 100%;
	height: 0.74rem;
	background: #fff;
	font-size: 0.24rem;
	color: #333;
	text-align: center;
	line-height: 0.74rem;
	font-family: Arial;
	box-sizing: border-box;
	border-bottom: 1px solid #f0f0f0;
	display: -webkit-flex;
	display: flex;
	align-items:center;
	justify-content: center;
}
.show-result .banner span{
	width: 0.24rem;
	height: 0.24rem;
	margin-left: 0.1rem;
	background-position: center;
	background-size: 100% 100%;
}
.show-result .names .names-banner{
	height: 0.36rem;
	line-height: 0.36rem;
	font-size: 0.18rem;
	color: #999;
	font-family:Arial;
	background: #fff;
}
.show-result .names li{
	width: 100%;
	height: 0.6rem;
	line-height: 0.6rem;
	font-size: 0.24rem;
	color:#333;
}
.show-result .names li:nth-child(even){
	background: #fff;
}
.show-result .names li:nth-child(odd){
	background: #f0f0f0;
}
.show-result .names .flex{
	display: -webkit-flex;
	display: flex;
}
.show-result .names .flex-content-l{
	width:1.04rem;
	text-align:center;
}
.show-result .names .flex-content-r{
	width: 2.46rem;
	text-align: left;
}
