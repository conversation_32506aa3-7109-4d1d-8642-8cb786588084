*{
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;
}
::selection{
	background: transparent;
}
@font-face {
    font-family: 'ARLRDBD';  /*给自定义字体命名*/
  /*  src:url('font/ARLRDBD.woff') format('woff'),
         url('font/ARLRDBD.TTF') format('truetype'),
         url('font/ARLRDBD.svg#webfontjKg17VrE') format('svg');*/
}

html,body{
	width: 100%;
	height: 100%;
  font-family: Arial;
  overflow: hidden;
/*	font-family:ARLRDBD;*/
}
ul,li{
  	list-style: none;
}
a{
	text-decoration: none;
}
.fl{
	float: left;
}
.fr{
	float: right;
}
.clearfix:after{
	content: '';
	display: block;
	clear: both;
}
.layer{
	position: absolute;
	width: 100%;
	height: 100%;
	line-height: 5rem;
	background: rgba(0,0,0,.7);
	font-size: 36px;
	color: #fff;
	z-index: 9999;
	top: 0;
	text-align: center;
}
.hide{
	display: none;
}
/*commen container*/
.container{
	width: 19.2rem;
	height: 10.8rem;
	position: relative;
	overflow: hidden;
	left: 50%;
	top: 50%;
	margin-left: -9.6rem;
	margin-top: -5.4rem;
	background-size: 100% 100%;
	background-position: center center;
	background-repeat: no-repeat;
}
/*commen description*/
.sec-desc{
	height: 0.76rem;
}
.desc{
	position: absolute;
	top: .5rem;
	left: 0;
	width: 100%;
	height: .8rem;
	text-align: center;
	font-family: Arial;
	font-size: 0.7rem;
	color: #333333;
	font-weight: 700;
	line-height: 0.8rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	list-style:disc inside none;
	display: none;
	z-index: 20;
}
/*commen title*/
.title{
	position: absolute;
	height: 0.96rem;
	width: 11.5rem;
	top: 1.34rem;
    left: 50%;
    margin-left: -5.75rem;
	text-align: center;
	display: none;
    z-index: 20;
}
.title h3{
	font-weight: normal;
	font-size: 0.36rem;
    height: 0.96rem;
	line-height: .48rem;
	font-weight: 700;
	color: #333333;
	overflow: hidden;
}
/*commom tg*/
.tg{
	position: absolute;
	z-index: 999;
	left: 0;
	top: 0.96rem;
	width: 0.72rem;
	height: 0.96rem;
	cursor: pointer;
}
.tg-btn{
	position: absolute;
	z-index: 999;
	left: 0;
	top: 0.96rem;
	width: 0.72rem;
	height: 0.96rem;
}
.tg-content{
	box-sizing: border-box;
	position: absolute;
	z-index: 999;
	left: 0;
	top: 0.42rem;
	z-index: 999;
	width: 8.24rem;
	height:　9.63rem;
	overflow-x: hidden;
	overflow-y: auto;
	background: rgba(255,255,255,0.85);
	border-radius: 0 0.1rem 0.1rem 0;
	padding: 0.1rem 0.25rem 1rem;
	-webkit-transform: translateX(-8.24rem);
	transform: translateX(-8.24rem);
	/* -webkit-transition: transform 0.3s;
	transition: transform 0.3s; */
}
.tg-content li{
	padding-bottom: 0.18rem;
	border-bottom: 0.02rem #e4e4e4 solid;
}
.tg-list-tit{
	height: 0.76rem;
	line-height: 0.76rem;
	display: flex;
}
.tg-list-num{
	width: 0.36rem;
	height: 0.36rem;
	text-align: center;
	line-height: 0.36rem;
	border-radius: 100%;
	background: #ff7200;
	color: #fff;
	font-size: 0.24rem;
	font-weight: bold;
	font-family: Arial;
	margin-top: 0.2rem;
}
.tg-list-tit h3{
	flex: 1;
	margin-left: .22rem;
    color: #000;
    font-size: .36rem;
    font-weight: 400;
    height: 0.76rem;
    width: 6.8rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.tg-list-des{
	padding-left: 0.58rem;
	color: #666;
	line-height: 0.37rem;
	font-size: 0.22rem;
	font-family: 'Microsoft YaHei'
}
.tg-content.active{
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg-content.re-active{
	-webkit-transform: translateX(-8.24rem);
	transform: translateX(-8.24rem);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg.active{
	-webkit-transform: translateX(8.23rem);
	transform: translateX(8.23rem);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.tg.re-active{
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.level {
    width: 0rem;
    height: 2rem;
    position: absolute;
    left: 0;
    top: 8rem;
}
.highTg {
	position: absolute;
	left: 0;
	top:0rem;
	width: 8.24rem;
	height: 2rem;
    z-index: 1000;
    transform: translateX(-8.24rem);
}
.highTg.active{
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.highTg.re-active{
	-webkit-transform: translateX(-8.24rem);
	transform: translateX(-8.24rem);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.highTgCon {
    border-radius: 0 .1rem .1rem 0;
	width: 100%;
    height: 100%;
    background: #fff;
	box-sizing: border-box;
	padding: .2rem .2rem .2rem .9rem;
	overflow: auto;
}
.highTitle {
    width: 100%;
    height: 0.76rem;
	line-height: 0.76rem;
	display: flex;
}
.highIndex{
    display: block;
	width: 0.36rem;
	height: 0.36rem;
	text-align: center;
	line-height: 0.36rem;
	border-radius: 100%;
	background: #ff7200;
	color: #fff;
	font-size: 0.24rem;
	font-weight: bold;
	font-family: Arial;
	margin-top: 0.2rem;
}
.highTitle h3{
	flex: 1;
	margin-left: .22rem;
    color: #000;
    font-size: .36rem;
    font-weight: 400;
    height: 0.76rem;
    width: 6.8rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.highCon {
    padding-left: 0.58rem;
	color: #666;
	line-height: 0.37rem;
	font-size: 0.22rem;
	font-family: 'Microsoft YaHei';
    word-wrap:break-word;
    padding-bottom: .2rem;
    border-bottom: .02rem #e4e4e4 solid;
}

.highTgBtn {
    cursor: pointer;
	position: absolute;
	right: -.7rem;;
	top: .1rem;
	width: .7rem;
	height: .8rem;
	text-align: center;
	line-height: .8rem;
	color: #fff;
	background: #ed5564;
    border-radius: 0 .15rem .15rem 0;
    font-size: .4rem;
}


.lowTg {
	position: absolute;
	left: 0;
	top:0rem;
	width: 8.24rem;
	height: 2rem;
    z-index: 1000;
    transform: translateX(-8.24rem);
}
.lowTg.active{
	-webkit-transform: translateX(0);
	transform: translateX(0);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}
.lowTg.re-active{
	-webkit-transform: translateX(-8.24rem);
	transform: translateX(-8.24rem);
	-webkit-transition: transform 0.3s;
	transition: transform 0.3s;
}

.lowTgCon {
    border-radius: 0 .1rem .1rem 0;
	width: 100%;
    height: 100%;
    background: #fff;
	box-sizing: border-box;
	padding: .2rem .2rem .2rem .9rem;
	overflow: auto;
}
.lowTitle {
    width: 100%;
    height: 0.76rem;
	line-height: 0.76rem;
	display: flex;
}
.lowIndex{
    display: block;
	width: 0.36rem;
	height: 0.36rem;
	text-align: center;
	line-height: 0.36rem;
	border-radius: 100%;
	background: #ff7200;
	color: #fff;
	font-size: 0.24rem;
	font-weight: bold;
	font-family: Arial;
	margin-top: 0.2rem;
}
.lowTitle h3{
	flex: 1;
	margin-left: .22rem;
    color: #000;
    font-size: .36rem;
    font-weight: 400;
    height: 0.76rem;
    width: 6.8rem;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.lowCon {
    padding-left: 0.58rem;
	color: #666;
	line-height: 0.37rem;
	font-size: 0.22rem;
	font-family: 'Microsoft YaHei';
    word-wrap:break-word;
    padding-bottom: .2rem;
    border-bottom: .02rem #e4e4e4 solid;
}

.lowTgBtn {
    cursor: pointer;
	position: absolute;
	right: -.7rem;;
	top: 1.1rem;
	width: .7rem;
	height: .8rem;
	text-align: center;
	line-height: .8rem;
	color: #fff;
	background: #48cfae;
    border-radius: 0 .15rem .15rem 0;
    font-size: .4rem;
}
.shadow{
	box-shadow: 0 0.32rem 0.38rem 0rem rgba(0,0,0,.15);
}
/* tea-show-result */
.show-result{
	width: 3.5rem;
	position:absolute;
	z-index: 999;
	left:0;
	top: 2.4rem;
	cursor: pointer;
	border-radius: 0.3rem;
	font-family: Arial;
	overflow:hidden;
	background: #ccc;
}
.show-result .banner{
	width: 100%;
	height: 0.74rem;
	background: #fff;
	font-size: 0.24rem;
	color: #333;
	text-align: center;
	line-height: 0.74rem;
	font-family: Arial;
	box-sizing: border-box;
	border-bottom: 1px solid #f0f0f0;
	display: -webkit-flex;
	display: flex;
	align-items:center;
	justify-content: center;
}
.show-result .banner span{
	width: 0.24rem;
	height: 0.24rem;
	margin-left: 0.1rem;
	background-position: center;
	background-size: 100% 100%;
}
.show-result .names .names-banner{
	height: 0.36rem;
	line-height: 0.36rem;
	font-size: 0.18rem;
	color: #999;
	font-family:Arial;
	background: #fff;
}
.show-result .names li{
	width: 100%;
	height: 0.6rem;
	line-height: 0.6rem;
	font-size: 0.24rem;
	color:#333;
}
.show-result .names li:nth-child(even){
	background: #fff;
}
.show-result .names li:nth-child(odd){
	background: #f0f0f0;
}
.show-result .names .flex{
	display: -webkit-flex;
	display: flex;
}
.show-result .names .flex-content-l{
	width:1.04rem;
	text-align:center;
}
.show-result .names .flex-content-r{
	width: 2.46rem;
	text-align: left;
}
.dialog .dialog-role{
  position: absolute;
  left: 0;
  top: 0;
  /* width: 6.76rem;
  height: 6.71rem; */
  z-index: 99;
}
.dialog .dialog-text{
  position: absolute;
  left: 0;
  top: 0;
  /* width: 8.42rem;
  height: 5.06rem; */
  z-index: 100;
}
.dialog .dialog-text img{
  display: block;
  height: 100%;
  width: 100%;
}
