@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.hide{
    display: none;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.container{
    background: url(../image/bk.png) no-repeat;
    background-size: auto 100%;
	position: relative;
    // font-family:"ARLRDBD";

} 

.section{
	width:19.2rem;
	height:9.3rem;
}
.look{
	width:100%;
	font-size:0.8rem;
	text-align:center;
	overflow:hidden;
	color:#333333;
	font-weight:700;
	span{
		display:inline-block;
		margin-top:0.45rem;
	}
}
audio{
	width:0;
	height:0;
	opacity:0;
	position:absolute;
}
.no-click{
	cursor:not-allowed;
}
.problem-area{
	width:16.4rem;
	height:8.2rem;
	margin:0 auto;
	padding:0 0.78rem;
	box-sizing:border-box;
	position:relative;
	.pic{
		width:3.44rem;
		height:8.2rem;
		position:absolute;
		left:0.78rem;
		top:0;
		box-sizing:border-box;
/*		display:flex;
		display:-webkit-flex;
		-webkit-flex-direction:column;
		flex-direction:column;
		-webkit-justify-content:center;
		justify-content:center;*/
		.options{
			width:100%;
			height:1.94rem;
			background:transparent;
			border-radius:0.32rem;
			box-sizing:border-box;
			position:relative;
			left: 0;
			box-shadow:0 0 0.14rem rgba(0,0,0,0.25);
			img{
				width:100%;
				height:100%;
				position:absolute;
				border-radius:0.3rem;
				left:50%;
				top:50%;
				transform:translateY(-50%) translateX(-50%);
			}
		}
		.imgOption{
  			border:0.08rem solid #03a011;
  			background:#03a011;
		}
	}
	.answers{
		width:8rem;
		height:8.2rem;
		position:absolute;
	  	right:2rem;
	 /* 	left: 6.4rem;*/
	  	box-sizing:border-box;
/*		display:flex;
		display:-webkit-flex;
		-webkit-flex-direction:column;
		flex-direction:column;
		-webkit-justify-content:center;
		justify-content:center;*/
		.ansList{
			width:8em;
			height:1.5rem;
		    box-sizing:border-box;
		    border-radius:0.32rem;
		   /* overflow:hidden;*/
		    position:relative;
		    background:rgba(255,255,255,0.75);
		    box-shadow:0 0 0.14rem rgba(0,0,0,0.25);
		    .optionsText{
			    width:1.3rem;
			    height:1.3rem;
			    position:absolute;
			    left:0.14rem;
			    top:50%;
			    transform:translateY(-50%);
				background-size:95%;
				background-repeat:no-repeat;
				background-position: 0.04rem 0.08rem;
			}
			.anslistBox{
				width:100%;
				height:100%;
				padding-left:1.55rem;
				padding-right:0.36rem;
				display: flex;
				box-sizing:border-box;
				align-items: center;
				font-size:0.36rem;
				color:#333333;
				font-weight:600;
				line-height:0.48rem;
				border-radius:0.6rem;
				overflow:hidden;
			}
		}
		.ansList:nth-child(1) .optionsText{
			background-image:url('../image/tmp_A.png');
		}
		.ansList:nth-child(2) .optionsText{
			background-image:url('../image/tmp_B.png');
		}
		.ansList:nth-child(3) .optionsText{
			background-image:url('../image/tmp_C.png');
		}
		.ansList:nth-child(4) .optionsText{
			background-image:url('../image/tmp_D.png');
		}
		.ansList.textOption{
			border: .08rem solid #03a011;
		}
		.ansListAudio{
			width: 0.9rem;
			height: 0.9rem;
			position: absolute;
			top: 0.3rem;
			right: -1.1rem;
			background: url('../image/btn-audio-bgs.png') no-repeat;
			background-size: 100% 93%;
			background-position: 0 0.03rem;
			cursor: pointer;
			img{
				width: 0.5rem;
				height: 0.4rem;
				position: absolute;
				top: 0.26rem;
				left: 0.22rem;
				// margin-top:-0.19rem;
				// margin-left: -0.22rem; 
			}
		}
	}
	.lineBox{

		position:absolute;
		.line{
		  	position:absolute;
		  	background:#03a011;
		  	height:0.06rem;
		  	left:0rem;
		  	z-index:10;
		  	 border-radius:0.05rem;
		  	div{
		  		width: .5rem;
			    height: .06rem;
			    position: absolute;
			    top: 0;
			    left: -0.05rem;
			    background:#03a011;
			    border-radius:0.05rem;
			    z-index:-2;
		  	}
	  	}
	 }
}
.problem-area:after,.problem-area:before{
	display:table;
	content:"";
}
.problem-area:after{
	clear:both;
}

