"use strict"
import '../../common/js/common_1v1.js';
import "../../common/js/lottie.js";
import "../../common/template/dialog/index.js";
import "../../common/js/teleprompter.js"
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量

import {
  showRecord,
  recording,
  hideRecord,
  greatAnimation,
  goodJobAnimation,
  processAnimation,
  waitAnimation,
  playAnimation
} from './lottie_json.js';
$(function () {
  SDK.reportTrackData({
      action: "PG_FT_INTERACTION_LIST",
      data: {
        roundcount: configData.source.cardList.length || 0,
      },
      teaData: {
        teacher_type: TEACHER_TYPE.PRACTICE_OUTPUT,
        interaction_type: INTERACTION_TYPE.CLICK,
        useraction_type: USERACTION_TYPE.SPEAK,
      },
    });
    window.h5Template = {
    	hasPractice: '1'
    }
    let h5SyncActions = parent.window.h5SyncActions;
    let configDatas = configData;
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
    const userType = SDK.getUserType();
    let processStatus = 0; //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
    let duration = 3; //时间
    let timeIntStu = null; //进度条倒计时动画
    let start =true; // 点击
    // 初始化显示第一个 .hand 元素
    let currentIndex = -1; // 当前显示的 .hand 元素的索引


    if (configData.bg == '') {
        $(".container").css({ 'background-image': 'url(./image/defaultBg.png)' })
    }
    if (isSync) {
        // if (window.frameElement.getAttribute('user_type') == 'tea') {
            $('.hand').css('opacity','1');
        // }
    } else {
        $('.hand').css('opacity','1');
    }
      //初始化音频
      initAudio()
      //初始化麦克风图标位置
      initPosition();
      function initAudio() {
        if (configData.source.audioPosition && configData.source.audio) {
            $("#audioExample").attr("src", configData.source.audio)
            let startPos = configData.source.audioPosition;
            let left = ($('.pos_' + startPos).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + startPos).offset().top - $('.container').offset().top) / window.base + 'rem';
            $('.audioDiv').css({
                left: left,
                top: top
            });
            $('.audioDiv').show();
        }
    }

    function initPosition() {
        if (configData.source.microphonePosition) {
            let startPos = configData.source.microphonePosition;
            let left = ($('.pos_' + startPos).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + startPos).offset().top - $('.container').offset().top) / window.base + 'rem';
            $('#microphoneImg').css({
                left: left,
                top: top
            });
            $('#microphoneImg').show();
        }
    }
    if (userType === 'tea') {
      $(".startBtn").hide();
  }
    /**
     * 创建dom
    */
    let cardBgImg = configDatas.source.cardBg;
    let cardImgSrc = './image/card.png';
    if (cardBgImg) {
        cardImgSrc = cardBgImg
    };
    // 判断几张卡片
    var posLeftThree = [1,5.5,9.9];
    var posLeftFour = [0,3.8,7.4,11.1];
    var posLeft = [];
    let clickCardNum = 0;
    if (configDatas.source.cardList.length == '4') {
        posLeft = posLeftFour;
        $('.card_list').css('width','11.3rem');
        $('.card').css({
            width:'2.98rem',
            height:'3.82rem'
            // width: "3.64rem",
            // height: "5.18rem"
        })
    } else {
        posLeft = posLeftThree;
    }
    let cardHtml = ''
    for (let i = 0; i<configDatas.source.cardList.length; i++) {
        cardHtml += `<div class="card c_${i+1}" style="left:${posLeft[i]}rem" data-syncactions='card_item_${i}'>
            <audio src='./audio/re.mp3' data-syncaudio="audio3"></audio>
            <div class="num_box">
                <img src=${cardImgSrc} alt="" class="card_img">
                <div class="hand"></div>
                <audio src="${configDatas.source.cardList[i].audioItme}" class="audio_card_${i+1}" data-syncaudio="audioSyncaudio${i+1}"></audio>
            </div>
            <div class="img_box">
                <img src=${configDatas.source.cardList[i].img} alt="" class="main_img">
            </div>
        </div>`
    }
    $('.card_list').html(cardHtml)
    $('.hand').eq(0).css('opacity','1');
    // setTimeout(function () {
    //   $('.hand').eq(clickCardNum*1).css('opacity','1');
    // }, 300);
    // $('.card').addClass('card_rotate_one');

    if (configDatas.source.cardList.length == '4') {
        $('.card').css({
          width:'2.98rem',
          height:'3.82rem'
        })
    }
    /**
     * 点击魔术师 开始执行动画
    */

    let game_num = 1;
    let itemClick = true;
    let cardCanClick = true;
    let hasCLArr = [];
    let cardNum = configDatas.source.cardList.length;// 图片数量
    let indexNum = 0

    /**
     * 点击卡片
    */

    $('.card').on('click touchstart', function (e,message) {
      console.log("点击卡片1111")
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation(cardCanClick);
        if (game_num<=cardNum){
            if (cardCanClick) {
                cardCanClick = false;
                if (!isSync) {
                    $(this).trigger('cardClick')
                    return;
                }
                console.log("点击卡片222")
                hasCLArr.push($(this).index());
                console.log("点击卡片333")
                SDK.bindSyncEvt({
                    index: $(e.currentTarget).data('syncactions'),
                    eventType: 'click',
                    method: 'event',
                    funcType: 'audio',
                    syncName: 'cardClick',
                    otherInfor:{
                        hasClsaaArr: hasCLArr,
                        game_num_msg: game_num,
                        clickCardNum_msg: clickCardNum
                    },
                    recoveryMode:'1'
                });
            }
        }
    })
    $('.card').on('cardClick', function (e, message) {
      console.log("点击卡片4444")
      if(!start) return
      start = false;
      const index = $(this).index();
      indexNum = index;

      const selector = `.audio_card_${index + 1}`;

      const audioElement = $(selector);

      if (audioElement.length === 0) return;

      const element = audioElement.get(0);
      const syncName = audioElement.attr(`audioSyncaudio${index + 1}`);

      console.log("clickCardNum", index + 1, element);
      SDK.playRudio({
          index: element,
          syncName: syncName
      });


      $(element).on('ended', function() {

          cardCanClick = true;
          if (userType !== 'tea') return;
          if (isSync) {
              SDK.bindSyncEvt({
                  sendUser: '',
                  receiveUser: '',
                  index: $('.startBtn').data("syncactions"),
                  eventType: 'click',
                  method: 'event',
                  syncName: 'startBtnClick',
                  otherInfor: {
                      supporth5audioperation: SDK.getClassConf().course.supporth5audioperation, //老师端是否支持语音播放功能  此信息会传递给学生端
                  },
                  recoveryMode: '2', //不用做断线重连
              });
          } else {
            $(".startBtn").trigger("startBtnClick");
          }
      });
      // $('.hand').eq(index + 1).css('opacity','0');
      $('.hand').eq(0).css('opacity','0');
        if (isSync && message.operate=='5') {
            var otherInfor = message.data[0].value.syncAction.otherInfor;
            clickCardNum = otherInfor.clickCardNum_msg*1;
            hasCLArr = otherInfor.hasClsaaArr;
            game_num = otherInfor.game_num_msg;
            person_fn (game_num,hasCLArr,clickCardNum)
            SDK.setEventLock();
        } else {
            SDK.reportTrackData({
              action: "CK_FT_INTERACTION_ITEM",
              data: {
                roundid: $(this).index() + 1,
              },
            });
            // $(this).find('audio').get(0).play();
            console.log("点击卡片55555")
            SDK.playRudio({
                index: $(this).find('audio').get(0),
                syncName: $(this).find('audio').attr("data-syncaudio")
            })
            if (isSync) {
                var otherInfor = message.data[0].value.syncAction.otherInfor;
                hasCLArr = otherInfor.hasClsaaArr;
            }
            clickCardNum ++;
            // setTimeout(()=>{
            //   $('.hand').eq(clickCardNum).css('opacity','1');
            // },1000)
            $(this).addClass('card_rotate_one');
            if (clickCardNum>=cardNum) {
                clickCardNum = 0;
                game_num++;
                itemClick = true;
            } else {
                cardCanClick = true;
            }
            cardCanClick = false;
            SDK.setEventLock();
        }
    })
    function person_fn (game_nums,hasCLArrs,clickCardNums) {
        cardCanClick = false;
        itemClick = false;
        $('.hand').css('opacity','0');
        $('.card').removeClass('card_rotate_one');
        $('#person').addClass('person_animate');
        $('#person').on('animationend webkitAnimationEnd', function () {
            $('#person').removeClass('person_animate');
        })
        $('.hand').eq(clickCardNum).css('opacity','0');
        setTimeout(function () {
            if (cardNum == '3') {

                setTimeout(function () {

                    $('.card').css('cursor', 'pointer');
                    cardCanClick = true;
                    game_num = game_nums*1;
                    clickCardNum = clickCardNums*1;
                    clickCardNum ++;

                    for (let i = 0; i<hasCLArr.length; i++) {
                        $('.card').eq(hasCLArr[i]).addClass('card_rotate_one');
                    }
                    if (clickCardNum>=cardNum) {
                        clickCardNum = 0;
                        game_num++;
                        itemClick = true;
                    } else {
                        cardCanClick = true;
                    }
                    SDK.setEventLock();
                }, 500)
            } else {

                setTimeout(function () {

                    $('.card').css('cursor', 'pointer')
                    $('.card').css('cursor', 'pointer')
                    cardCanClick = true;
                    game_num = game_nums*1;
                    clickCardNum = clickCardNums*1;
                    clickCardNum ++;
                    for (let i = 0; i<hasCLArr.length; i++) {
                        $('.card').eq(hasCLArr[i]).addClass('card_rotate_one');
                    }
                    if (clickCardNum>=cardNum) {
                        clickCardNum = 0;
                        game_num++;
                        itemClick = true;
                    } else {
                        cardCanClick = true;
                    }
                    SDK.setEventLock();
                }, 500)
            }
        }, 1000)
    }
  // 小话筒

      /**
     * 动画部分
     */
    // 举起麦克风
    var showRecordInit = lottie.loadAnimation({
      container: document.getElementById("showRecord"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: showRecord(), //如果使用的是JSON
  });
  // 话筒两侧线条跳动
  var recordingInit = lottie.loadAnimation({
      container: document.getElementById("recording"), // 容器
      renderer: "svg",
      loop: true,
      autoplay: false,
      animationData: recording(), //如果使用的是JSON
  });
  // 录音进度条
  var processInit = lottie.loadAnimation({
      container: document.getElementById("processAni"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: processAnimation(), //如果使用的是JSON
  });
  // 放下麦克风
  var hideRecordInit = lottie.loadAnimation({
      container: document.getElementById("hideRecord"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: hideRecord(), //如果使用的是JSON
  });
  // 等待中动画  等待打分结果时使用
  var waitInit = lottie.loadAnimation({
      container: document.getElementById("wait"), // 容器
      renderer: "svg",
      loop: true,
      autoplay: false,
      animationData: waitAnimation(), //如果使用的是JSON
  });
  // 播放动画
  var playInit = lottie.loadAnimation({
      container: document.getElementById("play-record"), // 容器
      renderer: "svg",
      loop: true,
      autoplay: false,
      animationData: playAnimation(), //如果使用的是JSON
  });
  var greatInit = lottie.loadAnimation({
      container: document.getElementById("great"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: greatAnimation(), //如果使用的是JSON
  });
  var goodJobInit = lottie.loadAnimation({
      container: document.getElementById("goodJob"), // 容器
      renderer: "svg",
      loop: 0,
      autoplay: false,
      animationData: goodJobAnimation(), //如果使用的是JSON
  });
  // 点击开始录音按钮
//   $(".startBtn").on("click touchstart", function(e) {

// })
  $(".startBtn").on('startBtnClick', function(e, message) {

        $(".startBtn").hide();

        startRecord(); //开始录音动画(录音功能有一定时间延迟，因此动画较晚)
        SDK.setEventLock();
    })
    // 举起麦克风
    function startRecord() {
      if (userType === 'tea') {
          $(".record-con-tea").show(); // 老师端显示"recording Stu‘s voice" 学生录音中
      }
      $("#showRecord").show();
      showRecordInit.play();
      SDK.playRudio({
          index: $('#startAudio').get(0),
          syncName: $('#startAudio').attr("data-syncaudio")
      })
      showRecordInit.addEventListener('complete', function() {
          setTimeout(function() {
              $("#showRecord").hide();
              $("#recording").show();
              showRecordInit.stop();
              recordingInit.play();
              processStart();
          }, 500)
      });
      console.log("提前绑定放下麦克风动画的事件")
      hideRecordListener();//提前绑定放下麦克风动画的事件
  }
  //进度条动画
  function processStart() {
    processStatus = 1;
    $("#processAni").show();
    processInit.setSpeed(1)
    if (duration > 20) {
        processInit.setSpeed(4.5 / duration)
    } else {
        if (duration < 5) {
            processInit.setSpeed(6 / duration)
        } else {
            processInit.setSpeed(5 / duration)
        }
    }
    processInit.play(); //开始进度条动画
    processEnd() //录音倒计时
}
/**
     * 录音倒计时
     */
function processEnd() {
  if (timeIntStu) {
      clearTimeout(timeIntStu);
      timeIntStu = null;
  }
  timeIntStu = setTimeout(function() {
      if (processStatus != 2 && processStatus != 3) {
          console.log("processStatus", processStatus)
          finishRecord(); //结束进度条 放下麦克风
      }
  }, duration * 1000);
};

// 结束进度条 放下麦克风
function finishRecord() {
  processInit.stop();
  $("#recording").hide();
  console.log("进度条结束，准备放下麦克风")
  $("#hideRecord").show();
  hideRecordInit.play();
  $("#processAni").fadeOut(100);
  processStatus = 2; //当前进度  0 初始状态  1 显示进度条中 2 已开始播放放下麦克风动画  3 放下麦克风动画已播放完毕   6显示评价条中  7 评价动画播放中
}
//放下麦克风 监听事件。
function hideRecordListener(){
  console.log("放下麦克风 已绑定事件")
  hideRecordInit.addEventListener('complete', function() {
      console.log("麦克风已放下")
      //移除进度条，播放退出动画
      processStatus = 3;
      $("#hideRecord").hide();
      $(".record-con-tea").hide();
      hideRecordInit.stop();

      if (userType === 'stu') {
        $("#wait-container").show();
        // waitInit.play(); //显示等待中动画
        showEvaluate(); //告知老师需要显示评价
        $("#wait-container").remove();
        // waitInit.stop();
      }else {
        showEvaluate(); //告知老师需要显示评价
      }
  })
}

//开始播放动画
$("#playSyncbtn").on('playSyncName', function(e, message) {
  console.log("开始播放动画")
  $("#play-record-container").show();
  playInit.play();
  SDK.setEventLock();
})

//学生已收到结束播放通知，结束播放动画  同时告知老师结束播放动画（只有学生可收到）
function stopBySDK() {
  SDK.bindSyncEvt({
      sendUser: '',
      receiveUser: '',
      index: 'stopPlaySync',
      eventType: 'click',
      method: 'event',
      syncName: 'stopPlaySyncName',
      recoveryMode: '2', //不用做断线重连
  });
}

//结束播放动画 (学生是直接调用 老师是收到通知调用)
$("#stopPlaySyncbtn").on('stopPlaySyncName', function(e, message) {
  console.log("结束播放动画")
  playInit.stop();
  $("#play-record-container").hide();
  SDK.setEventLock();
  showEvaluate(); //告知老师需要显示评价
})
//告知老师显示评价
function showEvaluate() {
  console.log("告知老师显示评价")
  if (!isSync) {
    $(".evaluateTip").show();
  }else {
    SDK.bindSyncEvt({
      sendUser: '',
      receiveUser: '',
      index: 'showEvaluateSync',
      eventType: 'click',
      method: 'event',
      syncName: 'showEvaluateSyncName',
      recoveryMode: '2', //不用做断线重连
  });
  }

}
 // 老师显示评价
 $("#showEvaluateSyncbtn").on('showEvaluateSyncName', function(e, message) {
  SDK.setEventLock();
  if (processStatus != 6) {
      if (userType !== 'tea') return;
      processStatus = 6
      console.log("evaluateTip")
      $(".evaluateTip").show();
  }
})
// 点击great按钮
$(".great-btn").on("click touchstart", function(e) {
  if (e.type == "touchstart") {
      e.preventDefault()
  }
  e.stopPropagation();
  $(".evaluateBtn").addClass("disableBtn").attr('disabled', 'disabled');
  $(".evaluateTip").hide();
  console.log("great-btn 点击1");
  SDK.reportTrackData(
    {
      action: "CK_FT_INTERACTION_SPOKEBUTTON",
      data: {},
    },
    USER_TYPE.TEA
  );
  if (clickCardNum >= configData.source.cardList.length) {
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_COMPLETE",
        data: {
          result: "success",
        },
      },
      USER_TYPE.TEA
    );
  }
  if (isSync) {
      SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'greatBtnClick',
          recoveryMode: '2', //不用做断线重连
      });
  } else {
      $(this).trigger("greatBtnClick");
  }
})
$(".great-btn").on('greatBtnClick', function(e, message) {
  console.log("great-btn 点击2")
  greatAni();
  SDK.setEventLock();
  // $('.hand').eq(clickCardNum).css('opacity','1');
})
// 点击goodjob按钮
$(".goodjob-btn").on("click touchstart", function(e) {
  if (e.type == "touchstart") {
      e.preventDefault()
  }
  e.stopPropagation();
  $(".evaluateBtn").addClass("disableBtn").attr('disabled', 'disabled');
  $(".evaluateTip").hide();
  SDK.reportTrackData(
    {
      action: "CK_FT_INTERACTION_SPOKEBUTTON",
      data: {},
    },
    USER_TYPE.TEA
  );
  if (clickCardNum >= configData.source.cardList.length) {
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_COMPLETE",
        data: {
          result: "success",
        },
      },
      USER_TYPE.TEA
    );
  }
  if (isSync) {
      SDK.bindSyncEvt({
          sendUser: '',
          receiveUser: '',
          index: $(e.currentTarget).data("syncactions"),
          eventType: 'click',
          method: 'event',
          syncName: 'goodJobBtnClick',
          recoveryMode: '2', //不用做断线重连
      });
  } else {
      $(this).trigger("goodJobBtnClick");
  }
})
$(".goodjob-btn").on('goodJobBtnClick', function(e, message) {
  goodJobAni();
  SDK.setEventLock();
  // $('.hand').eq(clickCardNum).css('opacity','1');
})
//great动画
function greatAni() {
  console.log("great-btn 点击3")
  var timeIntFinish = setInterval(function() {
    console.log("great-btn 点击44")
      $("#great").show();
      SDK.playRudio({
          index: $('#greatAudio').get(0),
          syncName: $('#greatAudio').attr("data-syncaudio")
      })
      greatInit.play();
      console.log("great-btn 点击开始播放")
      greatInit.addEventListener('complete', function() {
          greatInit.stop();
          $("#great").hide();
          $(".evaluateBtn").removeClass("disableBtn").removeAttr('disabled');
          start = true;
          cardCanClick = true;
          // $('.hand').eq(indexNum + 1).css('opacity','1');
          processStatus = 6
      });
      clearInterval(timeIntFinish);
      timeIntFinish = null;
  }, 500)
}

//goodJob动画
function goodJobAni() {
  var timeIntFinish = setInterval(function() {
      $("#goodJob").show();
      SDK.playRudio({
          index: $('#goodJobAudio').get(0),
          syncName: $('#goodJobAudio').attr("data-syncaudio")
      })
      goodJobInit.play();
      goodJobInit.addEventListener('complete', function() {
          goodJobInit.stop();
          $("#goodJob").hide();
          $(".evaluateBtn").removeClass("disableBtn").removeAttr('disabled');
          start = true;
          cardCanClick = true;
          // $('.hand').eq(indexNum + 1).css('opacity','1');
          processStatus = 6
      });
      clearInterval(timeIntFinish);
      timeIntFinish = null;
  }, 500)
}



})
