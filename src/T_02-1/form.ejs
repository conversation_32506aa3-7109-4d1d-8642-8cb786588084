<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TOV0002FT_记忆翻牌FT</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TOV0002FT_记忆翻牌FT</div>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
			<!-- 对话框 -->
      <% include ./src/common/template/dialog/form %>
			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>卡片背面图 <em>( 非必填 )</em></label>
					<div class="field-wrap">
						<label class="field-label"  for="">上传图片</label><label class="btn btn-show upload" for="card_Bg" v-if="configData.source.cardBg==''?true:false">上传</label><label  class="btn upload re-upload" v-if="configData.source.cardBg!=''?true:false">重新上传</label><span class='txt-info'>（尺寸：298*382，大小：≤50KB)</span>
						<input type="file"  v-bind:key="Date.now()" class="btn-file" id="card_Bg" size="298*382" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'cardBg',50)">
					</div>
					<div class="img-preview" v-if="configData.source.cardBg!=''?true:false">
						<img v-bind:src="configData.source.cardBg" alt=""/>
						<div class="img-tools">
							<span class="btn btn-delete" v-on:click="configData.source.cardBg=''">删除</span>
						</div>
					</div>
					<label>卡片图片 <em>( 图片必填 )</em></label>
					<div class="c-well" v-for="(item,index) in configData.source.cardList">
						<div class="well-title">
							<p>选项{{index+1}}</p>
							<span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.cardList.length>3?true:false'></span>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span class='txt-info'>（尺寸：298*382，大小：≤50KB)<em>*</em></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size="298*382" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
							<div class="field-wrap">
								<label class="field-label"  for="">点击播放声音上传</label><div class="audio-preview" v-show="item.audioItme!=''?true:false">
									<div class="audio-tools">
										<p v-show="item.audioItme!=''?true:false">{{item.audioItme}}</p>
										<img src="" alt="" v-show="item.audioItme==''?true:false">
									</div>
									<span class="play-btn" v-on:click="play($event)">
										<audio v-bind:src="item.audioItme"></audio>
									</span>
								</div><span class="btn btn-audio-dele" v-show="item.audioItme!=''?true:false" v-on:click="item.audioItme=''">删除</span><label v-bind:for="'audio-upload'+index" class="btn btn-show upload" v-if="item.audioItme==''?true:false">上传</label><label  v-bind:for="'audio-upload'+index" class="btn upload re-upload mar" v-if="item.audioItme!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤100KB)<em>*</em></span>
								<input type="file" v-bind:id="'audio-upload'+index" class="btn-file upload" size="" volume="100" accept=".mp3" v-on:change="audioUpload($event,item,'audioItme')" v-bind:key="Date.now()">
							</div>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele()" v-show='configData.source.cardList.length<4?true:false'>+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
