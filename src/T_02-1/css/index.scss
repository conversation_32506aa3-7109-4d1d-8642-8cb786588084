@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.main {
    width: 100%;
    height: 100%;
    position: relative;
}
.container{
	position: relative;

}
.stage{
	width: 100%;
    height: 100%;
    position: relative;
    .card_list {
        width: 9.5rem;
        height: 4.3rem;
        position: absolute;
        left: 2.6rem;
        top: 3.6rem;
        .card{
            width: 2.98rem;
            height: 3.82rem;
            position: absolute;
            border: .5rem;
            transform-style: preserve-3d;
            transition: .3s linear;
            cursor: pointer;
            .num_box{
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: 6;
                backface-visibility: hidden;
                border-radius: .2rem;
                overflow: hidden;
                // transition: .5s;
                .card_img{
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                }
                .num_img{
                    position: absolute;
                    left: 0;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    margin: auto;
                    width: 1.6rem;
                    height: auto;
                    display: none;
                }
               /*  .num_shake{
                    animation: numshake 1s;
                }
                @keyframes numshake {
                    0%{
                        transform: scale(1)
                    }
                    20%{
                        transform: scale(1.1)
                    }
                    40%{
                        transform: scale(0.9)
                    }
                    60%{
                        transform: scale(1.1)
                    }
                    80%{
                        transform: scale(0.9)
                    }
                    100%{
                        transform: scale(1)
                    }
                } */
            }
            .img_box{
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: 4;
                transform:rotateY(-180deg);
                backface-visibility: hidden;
                border-radius: .2rem;
                overflow: hidden;
                .main_img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
        // .c_1{
        //     left: .17rem;
        // }
        // .c_2{
        //     left:3.96rem;
        // }
        // .c_3{
        //     left:7.62rem;
        // }
        .card_rotate_one {
            transform: rotateY(180deg);
        }
    }

    .person{
        // width: 5.55rem;
        // height:6.54rem;
        width: 2.63rem;
        height: 4.12rem;
        cursor: pointer;
        position: absolute;
        bottom: 2.7rem;
        left: .1rem;
        // background: url('//cdn.51talk.com/apollo/images/9c95f2259f279b53fdd58427e27d2969.png') no-repeat;
        // background-image: url('//cdn.51talk.com/apollo/images/9c95f2259f279b53fdd58427e27d2969.png');
        background-image: url('../image/9c95f2259f279b53fdd58427e27d2969.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }
    // .person_animate {
    //     animation: person steps(6) .8s 2;
    // }
    // @keyframes person {
    //     0%{
    //         background-position: 0 0;
    //     }
    //     100%{
    //         background-position: 120% 0;
    //     }
    // }
    // @-webkit-keyframes person {
    //     0%{
    //         background-position: 0 0;
    //     }
    //     100%{
    //         background-position: 120% 0;
    //     }
    // }
    .hand{
        position: absolute;
        width:1.8rem;
        height:1.8rem;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        margin-left: -0.35rem;
        background: url('../image/hands.png');
        background-size: 7.2rem 1.8rem;
        cursor: pointer;
        animation: hand 1s steps(4, end) 0s infinite;
        -webkit-animation: hand 1s steps(4, end) 0s infinite;
        opacity: 0;
    }
    @keyframes hand {
        0%{
            background-position: 0 0;
        }
        100%{
            background-position:133% 0;
        }
    }
    @-webkit-keyframes hand {
        0%{
            background-position: 0 0;
        }
        100%{
            background-position:133% 0;
        }
    }
  }
  //开始按钮
  .startBtn {
    display: none;
    position: absolute;
    bottom: 0.1rem;
    left: 50%;
    width: 2.7rem;
    height: 0.76rem;
    background: rgba(255, 255, 255, 1);
    border-radius: 0.38rem;
    transform: translate(-50%, 0px);
    cursor: pointer;

    .startContent {
        width: 2.54rem;
        height: 0.6rem;
        background: rgba(229, 171, 66, 1);
        border-radius: 0.3rem;
        margin: 0 auto;
        position: relative;
        top: 50%;
        transform: translate(0, -50%);
        font-size: 0.3rem;
        font-family: Century Gothic;
        font-weight: bold;
        color: rgba(255, 255, 255, 1);
        line-height: 0.46rem;
        line-height: .6rem;
        text-align: center;
    }
}
 // 麦克风动画
 .animation-con {
  position: absolute;
  bottom: 0px;
  width: 100%;

  .record-con-tea {
      display: none;
      text-align: center;
      position: absolute;
      bottom: 2.51rem;
      width: 4rem;
      height: 0.88rem;
      left: 50%;
      transform: translate(-50%, 0px);
      background: rgba(0, 0, 0, 0.6);
      border-radius: 16px;
      font-size: 0.3rem;
      font-weight: 400;
      line-height: 0.88rem;
      color: #fff;
  }

  .record-con {
      display: none;
      position: absolute;
      bottom: 0;
      width: 4rem;
      height: 2.33rem;
      left: 50%;
      transform: translate(-50%, 0px);
      background: rgba(0, 0, 0, 0.6);
      border-radius: 40px 40px 0px 0px;
  }

  .processAni {
      background: transparent;
  }

  .waitAni {
      width: 3.48rem;
      height: 1.74rem;
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0px);
      bottom: 0.3rem;
  }

  .playAni {
      width: 3.48rem;
      height: 1.74rem;
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0px);
      bottom: 0.3rem;
  }
}
// 评价动画
.finialAni {
  position: absolute;
  width: 100%;
  height: 100%;
  display: none;
  top: 0;
  left:0;
  z-index: 9999;
}

// 评价
.evaluateTip {
  display: none;
  width: 12.1rem;
  height: 1rem;
  border-radius: 0.16rem;
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0px);
  bottom: 0;
  background: rgba(255, 255, 255, 0.6);
  font-size: .3rem;

  .tip {
      height: 100%;
      width: 7.6rem;
      line-height: 1rem;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
  }

  .evaluateBtn {
      position: absolute;
      top: .12rem;
      color: #fff;
      text-align: center;
      line-height: 0.5rem;
      border-radius: .38rem;
      border: #fff solid 0.08rem;
      height: 0.6rem;
      cursor: pointer;
  }



  .great-btn {
      background: #ED6E22;
      right: 2.68rem;
      width: 1.78rem;
  }

  .goodjob-btn {
      background: #f1a91e;
      right: 0.46rem;
      width: 1.84rem;
  }
  .disableBtn {
    background:#DDDDDD;
    cursor: not-allowed;
  }
}

#playSyncbtn,
#stopPlaySyncbtn,
#showEvaluateSyncbtn {
    display: none;
}

