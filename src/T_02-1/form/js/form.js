//var domain = 'http://172.16.0.107:9011/pages/159/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
var domain = '';
// 对话框初始化数据
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: ""
     }
  ],
  messageLocationX: '', // 消息内容位置x
  messageLocationY: '', // 消息内容位置y
  roleLocationX: '', // 角色位置x
  roleLocationY: '', // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2" // 播放完之后状态
}
var Data = {
    configData: {
        bg: "",
        desc: "",
        title: "",
        tImg: '',
        tImgX: '',
        tImgY: '',
        instructions: [{
            commandId: '-1'
        }],
        tg: [{
            title: "",
            content: "",
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
            cardBg: "",
            dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
            cardList: [{
                    img: '',
                    audioItme:''
                },
                {
                    img: '',
                    audioItme:''
                },
                {
                    img: '',
                    audioItme:''
                }
            ]
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
    dynamicInstructions: [], //交互提示标签
};

$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if(!Data.configData.tImg){
                Data.configData.tImg = '';
            }
            if(!Data.configData.tImgX){
                Data.configData.tImgX = 1340
            }
            if(!Data.configData.tImgY){
                Data.configData.tImgY = 15
            }
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
            if(!Data.configData.instructions){
                Data.configData.instructions = [{
                    commandId: '-1'
                }]
            }
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    mounted: function() {
      this.getDynamicInstructions();
    },
    methods: {
      getDynamicInstructions: function() {
        var that = this;
        getDynamicInstructions(function(res, newIstructions) {
        	that.dynamicInstructions = res;
            that.configData.instructions = newIstructions;
        }, that.configData.instructions);
      },
      addInstruction: function() {
        addInstruction(this.configData);
      },
      removeInstruction: function(index) {
        removeInstruction(index, this.configData);
      },
      validateInstructions: function() {
        return validateInstructions(this.configData);
      },
        //辅助提示图片上传
        tImageUpload: function(e, attr, fileSize) {
            console.log("tImageUpload",e)
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;
            var item = this.configData;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.tImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        //辅助提示图片大小校检
        tImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && (height+1) > data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width+"*"+data.height);
            }
            return checkSize;
        },

        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "K上限，请检查后上传！");
                e.target.value = '';
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        // lottie 图片上传
        lottieUpload: function (e, item, attr, fileSize) {
          var file = e.target.files[0],
            size = file.size,
            that = this;
          if ((size / 1024).toFixed(2) > fileSize) {
            alert(
              "您上传的图片大小为" +
                (size / 1024).toFixed(2) +
                "KB, 超过" +
                fileSize +
                "K上限，请检查后上传！"
            );
            e.target.value = "";
            return;
          }
          const reader = new FileReader();
          reader.onload = async function (processEvent) {
            const jsonData = JSON.parse(processEvent.target.result);
            // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
            const naturalWidth = jsonData.w || jsonData.width;
            const naturalHeight = jsonData.h || jsonData.height;
            var check = that.sourceImgCheck(
              e.target,
              {
                height: naturalHeight,
                width: naturalWidth,
              },
              item,
              attr
            );
            if (check) {
              that.postData(file, item, attr);
              img = null;
            } else {
              img = null;
            }
          };
          reader.readAsText(file);
        },
        sourceImgCheck: function(input, data, item, attr) {
            var dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            var checkSize = size.some(function(item, idx) {
                var _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
                e.target.value = '';
            }
            return checkSize;
        },
        validate: function() {
            if(this.validateDialog()) {
              alert(this.validateDialog());
              return false;
            }
            var isPass = this.configData.source.cardList.some(function(item) {
                return item.img == ''
            });
            var audioItme = this.configData.source.cardList.some(function(item) {
              return item.audioItme == ''
            });

            if (isPass == true) {
                alert("请上传编辑问题-卡片图片上传！");
                return false;
            }
            if (audioItme == true) {
              alert("点击播放声音上传！");
              return false;
          }
            return true;
        },
        // 对话数据校验
        validateDialog: function() {
          var dialogs = this.configData.source.dialogs;
          // 如果数据为初始状态，则不校验
          if (JSON.stringify(dialogs) == JSON.stringify(dialogsInitData)) {
            return "";
          }
          if(dialogs.roleImg == "") {
            return "请上传对话IP对象";
          }
          if(dialogs.roleLocationX == "" || dialogs.roleLocationY == "") {
            return "请输入IP对象位置";
          }
          var isMessagePass = dialogs.messages.some(function (item) {
            return item.text == "" || item.audio == "";
          });
          if (isMessagePass == true) {
            return "请上传对话内容";
          }
          if(dialogs.messageLocationX == "" || dialogs.messageLocationY == "") {
            return "请输入对话位置";
          }
          return "";
        },
        onSend: function() {
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var val = this.validate();
            if (val === true && this.validateInstructions()) {
                var _data = JSON.stringify(data);
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    item[attr] = '';
                }
            })
        },
        audioUpload: function(e, item, attr) {
          console.log("postData",item)
            //校验规则
            //var _type = this.rules.audio.sources.type;

            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            // if ((size / 1024).toFixed(2) > 500) {
            //     console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            // } else {
            //     console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            // }
            if ((!isNaN(parseInt($(e.target).attr('volume')))) && (size / 1024).toFixed(2) > parseInt($(e.target).attr('volume'))) {
                alert("您上传的音频大小为" + (size / 1024).toFixed(2) + "KB, 超过" + $(e.target).attr('volume') + "K上限，请检查后上传！");
                e.target.value = '';
                return;
            }
            item[attr] = "./form/img/loading.jpg";

            this.postData(file, item, attr);
        },
        addSele: function() {
            this.configData.source.cardList.push({
                img: '',
                audioItme:''
            });
        },
        delSele: function(item) {
            this.configData.source.cardList.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({ title: '', content: '' });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        // 添加对话框
        addDialog: function() {
          this.configData.source.dialogs.messages.push({
            text: "",
            audio: ""
          });
        },
        // 删除对话框
        delDialog: function (item) {
          this.configData.source.dialogs.messages.remove(item);
        },
        // 删除对话框音频
        delDialogPrew: function (item, key) {
          if (key) {
            item[key] = "";
          } else {
            item.dialog = "";
          }
        },
    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
