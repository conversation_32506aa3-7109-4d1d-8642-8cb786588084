@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.container{
    background-size: 100%;
	position: relative;
    // font-family:"ARLRDBD";

}
.stage{
	width: 100%;
    height: 100%;
    z-index: 3;
    position: relative;
    .article {
        position: absolute;
        top: 1.6rem;
        left: 1.4rem;
        width: 6.8rem;
        height: 7.6rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // overflow: hidden;
        .article_text {
            width: 100%;
            max-height: 100%;
            line-height: .7rem;
            border-radius: .3rem;
            background-color: #fff;
            font-size: .5rem;
            word-break: break-all;
            padding: .3rem;
            box-sizing: border-box;
            background: rgba(255, 255, 255 ,.6);
            box-shadow: 0.2rem 0.2rem 0.3rem 0 rgba(0,0,0,.15);
            display: none;
            overflow: auto;
            span{
                color: red;
                font-weight: bold;
            }
        }
    }
    .flower{
        position: absolute;
        right: 1.8rem;
        top: 1.5rem;
        width: 9rem;
        height: 8.4rem;
        // background: blue;
        .button{
            width: 3.3rem;
            height: 2.8rem;
            position: absolute;
            left: 50%;
            top: 50%;
            margin-left: -1.7rem;
            margin-top: -1.4rem;
            cursor: pointer;
            // background: red;
            z-index: 14;
        }
        .hand{
            width:1.8rem;
            height:1.8rem;
            background: url('../image/hds.png');
            background-size: 7.2rem auto;
            background-position: 0 0;
            position: absolute;
            bottom: .1rem;
            left: 1.2rem;
            animation: handClick 1s steps(2,end) infinite;
            overflow: hidden;
            // -webkit-animation: handClick 1s steps(4,end) infinite;
            // opacity: 0;
            
            // z-index: 10;
        }
        @keyframes handClick {
            0% {
                background-position-x: 0%;
            }
            100% {
                background-position-x: 133%;
            }
        }
        .pos{
            width: 3.30rem;
            height: 2.8rem;
            position: absolute;
            z-index: 12;
            img{
                width: 100%;
                height: auto;
                position: absolute;
                left: 0;
                top: 0;
                right: 0;
                bottom: 0;
                margin: auto;
            }
        }
        .posCh{
            // filter: grayscale(70%) blur(1px);
            // -webkit-filter: grayscale(70%) blur(1px);
        }
        .max{
            transform: scale(1.2);
            z-index: 14;
            // filter: grayscale(0%);
            // -webkit-filter: grayscale(0%);
        }
        .pos_1 {
            top: 1.4rem;
            left: .45rem;
        }
        .pos_2 {
            top: 0rem;
            left: 2.8rem;
        }
        .pos_3 {
            top: 1.4rem;
            right: .5rem;
        }
        .pos_4 {
            bottom: 1.4rem;
            right: .5rem;
        }
        .pos_5 {
            bottom: 0rem;
            left: 2.8rem;
        }
        .pos_6 {
            bottom: 1.4rem;
            left: .45rem;
        }
    }
    .right {
        right: 1.8rem;
    }
    .left {
        left: 1.8rem;
    }
    .center {
        left: 50%;
        margin-left: -4.5rem;
    }
}
.mask {
    position: absolute;
    left: 0;
    top: 0;
    width: 19.2rem;
    height: 10.8rem;
    opacity: .5;
    z-index: 13;
    background: black;
    display: none;
}

