@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.hide{
    display: none;
}
.noShow{
    opacity: 0;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.container{
    // background: url(../image/1.jpg) no-repeat;
     background-size: auto 100%;
	position: relative;
    // font-family:"ARLRDBD";

}
.tv {
    position: absolute;
    bottom: 1.4rem;
    left: 2.1rem;
    width: 8.98rem;
    height: 6.86rem;
    z-index: 6;
    .tv-child{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        background:  url(../image/suger.png) no-repeat;
        background-size: 100% 100%;
    }
    .list {
        position: absolute;
        top: .49rem;
        left: .49rem;
        width: 8rem;
        height: 5.88rem;
        border-radius: .8rem;
        overflow: hidden;
        z-index: 5;
        ul{
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            height: 100%;
            li {
                height: 100%;
                width: 8rem;
                border-radius: .8rem;
                overflow: hidden;
                img{
                    width: 100%;
                    height: 100%;
                    border-radius: .8rem;
                }
            }
        }
    }
    .pageNum {
        position: absolute;
        bottom: -0.4rem;
        left: 50%;
        margin-left: -.5rem;
        width: 1.05rem;
        height: .45rem;
        background: rgba(76, 76, 76, 0.75);
        font-size: .35rem;
        text-align: center;
        line-height: .45rem;
        border-radius: .5rem;
        color: #ffffff;
        z-index: 21;
    }
}
.leftBtn {
    position: absolute;
    left:.6rem;
    bottom: 4.1rem;
    width: 1.61rem;
    height: 1.55rem;
    background: url(../image/button.png) no-repeat;
    background-size: 100% 100%;
    transform: rotateY(180deg);
    cursor: pointer;
}

.rightBtn {
    position: absolute;
    left: 11rem;
    bottom: 4.1rem;
    width: 1.61rem;
    height: 1.55rem;
    background: url(../image/button.png) no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
}
.noClick {
    opacity: .5;
    cursor: not-allowed;
}
.voice {
    position: absolute;
    width: 9.42rem;
    height: 2.48rem;
    bottom: 0;
    left: 5.3rem;
    background: url(../image/voice.png) no-repeat;
    background-size: 100% 100%;
    z-index: 5;
}
// 声音区域
.control {
    position: absolute;
    right:1.08rem;
    top: 3.2rem;
    height: 3.81rem;
    width: 1.11rem;
    // height: 5.75rem;
    // width: 1.4rem;
    // background: #ededed;
    // border-radius: 1.5rem;
    // box-shadow: 0 0 0.4rem rgba(0,0,0,0.4);
    box-sizing: border-box;
    padding: .05rem 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background:  url(../image/voiceBg.png) no-repeat;
    background-size: contain;
    span {
        width: 0.6rem;
        // background: #574360;
        overflow: hidden;
        cursor: pointer;
        position: relative;
        left: .25rem;
        em {
            display: block;
            width: 100%;
            height: 0;
            position: absolute;
            bottom: 0;
            transition: all 1s linear;
        }
    }
    .high {
        height: 1.27rem;
        border-radius: 1.1rem 1.1rem 0 0;
    }
    .middle {
        height: 1.27rem;
    }
    .low {
        height: 1.27rem;
        border-radius: 0 0 1.1rem 1.1rem;
        // opacity: 0;
    }
    .highChoose {
        background: linear-gradient(#ff4e00,#fca309);
    }
    .middleChoose {
        background: linear-gradient(#fca309,#ffc900);
    }
    .lowChoose {
        background: linear-gradient(#ffc900,#ffed24);
    }
    .toMax {
        animation: toMax .8s linear forwards;
    }
    .toMin {
        animation: toMin .8s linear forwards;
    }
    @keyframes toMax {
        0% {
            height: 0;
        }
        100%{
            height: 100%;
        }
    }
    @keyframes toMin {
        0% {
            height:100%;
        }
        100%{
            height: 0;
        }
    }
}
// 选择按钮
.chooseBtns {
    position: absolute;
    top: 4.9rem;
    right: 1rem;
    width: 2.4rem;
    height: 4.1rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .monkey {
        position: absolute;
        top: -1.7rem;
        left: -2.4rem;
        height: 2.2rem;
        width: 2.4rem;
        background: url(../image/monkey.png) no-repeat;
        background-size: 6.6rem 3.02rem;
        background-position: -3.5rem -.5rem;
    }
    .monkeyNow {
        background-position: -.8rem -.5rem;
        animation: shake 2s;
    }
    @keyframes shake {
        0% {
            transform: rotateZ(10deg)
        }
        20%{
            transform: rotateZ(-10deg)
        }
        40% {
            transform: rotateZ(10deg)
        }
        60% {
            transform: rotateZ(-10deg)
        }
        80%{
            transform: rotateZ(10deg)
        }
        100% {
            transform: rotateZ(0deg)
        }
    }
    .choose {
        width: 2.4rem;
        height: 1.1rem;
        background:  url(../image/chooseBtn.png) no-repeat;
        background-size: 10rem 5rem;
        cursor: pointer;
    }
    .choose:hover{
        transform: scale(1.1)
    }
    .initPre {
        background-position: -6.6rem -1.03rem;
    }
    .initGreat {
        background-position: -3.85rem -1.03rem;
    }
    .initGood {
        background-position: -1rem -1.03rem;
    }
    .choosePre {
        background-position: -6.6rem -2.6rem;
    }
    .chooseGreat {
        background-position: -3.85rem -2.6rem;
    }
    .chooseGood {
        background-position: -1rem -2.6rem;
    }
}

.volumn{
    position: absolute;
    top: 2.3rem;
    left: 13.2rem;;
    width: 3.28rem;
    height: 7.85rem;
    background:  url('../image/microphone.png') no-repeat;
    background-size: contain;
    z-index: 20;
    .lineup,.linedown{
       position: absolute; 
       top: 4.4rem;
       right: 1.08rem;
       width: 1.16rem;
       height: 0.22rem;
       background:url('../image/volume.png') no-repeat;
       background-size: 1.16rem .22rem;
       z-index: 10;
    }
    .linedown{
        top:5.65rem;
    }
}
.role{
    position: absolute;
    right: 3.4rem;
    bottom: 1.8rem;
    width: 2.81rem;
    height: 2.88rem;
    li{
        position: absolute;
        left: 0;
        top:0;
        width: 100%;
        height: 100%;
        z-index: 2;
    }
    .high{
        background:  url('../image/hight.png') no-repeat center bottom;
        background-size: 2.3rem 2.88rem;
    }
    .middle{
        background:  url('../image/middle.png') no-repeat center bottom;
        background-size:2.81rem 2.57rem;
    }
    .low{
        background:  url('../image/low.png') no-repeat center bottom;
        background-size:2.28rem 2.56rem;
    }
    .yin{
        top: 2.68rem;
        left: .9rem;
        width: 1rem;   //2.81
        height: 0.4rem;  //2.88
        background: #333;
        border-radius: 100%;
        z-index: 1;
        opacity: 0.4;
        box-shadow: 0 0 .6rem red;
    }
}

@keyframes look{
    0%{
        transform: rotate(0deg);
        transform-origin: center bottom;
        opacity: 0;
        z-index: 1;
    }
    50%{
        opacity: 1;
        z-index: 1;
    }
    100%{
        transform: rotate(-40deg) ;
        transform-origin: center bottom;
        z-index: 1;
    }
}
@keyframes jump{
    0%{
        opacity: 0;
        transform: translate(0rem ,0rem);
    }
    80%{
        opacity: 1;
        transform: translate(-1.5rem ,0rem);
        z-index: 30;
    }
    85%{
        z-index: 30;
        transform: translate(-1.5rem ,0.3rem);
    }
    100%{
        transform: translate(-1.5rem ,-4rem);
        z-index: 30;
    }
}
@keyframes yin{
    0%{
      transform: scale(1,1) 
    }
    100%{
        transform: scale(0.6,0.6) 
    }
}
@-webkit-keyframes look{
    0%{
        transform: rotate(0deg);
        transform-origin: center bottom;
        opacity: 0;
        z-index: 1;
    }
    50%{
        opacity: 1;
        z-index: 1;
    }
    100%{
        transform: rotate(-40deg) ;
        transform-origin: center bottom;
        z-index: 1;
    }
}
@-webkit-keyframes jump{
    0%{
        opacity: 0;
        transform: translate(0rem ,0rem);
    }
    80%{
        opacity: 1;
        transform: translate(-1.5rem ,0rem);
        z-index: 30;
    }
    85%{
        z-index: 30;
        transform: translate(-1.5rem ,0.3rem);
    }
    100%{
        transform: translate(-1.5rem ,-4rem);
        z-index: 30;
    }
}
@-webkit-keyframes yin{
    0%{
      transform: scale(1,1) 
    }
    100%{
        transform: scale(0.6,0.6) 
    }
}
