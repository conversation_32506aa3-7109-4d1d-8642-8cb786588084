@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/multyDialog/style.scss';
@import "../../common/template/avatarUpWall/style.scss";

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.container{
	position: relative;
}
audio{
	width: 0;
	height: 0;
	opacity: 0;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.hide{
    opacity:0;

}
.mainArea{
    position: relative;
    height: 10.8rem;
    opacity: 1;
  transition: opacity 0.3s ease-in-out;

  .loading {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }
  .hands{
    position: absolute;
    width:1.8rem;
    height:1.8rem;
    margin-left: -0.25rem;
    background: url('../image/hands.png');
    background-size: 7.2rem 1.8rem;
    pointer-events: none;
    display: none;
  }
  .textImg{
    position: absolute;
    left: 0;
    top: 0;
  }

  .elements-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

}
@keyframes hand {
  0%{
    background-position: 0 0;
  }
  100%{
    background-position:133% 0;
  }
}
@-webkit-keyframes hand {
  0%{
    background-position: 0 0;
  }
  100%{
    background-position:133% 0;
  }
}
