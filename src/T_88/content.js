var configData = {
	bg: '',
	desc: '',
	title:'',
	tg: [
		{
			content: "c",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}
	],
	level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
				content: "eegeg appy family. My father i appy family. My father i",
				title: "weqwf appy family. My father i"
		}]
	},
  source: {
    options: [
      {
        initPic: 'assets/images/Doll_turn1.json',
        initPicLocation: {
          x: '500',
          y: '500'
        },
        tagPic: 'assets/images/Doll_turn1.json',
        tagPicLocation: {
          x: '1200',
          y: '600'
        },
        textPic: 'assets/images/duihua1.png',
        textPicLocation: {
          x: '100',
          y: '50'
        },
        audio: 'assets/audios/01.mp3',
        showHand: '1',
        handLocation: {
          x: '600',
          y: '500'
        },
      },
      {
        initPic: 'assets/images/table.png',
        initPicLocation: {
          x: '800',
          y: '800'
        },
        tagPic: 'assets/images/coloredTable.png',
        tagPicLocation: {
          x: '800',
          y: '800'
        },
        textPic: 'assets/images/duihua1.png',
        textPicLocation: {
          x: '500',
          y: '50'
        },
        audio: 'assets/audios/01.mp3',
        showHand: '1',
        handLocation: {
          x: '900',
          y: '800'
        },
      },
      {
        initPic: 'assets/images/clock.png',
        initPicLocation: {
          x: '300',
          y: '300'
        },
        tagPic: 'assets/images/coloredClock.png',
        tagPicLocation: {
          x: '300',
          y: '300'
        },
        textPic: 'assets/images/duihua1.png',
        textPicLocation: {
          x: '900',
          y: '50'
        },
        audio: 'assets/audios/01.mp3',
        showHand: '1',
        handLocation: {
          x: '400',
          y: '300'
        },
      },
    ],
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "",
          audio: "",
        },
      ],
      messageLocationX: "", // 消息内容位置x
      messageLocationY: "", // 消息内容位置y
      roleLocationX: "100", // 角色位置x
      roleLocationY: "600", // 角色位置y
      roleImg: "", // 角色图片
      playAfterStauts: "2", // 播放完之后状态
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框
      hiddenStatus: "1", // 播放完是否应藏的状态
    }
  },
  dynamicEffect:'./assets/images/shuazi.json',
  feedbackLists:[
    {
      positiveFeedback: '2',
      feedbackList:[
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./image/prefect.json', mp3:'./audio/prefect.mp3' },
        { id:'1', json:'./image/goldCoin.json', mp3:'./audio/goldCoin.mp3' },
        { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
        { id:'9', json: './image/feedback.json', mp3: './audio/feedback01.mp3' },
      ],
      feedbackObj: { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
      feedback:'',
      feedbackAudio:'',
      feedbackName:'整体反馈',
      key:'feedKey1',
    }
  ]
};
