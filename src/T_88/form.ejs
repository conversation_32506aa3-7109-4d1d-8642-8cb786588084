<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>BHTY0001FT_点击变化听音FT</title>
    <link rel="stylesheet" href="form/css/style.css">
    <script src='form/js/jquery-2.1.1.min.js'></script>
    <script src='form/js/vue.min.js'></script>
</head>
<body>
    <div id="container">
        <div class="edit-form">
            <div class="h-title">BHTY0001FT_点击变化听音FT</div>

            <% include ./src/common/template/common_head %>

        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <% include ./src/common/template/multyDialog/form.ejs %>

                    <div class="c-group">
                        <div class="c-title">设置点击内容(最多12组)</div>
                        <div class="c-area upload img-upload">
                            <ul>
                                <li v-for="(item, index) in configData.source.options">
                                    <div class="c-well">
                                        <span class="dele-tg-btn" style="z-index: 9;position: relative"
                                            v-show="configData.source.options.length > 1"
                                            v-on:click="deleConfig(item)"></span>
                                        <div class="field-wrap">
                                            <label class="field-label">初始图片{{index + 1}}
                                                <em>*</em>
                                            </label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-initPic'+index" class="btn btn-show upload"
                                                    v-if="!item.initPic">上传</label>
                                                <label :for="'content-pic-initPic'+index" class="btn upload re-upload"
                                                    v-if="item.initPic">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG、JSON格式小于等于240Kb</em></div>
                                            <input type="file" v-bind:key="Date.now()" class="btn-file"
                                                :id="'content-pic-initPic'+index" size="" accept=".jpg,.png,.json"
                                                @change="feedbackUpload($event,item,'initPic',350)">
                                        </div>

                                        <div class="img-preview" v-if="item.initPic">
                                            <img :src="item.initPic.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.initPic"
                                                alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete" @click="delPrew(item,'initPic')">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item" style="margin-top: 10px">
                                            <span>初始图片{{index+1}}位置<em>*</em>&nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.initPicLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.initPicLocation.y">
                                            <br>
                                        </div>

                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label">目标图片{{index + 1}}<em>*</em></label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-tagPic'+index" class="btn btn-show upload"
                                                    v-if="!item.tagPic">上传</label>
                                                <label :for="'content-pic-tagPic'+index" class="btn upload re-upload"
                                                    v-if="item.tagPic">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG、JSON格式小于等于240Kb</em></div>
                                            <input type="file" v-bind:key="Date.now()" class="btn-file"
                                                :id="'content-pic-tagPic'+index" size="" accept=".jpg,.png,.json"
                                                @change="feedbackUpload($event,item,'tagPic',350)">
                                        </div>
                                        <div class="img-preview" v-if="item.tagPic">
                                            <img :src="item.tagPic.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.tagPic"
                                                alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete" @click="delPrew(item,'tagPic')">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item">
                                            <span>目标图片{{index + 1}}位置<em>*</em> &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.tagPicLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.tagPicLocation.y">
                                            <br>
                                        </div>

                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label">文字图片{{index + 1}}</label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-textPic'+index" class="btn btn-show upload"
                                                    v-if="!item.textPic">上传</label>
                                                <label :for="'content-pic-textPic'+index" class="btn upload re-upload"
                                                    v-if="item.textPic">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG格式，最大尺寸不超过1000x500，小于等于50Kb </em></div>
                                            <input type="file" v-bind:key="Date.now()+'textPic'" class="btn-file"
                                                :id="'content-pic-textPic'+index" size="1000*500" accept=".jpg,.png"
                                                @change="imageUpload($event,item,'textPic',50)">
                                        </div>
                                        <div class="img-preview" v-if="item.textPic">
                                            <img :src="item.textPic" alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete" @click="delPrew(item,'textPic')">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item">
                                            <span>文字图片{{index + 1}}位置: &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.textPicLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.textPicLocation.y">
                                            <br>
                                        </div>


                                        <!-- 目标图片音频 -->
                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label" for="">目标图片音频</label>
                                            <span>
                                                <label :for="'audio-upload-audio'+index" class="btn btn-show upload"
                                                    v-if="item.audio==''?true:false">上传</label>
                                                <label :for="'audio-upload-audio'+index"
                                                    class="btn upload re-upload mar"
                                                    v-if="item.audio!=''?true:false">重新上传</label>
                                            </span>
                                            <div style="color: red">mp3格式，小于等于50Kb</div>

                                            <div class="audio-preview" v-show="item.audio!=''?true:false">
                                                <div class="audio-tools">
                                                    <p v-show="item.audio!=''?true:false">{{item.audio}}</p>
                                                </div>
                                                <span class="play-btn" v-on:click="play($event)">
                                                    <audio v-bind:src="item.audio"></audio>
                                                </span>
                                            </div>
                                            <span class="btn btn-audio-dele" v-show="item.audio!=''?true:false"
                                                v-on:click="item.audio=''">删除</span>
                                            <input type="file" :id="'audio-upload-audio'+index" class="btn-file upload"
                                                size="" accept=".mp3" v-on:change="audioUpload($event,item,'audio',50)"
                                                v-bind:key="Date.now()+'audio'">
                                        </div>

                                        <div style="margin-top: 20px">
                                            <span>顺序展示动态小手</span>
                                            <label class="inline-label" :for="'imgTypeEffect'+index"
                                                style="display: inline-block;margin-left: 10px"><input type="radio"
                                                    :name="'imgTypeEffect'+index" value="1" v-model="item.showHand">
                                                是</label>
                                            <label class="inline-label" :for="'imgTypeEffect'+index"
                                                style="display: inline-block;margin-left: 10px"><input type="radio"
                                                    :name="'imgTypeEffect'+index" value="2" v-model="item.showHand">
                                                否</label>
                                        </div>
                                        <div class="field-wrap-item" v-if="item.showHand === '1'">
                                            <span>小手位置<em>*</em> &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.handLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.handLocation.y">
                                            <br>
                                        </div>

                                        <button class="add-tg-btn" style="margin-top: 20px" v-on:click="addConfig"
                                            v-if="index < 11 && index === configData.source.options.length -1">+</button>
                                    </div>
                                </li>
                            </ul>
                            <div class="field-wrap">
                                <label class="field-label">点击后动效</label>
                                <span class="field-wrap">
                                    <label for="content-dynamic-effect" class="btn btn-show upload"
                                        v-if="!configData.dynamicEffect">上传json</label>
                                    <label for="content-dynamic-effect" class="btn upload re-upload"
                                        v-if="configData.dynamicEffect">重新上传</label>
                                </span>
                                <div style="color: red">小于等于200Kb</div>
                                <input type="file" v-bind:key="Date.now()+'effect'" class="btn-file"
                                    id="content-dynamic-effect" accept=".json" @change="clickEffectUpload($event)">
                            </div>
                            <div class="img-preview" v-if="configData.dynamicEffect">
                                <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt="" />
                                <div class="img-tools">
                                    <span class="btn btn-delete" @click="configData.dynamicEffect=''">删除</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 头像上墙 -->
                    <% include ./src/common/template/avatarUpWall/form %>
                    <!-- 反馈动画添加 -->
                    <% include ./src/common/template/feedbackAnimation/form %>
                        <button class="send-btn" v-on:click="onSend">提交</button>
        </div>
        <div class="edit-show">
            <div class="show-fixed">
                <div class="show-img">
                    <img src="form/img/preview.png?v=<%= new Date().getTime() %>" alt="">
                </div>
                <ul class="show-txt">
                    <li><em>图片格式：</em>JPG/PNG/GIF</li>
                    <li><em>声音格式：</em>MP3/WAV</li>
                    <li><em>视频格式：</em>MP4</li>
                    <li>带有“ * ”号为必填项</li>
                </ul>
            </div>
        </div>
    </div>
</body>
<script src='form/js/form.js?v=<%= new Date().getTime() %>'></script>
</html>
