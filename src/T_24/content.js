var configData = {
	bg: '',
	desc: "czxc",
	title: "zc",
	// tImg: '//cdn.51talk.com/apollo/images/b164806a1b826b1b386f5496478573fe.png',
	tImg: './image/b164806a1b826b1b386f5496478573fe.png',
	tImgX: 1340,
	tImgY: 15,
	tg: [
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		},
		{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}
	],
	level:{
		high:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}],
		low:[{
			content: "eegeg appy family. My father i appy family. My father i",
			title: "weqwf appy family. My father i"
		}]
	},
	source: {
		person: './assets/images/person.png', // 动态主元素(人)
		toy: './assets/images/toy.png', //动态辅助元素(人手里的玩具)
		text: './assets/images/bubble.png',//（话泡边框 实际一般不使用）
		word: '22222', // 未使用
		audio: './assets/audio/wrong.mp3',
		isMax: false
	}
};
