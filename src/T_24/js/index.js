"use strict"
import '../../common/js/common_1v1.js'
import "../../common/js/teleprompter.js"

$(function () {
    // 控制器是否显示授权按钮
    window.h5Template = {
        hasPractice: '0'
    }

    const h5SyncActions = parent.window.h5SyncActions;
    const isSync = h5SyncActions && h5SyncActions.isSync;

    const self_frame_id = $(window.frameElement).attr('id');
    /**
     * 创建dom元素
     */
    let source = configData.source

    if (configData.bg == '') {
        $(".container").css({ 'background-image': 'url(./image/defaultBg.png)' })
    }

    if (source.toy) {
        $('.toy').css({
            'background': `url(${source.toy}) no-repeat center`,
            'background-size': 'contain'
        })
    }

    $('.person .box').css({
        'background': `url(${source.person}) no-repeat`,
        'background-size': '18rem auto'
    })

    if (source.text) {
        $('.text').attr('src', source.text)
    }
    let audio = $('.main-box audio');
    if (source.audio) {
        $('.ans-audio').show();
        audio.attr({
            'src': source.audio
        })

    }
    // 老师端或者浏览器环境下显示小手图标
    isShowHand();
    function isShowHand() {
        if (isSync) { // 上课模式
            if (window.frameElement.getAttribute('user_type') == 'stu') { // 如果当前窗口的frameElement属性user_type为'stu'
                $('.btns').hide(); // 隐藏小手
            } else {
                $('.btns').show(); // 显示小手
            }
        } else {
            var hrefParam = parseURL("http://www.example.com"); // 解析URL，获取参数
            if (top.frames[0] && top.frames[0].frameElement) { // 如果存在上层框架元素
                hrefParam = parseURL(top.frames[0].frameElement.src); // 解析上层框架元素的URL，获取参数
            }
            var role_num = hrefParam.params['role']; // 获取参数中的role参数值
            // 解析URL的工具函数
            function parseURL(url) {
                var a = document.createElement('a'); // 创建一个a元素
                a.href = url; // 设置a元素的URL属性为传入的url参数
                return {
                    source: url, // 源URL
                    protocol: a.protocol.replace(':', ''), // URL的协议
                    host: a.hostname, // URL的主机名
                    port: a.port, // URL的端口号
                    query: a.search, // URL的查询字符串
                    params: (function () {
                        var ret = {}, // 存储参数的对象
                            seg = a.search.replace(/^\?/, '').split('&'), // 将查询字符串拆分为键值对数组
                            len = seg.length, i = 0, s // 键值对数组的长度、初始值和临时变量
                        for (; i < len; i++) {
                            if (!seg[i]) { continue; } // 如果当前键值对为空，则跳过
                            s = seg[i].split('='); // 将键值对按等号拆分出键和值
                            ret[s[0]] = s[1]; // 将键值对存储到ret对象中
                        }
                        return ret; // 返回存储参数的对象
                    })(),
                    file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1], // 文件路径名
                    hash: a.hash.replace('#', ''), // URL的哈希值
                    path: a.pathname.replace(/^([^\/])/, '/$1'), // URL的路径部分
                    relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1], // 相对路径
                    segments: a.pathname.replace(/^\//, '').split('/') // URL的路径部分的路径段
                }
            }
            if (role_num == '1' || role_num == undefined) { // 如果角色为1或未定义
                $('.btns').show(); // 显示所有按钮
            } else if (role_num == '2') { // 如果角色为2
                $('.btns').hide(); // 隐藏所有按钮
            }
        }
    }
    // 开始动画（放大）
    function startAnimation() {
        if (source.isMax) {
            setTimeout(() => {
                $('.main-box').css({
                    'transform': 'scale(1.7)'
                })

                setTimeout(() => {
                    fnAni()
                }, 1500)
            }, 500)
        } else {
            fnAni();
        }
        function fnAni() {
            if (source.toy) {
                $('.toy').addClass('shake')
            }
            if (source.text) {
                $('.textBox').show();
            }
            if (source.audio) {
                $('.box').addClass('talk')
                let isSync_bool = (self_frame_id == 'h5_course_self_frame' || !isSync)
                if (isSync_bool) {
                    // audio[0].play();
                    SDK.playRudio({
                        index: audio[0],
                        syncName: $('.main-box audio').attr("data-syncaudio")
                    })
                    $('.audioPng').hide();
                    $('.audioGif').show();
                    audio.on('ended', () => {
                        $('.audioPng').show();
                        $('.audioGif').hide();
                        $('.box').removeClass('talk');
                        $('.main-box').removeClass('transition');
                        if (source.toy) {
                            $('.toy').removeClass('shake');
                        }
                        isShowHand();
                    })
                }
            } else {
                if (source.toy) {
                    $('.toy').removeClass('shake');
                }
                $('.box').removeClass('talk');
                $('.main-box').removeClass('transition')
                isShowHand()
            }
        }
    }

    let itemHandClick = true;
    $('.btns').on('click touchstart', function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (itemHandClick) {
            itemHandClick = false;
            if (!isSync) {
                $(this).trigger('handClick')
                return;
            }
            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                funcType: 'audio',
                syncName: 'handClick',
                otherInfor: {
                    clickBtn: true
                },
                recoveryMode: '1'
            });
        }
    })
    $('.btns').on('handClick', function (e, message) {
        if (isSync && message.operate == '5') {
            let msg = message.data[0].value.syncAction.otherInfor.clickBtn;
            callbackFn(msg);
            SDK.setEventLock();
            return;
        }
        $('.main-box').css({
            'transform': 'scale(1)'
        })
        $('.textBox').hide();
        $('.btns').hide().addClass('again');
        $('.hand').hide();
        setTimeout(() => {
            $('.main-box').addClass('transition')
            startAnimation();
            itemHandClick = true;
            SDK.setEventLock();
        }, 100)
    })

    /**
     * 点击声音按钮
    */

    let itemAudioClick = true;
    $('.ans-audio').on('click touchstart', function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (itemAudioClick) {
            itemAudioClick = false;
            if (!isSync) {
                $(this).trigger('AudioClick')
                return;
            }
            SDK.bindSyncEvt({
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                funcType: 'audio',
                syncName: 'AudioClick',
                otherInfor: {
                    clickBtn: true
                },
                recoveryMode: '1'
            });
        }
    })
    $('.ans-audio').on('AudioClick', function (e, message) {
        if (isSync && message.operate == '5') {
            let msg = message.data[0].value.syncAction.otherInfor.clickBtn;
            callbackFn(msg);
            SDK.setEventLock();
            return;
        }
        if (self_frame_id == 'h5_course_self_frame' || !isSync) {
            $('.audioPng').hide();
            $('.audioGif').show();
            $('.box').addClass('talk');
            // audio[0].play()
            SDK.playRudio({
                index: audio[0],
                syncName: $('.main-box audio').attr("data-syncaudio")
            })
            audio.on('ended', () => {
                $('.box').removeClass('talk')
                $('.audioPng').show();
                $('.audioGif').hide();
                itemAudioClick = true;
            })
        }
    })

    /**
     * 断线重新进入教室回调
    */
    function callbackFn(msg) {
        if (msg) {
            if (isSync) {
                if (window.frameElement.getAttribute('user_type') == 'stu') {
                    $('.btns').hide();
                } else {
                    $('.btns').show().addClass('again');
                }
            }
            if (source.isMax) {
                $('.main-box').removeClass('transition')
                $('.main-box').css({
                    'transform': 'scale(1.7)'
                })
                if (source.text) {
                    $('.textBox').show();
                }
            } else {
                if (source.text) {
                    $('.textBox').show();
                }
            }
        }
    }
})
