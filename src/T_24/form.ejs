<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>TRE0003_单人话泡</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TRE0003_单人话泡</div>
			
			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<!-- 单选按钮 -->
					<div class="c-well">
						<div class="radioBox" style="display: flex;">
							<label class="field-label" for="">人物放大动画 <em>*</em> ：</label>
							<div class="field-tools" style="display: flex;">
								<div class="field-radio-wrap radio ">
									<div class="circle radio-outer " v-bind:class="{ active:configData.source.isMax == false}" v-on:click="isMax(false)">
										<div class="circle radio-inner pos-center"></div>
									</div>
									<label class='field-label' for="">不放大</label>
								</div>
								<div class="field-radio-wrap radio ">
									<div class="circle radio-outer " v-bind:class="{ active:configData.source.isMax == true}" v-on:click="isMax(true)">
										<div class="circle radio-inner pos-center"></div>
									</div>
									<label class='field-label' for="">放大</label>
								</div>
							</div>
						</div>
					</div>
					<!-- 上传人物图片 -->
					<div class="c-well">
						<div class="field-wrap">
							<label class="field-label">人物</label>
							<label for="person" class="btn btn-show upload" v-if="configData.source.person==''?true:false">上传</label>
							<label for="person" class="btn upload re-upload" v-if="configData.source.person!=''?true:false">重新上传</label>
							<span class='txt-info'><em>*</em> （尺寸：1800X850，大小：≤100KB）</span>
							<input type="file" 
								v-bind:key="Date.now()" class="btn-file" id="person" 
								size="1800*850" 
								accept=".gif,.jpg,.jpeg,.png"
								v-on:change="imageUpload($event,configData.source,'person',100)">
						</div>
						<div class="img-preview" v-if="configData.source.person!=''?true:false">
							<img v-bind:src="configData.source.person" alt="" />
							<div class="img-tools">
								<span class="btn btn-delete" v-on:click="configData.source.person=''">删除</span>
							</div>
						</div>
					</div>

					<!-- 上传玩具图片 -->
					<div class="c-well">
						<div class="field-wrap">
							<label class="field-label">玩具</label>
							<label for="toy" class="btn btn-show upload" v-if="configData.source.toy==''?true:false">上传</label>
							<label for="toy" class="btn upload re-upload" v-if="configData.source.toy!=''?true:false">重新上传</label>
							<span class='txt-info'>（尺寸：330X410，大小：≤50KB）</span>
							<input type="file" 
								v-bind:key="Date.now()" class="btn-file" id="toy" 
								size="330*410" 
								accept=".gif,.jpg,.jpeg,.png"
								v-on:change="imageUpload($event,configData.source,'toy',50)">
						</div>
						<div class="img-preview" v-if="configData.source.toy!=''?true:false">
							<img v-bind:src="configData.source.toy" alt="" />
							<div class="img-tools">
								<span class="btn btn-delete" v-on:click="configData.source.toy=''">删除</span>
							</div>
						</div>
					</div>

					<!-- 上传句子图片 -->
					<div class="c-well">
						<div class="field-wrap">
							<label class="field-label">句型图片1</label>
							<label for="text" class="btn btn-show upload" v-if="configData.source.text==''?true:false">上传</label>
							<label for="text" class="btn upload re-upload" v-if="configData.source.text!=''?true:false">重新上传</label>
							<span class='txt-info'>（尺寸：1000X400，大小：≤50KB）</span>
							<input type="file" 
								v-bind:key="Date.now()" class="btn-file" id="text" 
								size="1000*400" 
								accept=".gif,.jpg,.jpeg,.png"
								v-on:change="imageUpload($event,configData.source,'text',50)">
						</div>
						<div class="img-preview" v-if="configData.source.text!=''?true:false">
							<img v-bind:src="configData.source.text" alt="" />
							<div class="img-tools">
								<span class="btn btn-delete" v-on:click="configData.source.text=''">删除</span>
							</div>
						</div>
					</div>

					
					<!-- 上传句子 -->
					<div class="c-well">
						<div class="well-con">
							<label>句型
								<em>&nbsp;&nbsp;长度：≤300,&nbsp;&nbsp;需换行位置输入“&lt;br&gt;”符号</em>
							</label>
							<textarea name="" cols="56" rows="2" maxlength="300" placeholder="请输入句型一" v-model="configData.source.word"></textarea>
						</div>
					</div>

					<!-- 上传声音 -->
					<div class="c-well">
						<div class="field-wrap">
							<label class="field-label"  for="">上传音频</label>
							<span class='txt-info'>大小：≤50KB</span>
							<input type="file" accept=".mp3" class="btn-file"
								v-bind:key="Date.now()"  
								id="audio" 
								volume="50"
								@change="audioUpload($event,configData.source,'audio',50)">
						</div>
						<div class="field-wrap">
							<label for="audio" class="btn btn-show upload" v-if="!configData.source.audio">上传</label>
							<div class="audio-preview" v-show="configData.source.audio">
								<div class="audio-tools">
									<p v-show="configData.source.audio">{{configData.source.audio}}</p>
								</div>
								<span class="play-btn" v-on:click="play($event)">
									<audio v-bind:src="configData.source.audio"></audio>
								</span>
							</div>
							<label for="audio" class="btn upload btn-audio-dele" v-if="configData.source.audio" @click="configData.source.audio=''">删除</label>
							<label for="audio" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
						</div>
					</div>

				</div>
			</div>

			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>