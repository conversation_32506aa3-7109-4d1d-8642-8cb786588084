{"v": "5.9.6", "fr": 26, "ip": 0, "op": 73, "w": 600, "h": 600, "nm": "合成 4", "ddd": 0, "assets": [{"id": "comp_0", "nm": "bee 1", "fr": 25, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "空 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [720, 540, 0], "to": [3.5, 7.5, 0], "ti": [-8, -0.667, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 26, "s": [741, 585, 0], "to": [8, 0.667, 0], "ti": [3.5, 7.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 50.96, "s": [768, 544, 0], "to": [-3.5, -7.5, 0], "ti": [8, 0.667, 0]}, {"t": 72.8, "s": [720, 540, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 78, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "图层 3", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [-19]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 4').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2340.19, -179.927, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2340.19, -179.927, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.416, -37.341], [-3.249, 5.243], [22.359, 25.659], [-6.034, 3.811], [-3.811, -6.034]], "o": [[-1.518, 1.646], [8.95, -14.442], [-4.689, -5.381], [6.034, -3.811], [3.811, 6.034]], "v": [[2363.323, -195.987], [2360.567, -204.827], [2354.505, -267.934], [2358.53, -285.76], [2376.356, -281.735]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.035, 0.055], [-1.697, 1.197], [-1.775, 29.295], [10.402, 9.4], [-1.439, 1.59], [-1.59, -1.436], [0.849, -13.878], [1.661, -1.169], [1.233, 1.751]], "o": [[-1.096, -1.735], [0.376, -0.265], [0.692, -11.404], [-1.578, -1.438], [1.437, -1.588], [12.245, 11.077], [-2.017, 33.025], [-1.75, 1.232], [-0.038, -0.055]], "v": [[2334.755, -175.906], [2335.802, -181.145], [2375.218, -237.307], [2360.582, -268.667], [2360.307, -274.146], [2365.784, -274.421], [2382.959, -236.819], [2340.266, -174.803], [2334.865, -175.742]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "图层 4", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [-19]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 4').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2290.168, -209.997, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2290.168, -209.997, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[19.64, -50.71], [-1.18, 6.499], [32.346, 16.793], [-4.517, 6.165], [-6.165, -4.517]], "o": [[-0.866, 2.236], [3.251, -17.901], [-6.783, -3.521], [4.517, -6.165], [6.165, 4.517]], "v": [[2304.557, -232.394], [2298.343, -240.121], [2267.541, -300.621], [2264.557, -319.962], [2283.898, -322.946]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.057, 0.041], [-1.221, 1.858], [9.726, 29.885], [14.053, 5.284], [-0.81, 2.149], [-2.147, -0.807], [-4.599, -14.161], [1.196, -1.816], [1.915, 1.261]], "o": [[-1.773, -1.299], [0.271, -0.411], [-3.785, -11.634], [-2.137, -0.814], [0.808, -2.146], [16.547, 6.232], [10.948, 33.697], [-1.261, 1.914], [-0.059, -0.04]], "v": [[2283.972, -201.177], [2282.959, -206.808], [2300.196, -278.232], [2273.308, -303.735], [2270.885, -309.087], [2276.234, -311.51], [2308.101, -280.783], [2289.896, -202.24], [2284.146, -201.057]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "图层 5", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [61]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 5').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2183.504, 122.237, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2183.504, 122.237, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.646, -1.838], [3.9, 1.569], [-2.043, 3.904], [-0.166, 5.519], [1.998, 3.559], [0, 0], [-1.778, -10.391]], "o": [[-1.495, 4.254], [-3.9, -1.569], [1.132, -2.162], [0.134, -4.472], [-2.416, -4.303], [0, 0], [1.396, 8.161]], "v": [[2196.083, 157.132], [2187.315, 162.363], [2183.938, 152.863], [2186.916, 140.412], [2183.945, 128.22], [2188.878, 122.349], [2195.834, 140.156]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.316, 0.345], [-0.61, 1.345], [0.146, 0.171], [-1.496, 1.284], [-1.304, -1.475], [9.792, -21.575], [1.799, 0.815]], "o": [[-0.939, -1.026], [7.884, -17.371], [-1.281, -1.5], [1.499, -1.283], [0.719, 0.834], [-0.815, 1.8], [-0.453, -0.206]], "v": [[2187.356, 161.06], [2186.736, 157.165], [2180.139, 122.541], [2180.528, 117.501], [2185.565, 117.878], [2193.25, 160.12], [2188.515, 161.901]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "图层 6", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [-45]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 6').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2279.397, 40.79, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2279.397, 40.79, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.772, -1.788], [3.781, 1.837], [-2.31, 3.752], [-0.549, 5.494], [1.745, 3.689], [0, 0], [-1.05, -10.489]], "o": [[-1.787, 4.139], [-3.781, -1.837], [1.279, -2.078], [0.445, -4.452], [-2.11, -4.46], [0, 0], [0.825, 8.239]], "v": [[2289.624, 78.327], [2280.513, 82.934], [2277.806, 73.223], [2281.643, 61.009], [2279.528, 48.64], [2284.858, 43.126], [2290.558, 61.375]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.291, 0.366], [-0.702, 1.299], [0.134, 0.181], [-1.582, 1.177], [-1.198, -1.562], [11.27, -20.841], [1.738, 0.939]], "o": [[-0.865, -1.089], [9.074, -16.78], [-1.173, -1.585], [1.584, -1.176], [0.659, 0.882], [-0.939, 1.739], [-0.438, -0.237]], "v": [[2280.645, 81.637], [2280.298, 77.709], [2276.127, 42.709], [2276.865, 37.708], [2281.864, 38.436], [2286.591, 81.11], [2281.743, 82.557]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "图层 7", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [28]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 7').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2093.999, 19.982, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2093.999, 19.982, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.3, -1.81], [3.777, 2.978], [-3.499, 3.628], [-1.957, 6.006], [1.049, 4.55], [0, 0], [1.392, -11.98]], "o": [[-3.01, 4.189], [-3.777, -2.978], [1.938, -2.01], [1.586, -4.867], [-1.268, -5.501], [0, 0], [-1.093, 9.41]], "v": [[2095.411, 63.711], [2084.101, 66.632], [2083.451, 55.116], [2090.726, 42.404], [2091.386, 28.062], [2098.692, 23.203], [2100.6, 44.992]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.236, 0.48], [-1.103, 1.28], [0.106, 0.235], [-2.056, 0.929], [-0.957, -2.039], [17.693, -20.537], [1.712, 1.474]], "o": [[-0.701, -1.428], [14.245, -16.536], [-0.924, -2.058], [2.058, -0.927], [0.521, 1.147], [-1.475, 1.714], [-0.431, -0.372]], "v": [[2084.565, 65.215], [2085.138, 60.74], [2089.036, 20.602], [2091.084, 15.193], [2096.493, 17.228], [2091.34, 66.08], [2085.568, 66.511]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "图层 8", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [-54]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 6').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2156.524, -56.899, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2156.524, -56.899, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.3, -1.81], [3.777, 2.978], [-3.499, 3.628], [-1.957, 6.006], [1.049, 4.55], [0, 0], [1.392, -11.98]], "o": [[-3.01, 4.189], [-3.777, -2.978], [1.938, -2.01], [1.586, -4.867], [-1.268, -5.501], [0, 0], [-1.093, 9.41]], "v": [[2157.937, -14.17], [2146.627, -11.249], [2145.977, -22.765], [2153.252, -35.477], [2153.912, -49.819], [2161.218, -54.678], [2163.126, -32.889]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.236, 0.48], [-1.103, 1.28], [0.106, 0.235], [-2.056, 0.929], [-0.957, -2.039], [17.693, -20.537], [1.712, 1.474]], "o": [[-0.701, -1.428], [14.245, -16.536], [-0.924, -2.058], [2.058, -0.927], [0.521, 1.147], [-1.475, 1.714], [-0.431, -0.372]], "v": [[2147.091, -12.666], [2147.664, -17.141], [2151.562, -57.279], [2153.61, -62.688], [2159.019, -60.653], [2153.866, -11.801], [2148.094, -11.37]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "图层 9", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2289.102, -59.771, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2289.102, -59.771, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.372, 2.439], [-2.439, 7.372]], "o": [[-2.439, 7.372], [-7.372, -2.439], [0, 0]], "v": [[2302.809, -60.178], [2285.043, -51.246], [2276.112, -69.011]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.317647069693, 0.184313729405, 0.066666670144, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7.394, "ix": 5}, "lc": 2, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "图层 10", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2241.639, -122.557, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2241.639, -122.557, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.297, -17.835], [17.835, -1.297], [1.297, 17.835], [-17.835, 1.297]], "o": [[1.298, 17.835], [-17.835, 1.297], [-1.298, -17.835], [17.835, -1.297]], "v": [[2273.932, -124.906], [2243.988, -90.264], [2209.346, -120.208], [2239.29, -154.85]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.384313732386, 0.29411765933, 0.235294118524, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "图层 11", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2235.034, -121.061, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2235.034, -121.061, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.988, -27.331], [27.332, -1.988], [1.988, 27.331], [-27.332, 1.988]], "o": [[1.989, 27.332], [-27.331, 1.988], [-1.989, -27.332], [27.331, -1.988]], "v": [[2284.522, -124.661], [2238.634, -71.573], [2185.546, -117.461], [2231.435, -170.549]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.831372559071, 0.658823549747, 0.243137255311, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.153, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "图层 12", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2355.786, -97.234, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2355.786, -97.234, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.114, -15.315], [15.315, -1.114], [1.114, 15.315], [-15.315, 1.114]], "o": [[1.114, 15.315], [-15.315, 1.114], [-1.114, -15.315], [15.315, -1.114]], "v": [[2383.516, -99.252], [2357.803, -69.504], [2328.056, -95.217], [2353.769, -124.964]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.384313732386, 0.29411765933, 0.235294118524, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "图层 13", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2346.402, -99.702, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2346.402, -99.702, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.707, -23.469], [23.47, -1.707], [1.707, 23.469], [-23.47, 1.707]], "o": [[1.708, 23.47], [-23.469, 1.707], [-1.708, -23.47], [23.469, -1.707]], "v": [[2388.897, -102.793], [2349.493, -57.207], [2303.907, -96.61], [2343.311, -142.197]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.831372559071, 0.658823549747, 0.243137255311, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4.153, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "图层 14", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2353.535, -46.761, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2353.535, -46.761, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.65, -9.34], [17.164, 4.87], [-2.65, 9.34], [-17.164, -4.871]], "o": [[-2.65, 9.34], [-17.164, -4.871], [2.65, -9.34], [17.164, 4.871]], "v": [[2384.613, -37.941], [2348.736, -29.848], [2322.457, -55.58], [2358.334, -63.673]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.92549020052, 0.509803950787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "图层 15", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [2192.702, -87.181, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2192.702, -87.181, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.468, -10.498], [19.292, 8.21], [-4.468, 10.498], [-19.292, -8.21]], "o": [[-4.468, 10.498], [-19.292, -8.21], [4.468, -10.498], [19.292, 8.21]], "v": [[2227.633, -72.315], [2184.613, -68.172], [2157.77, -102.047], [2200.791, -106.19]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.92549020052, 0.509803950787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "图层 16", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [54.689, -39.477, 0], "to": [0, 2.833, 0], "ti": [0, -2.833, 0]}, {"t": 9.36, "s": [54.689, -22.477, 0]}], "ix": 2, "l": 2, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 16').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "a": {"a": 0, "k": [2180.398, -41.231, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-36.863, -26.852], [22.268, -2.937], [-11.794, 40.821], [-7.203, 3.607]], "o": [[-20.287, 10.156], [-156.608, -96.921], [6.618, -4.848], [-11.515, 112.527]], "v": [[2221.17, 123.698], [2155.081, 141.954], [2029.994, -116.38], [2050.765, -129.066]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.752941191196, 0.501960813999, 0.1254902035, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.632, 15.356], [-4.785, 3.787], [-43.567, -5.465], [14.311, -12.317], [56.788, 54.356]], "o": [[3.957, -1.276], [47.784, 149.789], [-10.469, 12.318], [-25.112, -1.52], [-61.804, -59.157]], "v": [[2095.816, -143.144], [2109.52, -150.407], [2333.778, 38.264], [2298.771, 74.107], [2153.164, -4.066]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.752941191196, 0.501960813999, 0.1254902035, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[54.09, 45.697], [51.952, -61.495], [3.176, -4.821], [0, 0], [0, 0], [0, 0], [22.277, -26.369], [-32.633, -53.684], [-42.792, -4.488], [0, 0], [0, 0]], "o": [[-61.494, -51.952], [-3.818, 4.52], [0, 0], [-11.111, 17.718], [0, 0], [-31.422, 6.202], [-27.999, 33.142], [28.122, 46.262], [177.965, 9.703], [0, 0], [33.129, -59.646]], "v": [[2334.414, -192.377], [2129.001, -175.099], [2118.551, -161.058], [2117.756, -159.911], [2089.092, -142.005], [2087.91, -141.795], [2004.601, -92.707], [1992.67, 74.107], [2104.445, 143.921], [2367.868, -10.378], [2367.852, -10.388]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.890196084976, 0.721568644047, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.52, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.854901969433, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "图层 17", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [17]}, {"t": 2.08, "s": [-37]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 17').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2102.831, -81.234, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2102.831, -81.234, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.989, -44.704], [44.704, -11.989], [11.989, 44.704], [-44.704, 11.989]], "o": [[11.989, 44.704], [-44.704, 11.989], [-11.989, -44.704], [44.704, -11.989]], "v": [[2144.65, -229.761], [2104.593, -55.599], [1982.764, -186.345], [2041.999, -288.996]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.52, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.949019610882, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "图层 18", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-18]}, {"t": 2.08, "s": [36]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 18').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [2245.881, 20.154, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [2245.881, 20.154, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[35.736, -8.915], [-8.915, -35.736], [-35.736, 8.915], [8.915, 35.736]], "o": [[-35.736, 8.915], [8.915, 35.736], [35.736, -8.915], [-8.915, -35.736]], "v": [[2331.67, -73.265], [2225.942, 21.844], [2363.955, 56.146], [2412.518, -24.702]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 15.52, "ix": 5}, "lc": 1, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058832645, 0.949019610882, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "图层 19", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 9.36, "s": [21]}], "ix": 10, "x": "var $bm_rt;\nvar fx = thisComp.layer('图层 19').effect('循环器');\nvar isIn = fx(2).value;\nvar isOut = fx(7).value;\nvar inType = fx(3).value;\nvar outType = fx(8).value;\nvar outNumKeyframes = fx(9).value;\nvar inNumKeyframes = fx(4).value;\nvar result = value;\nif (numKeys > 1) {\n    if (time < key(1).time && isIn) {\n        if (inType == 1)\n            result = loopIn('cycle', outNumKeyframes);\n        else if (inType == 2)\n            result = loopIn('offset', outNumKeyframes);\n        else if (inType == 3)\n            result = loopIn('pingpong', outNumKeyframes);\n        else if (inType == 4)\n            result = loopIn('continue');\n    } else if (time > key(numKeys).time && isOut) {\n        if (outType == 1)\n            result = loopOut('cycle', outNumKeyframes);\n        else if (outType == 2)\n            result = loopOut('offset', outNumKeyframes);\n        else if (outType == 3)\n            result = loopOut('pingpong', outNumKeyframes);\n        else if (outType == 4)\n            result = loopOut('continue');\n    }\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [1998.604, 20.066, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [1998.604, 20.066, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "循环器", "np": 12, "mn": "Pseudo/DUIK looper", "ix": 1, "en": 1, "ef": [{"ty": 6, "nm": "In", "mn": "Pseudo/DUIK looper-0001", "ix": 1, "v": 0}, {"ty": 7, "nm": "In", "mn": "Pseudo/DUIK looper-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0004", "ix": 4, "v": {"a": 0, "k": 0, "ix": 4}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0005", "ix": 5, "v": 0}, {"ty": 6, "nm": "Out", "mn": "Pseudo/DUIK looper-0006", "ix": 6, "v": 0}, {"ty": 7, "nm": "Out", "mn": "Pseudo/DUIK looper-0007", "ix": 7, "v": {"a": 0, "k": 1, "ix": 7}}, {"ty": 7, "nm": "Type", "mn": "Pseudo/DUIK looper-0008", "ix": 8, "v": {"a": 0, "k": 3, "ix": 8}}, {"ty": 0, "nm": "Number of keyframes", "mn": "Pseudo/DUIK looper-0009", "ix": 9, "v": {"a": 0, "k": 0, "ix": 9}}, {"ty": 6, "nm": "", "mn": "Pseudo/DUIK looper-0010", "ix": 10, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.03, 2.717], [0, 0], [-0.176, -3.867], [0, 0], [3.794, 0.508], [0, 0]], "o": [[0, 0], [3.759, -0.926], [0, 0], [0.174, 3.824], [0, 0], [-11.259, -1.506]], "v": [[1926.972, 18.326], [2004.726, -0.825], [2012.329, 4.857], [2014.055, 42.716], [2007.106, 49.083], [1928.068, 38.508]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.470588237047, 0.333333343267, 0.23137255013, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 78, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "bee 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [226, 338, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [720, 540, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 1440, "h": 1080, "ip": 0, "op": 72.8, "st": 0, "bm": 0}], "markers": []}