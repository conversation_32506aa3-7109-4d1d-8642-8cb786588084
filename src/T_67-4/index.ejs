<!DOCTYPE html>
<html lang="en">
  <head>
    <% var title="LCH0003FTMSQ_对话听音弹窗FT免授权版"; %> <%include
    ./src/common/template/index_head %>
    <!-- 提示模式 -->
    <% include ./src/common/template/hint/index.ejs %>
  </head>

  <body>
    <div class="container" id="container" data-syncresult="1">
      <section class="commom">
        <div class="desc">123132</div>
      </section>
      <section class="main">
        <!-- 弹窗 -->
        <div class="modal" id="carousel-modal" data-syncactions="carouselModal">
          <div class="modal-close" data-syncactions="carouselModal">

          </div>
          <div class="modal-content">
            <div class="modal-title">
              <div class="title-carousel-inner"></div>
            </div>
            <div class="modal-content-img" id="dialogBg"></div>
            <div class="carousel">
              <div class="carousel-inner"></div>
            </div>
            <div class="prev-btn" data-syncactions="prevBtn"></div>
            <div class="next-btn" data-syncactions="nextBtn"></div>
          </div>
        </div>
        <!-- 选项 -->
        <ul class="optionUl"></ul>
        <!-- 像素格 -->
        <div class="boxList">
          <ul class="boxUl"></ul>
        </div>

        <!-- 终局页 -->
        <div class="tea-stu-not-in"></div>
        <div class="overBtn" data-syncactions="overBtn"></div>
        <div class="outBtn" data-syncactions="outBtn"></div>
        <div class="runing" data-syncactions="runing"></div>
      </section>

      <div class="doneTip tip hide">
        <p>It's S's turn, you can't play now.</p>
      </div>
      <script type="text/javascript">
        document.documentElement.addEventListener(
          "touchstart",
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault();
            }
          },
          false
        );
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener(
          "touchend",
          function (event) {
            var now = Date.now();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
      </script>
    </div>
    <!-- 图片预加载 -->
    <div id="preload-01"></div>
    <div id="preload-02"></div>
    <div id="preload-03"></div>
    <%include ./src/common/template/index_bottom %>
    <%include ./src/common/template/lottie %>
  </body>
</html>
