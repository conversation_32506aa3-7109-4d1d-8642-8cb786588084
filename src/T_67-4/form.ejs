<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>LCH0003FTMSQ_对话听音弹窗FT免授权版</title>
    <link
            rel="stylesheet"
            href="./form/css/style.css?v=<%= new Date().getTime() %>"
    />
    <script src="./form/js/jquery-2.1.1.min.js"></script>
    <script src="./form/js/vue.min.js"></script>
</head>

<body>
<div id="container">
    <div class="edit-form">
        <h3 class="module-title">LCH0003FTMSQ_对话听音弹窗FT免授权版</h3>

        <!-- 公共区域 -->
        <% include ./src/common/template/common_head_nontitle %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <!-- 对话框 -->
        <% include ./src/common/template/multyDialog/form %>
        <% include ./src/common/template/memory/form.ejs %>
<!--记忆组件图片-->
        <div class="c-group">
            <div class="c-title">记忆组件图片</div>
        </div>

        <!--  每轮游戏设计 -->
        <div class="c-group">
            <div class="c-title">每轮游戏设计</div>
            <div class="c-desc">
                <label class="text">
                    - 每轮游戏1个题干声音，1个正确选项图片，1个遮罩图片。</label
                >
                <label class="text">
                    - 学员点击“遮罩图片”、“正确选项图片”，系统判断为正确;
                    因此请设置合理大小的遮罩图片。</label
                >
            </div>
            <!-- <div class="field-wrap-models">
                <label class="field-label" style="width: 100px;">*弹窗样式（尺寸及数字限制）</label>
                <select id="teachTime" v-model="configData.source.showPopup" style="width: 150px;">
                  <option name="optive" value="large">大弹窗</option>
                  <option name="optive" value="small">小弹窗</option>
                </select>
              </div> -->
            <div
                    class="c-area"
                    v-for="(item,index) in configData.source.gameList"
            >
            <span
                    class="dele-tg-btn"
                    v-on:click="delOption(configData.source.gameList,item)"
                    v-show="configData.source.gameList.length>1"
            ></span>
                <h2>第{{ index + 1 }}轮</h2>
                <!-- 题干声音 -->
                <div class="c-well upload img-upload radio-group">
                    <div class="field-wrap">
                        <span class="fixed-width">题干声音</span>
                        <input
                                type="file"
                                accept=".mp3"
                                v-bind:key="Date.now()"
                                class="btn-file"
                                :id="'audio-audio-'+index"
                                v-on:change="audioUpload($event,item,'audio',200)"
                        />
                        <label
                                :for="'audio-audio-'+index"
                                class="btn btn-show upload"
                                v-if="!item.audio"
                        >上传</label
                        ><em class="game_em">大小：≤20KB</em>
                    </div>
                    <div class="audio-preview" v-show="item.audio">
                        <div class="audio-tools">
                            <p v-show="item.audio">{{ item.audio }}</p>
                        </div>
                        <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="item.audio"></audio>
                </span>
                    </div>
                    <div class="field-wrap">
                        <label
                                class="btn upload btn-audio-dele"
                                v-if="item.audio"
                                @click="item.audio=''"
                        >删除</label
                        >
                        <label
                                :for="'audio-audio-'+index"
                                class="btn upload re-upload"
                                v-if="item.audio"
                        >重新上传</label
                        >
                    </div>
                    <label>
                        <span class="game_span">题干声音长度</span>
                        <input
                                type="number"
                                class="c-input-txt game_input"
                                v-model="item.audioTime"
                                oninput="if(value<1000)value=1000"
                        />
                        <em class="game_em">毫秒，(1秒等于1000毫秒，不低于1000毫秒)</em>
                    </label>
                </div>
                <!-- 选项图片设置 -->
                <div class="c-well upload img-upload radio-group">
                    <div class="game">
                        <!-- 上传游戏图片 -->
                        <div class="field-wrap">
                            <span class="fixed-width">目标图片 * </span>
                            <input
                                    type="file"
                                    v-bind:key="Date.now()"
                                    class="btn-file"
                                    :id="'rightImg-'+index"
                                    size=""
                                    v-on:change="uploadTime($event,item,'rightImg',3000)"
                            />
                            <label
                                    :for="'rightImg-'+index"
                                    class="btn btn-show upload"
                                    v-if="item.rightImg==''?true:false"
                            >上传</label
                            >
                            <label
                                    :for="'rightImg-'+index"
                                    class="btn upload re-upload"
                                    v-if="item.rightImg!=''?true:false"
                            >重新上传</label
                            ><em class="game_em">jpg、png、json格式，≤3000KB</em>
                            <div class="img-preview" v-if="item.rightImg!=''?true:false">
                                <img v-bind:src="item.rightImg" alt=""/>
                                <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.rightImg=''"
                      >删除</span
                      >
                                </div>
                            </div>
                        </div>
                        <!-- 设置位置 -->
                        <label>
                            <span class="game_span">初始位置*</span>
                            <!-- <input type="number" class="c-input-txt game_input" v-model="item.rightPositionStart"> -->
                            X:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                                    v-model="item.rightPositionStartX"
                            />
                            Y:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                                    v-model="item.rightPositionStartY"
                            />
                            <br/>
                            <em class="game_em"
                            >数字,0<=x<=1920,0<=y<=1080，初始位置会被遮罩图片挡住</em
                            >
                        </label>
                        <label>
                            <span class="game_span">正确反馈位置*</span>
                            <!-- <input type="number" class="c-input-txt game_input" v-model="item.rightPositionEnd"> -->
                            X:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                                    v-model="item.rightPositionEndX"
                            />
                            Y:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                                    v-model="item.rightPositionEndY"
                            />
                            <br/>
                            <em class="game_em"
                            >数字,0<=x<=1920,0<=y<=1080，答案显示时，会覆盖在最上层</em
                            >
                        </label>
                    </div>
                </div>
                <!-- 遮罩图片设置 -->
                <div class="c-well upload img-upload radio-group">
                    <div class="game">
                        <!-- 上传游戏图片 -->
                        <div class="field-wrap">
                            <span class="fixed-width">遮罩图片 </span>
                            <input
                                    type="file"
                                    v-bind:key="Date.now()"
                                    class="btn-file"
                                    :id="'shadeImg-'+index"
                                    size=""
                                    v-on:change="imageUpload($event,item,'shadeImg',30)"
                            />
                            <label
                                    :for="'shadeImg-'+index"
                                    class="btn btn-show upload"
                                    v-if="item.shadeImg==''?true:false"
                            >上传</label
                            >
                            <label
                                    :for="'shadeImg-'+index"
                                    class="btn upload re-upload"
                                    v-if="item.shadeImg!=''?true:false"
                            >重新上传</label
                            ><em class="game_em">尺寸、比例自定义，大小：≤30KB</em>
                            <div class="img-preview" v-if="item.shadeImg!=''?true:false">
                                <img v-bind:src="item.shadeImg" alt=""/>
                                <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.shadeImg=''"
                      >删除</span
                      >
                                </div>
                            </div>
                        </div>
                        <div>
                            <span class="fixed-width">点击后的遮罩状态</span>
                            <label class="inline-label" for="clickMaskStatus"
                            ><input type="radio" value="1" v-model="item.clickMaskStatus"/>
                                隐藏</label
                            >
                            <label class="inline-label" for="clickMaskStatus"
                            ><input type="radio" value="2" v-model="item.clickMaskStatus"/>
                                不隐藏</label
                            >
                        </div>
                        <!-- 设置位置 -->
                        <label>
                            <span class="game_span">图片位置*</span>
                            <!-- <input type="number" class="c-input-txt game_input" v-model="item.shadePosition"> -->
                            X:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                                    v-model="item.shadePositionX"
                            />
                            Y:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                                    v-model="item.shadePositionY"
                            />
                            <br/>
                            <em class="game_em">数字,0<=x<=1920,0<=y<=1080</em>
                        </label>
                    </div>
                </div>

                <div class="c-well upload img-upload radio-group">
                    <div class="field-wrap">
                        <label class="field-label" style="width: 140px"
                        >是否展示动态小手</label
                        >
                        <select
                                id="teachTime"
                                v-model="item.hasHandAnimation"
                                style="width: 150px"
                        >
                            <option name="optive" value="1">展示</option>
                            <option name="optive" value="0">隐藏</option>
                        </select>
                    </div>
                    <!-- 小手位置 -->
                    <div class="field-wrap" v-if="item.hasHandAnimation === '1'">
                        <label class="field-label" style="width: 100px"
                        >小手位置</label
                        >
                        X:<input
                                type="number"
                                class="c-input-txt"
                                style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                v-model="item.handPositionX"
                        />
                        Y:<input
                                type="number"
                                class="c-input-txt"
                                style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                v-model="item.handPositionY"
                        />
                    </div>
                </div>

                <!-- 正确反馈图片设置 -->
                <div class="c-well" v-show="configData.source.isNeedLandMark == 2">
                    <div class="game">
                        <label>
                            <span class="game_span">正确反馈后， 标志物的位置</span>
                            <!-- <input type="number" class="c-input-txt game_input" v-model="item.landMarkPositionIngame"> -->
                            X:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                                    v-model="item.landMarkPositionIngameX"
                            />
                            Y:<input
                                    type="number"
                                    class="c-input-txt"
                                    style="
                      margin: 0 10px;
                      width: 60px !important;
                      display: inline-block;
                    "
                                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                                    v-model="item.landMarkPositionIngameY"
                            />
                            <br/>
                            <em class="game_em"
                            >数字,0<=x<=1920,0<=y<=1080，若有标志图图片则必填。</em
                            >
                        </label>
                    </div>
                </div>
                <!-- 弹窗 设置-->
                <div class="c-well upload img-upload radio-group">
                    <div class="modal-title">弹窗配置：</div>
                    <span class="fixed-width">是否全屏展示</span>
                    <label class="inline-label" for="modalMode"
                    ><input
                                type="radio"
                                value="1"
                                v-model="item.modalMode"
                        />否</label
                    >
                    <label class="inline-label" for="modalMode"
                    ><input type="radio" value="2" v-model="item.modalMode"/>
                        是（不展示弹窗）</label
                    >
                    <div>
                        <span class="fixed-width">是否展示半透明蒙版</span>
                        <label class="inline-label" for="showMask"
                        ><input type="radio" value="1" v-model="item.showMask"/>
                            展示</label
                        >
                        <label class="inline-label" for="showMask"
                        ><input
                                    type="radio"
                                    value="2"
                                    v-model="item.showMask"
                            />不展示</label
                        >
                    </div>
                    <div v-for="(modalItem,indexs) in item.modalList">
                        <div class="title-itme">
                            第{{ index + 1 }}轮弹窗内容配置：<span
                                    class="dele-tg-btn"
                                    v-on:click="delOption(item.modalList,modalItem)"
                                    v-show="item.modalList.length>0"
                            ></span>
                        </div>

                        <div class="field-wrap">
                            <span class="fixed-width">弹窗内容* </span>
                            <input
                                    type="file"
                                    v-bind:key="Date.now()"
                                    class="btn-file"
                                    :id="'content-modle-'+index+indexs"
                                    size=""
                                    v-on:change="uploadTime($event,modalItem,'content',240)"
                            />
                            <label
                                    :for="'content-modle-'+index+indexs"
                                    class="btn btn-show upload"
                                    v-if="modalItem.content==''?true:false"
                            >上传</label
                            >
                            <label
                                    :for="'content-modle-'+index+indexs"
                                    class="btn upload re-upload"
                                    v-if="modalItem.content!=''?true:false"
                            >重新上传</label
                            ><em class="game_em"
                            >JPG、PNG、JSON、MP4格式，大小：≤240KB</em
                            >
                            <div
                                    class="img-preview"
                                    v-if="modalItem.content!=''?true:false"
                            >
                                <img v-if="isImage(modalItem.content)" v-bind:src="modalItem.content" alt=""/>
                                <video controls v-else-if="isVideo(modalItem.content)">
                                    <source
                                            v-bind:src="modalItem.content"
                                            type="video/mp4"
                                    />
                                </video>
                                <div v-else class="json">
                                    {{ modalItem.content }}
                                </div>
                                <div class="img-tools">
                      <span
                              class="btn btn-delete"
                              v-on:click="modalItem.content=''"
                      >删除</span
                      >
                                </div>
                            </div>
                        </div>

                        <div class="field-wrap">
                            <span class="fixed-width">视频首帧图片</span>
                            <input
                                    type="file"
                                    v-bind:key="Date.now()"
                                    class="btn-file"
                                    accept=".jpg,.png"
                                    :id="'videoFirstFrame-'+index+indexs"
                                    size="1920*1080"
                                    v-on:change="imageUpload($event,modalItem,'videoFirstFrame',120)"
                            />
                            <label
                                    :for="'videoFirstFrame-'+index+indexs"
                                    class="btn btn-show upload"
                                    v-if="modalItem.videoFirstFrame==''?true:false"
                            >上传</label
                            >
                            <label
                                    :for="'videoFirstFrame-'+index+indexs"
                                    class="btn upload re-upload"
                                    v-if="modalItem.videoFirstFrame!=''?true:false"
                            >重新上传</label
                            ><em class="game_em"
                            >JPG、PNG格式，1920x1080，大小：≤120Kb</em
                            >
                            <div
                                    class="img-preview"
                                    v-if="modalItem.videoFirstFrame!=''?true:false"
                            >
                                <img v-bind:src="modalItem.videoFirstFrame" alt=""/>
                                <div class="img-tools">
                      <span
                              class="btn btn-delete"
                              v-on:click="modalItem.videoFirstFrame=''"
                      >删除</span
                      >
                                </div>
                            </div>
                        </div>

                        <div class="field-wrap top-margin">
                            <span class="fixed-width">文字图片 </span>
                            <input
                                    type="file"
                                    v-bind:key="Date.now()"
                                    class="btn-file"
                                    :id="'title-'+index+indexs"
                                    size=""
                                    accept=".jpg,.png"
                                    v-on:change="imageUpload($event,modalItem,'title',50)"
                            />
                            <label
                                    :for="'title-'+index+indexs"
                                    class="btn btn-show upload"
                                    v-if="modalItem.title==''?true:false"
                            >上传</label
                            >
                            <label
                                    :for="'title-'+index+indexs"
                                    class="btn upload re-upload"
                                    v-if="modalItem.title!=''?true:false"
                            >重新上传</label
                            ><em class="game_em">（图片文件）大窗口尺寸要求≤50kb</em>
                            <div
                                    class="img-preview"
                                    v-if="modalItem.title!=''?true:false"
                            >
                                <img v-bind:src="modalItem.title" alt=""/>
                                <div class="img-tools">
                      <span
                              class="btn btn-delete"
                              v-on:click="modalItem.title=''"
                      >删除</span
                      >
                                </div>
                            </div>

                            <div v-if="item.modalMode=='2'">
                                <label>
                                    <span class="game_span">文字图片位置</span>
                                    X:<input
                                            type="number"
                                            class="c-input-txt"
                                            style="
                          margin: 0 10px;
                          width: 60px !important;
                          display: inline-block;
                        "
                                            oninput="if(value>1920)value=1920;if(value<0)value=0"
                                            v-model="modalItem.titlePositionX"
                                    />
                                    Y:<input
                                            type="number"
                                            class="c-input-txt"
                                            style="
                          margin: 0 10px;
                          width: 60px !important;
                          display: inline-block;
                        "
                                            oninput="if(value>1080)value=1080;if(value<0)value=0"
                                            v-model="modalItem.titlePositionY"
                                    />
                                    <br/>
                                </label>
                            </div>
                        </div>
                        <div class="field-wrap top-margin">
                            <div class="field-wrap">
                                <span class="fixed-width">音频</span>
                                <input
                                        type="file"
                                        accept=".mp3"
                                        v-bind:key="Date.now()"
                                        class="btn-file"
                                        :id="'modalAudio-audio-modle-'+index+indexs"
                                        v-on:change="audioUpload($event,modalItem,'modalAudio',200)"
                                />
                                <label
                                        :for="'modalAudio-audio-modle-'+index+indexs"
                                        class="btn btn-show upload"
                                        v-if="!modalItem.modalAudio"
                                >上传</label
                                ><em class="game_em"
                                >大小：≤200KB，弹窗弹出完成后播放音频</em
                                >
                            </div>
                            <div class="audio-preview" v-show="modalItem.modalAudio">
                                <div class="audio-tools">
                                    <p v-show="modalItem.modalAudio">
                                        {{ modalItem.modalAudio }}
                                    </p>
                                </div>
                                <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="modalItem.modalAudio"></audio>
                    </span>
                            </div>
                            <div class="field-wrap">
                                <label
                                        class="btn upload btn-audio-dele"
                                        v-if="modalItem.modalAudio"
                                        @click="modalItem.modalAudio=''"
                                >删除</label
                                >
                                <label
                                        :for="'modalAudio-audio-modle-'+index+indexs"
                                        class="btn upload re-upload"
                                        v-if="modalItem.modalAudio"
                                >重新上传</label
                                >
                            </div>
                        </div>
                    </div>
                    <button
                            type="button"
                            class="add-tg-btn text-add-btn add-btn-61 add-btn-ft magrin-top"
                            v-show="item.modalList.length < 3"
                            v-on:click="addOptionsModels(item.modalList, {title: '',content: '',modalAudio:'',titlePositionX:0,titlePositionY:0,videoFirstFrame:''})"
                    >
                        添加一组弹窗内容
                    </button>
                </div>
            </div>
            <button
                    type="button"
                    class="add-tg-btn text-add-btn add-btn-61"
                    v-on:click="addOptions(configData.source.gameList, {
            audio: '',
            audioTime:'',
            rightImg: '',
            rightPositionStartX: '',
            rightPositionStartY: '',
            rightPositionEndX:'',
            rightPositionEndY:'',
            shadeImg: '',
            shadePositionX: '',
            shadePositionY: '',
            landMarkPositionIngameX: '',
            landMarkPositionIngameY: '',
            modalList: [],
            modalMode:'1',
            showMask: '1',
            handPositionX: 0,
            handPositionY: 0,
            hasHandAnimation: '1',
            clickMaskStatus: '1',
            memoryId:''
          })"
            >
                添加一轮
            </button>
        </div>
        <!-- 是否展示授权按钮 -->
        <div class="c-group">
            <div class="c-title">授权按钮</div>
            <div class="c-area">
                <div class="field-wrap">
                    <label class="field-label" style="width: 140px"
                    >是否展示授权按钮</label
                    >
                    <select
                            id="teachTime"
                            v-model="configData.source.hasPractice"
                            style="width: 150px"
                    >
                        <option name="optive" value="1">展示</option>
                        <option name="optive" value="0">隐藏</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 弹窗样式 -->
        <div class="c-group">
            <div class="c-title">弹窗样式</div>
            <div class="c-area">
                <div class="field-wrap">
                    <div class="field-wrap img-upload">
                        <span class="fixed-width">背景图片 </span>
                        <input
                                type="file"
                                v-bind:key="Date.now()"
                                class="btn-file"
                                id="dialogBg"
                                size="3006*2003"
                                v-on:change="dialogImageUpload($event,'dialogBg',200)"
                        />
                        <label
                                for="dialogBg"
                                class="btn btn-show upload"
                                v-if="configData.source.dialogBg === ''"
                        >上传</label
                        >
                        <label
                                for="dialogBg"
                                class="btn upload re-upload"
                                v-if="configData.source.dialogBg !== ''"
                        >重新上传</label
                        ><em class="game_em">尺寸：3006 x 2003，大小不超过200Kb</em>
                        <div
                                class="img-preview"
                                v-if="configData.source.dialogBg !== ''"
                        >
                            <img v-bind:src="configData.source.dialogBg" alt=""/>
                            <div class="img-tools">
                    <span
                            class="btn btn-delete"
                            v-on:click="configData.source.dialogBg=''"
                    >删除</span
                    >
                            </div>
                        </div>
                        <p>(非必填，默认黄色木质样式)</p>
                    </div>
                    <br/>
                    <div class="field-wrap img-upload">
                        <span class="fixed-width">激活按钮图片-左</span>
                        <input
                                type="file"
                                v-bind:key="Date.now()"
                                class="btn-file"
                                id="dialogBtnActiveL"
                                size="302*434"
                                v-on:change="dialogImageUpload($event,'dialogBtnActiveL',50)"
                        />
                        <label
                                for="dialogBtnActiveL"
                                class="btn btn-show upload"
                                v-if="configData.source.dialogBtnActiveL === ''"
                        >上传</label
                        >
                        <label
                                for="dialogBtnActiveL"
                                class="btn upload re-upload"
                                v-if="configData.source.dialogBtnActiveL !== ''"
                        >重新上传</label
                        ><em class="game_em">尺寸：302*434，大小不超过50Kb</em>
                        <div
                                class="img-preview"
                                v-if="configData.source.dialogBtnActiveL !== ''"
                        >
                            <img v-bind:src="configData.source.dialogBtnActiveL" alt=""/>
                            <div class="img-tools">
                    <span
                            class="btn btn-delete"
                            v-on:click="configData.source.dialogBtnActiveL=''"
                    >删除</span
                    >
                            </div>
                        </div>
                        <p>（非必填，默认黄色木质）</p>
                    </div>
                    <br/>
                    <div class="field-wrap img-upload">
                        <span class="fixed-width">激活按钮图片-右</span>
                        <input
                                type="file"
                                v-bind:key="Date.now()"
                                class="btn-file"
                                id="dialogBtnActiveR"
                                size="302*434"
                                v-on:change="dialogImageUpload($event,'dialogBtnActiveR',50)"
                        />
                        <label
                                for="dialogBtnActiveR"
                                class="btn btn-show upload"
                                v-if="configData.source.dialogBtnActiveR === ''"
                        >上传</label
                        >
                        <label
                                for="dialogBtnActiveR"
                                class="btn upload re-upload"
                                v-if="configData.source.dialogBtnActiveR !== ''"
                        >重新上传</label
                        ><em class="game_em">尺寸：302*434，大小不超过50Kb</em>
                        <div
                                class="img-preview"
                                v-if="configData.source.dialogBtnActiveR !== ''"
                        >
                            <img v-bind:src="configData.source.dialogBtnActiveR" alt=""/>
                            <div class="img-tools">
                    <span
                            class="btn btn-delete"
                            v-on:click="configData.source.dialogBtnActiveR=''"
                    >删除</span
                    >
                            </div>
                        </div>
                        <p>（非必填，默认黄色木质）</p>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
        <div class="show-fixed">
            <div class="show-img">
                <img src="./form/img/bg.webp?_=<%= Date.now() %>" alt=""/>
            </div>
            <ul class="show-txt">
                <li><em>图片格式：</em>JPG/PNG/GIF</li>
                <li><em>声音格式：</em>MP3/WAV</li>
                <li><em>视频格式：</em>MP4</li>
                <li>带有" * "号为必填项</li>
            </ul>
        </div>
    </div>
</div>
</body>
<script src="./form/js/form.js?_=<%= Date.now() %>"></script>
</html>
