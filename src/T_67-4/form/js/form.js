// var domain = 'http://172.16.0.107:9011/pages/1152/';
import {
  addInstruction,
  validateInstructions,
  removeInstruction,
  getDynamicInstructions
} from "../../../common/template/dynamicInstruction/form.js";
import {getMemoryForm, setMemoryForm} from "../../../common/template/memory/form.js";

var domain = '';
// 对话框初始化数据
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: ""
    }
  ],
  messageLocationX: '', // 消息内容位置x
  messageLocationY: '', // 消息内容位置y
  roleLocationX: '100', // 角色位置x
  roleLocationY: '600', // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2",// 播放完之后状态
  scale: 100, //缩放比例  1-500
  autoNext: "1", // 是否自动播放下一条对话框
  hiddenStatus: "1", // 播放完是否应藏的状态
}
var Data = {
  configData: {
    bg: '',
    desc: '',
    title: "",
    tImg: '',
    tImgX: 1340,
    tImgY: 15,
    instructions: [{
      commandId: '-1'
    }],
    tg: [{
      title: '',
      content: ''
    }],
    level: {
      high: [{
        title: "",
        content: ""
      }],
      low: [{
        title: "",
        content: "",
      }]
    },
    source: {
      //标志物
      isNeedLandMark: 1, //是否需要标志物
      landMarkImg: "", //标志物图片
      landMarkShowType: 1, //标志物显示类型  1  原地显示  2.飞入
      landMarkPositionX: '', //标志物位置X
      landMarkPositionY: '', //标志物位置Y
      dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
      hasPractice: '1',  //是否展示授权按钮 0 无授权功能  1  默认值，普通授权模式
      dialogBg: '',
      dialogBtnActiveR: '',
      dialogBtnActiveL: '',
      //游戏资源
      gameList: [{
        memoryId: '',
        audio: "", //题干音频
        audioTime: "", //音频长度
        rightImg: "", //正确图片
        rightPositionStartX: "", //正确图片初始位置X
        rightPositionStartY: "", //正确图片初始位置Y
        rightPositionEndX: "", //正确图片最终位置X
        rightPositionEndY: "", //正确图片最终位置Y
        shadeImg: "", //遮罩图片
        shadePositionX: "", //遮罩图片位置X
        shadePositionY: "", //遮罩图片位置Y
        landMarkPositionIngameX: "", //游戏中标志物位置X
        landMarkPositionIngameY: "", //游戏中标志物位置Y
        modalList: [],
        modalMode: "1",// 1 弹窗模式    2 全屏模式
        clickMaskStatus: "1", //点击后的遮罩状态 1 隐藏 2 不隐藏
        showMask: "1", // 是否展示半透明蒙版 1 展示 2 不展示
        hasHandAnimation: '1',  //是否展示小手动画， 1 展示 0 不展示
        handPositionY: 0, //小手动画位置Y
        handPositionX: 0, //小手动画位置X
      }]
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
    }

  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [],
};
$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = '';
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15
      }
      if (!Data.configData.source.hasPractice) {
        Data.configData.source.hasPractice = '1';
      }
      if (!Data.configData.source.dialogBg) {
        Data.configData.source.dialogBg = '';
      }
      if (!Data.configData.source.dialogBtnActiveR) {
        Data.configData.source.dialogBtnActiveR = '';
      }
      if (!Data.configData.source.dialogBtnActiveL) {
        Data.configData.source.dialogBtnActiveL = '';
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [{
            title: "",
            content: "",
          }],
          low: [{
            title: "",
            content: "",
          }]
        }
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
        }
        Data.configData.log_editor = {
          isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }
      }
      // todo 升级IP组件多轮对话处理初始化数据
      if (!Data.configData.source.dialogs.scale) {
        Data.configData.source.dialogs.scale = 100
      }
      if (!Data.configData.source.dialogs.autoNext) {
        Data.configData.source.dialogs.autoNext = "2"
      }
      if (!Data.configData.source.dialogs.hiddenStatus) {
        Data.configData.source.dialogs.hiddenStatus = "1"
      }
      if (Data.configData.source.dialogs.roleLocationX === "" || Data.configData.source.dialogs.roleLocationX === undefined) {
        Data.configData.source.dialogs.roleLocationX = "100"
      }
      if (Data.configData.source.dialogs.roleLocationY === "" || Data.configData.source.dialogs.roleLocationY === undefined) {
        Data.configData.source.dialogs.roleLocationY = "600"
      }
      if (!Data.configData.instructions) {
        Data.configData.instructions = [{
          commandId: '-1'
        }]
      }

      // todo 初始化是否全屏
      if (Data.configData.source.gameList && Data.configData.source.gameList.length > 0) {

        Data.configData.source.gameList.forEach(item => {
          // 如果item没有hasHandAnimation字段，则从source继承，如果source也没有则默认为'1'
          if (!item.hasHandAnimation) {
            item.hasHandAnimation = Data.configData.source.hasHandAnimation || '1';
          }
          if (!item.modalMode) {
            item.modalMode = "1"
          }
          if (!item.showMask) {
            item.showMask = "1"
          }
          if (!item.clickMaskStatus) {
            item.clickMaskStatus = "1"
          }
        });
      }

    }
    getMemoryForm(Data)
  },
  error: function (res) {
    console.log(res)
  }
});

new Vue({
  el: '#container',
  data: Data,
  mounted() {
    this.getDynamicInstructions();
  },
  methods: {
    getDynamicInstructions: function () {
      var that = this;
      getDynamicInstructions(function (res) {
        that.dynamicInstructions = res;
      });
    },
    addInstruction: function () {
      addInstruction(this.configData);
    },
    removeInstruction: function (index) {
      removeInstruction(index, this.configData);
    },
    validateInstructions: function () {
      return validateInstructions(this.configData);
    },

    isImage(url) {
      return url && (url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif'));
    },
    isVideo(url) {
      return url && (url.endsWith('.mp4') || url.endsWith('.avi') || url.endsWith('.mov') || url.endsWith('.mkv'));
    },
    uploadTime: function (e, item, attr, fileSize) {
      console.log("uploadTime", item)
      var file = e.target.files[0],
        that = this;
      // 判断文件类型
      if (file.type.startsWith('image/')) {
        // 图片处理逻辑
        that.imageUpload(e, item, attr, fileSize);
      } else if (file.type.startsWith('video/')) {
        // 视频处理逻辑
        that.videoUpload(e, item, attr, fileSize);
      } else {
        // 其他文件处理逻辑
        that.lottieUpload(e, item, attr, fileSize);
      }
    },
    //图片上传
    imageUpload: function (e, item, attr, fileSize) {
      console.log("imageUpload", e)
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(e.target, {
          height: naturalHeight,
          width: naturalWidth
        }, item, attr);
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      }
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      }
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //图片上传
    dialogImageUpload: function (e, attr, fileSize) {
      console.log("dialogImageUpload", e)
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData.source;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(e.target, {
          height: naturalHeight,
          width: naturalWidth
        }, item, attr);
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      }
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      }
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e)
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(e.target, {
          height: naturalHeight,
          width: naturalWidth
        }, item, attr);
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      }
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      }
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && (height + 1) > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
      }
      return checkSize;
    },

    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          //   img = null;
        } else {
          //   img = null;
        }
      };
      reader.readAsText(file);
    },
    // 视频上传
    videoUpload: function (e, item, attr, fileSize) {
      console.log("videoUpload", e);
      var file = e.target.files[0],
        size = file.size,
        that = this;

      // 检查文件大小
      if ((size / 1024).toFixed(2) > fileSize) {
        alert("您上传的视频大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
        return;
      }

      // 设置加载中的提示
      item[attr] = "./form/img/loading.jpg";

      // 检查文件类型
      if (!file.type.startsWith('video/')) {
        alert("请选择一个有效的视频文件！");
        return;
      }

      // 创建视频对象
      var video = document.createElement('video');
      video.preload = 'metadata'; // 预加载元数据

      video.onloadedmetadata = function () {
        var check = that.sourceImgCheck(e.target, {
          duration: video.duration
        }, item, attr);
        if (check) {
          that.postData(file, item, attr);
          video = null;
        } else {
          video = null;
        }
      };

      var reader = new FileReader();
      reader.onload = function (evt) {
        video.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); // 读取文件
    },
    //图片大小校检
    sourceImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height == data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        //console.error("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
        item[attr] = "";
        alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
      }
      return checkSize;
    },

    //音频上传
    audioUpload: function (e, item, attr, fileSize) {
      console.log("audioUpload", item)
      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      var that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
        alert("您上传的音频大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
        return;
      }
      var url = URL.createObjectURL(file); //获取录音时长
      var audioElement = new Audio(url);
      var duration;
      audioElement.addEventListener("loadedmetadata", function (_event) {
        duration = audioElement.duration ? audioElement.duration : '';
        var check = that.sourceAudioCheck(e.target, {
          duration: duration,
        });
        if (check) {
          item[attr] = "./form/img/loading.jpg";
          that.postData(file, item, attr);
          audioElement = null;
        } else {
          audioElement = null;
        }
      });
    },
    //音频长度校检
    sourceAudioCheck: function (input, data) {
      let dom = $(input),
        time = dom.attr("time");
      if (time == "" || time == undefined || data.duration == '') return true;
      let checkSize = false;
      if (data.duration <= time) {
        checkSize = true;
      } else {
        alert(`您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`);
      }
      return checkSize;
    },
    validate: function () {
      var data = this.configData.source;
      // if(this.validateDialog()) {
      //   return this.validateDialog();
      // }

      // 验证 IP组件，如果传了json文件，就需要设置对话
      const dialogs = this.configData.source.dialogs;
      if (dialogs.roleImg) {
        for (let index = 0; index < dialogs.messages.length; index++) {
          const item = dialogs.messages[index];
          const {text, audio} = item;
          if (!text || !audio) {
            return "请上传对话内容"
          }
        }
      }
      var checkMsg = false;
      // 检查第一轮之前标志物位置有没有上传
      if (data.isNeedLandMark == 2 && data.landMarkShowType == 2 && (!data.landMarkPositionX || !data.landMarkPositionY)) {
        checkMsg = "请设置第1轮游戏前，标志物图片的位置";
        return checkMsg;
      }
      //验证游戏各轮元素 是否齐全
      for (let i of data.gameList) {
        if (!i.rightImg) {
          checkMsg = "请选择正确图片";
          return checkMsg;
        }

        if (!i.rightPositionStartX || !i.rightPositionStartY) {
          checkMsg = "请填写正确图片的初始位置";
          return checkMsg;
        }
        if (!i.rightPositionEndX || !i.rightPositionEndY) {
          checkMsg = "请填写正确反馈位置";
          return checkMsg;
        }

        // if (!i.shadeImg) {
        //     checkMsg = "请填写遮罩图片";
        //     return checkMsg;
        // }
        if (!i.shadePositionX || !i.shadePositionY) {
          checkMsg = "请选择遮罩图片位置";
          return checkMsg;
        }
        if (data.isNeedLandMark == 2 && (!i.landMarkPositionIngameX || !i.landMarkPositionIngameY)) {
          checkMsg = "请填写正确反馈后标志物的位置";
          return checkMsg;
        }
        if (!i.modalList || i.modalList.length === 0) {
          return checkMsg; // 如果 modalList 为空或未定义，直接返回
        } else {
          for (let m of i.modalList) {
            // if (!m.title) {
            //   checkMsg = "请填写弹窗标题图片";
            //   return checkMsg;
            // }
            if (!m.content) {
              checkMsg = "请填写弹窗内容";
              return checkMsg;
            }
          }
        }
      }

    },
    // 对话数据校验
    validateDialog: function () {
      var dialogs = this.configData.source.dialogs;
      // 如果数据为初始状态，则不校验
      if (JSON.stringify(dialogs) == JSON.stringify(dialogsInitData)) {
        return "";
      }
      if (!dialogs.roleImg) {
        return "请上传对话IP对象";
      }
      if (dialogs.roleLocationX == "" || dialogs.roleLocationY == "") {
        return "请输入IP对象位置";
      }
      var isMessagePass = dialogs.messages.some(function (item) {
        return item.text == "" || item.audio == "";
      });
      if (isMessagePass == true) {
        return "请上传对话内容";
      }
      if (dialogs.messageLocationX == "" || dialogs.messageLocationY == "") {
        return "请输入对话位置";
      }
      return "";
    },
    onSend: function () {
      var data = this.configData;
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == '-2') { //其他
        data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长")
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther
      }
      var _data = JSON.stringify(data);
      console.log(_data)
      var val = this.validate();
      if (!val && this.validateInstructions()) {
        setMemoryForm(data);
        $.ajax({
          url: domain + 'content?_method=put',
          type: 'POST',
          data: {
            content: _data
          },
          success: function (res) {
            console.log(res);
            window.parent.postMessage('close', '*');
          },
          error: function (err) {
            console.log(err)
          }
        });
      } else {
        alert(val);
      }
    },
    postData: function (file, item, attr) {
      var that = this;
      var FILE = 'file';
      var oldImg = item[attr];
      var data = new FormData();
      data.append('file', file);
      if (oldImg != "") {
        data.append('key', oldImg);
      }
      ;
      $.ajax({
        url: domain + FILE,
        type: 'post',
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          console.log(res.data.key);
          item[attr] = domain + res.data.key;
        },
        error: function (err) {
          console.log(err)
        }
      })
    },

    delQue: function (item, array) {
      array.remove(item);
    },
    addTg: function (item) {
      this.configData.tg.push({
        title: '',
        content: ''
      });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({
        title: '',
        content: ''
      });
    },
    addL: function (item) {
      this.configData.level.low.push({
        title: '',
        content: ''
      });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    addOptions: function (arr, item) {
      console.log("arrarrarrarr", arr)
      if (item) {
        arr.push(item)
      } else {
        arr.push('')
      }
    },
    addOptionsModels: function (arr, item) {
      if (item) {
        arr.push(item)
      } else {
        arr.push('')
      }
    },
    delOption: function (arr, item) {
      arr.remove(item)
    },
    setAnswer: function (item) {
      this.configData.source.right = item;
    },
    inputPlayNum: function () {
      var reg = new RegExp("^([1][0-9]{0,1}|[2][0]{0,1})$");
      var regResult = reg.test(Data.configData.source.palyTime);
      if (!regResult && Data.configData.source.palyTime != "#") {
        Data.configData.source.palyTime = "";
      }
    },
    // 添加对话框
    addDialog: function () {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: ""
      });
    },
    // 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },
    // 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },
  },
  watch: {}
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
