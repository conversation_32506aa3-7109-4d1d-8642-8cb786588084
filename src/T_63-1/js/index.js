"use strict"
import '../../common/js/common_1v1.js'
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js"
import {USER_TYPE, CLASS_STATUS, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE} from "../../common/js/constants.js";
import {
  feedbackAnimation
} from "../../common/template/feedbackAnimation/index.js";
// import '../../common/js/commonFunctions.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: '1' // 是否有授权按钮 1：是 0：否
  }

  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/bj.jpg)'
    })
  }
  let options = configData.source.options, //图片列表
    imgType = configData.source.imgType, // 1  静态图  2 2帧雪碧图
    imgWidth = configData.source.imgWidth, //设定图片的宽
    imgHeight = configData.source.imgHeight, //设定图片的高
    animationAudio = configData.source.animationAudio, //动画音效
    time = configData.source.time, //动画时长
    audio = configData.source.audio, //题干音频
    imgPosition = configData.source.imgPosition,//题干图标位置
    right = configData.source.right, //是否正确答案
    userType = window.frameElement && window.frameElement.getAttribute('user_type'),//用户身份学生还是老师
    imgIndex = 0,
    timeFlag = null,
    timeInit = null, //定时器
    clickAble = false,
    answerArr = [], //选中后的的正确答案
    optionsRightKey = [] //初始化正确答案的个数
  //添加音频
  if (audio) {
    $('.example-audio audio').attr('src', audio);
  } else {
    $('.example-audio').hide()
  }
  //生成像素表格
  renderPx();

  function renderPx() {
    let liHtml = '';
    for (let i = 1; i < 801; i++) {
      liHtml += `
				<li class="pos_${i}"></li>	`
    }
    $('.boxUl').html(liHtml);
  }

  //小手位置初始化
  initHand();
  console.log(optionsRightKey.map(item => item + 1).join(','))
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      correctanswer: optionsRightKey.map(item => item + 1).join(','),
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_INPUT,
      interaction_type: INTERACTION_TYPE.CHOOSE,
      useraction_type: USERACTION_TYPE.LISTEN
    },
  })

  function initHand() {
    /**
     * 找出正确答案
     */
    for (var i = 0; i < options.length; i++) {
      if (options[i].isRight) {
        optionsRightKey.push(i)
      }
    }
    let firtstPosition = options[optionsRightKey[0]].position;
    let imgIndex = 3;
    if (imgType == 1) {
      imgIndex = 1
    }
    let marginWidth = imgWidth / imgIndex / 2 / 100 - .6 + 'rem';
    let marginHg = imgHeight / 2 / 100 - .9 + 'rem'
    if (firtstPosition) {
      let left = ($('.pos_' + firtstPosition).offset().left - $('.container').offset().left) / window.base + 'rem';
      let top = ($('.pos_' + firtstPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
      $('.hand').css({
        left: left,
        top: top,
        marginLeft: marginWidth,
        marginTop: marginHg
      });
    }
  }

  // 初始化列表
  optionsFn();

  function optionsFn() {
    let str = '';
    for (let i = 0; i < options.length; i++) {
      let currentPosition = options[i].position,
        left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base + 'rem',
        top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
      str += `<li class="ans-img" data-right="${options[i].isRight}" data-imgType="${imgType}" data-width="${imgWidth}" data-key="${i}" data-syncactions="sele-${i}" data-positon="${options[i].position}" style="
              left:${left}; top:${top}; background-image:url(${options[i].img})">
              <audio class="audiolist" src="${options[i].listAudio}" data-syncaudio="audiolist${i}"></audio>
              <div class="right-answer shade">
                <img src="./image/sxdx-zhengque.png" alt="">
              </div>
            </li>`
    }
    $(".picList ul").html(str);
    initType()
  }

  //初始化   静态图（1） 2帧（3） 样式
  function initType() {
    let naturalWidth = imgWidth / 100,
      ele = $(".picList ul li"),
      naturalHeight = imgHeight / 100;
    if (imgType == 1) {
      ele.css({
        backgroundSize: '100% 100%',
        width: naturalWidth + 'rem',
        height: naturalHeight + 'rem'
      });
      clickAble = true;
    } else {
      ele.css({
        backgroundSize: '300% 100%',
        width: naturalWidth / 3 + 'rem',
        height: naturalHeight + 'rem',
        backgroundPosition: '0% 100%'
      });
      animationFn();
    }
  }

  // 定时器
  function timelyFun() {
    console.log("第二帧动画")
    if (imgIndex != 0) {
      pauseRudioFn();
    }
    clearInterval(timeInit);
    $(".picList ul li").eq(imgIndex).css({
      backgroundPosition: '-200% 100%'
    })
    imgIndex++;
    if (imgIndex == options.length) {
      //清除定时器
      clearInterval(timeFlag);
      clearInterval(timeInit);
      // 添加抖动效果
      $(".picList ul").addClass("shakeLeft");
      clickAble = true;
    } else {
      timeFn()
    }
  }

  // 雪碧图的动画
  function timeFn() {
    timeInit = setInterval(function () {
      console.log("第一帧动画")
      audioFn()
      $(".picList ul li").eq(imgIndex).css({
        backgroundPosition: '-100% 100%'
      })
    }, time)
  }

  function timeAnimatonFn() {
    timeFlag = setInterval(timelyFun, time * 2)
  }

  function animationFn() {

    timeAnimatonFn()
    timeFn()
  }

  // // 初始化雪碧图播放音频
  function audioFn() {
    if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
      SDK.playRudio({
        index: $('.animationAudio')[0],
        syncName: $('.animationAudio').attr("data-syncaudio")
      })
    }

  }

  // 暂定播放
  function pauseRudioFn() {
    if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
      SDK.pauseRudio({
        index: $('.animationAudio')[0],
        syncName: $('.animationAudio').attr("data-syncaudio")
      })
    }
  }

  // 音频初始化
  andioShow();

  function andioShow() {
    // 动画音频
    $('.animationAudio').attr('src', animationAudio);
    if (!audio) {
      $('.example-audio').hide();
    }
    if (audio && imgPosition) {
      let left = ($('.pos_' + imgPosition).offset().left - $('.container').offset().left) / window.base + 'rem',
        top = ($('.pos_' + imgPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
      $('.example-audio').css({
        top: top,
        left: left
      })
    }
  }

  //判断是学生还是老师显示底部按钮和mask
  isShowBtn();

  function isShowBtn() {
    if (isSync) {
      if (userType == 'tea') {
        $(".doneTip").show();
      } else {
        $(".doneTip").hide();
      }
    } else {
      var hrefParam = parseURL("http://www.example.com");
      if (top.frames[0] && top.frames[0].frameElement) {
        hrefParam = parseURL(top.frames[0].frameElement.src)
      }
      var role_num = hrefParam.params['role']

      function parseURL(url) {
        var a = document.createElement('a')
        a.href = url
        return {
          source: url,
          protocol: a.protocol.replace(':', ''),
          host: a.hostname,
          port: a.port,
          query: a.search,
          params: (function () {
            var ret = {},
              seg = a.search.replace(/^\?/, '').split('&'),
              len = seg.length,
              i = 0,
              s
            for (; i < len; i++) {
              if (!seg[i]) {
                continue;
              }
              s = seg[i].split('=')
              ret[s[0]] = s[1]
            }
            return ret
          })(),
          file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
          hash: a.hash.replace('#', ''),
          path: a.pathname.replace(/^([^\/])/, '/$1'),
          relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
          segments: a.pathname.replace(/^\//, '').split('/')
        }
      }

      if (role_num == '1' || role_num == undefined) {
        $(".doneTip").show();
      } else if (role_num == '2') {
        $(".doneTip").hide();
      }
    }
  }

  // 点击播放音频
  let soundClick = true,
    isPlaySound = true;
  $('.sound').on('click touchstart', function (e) {
    if (clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();

      if (soundClick) {
        soundClick = false;
        if (!isSync) {
          $(this).trigger('syncSoundClick');
          return;
        }
        if (window.frameElement.getAttribute('user_type') == 'tea') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            syncName: 'syncSoundClick',
            funcType: 'audio'
          });
        } else {
          $(this).trigger('syncSoundClick');
          return;
        }
      }
    }
  })
  $('.sound').on('syncSoundClick', function (e, message) {

    let gif = $(this).find('.gif');
    let png = $(this).find('.png');
    let audio = $(this).find('audio')[0];
    if (isPlaySound) {
      // audio.play();
      SDK.playRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.show();
      png.hide();
    } else {
      // audio.pause();
      SDK.pauseRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.hide();
      png.show();
    }
    audio.onended = function () {
      gif.hide();
      png.show();
      isPlaySound = true;
    }.bind(this);

    isPlaySound = !isPlaySound;

    SDK.setEventLock();
    soundClick = true;

  });

  let picListStatus = true;//正确错误音效播放完改变，是否可点下一项状态
  // 选择答案
  let itemClick = true;
  $(".picList li").on('click touchstart', function (e) {
    if (clickAble && picListStatus) {

      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if (itemClick) {
        itemClick = false;

        SDK.reportTrackData({
          action: 'CK_FT_INTERACTION_ITEM', //事件名称
          data: {}, // 老师和学生  都需要上报的数据
          teaData: {},  // 只有老师端会上报的数据
          stuData: {
            item: $(this).data('key')+1,
            result: $(this).data('right') ? 'right' : 'wrong',
          },  // 只有学生端会上报的数据
        }, USER_TYPE.STU)
        if (!isSync) {
          $(this).trigger('syncItemClick');
          return
        }
        if (window.frameElement.getAttribute('user_type') == 'stu') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            syncName: 'syncItemClick',
            otherInfor: {
              answer: answerArr
            },
            recoveryMode: '1'
          });
        }
      }
    }
  });


  $(".picList li").on('syncItemClick', function (e, message) {
    if (picListStatus) {
      // 答对后不可再选择
      if ($(this).hasClass('isRight')) {
        itemClick = true;
        if (optionsRightKey.length == answerArr.length) {
          itemClick = false;
          return;
        }
        SDK.setEventLock();
        return;
      } else {
        // optionsRightFn();
        if (optionsRightKey.length == answerArr.length) {
          itemClick = false;
          return;
        }
      }

      //断线重连
      if (isSync && message && message.operate == 5) {
        let obj = message.data[0].value.syncAction.otherInfor;
        let answer = obj.answer;
        if (answer.length) {
          for (var i = 0; i < answer.length; i++) {
            let picIndex = answer[i].key;
            $('.picList li').eq(picIndex).addClass('isRight');
            $('.shade').eq(picIndex).addClass('flex');
          }
          // 是否全答对
          if (optionsRightKey.length == answer.length) {
            itemClick = false;
            return;
          }

          // $('.picList li').addClass('isRight');
          // let mask = $('.shade').eq($(this).index());
          // mask.addClass('flex');
        }
        SDK.setEventLock();
        return;
      }
      if ($(this).attr('data-right') == 'true') {
        console.log('c-page:----------------%s: 答对了！');
        if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
          // $('.right')[0].play();
          picListStatus = false
          SDK.playRudio({
            index: $('.right')[0],
            syncName: $('.right').attr("data-syncaudio")
          })
        }

        $('.picList li').eq($(this).index()).addClass('isRight');
        // 选中正确答案的集合
        answerArr.push({
          key: $(this).index()
        })
        let mask = $('.shade').eq($(this).index());
        mask.addClass('flex');

        //播放当前点击内容语音
        currentAudio($(this).find('audio'), $(this).find('.audiolist').attr('data-syncaudio'), $('.right'), $(this).find('audio').attr('src'))

        // // 播放反馈动画
        feedback($(this).find('audio'), $('.right'), $(this).find('audio').attr('src'))

        // SDK.setEventLock();
        // starFun();
        itemClick = true;


      } else {
        itemClick = false;
        console.log('c-page:----------------%s: 答错了！');
        if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
          // $('.wrong')[0].play();
          picListStatus = false
          SDK.playRudio({
            index: $('.wrong')[0],
            syncName: $('.wrong').attr("data-syncaudio")
          })
        }
        $(this).addClass('shake');

        $(this).on('animationend webkitAnimationEnd', function () {
          $(this).removeClass('shake');

          //播放当前点击内容语音
          currentAudio($(this).find('audio'), $(this).find('.audiolist').attr('data-syncaudio'), $('.wrong'), $(this).find('audio').attr('src'))

          // SDK.setEventLock();
          setTimeout(function () {
            itemClick = true;
          }, 100)

        })

      }
    }
  })

  // 播放反馈动画
  function feedback(listAudio, beforeAudio, src) {
    if (answerArr.length == optionsRightKey.length) {
      if (!src || src == 'undefined' || src == '') {
        beforeAudio.one('ended', async function () {
          console.log('全部正确结束，执行反馈动画')
          await feedbackAnimation('feedKey1')
          SDK.setEventLock();
        });
      } else {
        listAudio.one('ended', async function () {
          console.log('全部正确结束，执行反馈动画')
          await feedbackAnimation('feedKey1')
          SDK.setEventLock();
        });
      }
      SDK.reportTrackData({
        action: 'CK_FT_INTERACTION_COMPLETE', //事件名称
        data: {
          result: 'success'
        }, // 老师和学生  都需要上报的数据
        teaData: {},  // 只有老师端会上报的数据
        stuData: {},  // 只有学生端会上报的数据
      },USER_TYPE.TEA)
    } else {

    }
  }

  //播放当前点击内容语音
  function currentAudio(currentAudio, audiolist, beforeAudio, currentAudioSrc) {
    //还未播放完当前物品音频重新播放
    $(".picList li audio").each(function () {
      this.pause();
      this.currentTime = 0;
    });

    beforeAudio.one("ended", function () {
      picListStatus = true;
      if (currentAudio[0] && currentAudioSrc && currentAudioSrc != 'undefined') {
        SDK.playRudio({
          index: currentAudio[0],
          syncName: audiolist,
        });
        currentAudio.one("ended", function () {
          if (answerArr.length != optionsRightKey.length) {
            SDK.setEventLock();
          }
        });
      } else {
        if (answerArr.length != optionsRightKey.length) {
          SDK.setEventLock();
        }
      }
      currentAudio[0] = "";
    });
  }

  //老师点击 hint 按钮
  let btnStatus = true;
  $(".hint-btn").on("click touchstart", function (e) {
    if (clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if (btnStatus) {
        btnStatus = false;
        if (!isSync) {
          $(this).trigger("btnClick");
          return;
        }
        if (window.frameElement.getAttribute('user_type') == 'tea') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data("syncactions"),
            eventType: 'click',
            method: 'event',
            syncName: 'btnClick',
            otherInfor: '',
            recoveryMode: '1'
          });
        }
      }
    }
  })
  $(".hint-btn").on('btnClick', function (e, message) {
    $('.hand').show();
    $('.right-top-hint').show();
    clickAble = false
    setTimeout(function () {
      $('.hand').hide();
      $('.right-top-hint').hide();
      btnStatus = true;
      clickAble = true;
    }, 3000)
    SDK.setEventLock();
  });


  //答题结束触发发送星星
  function starFun() {
    if (!isSync) {
      return false;
    }
    console.log('进入发星')
    var classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
    var support1v1h5star = parent.window.h5SyncActions.classConf.serverData.objCourseInfo.support1v1h5star;
    var device = parent.window.h5SyncActions.classConf.h5Course.device;
    if (window.frameElement.getAttribute('user_type') == 'stu' && classStatus == 2) {
      if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
        console.log("学生答题正确后发星星")
        SDK.bindSyncStart({
          type: "newH5StarData",
          num: 1
        });
      }

    }
  }


})
