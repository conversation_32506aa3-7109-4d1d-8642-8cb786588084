var configData = {
  bg: './assets/images/bg.jpg',
  desc: 'TCH0004_四项多选',
  title: '',
  tImg: './assets/images/b164806a1b826b1b386f5496478573fe.png',
  tImgX: 1340,
  tImgY: 15,
  sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
  tg: [{
    content: "eegeg appy family. My father i appy family. My father i",
    title: "1111111111111111111"
  },
  {
    content: "eegeg appy family. My father i appy family. My father i",
    title: "weqwf appy family. My father i"
  },
  {
    content: "eegeg appy family. My father i appy family. My father i",
    title: "weqwf appy family. My father i"
  }
  ],
  level: {
    high: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
    ],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    optionLength: 4,  //选项上限
    imgType: 2,       //1  静态图  2 2帧雪碧图
    imgWidth: 1200, //设定图片的宽
    imgHeight: 458, //设定图片的高
    options: [
      {
        img: "./assets/images/img01.png",
        position: 241,
        isRight: false,
      },
      {
        img: "./assets/images/img02.png",
        position: 372,
        isRight: false,
        listAudio:'./assets/audios/photo.mp3'
      },
      {
        img: "./assets/images/img03.png",
        position: 223,
        isRight: true,
      },
      {
        img: "./assets/images/img04.png",
        position: 314,
        isRight: true,
        listAudio:'./assets/audios/resultLose.mp3'
      }
    ],
    audio: './assets/audios/lemon.mp3', //题干音频
    imgPosition: '48', //题干图标位置
    animationAudio: '', //动画音效
    time: 150, //动画时长
    right: -1,
    widthImg: {
      width: 1200,
      height: 401,
    },
    isKeyCheck: true
  },
  feedbackLists:[
    {
      positiveFeedback: '-1',
      feedbackList:[
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./assets/images/prefect.json', mp3:'./assets/audios/prefect.mp3' },
        { id:'1', json:'./assets/images/goldCoin.json', mp3:'./assets/audios/goldCoin.mp3' },
        { id:'2', json:'./assets/images/FKJB.json', mp3:'./assets/audios/resultWin.mp3' },
        { id:'9', json: './assets/images/feedback.json', mp3: './assets/audios/feedback01.mp3' },
      ],
      feedbackObj:{ id:'9', json:'./assets/images/feedback.json', mp3:'./assets/audios/feedback01.mp3' },
      feedback:'./assets/images/1.png',
      feedbackAudio:'./assets/audios/01.mp3',
      feedbackName:'整体反馈',
      key:'feedKey1',
    }
  ]
};
(function (pageNo) { configData.page = pageNo })(0)
