<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>LCH0003_听音找图_中文版</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src='./form/js/vue.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">LCH0003_听音找图_中文版</h3>

            <!-- 公共区域 -->
            <% include ./src/common/template/common_head_nontitle_cn %>
      <!-- 标志物 -->
      <div class="c-group">
        <div class="c-title">标志物</div>
        <div class="c-area upload img-upload radio-group">

          <div class="c-well">
            <span class="fixed-width">标志物</span>
            <label class="inline-label" for="isNeedLandMark"><input type="radio" name="isNeedLandMark" value="1"
                v-model="configData.source.isNeedLandMark"> 不需要标志物</label>
            <label class="inline-label" for="isNeedLandMark"><input type="radio" name="isNeedLandMark" value="2"
                v-model="configData.source.isNeedLandMark"> 需要标志物</label>

            <!-- 如果需要标志物 -->
            <div class="field-wrap" v-show="configData.source.isNeedLandMark == 2">
              <span class="fixed-width">标志物图片</span>
              <input type="file" v-bind:key="Date.now()" class="btn-file" id="leftImg" size=""
                v-on:change="imageUpload($event,configData.source,'landMarkImg',30)" >
              <label for="leftImg" class="btn btn-show upload"
                v-if="configData.source.landMarkImg==''?true:false">上传</label>
              <label for="leftImg" class="btn upload re-upload"
                v-if="configData.source.landMarkImg!=''?true:false">重新上传</label><em class="red">尺寸比例自定义;大小≤30KB</em>
              <div class="img-preview" v-if="configData.source.landMarkImg!=''?true:false">
                <img v-bind:src="configData.source.landMarkImg" alt="" />
                <div class="img-tools">
                  <span class="btn btn-delete" v-on:click="configData.source.landMarkImg=''">删除</span>
                </div>
              </div>
            </div>
            <!-- 出现形式 -->
            <div class="field-wrap" v-show="configData.source.isNeedLandMark == 2">
              <span class="fixed-width">图片出现形式</span>
              <label class="inline-label" for="landMarkShowType" style="margin-bottom: 0;">
                <input type="radio" name="landMarkShowType" value="1" v-model="configData.source.landMarkShowType">
                原位置渐变出现
              </label>
              <br>
              <span class="fixed-width"> </span>
              <input type="radio" name="landMarkShowType" value="2" v-model="configData.source.landMarkShowType"
                style="margin-left: 30px;"> 从上一个位置移动至正确反馈位置
            </div>
            <!-- 标志物初始位置 -->
            <div class="field-wrap"
              v-show="configData.source.isNeedLandMark == 2 && configData.source.landMarkShowType == 2">
              <label>第1轮游戏前，标志物图片的位置<em>*</em>
                X:<input type="number" class="c-input-txt "
                  style="margin: 0 10px;width: 60px!important;display: inline-block;"
                  oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="configData.source.landMarkPositionX">
                Y:<input type="number" class="c-input-txt "
                  style="margin: 0 10px;width: 60px!important;display: inline-block;"
                  oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="configData.source.landMarkPositionY">
                <br><span style="font-size:  11px;
                  display: inline-block;">数字,0<=x<=1920,0<=y<=1080，初始位置会被遮罩图片挡住</span></label>
            </div>
          </div>

          <div class="audio-preview" v-show="configData.source.rightAudio">
            <div class="audio-tools">
              <p v-show="configData.source.rightAudio">{{configData.source.rightAudio}}</p>
            </div>
            <span class="play-btn" v-on:click="play($event)">
              <audio v-bind:src="configData.source.rightAudio"></audio>
            </span>
          </div>
          <div class="field-wrap" style="margin-top:20px;">
            <label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.rightAudio"
              @click="configData.source.rightAudio=''">删除</label>
            <label for="content-audio" class="btn upload re-upload" v-if="configData.source.rightAudio">重新上传</label>
          </div>
        </div>
      </div>


      <!--  每轮游戏设计 -->
      <div class="c-group">
        <div class="c-title"> 每轮游戏设计</div>
        <div class="c-desc">
          <label class="text"> - 共1~6轮游戏。 每轮游戏1个题干声音，1个正确选项图片，1个遮罩图片。</label>
          <label class="text"> - 学员点击“遮罩图片”、“正确选项图片”，系统判断为正确; 因此请设置合理大小的遮罩图片。</label>
        </div>
        <div class="c-area" v-for="(item,index) in configData.source.gameList">
          <span class="dele-tg-btn" v-on:click="delOption(configData.source.gameList,item)"
            v-show="configData.source.gameList.length>1"></span>
          <h2>第{{index+1}}轮</h2>
          <!-- 题干声音 -->
          <div class="c-well upload img-upload radio-group">
            <div class="field-wrap">
              <span class="fixed-width">题干声音*</span>
              <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" :id="'audio-audio-'+index"
                v-on:change="audioUpload($event,item,'audio',20)">
              <label :for="'audio-audio-'+index" class="btn btn-show upload" v-if="!item.audio">上传</label><em
                class="game_em">大小：≤20KB</em>
            </div>
            <div class="audio-preview" v-show="item.audio">
              <div class="audio-tools">
                <p v-show="item.audio">{{item.audio}}</p>
              </div>
              <span class="play-btn" v-on:click="play($event)">
                <audio v-bind:src="item.audio"></audio>
              </span>
            </div>
            <div class="field-wrap">
              <label class="btn upload btn-audio-dele" v-if="item.audio" @click="item.audio=''">删除</label>
              <label :for="'audio-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
            </div>
            <label>
              <span class="game_span">题干声音长度*</span>
              <input type="number" class="c-input-txt game_input" v-model="item.audioTime"
                oninput="if(value<1000)value=1000">
              <em class="game_em">毫秒，(1秒等于1000毫秒，不低于1000毫秒)</em>
            </label>
          </div>
          <!-- 选项图片设置 -->
          <div class="c-well upload img-upload radio-group">
            <div class="game">
              <!-- 上传游戏图片 -->
              <div class="field-wrap">
                <span class="fixed-width">正确图片 * </span>
                <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'rightImg-'+index" size=""
                  v-on:change="imageUpload($event,item,'rightImg',30)">
                <label :for="'rightImg-'+index" class="btn btn-show upload"
                  v-if="item.rightImg==''?true:false">上传</label>
                <label :for="'rightImg-'+index" class="btn upload re-upload"
                  v-if="item.rightImg!=''?true:false">重新上传</label><em class="game_em">尺寸、比例自定义，大小：≤30KB </em>
                <div class="img-preview" v-if="item.rightImg!=''?true:false">
                  <img v-bind:src="item.rightImg" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.rightImg=''">删除</span>
                  </div>
                </div>
              </div>
              <!-- 设置位置 -->
              <label>
                <span class="game_span">初始位置*</span>
                <!-- <input type="number" class="c-input-txt game_input" v-model="item.rightPositionStart"> -->
                X:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.rightPositionStartX">
              Y:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.rightPositionStartY">
                <br>
                <em class="game_em">数字,0<=x<=1920,0<=y<=1080，初始位置会被遮罩图片挡住</em>
              </label>
              <label>
                <span class="game_span">正确反馈位置*</span>
                <!-- <input type="number" class="c-input-txt game_input" v-model="item.rightPositionEnd"> -->
                X:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.rightPositionEndX">
              Y:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.rightPositionEndY">
                <br>
                <em class="game_em">数字,0<=x<=1920,0<=y<=1080，答案显示时，会覆盖在最上层</em>
              </label>
            </div>
          </div>
          <!-- 遮罩图片设置 -->
          <div class="c-well upload img-upload radio-group">
            <div class="game">
              <!-- 上传游戏图片 -->
              <div class="field-wrap">
                <span class="fixed-width">遮罩图片* </span>
                <input type="file" v-bind:key="Date.now()" class="btn-file" :id="'shadeImg-'+index" size=""
                  v-on:change="imageUpload($event,item,'shadeImg',30)">
                <label :for="'shadeImg-'+index" class="btn btn-show upload"
                  v-if="item.shadeImg==''?true:false">上传</label>
                <label :for="'shadeImg-'+index" class="btn upload re-upload"
                  v-if="item.shadeImg!=''?true:false">重新上传</label><em class="game_em">尺寸、比例自定义，大小：≤30KB </em>
                <div class="img-preview" v-if="item.shadeImg!=''?true:false">
                  <img v-bind:src="item.shadeImg" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.shadeImg=''">删除</span>
                  </div>
                </div>
              </div>
              <!-- 设置位置 -->
              <label>
                <span class="game_span">图片位置*</span>
                <!-- <input type="number" class="c-input-txt game_input" v-model="item.shadePosition"> -->
                X:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.shadePositionX">
              Y:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.shadePositionY">
                <br>
                <em class="game_em">数字,0<=x<=1920,0<=y<=1080</em>
              </label>
            </div>
          </div>
          <!-- 正确反馈图片设置 -->
          <div class="c-well" v-show="configData.source.isNeedLandMark == 2">
            <div class="game">
              <label>
                <span class="game_span">正确反馈后， 标志物的位置</span>
                <!-- <input type="number" class="c-input-txt game_input" v-model="item.landMarkPositionIngame"> -->
                X:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1920)value=1920;if(value<0)value=0" v-model="item.landMarkPositionIngameX">
              Y:<input type="number" class="c-input-txt "
                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                oninput="if(value>1080)value=1080;if(value<0)value=0" v-model="item.landMarkPositionIngameY">
                <br>
                <em class="game_em">数字,0<=x<=1920,0<=y<=1080，若有标志图图片则必填。</em>
              </label>
            </div>
          </div>
        </div>
        <button type="button" class="add-tg-btn text-add-btn add-btn-61" v-show="configData.source.gameList.length<6"
          v-on:click="addOptions(configData.source.gameList, {
            audio: '',
            audioTime:'',
            rightImg: '',
            rightPositionStartX: '',
            rightPositionStartY: '',
            rightPositionEndX:'',
            rightPositionEndY:'',
            shadeImg: '',
            shadePositionX: '',
            shadePositionY: '',
            landMarkPositionIngameX: '',
            landMarkPositionIngameY: ''
          })">添加一轮</button>
      </div>
      <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.webp?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>

</html>
