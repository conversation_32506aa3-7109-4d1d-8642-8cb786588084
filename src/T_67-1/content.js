var configData = {
    lang:1,
    isShowCount:2, //1 显示321倒计时 2 不显示
    bg: "",
    desc: "大标题csb",
    title: "",
    tg: [{
        title: "1",
        content: "1"
    }],
    level: {
        high: [{
            title: "1",
            content: "1"
        }],
        low: [{
            title: "1",
            content: "1"
        }]
    },
    source: {
        //标志物
        isNeedLandMark: 2, //是否需要标志物 1不需要 2需要
        landMarkImg: "", //标志物图片
        landMarkShowType: 2, //标志物显示类型  1  原地显示  2.飞入
        landMarkPositionX: '129', //标志物位置X
        landMarkPositionY: '142', //标志物位置Y
        //游戏资源
        gameList: [


          {
                audio: "assets/audios/hippo.mp3", //河马 题干音频
                audioTime:2000, // 音频时长
                rightImg: "assets/images/p1.png", //正确图片
                rightPositionStartX: "489", //正确图片初始位置X
                rightPositionStartY: "352", //正确图片初始位置Y
                rightPositionEndX: "649", //正确图片最终位置X
                rightPositionEndY: "436", //正确图片最终位置Y
                shadeImg: "assets/images/z1.png", //遮罩图片
                shadePositionX: "369", //遮罩图片位置X
                shadePositionY: "394", //遮罩图片位置Y
                landMarkPositionIngameX: "489", //游戏中标志物位置X
                landMarkPositionIngameY: "604", //游戏中标志物位置Y
            },
            {
                audio: "assets/audios/gorilla.mp3", //猩猩 题干音频
                audioTime:2000, // 音频时长
                rightImg: "assets/images/p2.png", //正确图片
                rightPositionStartX: "1569", //正确图片初始位置X
                rightPositionStartY: "184", //正确图片初始位置Y
                rightPositionEndX: "1209", //正确图片最终位置X
                rightPositionEndY: "142", //正确图片最终位置Y
                shadeImg: "assets/images/z2.png", //遮罩图片
                shadePositionX: "1409", //遮罩图片位置X
                shadePositionY: "142", //遮罩图片位置Y
                landMarkPositionIngameX: "1449", //游戏中标志物位置X
                landMarkPositionIngameY: "226", //游戏中标志物位置Y
            },
            {
                audio: "assets/audios/goat.mp3", //羊 题干音频
                audioTime:2000, // 音频时长
                rightImg: "assets/images/p3.png", //正确图片
                rightPositionStartX: "1289", //正确图片初始位置X
                rightPositionStartY: "478", //正确图片初始位置Y
                rightPositionEndX: "1529", //正确图片最终位置X
                rightPositionEndY: "310", //正确图片最终位置Y
                shadeImg: "assets/images/z3.png", //遮罩图片
                shadePosition: "424", //遮罩图片位置
                shadePositionX: "1049", //遮罩图片位置X
                shadePositionY: "520", //遮罩图片位置Y
                landMarkPositionIngameX: "1689", //游戏中标志物位置X
                landMarkPositionIngameY: "688", //游戏中标志物位置Y
            },

            {
              audio: "assets/audios/rabbit.mp3", //兔子 题干音频
              audioTime:2000, // 音频时长
              rightImg: "assets/images/p4.png", //正确图片
              rightPositionStartX: "649", //正确图片初始位置X
              rightPositionStartY: "814", //正确图片初始位置Y
              rightPositionEndX: "849", //正确图片最终位置X
              rightPositionEndY: "730", //正确图片最终位置Y
              shadeImg: "assets/images/z4.png", //遮罩图片
              shadePositionX: "249", //遮罩图片位置X
              shadePositionY: "772", //遮罩图片位置Y
              landMarkPositionIngameX: "689", //游戏中标志物位置X
              landMarkPositionIngameY: "730", //游戏中标志物位置Y
          },

        ],
    }
};
(function(pageNo) {
    configData.page = pageNo
})(0)
