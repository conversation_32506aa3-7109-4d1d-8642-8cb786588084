<!DOCTYPE html>
<html lang="en">

<head>
        <% var title="TME0001_泡泡爱消除_中文版"; %>
        <%include ./src/common/template/index_head %>
</head>

<body>
    <div class="container" id="container" data-syncresult='show-result-1'>
        <section class="commom">
            <div class="desc"></div>
            <div class="title">
                <h3></h3>
            </div>
        </section>

        <section class="main">
            <audio src="./audio/true.mp3" class="trueMp3" data-syncaudio="audiotrue"></audio>
            <audio src="./audio/false.mp3" class="falseMp3" data-syncaudio="audiofalse"></audio>
            <!--初始泡泡 -->
            <div class="initBubble">
                <ul class="initBubbleList">
                </ul>
                <div class="bubbleBoxList">
                </div>
            </div>
            <!-- 添加泡泡 -->
            <!-- <div class="addBubble">
                    <ul class="addBubbleList">
                    </ul>
            </div> -->
            <!-- 倒计时 -->
            <!-- <div class="time">
                <span class="timeNum"><em class="num"></em>s</span>
            </div> -->
            <!-- 计时器 -->
            <div class="time">
                <div class="proprogressBarBox">
                    <p class="proprogressBar"></p>
                </div>
                <div class="watch"></div>
            </div>
            <!-- 控制台 -->
            <div class="control">
                <div class="font"></div>
                <div class="btns">
                    <span class="btn trueBtn" data-val="trueBtn" data-syncactions="trueBtn"></span>
                    <span class="btn falseBtn" data-val="falseBtn" data-syncactions="falseBtn"></span>
                </div>
            </div>
            <!-- 炮弹区域 -->
            <div class="table"></div>
            <div class="showBubble">
                <div class="showBubbleList">
                </div>
            </div>
            <!-- 遮罩层 -->
            <% include ./src/common/template/resultPage/index.ejs %>
            <!-- 倒计时 -->
            <div class="timeMask">
                <div class="startBox">
                    <!-- <img src="//cdn.51talk.com/apollo/images/b7c0fb23f96035782eb7f4077be0b989.png" class="startFont"/>
                    <img src="//cdn.51talk.com/apollo/images/ae3ffae2c80ffef4fbea1c105f81be9f.png" class="startBtn" data-syncactions="startBtn"/> -->
                    <img src="./image/b7c0fb23f96035782eb7f4077be0b989.png" class="startFont"/>
                    <img src="./image/ae3ffae2c80ffef4fbea1c105f81be9f.png" class="startBtn" data-syncactions="startBtn"/>
                </div>
                <div class="timeChangeBox hide">
                    <div class="timeBg">
                        <div class="numberList"></div>
                        <audio src="./audio/timeLow.mp3" class="timeLowAudio_1" data-syncaudio="audio1"></audio>
                        <audio src="./audio/timeLow.mp3" class="timeLowAudio_2" data-syncaudio="audio2"></audio>
                        <audio src="./audio/timeLow.mp3" class="timeLowAudio_3" data-syncaudio="audio3"></audio>
                        <audio src="./audio/timehigh.mp3" class="timeLowAudio_4" data-syncaudio="audio4"></audio>
                    </div>
                </div>
            </div>
            <!-- 发送信息 “时间结束而失败” -->
            <div class="sendLose hide" data-syncactions="sendLose"></div>
        </section>

    </div>
    <script inline type="text/javascript" src="js/funParabola.js"></script>
    <%include ./src/common/template/index_bottom %>
</body>

</html>
