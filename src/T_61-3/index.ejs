<!DOCTYPE html>
<html lang="en">

<head>
  <% var title="LCH0002FT_计时听音点选FT"; %>
  <%include ./src/common/template/index_head %>
  <script src="js/lottie_svg.min.js"></script>
</head>

<body>
  <div class="container" id="container" data-syncresult="1">
    <!-- 反馈动效 -->
    <%include ./src/common/template/feedbackAnimation/index.ejs %>

    <section class="commom">
      <div class="desc">123132</div>
    </section>

    <section class="main">
      <!-- demo模式黑色遮罩 -->
      <div class="right-top-demo"></div>
      <!-- 正确错误音效 -->
      <audio src="" class="rightAudio" data-syncaudio="rightAudio"></audio>
      <audio src="./audio/wrong.mp3" class="wrongAudio" data-syncaudio="wrongAudio"></audio>
      <!-- 选项 -->
      <ul class="optionUl"></ul>
      <!-- 小手 -->
      <div class="hand"></div>
      <!-- 像素格 -->
      <div class="boxList">
        <ul class="boxUl"></ul>
      </div>
      <!-- 计分 -->
      <!-- <div class="intSatge hide">
        <div class="countImg"></div>
        <span class="score">x <span class="scoreNum">0</span></span>
      </div> -->
      <!-- 计时器 -->
      <div class="time">
        <div class="proprogressBarBox hide">
          <p class="proprogressBar"></p>
          <div class="extraBox"></div>
        </div>

        <div class="horn hide" id="horn"></div>
        <audio class="frequencyAudio" data-syncaudio="frequencyAudio" ></audio>
      </div>
      <!-- 功能遮罩层 -->
      <div class="funcMask">
        <!-- 预习模式操作区 -->
        <div class="startBox">
          <div class="demoTextBox">
            <img src="./image/********************************.png" class="startMsg" />
          </div>
          <div class="demoBtnBox">
            <img src="./image/********************************.png"
              class="demo-btnStu" /><img src="./image/0250983386ad8a2c2226cac7b83be49e.png"
              class="startBtn">
          </div>

        </div>
        <!-- 倒计时 -->
        <div class="timeChangeBox hide">
          <div class="timeBg">
            <div class="numberList"></div>
            <audio src="./audio/timeLow.mp3" class="timeLowAudio_1" data-syncaudio="timeLowAudio_1"></audio>
            <audio src="./audio/timeLow.mp3" class="timeLowAudio_2" data-syncaudio="timeLowAudio_2"></audio>
            <audio src="./audio/timeLow.mp3" class="timeLowAudio_3" data-syncaudio="timeLowAudio_3"></audio>
            <audio src="./audio/timehigh.mp3" class="timeLowAudio_4" data-syncaudio="timeLowAudio_4"></audio>
          </div>
        </div>
      </div>
      <!-- 终局页 -->
      <div class="endGame">
        <div class="hiddenContain">
          <ul class="scrollUl scroll-1">
            <li data-syncactions='list0'></li>
            <li data-syncactions='list1'></li>
            <li data-syncactions='list2'></li>
          </ul>
          <ul class="scrollUl scroll-2">
            <li data-syncactions='list3'></li>
            <li data-syncactions='list4'></li>
            <li data-syncactions='list5'></li>
          </ul>
        </div>
        <div class="scrollBtn">
          <span class="leftScroll" data-syncactions="leftScroll"></span>
          <span class="rightScroll" data-syncactions="rightScroll"></span>
        </div>
      </div>
      <div class="tea-stu-not-in"></div>
      <div class="overBtn" data-syncactions="overBtn"></div>
      <div class="outBtn" data-syncactions="outBtn"></div>
      <div class="runing" data-syncactions="runing"></div>
    </section>
    <div class="doneTip hide">
      <p>It's S's turn, you can't play now.</p>
    </div>
    <script type="text/javascript">
      document.documentElement.addEventListener('touchstart', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault();
        }
      }, false);
      // 禁用手指双击缩放：

      var lastTouchEnd = 0;
      document.documentElement.addEventListener('touchend', function (event) {
        var now = Date.now();
        if (now - lastTouchEnd <= 300) {
          event.preventDefault();
        }
        lastTouchEnd = now;
      }, false);
    </script>
  </div>
  <!-- 图片预加载 -->
  <div id="preload-01"></div>
  <div id="preload-02"></div>
  <% include ./src/common/template/ribbonWin/index.ejs %>
  <%include ./src/common/template/index_bottom %>
</body>

</html>
