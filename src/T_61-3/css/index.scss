@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import '../../common/template/ribbonWin/style.scss';

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
    position: absolute;
    left: $l;
    top: $t;
    width: $w;
    height: $h;
}

body {
    overflow: hidden;
}

.commom {
    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 2.2rem;
    position: absolute;
    right: 0px;

    .desc {
        top: 0.6rem;
    }

    .title-first {
        width: 100%;
        height: 0.8rem;
        padding: 0 1.4rem;
        box-sizing: border-box;
        text-align: center;
        margin: 0.45rem auto 0.2rem;
        font-size: 0.8rem;
        font-weight: bold;
        color: #333;
    }
}

.container {
    background-size: auto 100%;
    position: relative;

    //记分台
    .intSatge {
        position: absolute;
        bottom: 2.4rem;
        left: 1.7rem;
        width: 1.7rem;
        height: 0.6rem;
        border-radius: 0.3rem;

        .countImg {
            position: absolute;
            background-size: 1.61rem 1.69rem;
            left: -1.1rem;
            top: -0.65rem;
            width: 2.15rem;
            height: 1.67rem;
            background-position-x: 0rem;
            background-repeat: no-repeat;
            transform: scale(0.65);
            z-index: 1;
        }

        .wrong {
            background-position-x: -10.75rem;
        }

        .score {
            width: 100%;
            height: 100%;
            border-radius: 0.3rem;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 0.46rem;
            color: #f9bc2e;
            box-sizing: border-box;
            padding-left: 0.7rem;
            line-height: 0.6rem;
            font-weight: 400;
            color: rgba(255, 100, 14, 1);
        }
    }

    // 计时器
    .time {
        position: absolute;
        left: 1.3rem;
        bottom: 1.05rem;
        height: 1.2rem;
        width: 5.5rem;

        .proprogressBarBox {

            position: absolute;
            left: 5.55rem;
            top: -7rem;
            width:5.6rem;
            height: .36rem;
            padding-left: 0.29rem;
            background: url("../image/c9ebadee589b4108e042603e1d682d3e.png") no-repeat;
            background-size:5.6rem .36rem;

            .proprogressBar {
                width: 0%;
                height: 0.28rem;
                background: url("../image/382dc6b901d4443bbdfd93906a954722.png") no-repeat;
                background-size: cover;
                border-radius: 1rem;
                transition: width 0.5s;
                position: absolute;
                top: 50%;
                left: 0.05rem;
                transform: translateY(-54%);
              }
        }
        .extraBox {
            position: absolute;
            right: 1%; /* 根据需要调整，使它在 .proprogressBarBox 的右边 */
            top: 35%; /* 保证垂直居中 */
            width: 0.63rem; /* 根据需要调整 */
            height: 0.63rem; /* 根据需要调整 */
            background-image: url("../image/f04bd28ac1fd1cdca5c63b0abffa80bf.png");
            background-size: contain; /* 防止图片变形 */
            background-repeat: no-repeat;
            background-position: center;
            transform: translateY(-50%); /* 居中策略 */
        }

        .horn {
            position: absolute;
            left: 0rem;
            top: -0.1rem;
            width: 1rem;
            height: 1rem;

        }
    }

    //喇叭动效
    .hornAnimation {
        animation: hornPlay 1s steps(3) infinite;
        -webkit-animation: hornPlay 1s steps(3) infinite;
    }

    @keyframes hornPlay {
        0% {
            background-position: 0 0;
        }

        100% {
            background-position: 18.69rem 0;
        }
    }

    @-webkit-keyframes hornPlay {
        0% {
            background-position: 0 0;
        }

        100% {
            background-position: 18.69rem 0;
        }
    }

    // 功能遮罩层
    .funcMask,
    .tea-stu-not-in {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 21;
        display: none;

        .startBox {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 7.02rem;
            width: 10.5rem;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 0.5rem;

            .demoTextBox {
                width: 9.26rem;
                height: 3.9rem;
                position: absolute;
                left: 50%;
                top: 0.64rem;
                transform: translate(-50%);
                border-radius: 0.3rem;
                background: rgba(0, 0, 0, 0.2);
                text-align: center;

                .startMsg {
                    width: 6.75rem;
                    height: 2.88rem;
                    margin-top: 0.4rem;
                }
            }

            .demoBtnBox {
                width: 5.72rem;
                height: 1.14rem;
                transform: translate(-50%);
                // background: rgba(0, 0, 0, 0.2);
                text-align: center;
                position: absolute;
                left: 50%;
                bottom: 0.75rem;

                .demo-btnStu {
                    width: 2.14rem;
                    height: auto;
                    cursor: pointer;
                    display: inline-block;
                }

                .startBtn {
                    width: 2.03rem;
                    height: auto;
                    cursor: pointer;
                    display: inline-block;
                    margin-right: 0;
                    margin-left: 1.55rem;
                }
            }
        }

        .timeChangeBox {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 4.8rem;
            width: 7.2rem;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 0.5rem;

            .timeBg {
                width: 3.79rem;
                height: 3.84rem;
                position: absolute;
                top: 1rem;
                background: url(../image/timeBg.png) no-repeat;
                background-size: 100% 100%;
                left: 50%;
                margin-left: -1.9rem;
                top: 50%;
                margin-top: -1.92rem;

                .numberList {
                    width: 1.5rem;
                    height: 1.5rem;
                    position: absolute;
                    left: 0;
                    bottom: 0;
                    right: 0;
                    top: 0;
                    margin: auto;
                    background: url(../image/number1.png) no-repeat;
                    background-size: 6rem 100%;
                    background-position-x: 0.1rem;
                }
            }
        }
    }

    //终局页
    .endGame {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        z-index: 20;
        background: rgba(0, 0, 0, 0.5);
        display: none;


        .hiddenContain {
            overflow: hidden;
            width: 85%;
            position: absolute;
            top: 50%;
            left: 50%;
            text-align: center;
            transform: translate(-50%, -50%);
            height: 7.1rem;
            margin-left: 0.42rem;

            ul {
                height: 7.1rem;
                overflow-x: auto;
                overflow-y: hidden;
                white-space: nowrap;
                display: flex;
                flex-flow: row nowrap;
                justify-content: center;
                align-items: center;

                li {
                    width: 4.05rem;
                    height: 5.8rem;
                    background: rgba(255, 255, 255, 1);
                    border-radius: 0.76rem;
                    display: inline-block;
                    margin-right: 0.9rem;
                    // background-size: 90%;
                    background-origin: content;
                    background-repeat: no-repeat;
                    background-position: center center;
                    opacity: 0;
                    position: relative;
                }
            }

            // .scroll-2{
            //   justify-content: left!important;
            // }

            // 音频播放
            .example-audio {
                display: flex;
                justify-content: center;
                align-items: center;
                position: absolute;
                width: 1.45rem;
                height: 1.34rem;
                background: url(../image/sound_bg.png) no-repeat center;
                background-size: cover;
                cursor: pointer;
                position: absolute;
                bottom: .13rem;
                left: .17rem;

                .small {
                    width: 0.9rem;
                    margin-top: 1px;
                }

                img {
                    width: 1.45rem;
                }

                .gif {
                    display: none;
                }
            }
        }

        .scrollBtn {
            position: absolute;
            bottom: 0.5rem;
            left: 50%;
            transform: translate(-50%);

            span {
                display: inline-block;
                width: 1.36rem;
                height: 1.36rem;
                cursor: pointer;
                left: 0.92rem;
            }

            .leftScroll {
                background: url(../image/left.png) no-repeat;
                background-size: contain;
                opacity: 0.5
            }

            .rightScroll {
                background: url(../image/right.png) no-repeat;
                background-size: contain;
            }
        }
    }
}

.big {
    transform: scale(1.05);
}

.content {
    width: 16.4rem;
    height: 4.5rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    margin-left: 1.4rem;
    position: absolute;
    bottom: 0.9rem;
}

.main {
    width: 100%;
    height: 100%;

    .optionUl {
        position: relative;
        width: 100%;
        height: 100%;

        li {
            position: absolute;
            cursor: pointer;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            display: none;
        }

        .optionRight {
            padding: 0.3rem;
            animation: imTypeTrue 1.3s 1 forwards;
        }
    }

    .right-top-demo {
        display: none;
        position: absolute;
        right: 0;
        top: 0;
        width: 1.95rem;
        height: 0.67rem;
        background: rgb(0, 0, 0);
        opacity: 0.6;
        border-bottom-left-radius: 0.22rem;
        color: #fff;
        line-height: 0.67rem;
        font-size: 0.43rem;
        width: 100%;
        height: 100%;

        img {
            width: 0.51rem;
            height: 0.53rem;
            margin-left: 0.22rem;
        }
    }


}

//终局特效
.perfectBox {
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 11.34rem;
    height: 11.33rem;
    transform: translateX(-50%) translateY(-50%);
    // background-color: red;

    .light {
        position: absolute;
        z-index: 100;
        width: 10.87rem;
        height: 10.87rem;
        // animation: rotation 6s linear infinite;
    }
    .star {
        position: absolute;
        z-index: 100;
        width: 9.87rem;
        height: 9.87rem;
        // animation: rotation 6s linear infinite;
    }
    .aperture {
        position: absolute;
        top: 10%;
        left: 10%;
        z-index: 100;
        width: 8.87rem;
        height: 8.87rem;
        // animation: rotation 6s linear infinite;
    }

    // @-webkit-keyframes rotation {
    //     from {
    //         -webkit-transform: rotate(0deg);
    //     }

    //     to {
    //         -webkit-transform: rotate(360deg);
    //     }
    // }

    .cup {
        position: absolute;
        top: 35%;
        left: 35%;
        z-index: 110;
        width: 3.34rem;
        height: 3.53rem;
    }

    .numBox {
        position: absolute;
        bottom: 0;
        width: 3.7rem;
        height: 1.38rem;
        background-color: #fff;
        border-radius: .68rem;
        z-index: 102;
        transform: translateX(-50%);
        left: 50%;

        img {
            width: 1.11rem;
            height: 1.17rem;
            position: absolute;
            left: 0.46rem;
            top: 50%;
            transform: translateY(-50%);
        }

        label {
            display: inline-block;
            width: 0.03rem;
            height: 0.7rem;
            background: rgba(147, 147, 147, 1);
            position: absolute;
            left: 1.93rem;
            top: 50%;
            transform: translateY(-50%);
        }

        .score {
            position: absolute;
            left: 2.27rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 108, 15, 1);
            font-size: 0.91rem;
        }
    }
}


.doneTip {
    width: 7.91rem;
    height: 1rem;
    border-radius: 0.2rem 0.2rem 0 0;
    position: absolute;
    margin-left: -3.5rem;
    left: 50%;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    font-size: 0.3rem;

    p {
        height: 100%;
        width: 100%;
        line-height: 1rem;
        text-align: center;
    }

    .btn {
        position: absolute;
        top: 0.2rem;
        height: 0.23rem;
        padding: 0.17rem 0.26rem;
        color: #fff;
        text-align: center;

        line-height: 0.23rem;
        border-radius: 0.3rem;
        cursor: pointer;
    }

    .demo-btn {
        background: #f1a91e;
        left: 5.6rem;
    }
}

.boxList {
    position: absolute;
    left: 1.08rem;
    top: 0.3rem;
    width: 17rem;
    height: 9.8rem;
    z-index: -1;

    .boxUl {
        position: absolute;
        left: 0.2rem;
        top: 0.7rem;
        width: 16rem;
        height: 8.4rem;
        display: flex;
        flex-wrap: wrap;

        li {
            // background: red;
            margin-left: 0.01rem;
            width: 0.39rem;
            height: 0.39rem;
        }
    }
}

//小手点击动画
.hand {
    position: absolute;
    left: 1.4rem;
    top: 1.3rem;
    margin-left: -0.35rem;
    background: url("../image/hands.png");
    background-size: 7.2rem 1.8rem;
    cursor: pointer;
    opacity: 0;
    z-index: 99;
    width: 0px;
    height: 0px;
}

.handAnimation {
    opacity: 1;
    width: 1.8rem;
    height: 1.8rem;
    left: 5.5rem;
    top: 6rem;
    animation-name: handLclick, handHide;
    animation-duration: 1s, 1s;
    animation-timing-function: steps(4, end), linear;
    animation-delay: 0s, 2s;
    animation-iteration-count: 2, 1;
    animation-fill-mode: forwards, forwards;
    animation-direction: normal, normal;

    // -webkit-animation-name: handLclick, handHide;
    // -webkit-animation-duration: 1s, 1s;
    // -webkit-animation-timing-function: steps(4, end), linear;
    // -webkit-animation-delay: 1s, 4s;
    // -webkit-animation-iteration-count: 3, 1;
    // -webkit-animation-fill-mode: forwards, forwards;
    // -webkit-animation-direction: normal, normal;
}

@keyframes handLclick {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 133% 0;
    }
}

@-webkit-keyframes handLclick {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 133% 0;
    }
}

@keyframes handHide {
    0% {
        opacity: 0;
        width: 0px;
    }

    100% {
        opacity: 0;
        width: 0px;
    }
}

@-webkit-keyframes handHide {
    0% {
        opacity: 0;
        width: 0px;
    }

    100% {
        opacity: 0;
        width: 0px;
    }
}



//选项上下抖动
.optionUDAni {
    animation: optionUD linear 1s 2s infinite alternate;
    -webkit-animation: optionUD linear 1s 2s infinite alternate;
}

@keyframes optionUD {
    0% {
        transform: translateY(0px);
    }

    100% {
        transform: translateY(10px);
    }
}

@-webkit-keyframes optionUD {
    0% {
        transform: translateY(0px);
    }

    100% {
        transform: translateY(10px);
    }
}

//其他选项
.optionOther {
    animation: imTypeOther 0.3s 1 forwards;
}

//正确选项动画
@keyframes imTypeTrue {

    //0.3秒放大  停留0.2s  0.8秒缩小并消失
    23% {
        transform: scale(1.2, 1.2);
    }

    38% {
        transform: scale(1.2, 1.2);
    }

    100% {
        transform: scale(0, 0);
    }
}

//其他选项动画
@keyframes imTypeOther {

    100% {
        opacity: 0;
    }
}

//2帧雪碧图正确反馈
.imgType2Right {
    animation: imgType2 0.5s steps(2) 1 forwards;
}

@keyframes imgType2 {
    0% {
        background-position: 0 0;
        transform: scale(1, 1);
    }

    100% {
        background-position: 200% 0;
        transform: scale(0, 0);
    }
}

@-webkit-keyframes imgType2 {
    0% {
        background-position: 0 0;
        transform: scale(1, 1);
    }

    100% {
        background-position: 200% 0;
        transform: scale(0, 0);
    }
}

//3帧雪碧图正确反馈
.imgType3Right {
    animation: imgType3 0.5s steps(3) 1 forwards;
}

@keyframes imgType3 {
    0% {
        background-position: 0 0;
        transform: scale(1, 1);
    }

    100% {
        background-position: 150% 0;
        transform: scale(0, 0);
    }
}

@-webkit-keyframes imgType3 {
    0% {
        background-position: 0 0;
        transform: scale(1, 1);
    }

    100% {
        background-position: 150% 0;
        transform: scale(0, 0);
    }
}

//错误反馈动画
.shake {
    animation: shakeUp 0.4s both ease-in;
}

@keyframes shakeUp {
    0% {
        transform: translateX(10px);
    }

    20% {
        transform: translateX(-10px);
    }

    40% {
        transform: translateX(10px);
    }

    60% {
        transform: translateX(-10px);
    }

    80% {
        transform: translateX(10px);
    }

    100% {
        transform: translateX(0px);
    }
}

//终局卡片底部切换特效(卡片大于3时)
@keyframes card1 {
    0% {
        background-position: 0 0;
        transform: scale(1, 1);
    }

    100% {
        // opacity: 0;
        // width: 0;
        background-position: 150% 0;
        transform: scale(0, 1);
    }
}

* {
    touch-action: pan-y;
}



#preload-01 {
    background: url(../image/light.png) no-repeat -9999px -9999px;
}

#preload-02 {
    background: url(../image/prefect.png) no-repeat -9999px -9999px;
}
