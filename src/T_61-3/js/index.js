"use strict";
import '../../common/js/common_1v1.js';
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import {
    resultWin,
    resultHide
} from '../../common/template/ribbonWin/index.js';
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import {
  feedbackAnimation
} from "../../common/template/feedbackAnimation/index.js";
import { USER_TYPE, CLASS_STATUS  ,TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";  // 导入常量

$(function() {
    SDK.reportTrackData({
        action: 'PG_FT_INTERACTION_LIST',
        data: {
           roundcount: configData.source.frequencyList.length,
        },
        teaData: {
          teacher_type:TEACHER_TYPE.PRACTICE_INPUT,
          interaction_type:INTERACTION_TYPE.CHOOSE,
          useraction_type:USERACTION_TYPE.LISTEN
        },
      })

    window.h5Template = {
        hasDemo: '1',
        hasPractice: '2'
    };

    let staticData = configData.source;
    let audioList = staticData.audioList;
    let imgList = staticData.imgList;
    let frequencyList = staticData.frequencyList;
    let timeIndex = 0;
    let userType = window.frameElement && window.frameElement.getAttribute('user_type');
    let classStatus = 1;
    let score = 0;
    let rightArr = {
        allRightArr: [],
        currentRightNum: 0
    };

    let progressPerStep = 95 / frequencyList.length;
    let newWidth = 0;
    let characterNum = 0;
    let optionClickStus = true;  // 是否允许点击泡泡选项
    let hideOptionArr = [];
    let demoTimer;
    let demoOptionNum = 4;
    let hasStarted = false;
    let gameOverSent = false;
    let isPlayingAudio = false; // 正在播放音频的状态
    let isFirst = true;
    if (configData.bg == '') {
        $(".container").css({
            'background-image': 'url(./image/defaultBg.jpg)',
        });
    }

    renderPx();
    initHand();
    sorceRightAudio();
    isShowBtn();
    preloadImg(imgList, []);
    staticInit();

    function renderPx() {
        let liHtml = '';
        for (let i = 1; i < 801; i++) {
            liHtml += `<li class="pos_${i}"></li>`;
        }
        $('.boxUl').html(liHtml);
    }

    function initHand() {
        let true_options = frequencyList[0].options.filter(function(item) {
            return (item.result == '1');
        });
        let firtstPosition = true_options[0].position;
        if (firtstPosition) {
            let left = ($('.pos_' + firtstPosition).offset().left - $('.container').offset().left) / window.base + 2 + 'rem';
            let top = ($('.pos_' + firtstPosition).offset().top - $('.container').offset().top) / window.base + 2 + 'rem';
            $('.hand').css({
                left: left,
                top: top
            });
        }
    }

    function sorceRightAudio() {
        $(".countImg").css({
            'background-image': 'url(' + staticData.countImg + ')'
        });
        $(".rightAudio").attr("src", staticData.rightAudio ? staticData.rightAudio : './audio/right.mp3');
    }

    function isShowBtn() {
        if (isSync) {
            classStatus = SDK.getClassConf().h5Course.classStatus;
            if (classStatus == 0 && userType == "stu") {
                $(".funcMask").show();
            }
        } else {
            var hrefParam = parseURL("http://www.example.com");
            if (top.frames[0] && top.frames[0].frameElement) {
                hrefParam = parseURL(top.frames[0].frameElement.src)
            }
            var role_num = hrefParam.params['role'];

            function parseURL(url) {
                var a = document.createElement('a');
                a.href = url;
                return {
                    source: url,
                    protocol: a.protocol.replace(':', ''),
                    host: a.hostname,
                    port: a.port,
                    query: a.search,
                    params: (function() {
                        var ret = {},
                            seg = a.search.replace(/^\?/, '').split('&'),
                            len = seg.length,
                            i = 0,
                            s;
                        for (; i < len; i++) {
                            if (!seg[i]) {
                                continue;
                            }
                            s = seg[i].split('=');
                            ret[s[0]] = s[1];
                        }
                        return ret;
                    })(),
                    file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
                    hash: a.hash.replace('#', ''),
                    path: a.pathname.replace(/^([^\/])/, '/$1'),
                    relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
                    segments: a.pathname.replace(/^\//, '').split('/')
                }
            }
            if (role_num == '1' || role_num == undefined) {
                $(".funcMask").show();
            } else if (role_num == '2') {
                $(".funcMask").show();
            }
        }
    }

    function staticInit() {
        let options = frequencyList[timeIndex].options;
        $(".optionUl").html("");
        options.forEach(function(img, index) {
            let liEle = $('<li class="optionLi static"></li>');
            let currentPosition = img.position;
            let left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base + 'rem';

            liEle.attr({
                result: img.result,
            }).css({
                display: 'block',
                left: left,
                top: top,
                'background-image': 'url(' + imgList[img.src - 1].image + ')'
            });

            $(".optionUl").append(liEle);

            let image = new Image();
            image.src = imgList[img.src - 1].image;
            image.onload = function() {
                initImg(liEle, {
                    naturalWidth: this.naturalWidth,
                    naturalHeight: this.naturalHeight
                });
            };
        });
    }

    window.SDK.actAuthorize = function(message) {
        if (isSync) {
            if (userType == 'tea' && SDK.getClassConf().h5Course.classStatus == 5) {
                $(".doneTip").removeClass('hide');
            }

            if (message && message.operate == 5) {
                isFirst = false;
            }
            if (message && message.type == 'practiceStart') {
                if (isFirst) {
                    isFirst = false;
                  SDK.reportTrackData({
                    action: 'CK_FT_INTERACTION_STARTBUTTON',
                    teaData: {}
                  },USER_TYPE.TEA)
                    $(".funcMask").show();
                    threeTwoOne();
                }
            }
        }
    }

    let startBtnStatus = true;
    $(".startBtn").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault();
        }
        e.stopPropagation();
        if (startBtnStatus) {
            startBtnStatus = false;
          SDK.reportTrackData({
            action: 'CK_FT_INTERACTION_STARTBUTTON',
            teaData: {}
          },USER_TYPE.TEA)
            threeTwoOne();
        }
    });

    window.SDK.actDemo = function(message) {
        demoFun(false);
        if (isSync) {
            SDK.bindSyncEvt({
                index: 'clear',
                eventType: 'click',
                method: 'event',
                syncName: 'clear',
                recoveryMode: '1'
            });
        }
        SDK.setEventLock();
    };

    $(".demo-btnStu").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault();
        }
        e.stopPropagation();
        demoFun(true);
    });

    function threeTwoOne() {
        let q = 1;
        $('.startBox').hide().siblings('.timeChangeBox').show().find('.numberList');
        SDK.playRudio({
            index: $('.timeLowAudio_' + q).get(0),
            syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
        });
        let audioPlay = setInterval(function() {
            q++;
            if (q > 4) {
                clearInterval(audioPlay);
                SDK.setEventLock();
                $('.funcMask').hide();
                $('.timeChangeBox').hide();
                optionsInit(timeIndex); // 不用看
                addEvent();
                initProgressBar();  // 不用看
                gameProcessBidSync();
            } else {
                SDK.playRudio({
                    index: $('.timeLowAudio_' + q).get(0),
                    syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
                });
                $('.numberList').css({
                    'background-position-x': -(1.5 * (q - 1)) + 'rem'
                });
            }
        }, 1000);
    }

    function initProgressBar() {
        $(".proprogressBar").css("width", "0%");
    }

    async function initLottie() {
        try {
            characterNum = await lottieAnimations.init(
                characterNum,
                // "//cdn.51suyang.cn/apollo/public/json/laba.json",
                "./image/laba.json",
                `#horn`
            );
            lottieAnimations.play(characterNum);
        } catch (error) {
            console.error("初始化 Lottie 动画失败:", error);
            return null;
        }
    }
    initLottie();

    function optionsInit(index, tip) {
        if (!tip) {
            $(".intSatge").removeClass("hide");
            $(".proprogressBarBox").removeClass("hide");
            $(".horn").removeClass("hide");
        }
        if (index > frequencyList.length - 1) {
            return;
        }
        hideOptionArr = [];
        let options = frequencyList[index].options;
        if (tip == 'tip') {
            demoOptionNum = frequencyList[index].options.length;
        }
        let audio = frequencyList[index].audio;

        let preList = [],
            preImgs = [];
        $(".optionUl").html("");
        options.forEach(function(img, index) {
            if (img.result == 1) {
                rightArr.allRightArr.push({
                    srcNum: img.src,
                    image: imgList[img.src - 1].image,
                    audio: audioList[audio - 1].audio
                });
            }
            let liEle = "";
            let currentPosition = img.position;
            let left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
            liEle = $('<li class="optionLi"></li>');
            liEle.attr({
                result: img.result,
                'data-syncactions': 'freq' + index + 'bubble' + index + '',
                'data-index': index
            });
            liEle.css({
                left: left,
                top: top,
                'background-image': 'url(' + imgList[img.src - 1].image + ')',
            });
            $(".optionUl").append(liEle);
            if (staticData.imgTypeEffect == 1) {
                $(".optionLi").addClass("optionUDAni");
            }

            preList.push({
                image: imgList[img.src - 1].image
            });
        });

        let optionIndex = 0;
        let intervalTimer = setInterval(() => {
            const bubble = $(".optionLi").eq(optionIndex);
            if (bubble.length) {
                bubble.stop(true, true).fadeIn(300);
                optionIndex++;
            } else {
                clearInterval(intervalTimer);
            }
        }, 300);

        $.when(preloadImg(preList, preImgs)).done(
            function() {
                preImgs.forEach(function(item, index) {
                    initImg($(".optionLi").eq(index), item);
                });
                optionClickStus = true; // 仅在图像加载并渲染后允许选项点击
            }
        );
        $(".frequencyAudio").attr("src", audioList[audio - 1].audio);
        if (tip == 'tip') {
            $(".frequencyAudio").removeAttr('loop');
        } else {
            $(".frequencyAudio").attr('loop', 'loop');
        }

        soundControl().pause();
        soundControl().play(tip);
        rightArr.currentRightNum = 0;
        $('.scoreNum').html(score);
    }

    function addEvent() {
        $(".optionLi").on("click touchstart", function(e) {
            if (e.type == "touchstart") {
                e.preventDefault();
            }
            e.stopPropagation();
            if (isSync) {
                classStatus = SDK.getClassConf().h5Course.classStatus;
                if (classStatus != 0 && userType == 'tea') {
                    SDK.setEventLock();
                    return false;
                }
            }
            if (optionClickStus) {
                optionClickStus = false; // 一旦开始处理点击，立刻设置为不可点击
                if (!isSync) {
                    $(this).trigger("syncItemClick");
                    return;
                }
                SDK.bindSyncEvt({
                    sendUser: '',
                    receiveUser: '',
                    index: $(e.currentTarget).data("syncactions"),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'syncItemClick',
                    otherInfor: {
                        timeIndex: timeIndex,
                        score: score,
                        currentRightNum: rightArr.currentRightNum,
                        allRightArr: rightArr.allRightArr
                    },
                    recoveryMode: '1'
                });
            }
        });

        $(".optionLi").on('syncItemClick', function(e, message) {
            let self = $(this);
            let result = self.attr("result");
            if (result == 1) {
                rightWrong(self).right();
                updateProgressBar();
                self.fadeOut(300, function() {
                    hideOptionArr = [];
                    hideOptionArr.push(self.data("syncactions"));
                    setTimeout(() => {
                        $(".optionLi").fadeOut(300);
                        soundControl().pause();
                        startNext(); // 开始下一轮
                    }, 1000);
                });
                optionClickStus = false;  // 确保音效结束后一定不会重复进入状态
            } else {
                rightWrong(self).wrong();
                optionClickStus = true; // 立即重新允许点击泡泡
            }
            SDK.reportTrackData({
                action:'CK_FT_ANSWER_RESULT', //事件名称
                data:{}, // 老师和学生  都需要上报的数据
                teaData:{
                    //点击的元素的src
                    roundid: timeIndex+1,
                    item: self.data("index")+1,
                    result: self.attr("result")== 1?'right':'wrong',
                },  // 只有老师端会上报的数据
            },USER_TYPE.TEA)

            self.on('animationend webkitAnimationEnd', function() {
                self.removeClass('shake');
                SDK.setEventLock();
            });
            SDK.setEventLock();
            gameProcessBidSync();
        });
    }

    function updateProgressBar() {
        newWidth += progressPerStep;
        if (timeIndex >= frequencyList.length - 1) {
            newWidth = 95;
        } else {
            newWidth = Math.min(newWidth, 95);
        }
        $(".proprogressBar").css("width", newWidth + "%");
        if (newWidth >= 95) {
            sendGameOver();
        }
    }

    function soundControl() {
        return {
            'play': function(tip) {
                isPlayingAudio = true;
                setTimeout(() => {
                    $(".frequencyAudio")[0].currentTime = 0;
                    SDK.playRudio({
                        index: $(".frequencyAudio")[0],
                        syncName: $(".frequencyAudio").attr("data-syncaudio")
                    });
                    lottieAnimations.play(characterNum);
                    isPlayingAudio = false; // 解除音频播放状态，仅在一轮音频播放启动后
                }, 200);
            },
            'pause': function() {
                SDK.pauseRudio({
                    index: $(".frequencyAudio")[0],
                    syncName: $(".frequencyAudio").attr("data-syncaudio")
                });
                $(".frequencyAudio")[0].currentTime = 0;
                lottieAnimations.stop(characterNum);
            }
        }
    }

    function gameProcessBidSync() {
        return
        if (isSync) {
            SDK.bindSyncEvt({
                index: 'runing',
                eventType: 'click',
                method: 'event',
                syncName: 'syncRuning',
                otherInfor: {
                    timeIndex: timeIndex,
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    hideOptionArr: hideOptionArr
                },
                recoveryMode: '1'
            });
        }
    }

    $(".runing").on("syncRuning", function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                rebackPage().whenClick(obj);
            }
        }
        SDK.setEventLock();
    });

    function startNext() {
        timeIndex++;
        if (timeIndex >= frequencyList.length) {
            sendGameOver();
        } else {
            optionsInit(timeIndex);
            addEvent();
            gameProcessBidSync();
        }
    }

    function sendGameOver() {
        soundControl().pause();
        if (gameOverSent) return;
        gameOverSent = true;
        if (!isSync) {
            $(".overBtn").trigger("gameOver");
        } else {
            SDK.bindSyncEvt({
                index: "overBtn",
                eventType: "mygameevent",
                method: 'event',
                syncName: "gameOver",
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                },
                recoveryMode: '1'
            });
        }
    }

    $(".overBtn").on("gameOver", function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == '5') {
                endGameappendImg('whenend');
                rebackPage().whenEnd(obj);
                return;
            } else {
                endGameappendImg('over');
            }
            if (userType == 'tea' && (SDK.getClassConf().h5Course.classStatus == '5' || SDK.getClassConf().h5Course.classStatus == '1')) {
                SDK.bindSyncCtrl({
                    'type': 'gameOverToCtrl',
                    'data': {
                        CID: SDK.getClassConf().course.id + '',
                        operate: '1',
                        data: [{
                            key: 'classStatus',
                            value: '6',
                            ownerUID: SDK.getClassConf().user.id
                        }]
                    }
                });
            }
        } else {
            endGameappendImg('over');
        }
        SDK.setEventLock();
    });

    async function endGameappendImg(type) {
        $(".doneTip").addClass('hide');
        var new_allRightArr = [];
        var new_allRightArr_index = [];
        rightArr.allRightArr.forEach(function(item, index) {
            if (!new_allRightArr_index.includes(item.srcNum)) {
                new_allRightArr_index.push(item.srcNum);
                new_allRightArr.push(item);
            }
        });
        rightArr.allRightArr = new_allRightArr;
        var time = 1;
        let feedbackObj= {};
        if (score > 0 && type == 'over') {
          feedbackObj = await feedback()//结束后执行反馈动画
          if(feedbackObj.status == true){
            time = 1
          }else{
            time = 2000
          }
            // resultWin({
            //     'WinIsLoop': false,
            //     'Mask_Z_Index': "500"
            // });

            // var perfect = $("<div class='perfectBox'><img src='./image/light1.png' class='light'><img src='./image/star.png' class='star'><img src='./image/aperture.png' class='aperture'><img src='./image/cup.png' class='cup'></div>");

            // $(".resultWin").append(perfect);
            // time = 5000;
        }
        setTimeout(function() {
            resultHide();
            let preImgs = [];
            $.when(preloadImg(rightArr.allRightArr, preImgs)).done(
                function() {
                    preImgs.forEach(function(item, index) {
                        var scale = item.naturalWidth / item.naturalHeight;
                        let liEle = $(`
                            <div class="example-audio sound" data-syncactions="audio-${index}">
                                <img src="./image/sound.gif" alt="" class="gif small soundInCard">
                                <img src="./image/sound.png" alt="" class="png small soundInCard">
                                <audio src="${rightArr.allRightArr[index].audio}" data-syncaudio="audio${index}"></audio>
                            </div>
                        `);
                        var dom;
                        if (index < 3) {
                            dom = $(".endGame .scroll-1 li").eq(index);
                        } else {
                            dom = $(".endGame .scroll-2 li").eq(index - 3);
                        }
                        dom.css({
                            'background-image': 'url(' + item.src + ')',
                            'opacity': 1,
                        });
                        dom.append(liEle);
                        endGameImgStyle($(".endGame .scrollUl li").eq(index), scale, item.naturalWidth, item.naturalHeight);
                    });
                    if (rightArr.allRightArr.length <= 3) {
                        $(".scrollBtn").hide();
                        if (rightArr.allRightArr.length == 1) {
                            $(".endGame .scroll-1 li").eq(1).remove();
                            $(".endGame .scroll-1 li").eq(1).remove();
                        } else if (rightArr.allRightArr.length == 2) {
                            $(".endGame .scroll-1 li").eq(2).remove();
                        }
                    }
                }
            );
            soundControl().pause();
            $(".endGame").show();
            if (isSync && type == 'over') {
                startFun();
            }
        }, time)
    }
    // 反馈动画
    async function feedback(){
        SDK.reportTrackData({
            action:'CK_FT_INTERACTION_COMPLETE', //事件名称
            teaData:{
                result:'success'
            },  // 只有老师端会上报的数据

        },USER_TYPE.TEA)
      try{
        return await feedbackAnimation('feedKey1')
      }catch(error){
        console.log(error)
      }
    }
    function startFun() {
        if (hasStarted) return;
        hasStarted = true;
        if (!isSync) {
            return false;
        }
        var support1v1h5star = SDK.getClassConf().serverData.objCourseInfo.support1v1h5star;
        classStatus = SDK.getClassConf().h5Course.classStatus;
        var device = SDK.getClassConf().h5Course.device;
        if (userType == 'stu' && (classStatus == 5 || classStatus == 6)) {
            if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
                SDK.bindSyncStart({
                    type: "newH5StarData",
                    num: score
                });
            }
        }
    }

    function endGameImgStyle(ele, scale, width, height) {
        if (staticData.imgType == 1) {
            if (scale < 1) {
                if (height > ele.height()) {
                    ele.css({
                        backgroundSize: 'auto 100%',
                        backgroundPosition: '50% 50%'
                    });

                } else {
                    ele.css({
                        backgroundSize: 'auto auto',
                        backgroundPosition: '50% 50%'
                    });
                }
            } else {
                if (width > ele.width()) {
                    ele.css({
                        backgroundSize: '100% auto',
                        backgroundPosition: '50% 50%'
                    });

                } else {
                    ele.css({
                        backgroundSize: 'auto auto',
                        backgroundPosition: '50% 50%'
                    });
                }
            }
        }
        if (staticData.imgType == 2) {
            ele.css({
                backgroundSize: '200% ' + 200 / scale * 0.69 + '%',
                backgroundPosition: '0% 50%'
            });
        }
        if (staticData.imgType == 3) {
            ele.css({
                backgroundSize: '300% ' + 300 / scale * 0.69 + '%',
                backgroundPosition: '0% 50%'
            });
        }
        soundControl().pause();
    }

    function initImg(ele, img) {
        let naturalWidth = img.naturalWidth / 100;
        let naturalHeight = img.naturalHeight / 100;
        if (staticData.imgType == 1) {
            ele.css({
                backgroundSize: '100% 100%',
                width: naturalWidth + 'rem',
                height: naturalHeight + 'rem'
            });
        }
        if (staticData.imgType == 2) {
            ele.css({
                width: naturalWidth / 2 + 'rem',
                height: naturalHeight + 'rem',
                backgroundSize: '200% 100%'
            });
        }
        if (staticData.imgType == 3) {
            ele.css({
                width: naturalWidth / 3 + 'rem',
                height: naturalHeight + 'rem',
                backgroundSize: '300% 100%'
            });
        }
    }

    function diffImgAni(self) {
        if (staticData.imgType == 1) {
            self.addClass('optionRight');
        }
        if (staticData.imgType == 2) {
            self.addClass('imgType2Right');
        }
        if (staticData.imgType == 3) {
            self.addClass('imgType3Right');
        }
    }

    function rebackPage(data) {
        return {
            'whenClick': function(data) {
                if (userType == 'tea' && SDK.getClassConf().h5Course.classStatus == 5) {
                    $(".doneTip").removeClass('hide');
                }
                rightArr.currentRightNum = data.currentRightNum;
                rightArr.allRightArr = data.allRightArr;

                score = data.score;
                timeIndex = data.timeIndex;
                hideOptionArr = data.hideOptionArr;

                optionsInit(timeIndex);
                addEvent();

                if (hideOptionArr && hideOptionArr.length > 0)
                    hideOptionArr.forEach(function(item) {
                        $(".optionLi[data-syncactions='" + item + "']").hide();
                    });
                newWidth += progressPerStep;
                $(".proprogressBar").css("width", newWidth + "%");

                gameProcessBidSync();
                SDK.setEventLock();
            },
            'whenEnd': function(data) {
                $(".doneTip").hide();
                rightArr.currentRightNum = data.currentRightNum;
                rightArr.allRightArr = data.allRightArr;
                score = data.score;
                timeIndex = data.timeIndex;
                endGameappendImg('whenEnd');
                SDK.setEventLock();
            },
        }
    }

    function rightWrong(self) {
        soundControl().pause();
        return {
            'right': function() {
                score++;
                self.addClass("already-hide");
                diffImgAni(self);
                rightArr.currentRightNum++;
                hideOptionArr.push(self.data("syncactions"));
                $('.scoreNum').html(score);
                SDK.playRudio({
                    index: $(".rightAudio")[0],
                    syncName: $(".rightAudio").attr("data-syncaudio")
                });
            },
            'wrong': function() {
                self.addClass('shake');
                SDK.playRudio({
                    index: $(".wrongAudio")[0],
                    syncName: $(".wrongAudio").attr("data-syncaudio")
                });
                $(".wrongAudio")[0].onended = function() {
                    soundControl().play();
                }
            }
        }
    }

    let scrollBtnStatus = true;
    $(".leftScroll,.rightScroll").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault();
        }
        e.stopPropagation();
        if (scrollBtnStatus) {
            scrollBtnStatus = false;
            if (!isSync) {
                $(this).trigger("btnScroll");
                return;
            }
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'btnScroll',
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                },
                recoveryMode: '1'
            });
        }
    });

    $(".leftScroll,.rightScroll").on("btnScroll", function(e, message) {
        let self = $(this);
        if (self.hasClass("leftScroll")) {
            $(".scroll-2").css({
                'display': 'none'
            });
            $(".scroll-1").css({
                'display': 'flex'
            });
        } else {
            $(".scroll-1").css({
                'display': 'none'
            });
            $(".scroll-2").css({
                'display': 'flex'
            });
        }
        self.css({
            opacity: 0.5
        });
        self.siblings().css({
            opacity: 1
        });
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                rebackPage().whenEnd(obj);
            }
        }
        scrollBtnStatus = true;
        SDK.setEventLock();
    });

    function demoFun(showfuncMask) {
        clearTimeout(demoTimer);
        optionsInit(0, 'tip');
        $(".horn").removeClass("hide");
        if (showfuncMask) {
            $(".funcMask").hide();
        }
        $('.right-top-demo').show();
        demoTimer = setTimeout(function() {
            $('.hand').addClass("handAnimation");
        }, demoOptionNum * 300 + 100);
        setTimeout(function() {
            diffImgAni($(".optionLi[result=1]").eq(0));
            SDK.playRudio({
                index: $(".rightAudio")[0],
                syncName: $(".rightAudio").attr("data-syncaudio")
            });
            $(".optionLi").on('animationend webkitAnimationEnd', function() {
                demoOut(showfuncMask);
            });
        }, demoOptionNum * 300 + 2000 - 260)
    }

    function demoOut(showfuncMask) {
        soundControl().pause();
        clearTimeout(demoTimer);
        $(".horn").addClass("hide");
        $(".optionUl").html('');
        $('.hand').removeClass("handAnimation");
        $('.right-top-demo').hide();
        if (showfuncMask) {
            $(".funcMask").show();
        }
        if (userType == 'tea') {
            SDK.bindSyncCtrl({
                'type': 'tplDemoOut',
                'data': {
                    CID: SDK.getClassConf().course.id + '',
                    operate: '1',
                    data: []
                }
            });
        }
    }

    window.SDK.memberChange = function(message) {
        if (isSync) {
            if (message.state == 'enter') {
                $(".tea-stu-not-in").hide();
                if (message.role == 'tea') {
                    $(".funcMask").hide();
                }
                soundControl().play();
            } else if (message.state == 'out') {
                $(".tea-stu-not-in").show();
                if (demoTimer) {
                    demoOut(false);
                }
            }
        }
    }

    let soundClick = true;
    let isPlayList = '';
    let isCardList = '';
    $('.scrollUl li').on('click touchend', function(e) {
        if (e.type == "touchstart") {
            e.preventDefault();
        }
        if (isCardList == $(e.currentTarget).data('syncactions') && $(e.target).is('li')) {
            return;
        }
        let audios = $(this).find('audio');

        if (audios.length == 0) {
            return;
        }

        if (soundClick) {
            soundClick = false;
            if (!isSync) {
                $(this).trigger('syncSoundClick');
                return;
            }
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                syncName: 'syncSoundClick',
                funcType: 'audio',
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                },
                recoveryMode: '1'
            });
        }
    });

    $('.hiddenContain ul').on('syncSoundClick', 'li', function(e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                rebackPage().whenEnd(obj);
            }
        }
        let gif = $(this).find('.gif');
        let png = $(this).find('.png');
        let audio = $(this).find('audio')[0];
        if (isPlayList) {
            let isPlayListDom = $(`[data-syncactions=${isPlayList}]`);
            SDK.pauseRudio({
                index: isPlayListDom.find('audio')[0],
                syncName: isPlayListDom.find('audio').attr("data-syncaudio")
            });
            isPlayListDom.find('audio')[0].currentTime = 0;
            isPlayListDom.find('.gif').hide();
            isPlayListDom.find('.png').show();
        }
        if (isCardList) {
            let isCardListDom = $(`[data-syncactions=${isCardList}]`);
            isCardListDom.css({
                'transform': `scale(1)`,
                'border': '#ffc71f 0 solid'
            });
        }

        $(e.currentTarget).css({
            'transform': `scale(1.16)`,
            'border': '#ffc71f 0.17rem solid'
        });
        isCardList = $(e.currentTarget).data('syncactions');
        audio.currentTime = 0;
        SDK.playRudio({
            index: audio,
            syncName: $(this).find('audio').attr("data-syncaudio")
        });
        console.log('播放动画')
        gif.show();
        png.hide();
        setTimeout(() => {
          isPlayList = $(e.currentTarget).data('syncactions');
        }, 10);
        audio.onended = function() {
          console.log('音频结束1011')
            isPlayList = "";
            console.log('结束动画')
            gif.hide();
            png.show();
        }.bind(this);

        SDK.setEventLock();
        soundClick = true;
    });
});

function preloadImg(list, imgs) {
    var def = $.Deferred(),
        len = list.length;
    $(list).each(function(i, e) {
        var img = new Image();
        img.src = e.image;
        if (img.complete) {
            imgs[i] = img;
            len--;
            if (len == 0) {
                def.resolve();
            }
        } else {
            img.onload = (function(j) {
                return function() {
                    imgs[j] = img;
                    len--;
                    if (len == 0) {
                        def.resolve();
                    }
                };
            })(i);
            img.onerror = function() {
                len--;
            };
        }
    });
    return def.promise();
};
