import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";

// var domain = 'http://172.16.0.107:9011/pages/1152/';
import {
  feedbackAnimationSend,
  feedbackData,
  initializeFeedback,
  feedBackChange
} from "../../../common/template/feedbackAnimation/form.js";
var domain = '';
var feedbackObjData1 = feedbackData({
  key:'feedKey1',
  name:'整体反馈',
});
var Data = {
    configData: {
        bg: '',
        desc: '',
        title: "",
        tImg: '',
        tImgX: '',
        tImgY: '',
        instructions: [{
            commandId: '-1'
        }],
        tg: [{
            title: '',
            content: ''
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        showAudUped: false, //仅显示已上传语音资源的项
        showImgUped: false, //仅显示已上传图片资源
        source: {
            //语音资源
            audioList: [{
                audio: "",
                mark: "",
                code: 1
            }, {
                audio: "",
                mark: "",
                code: 2
            }, {
                audio: "",
                mark: "",
                code: 3
            }, {
                audio: "",
                mark: "",
                code: 4
            },{
                audio: "",
                mark: "",
                code: 5
            }, {
                audio: "",
                mark: "",
                code: 6
            },{
                audio: "",
                mark: "",
                code: 7
            }, {
                audio: "",
                mark: "",
                code: 8
            }, {
                audio: "",
                mark: "",
                code: 9
            }, {
                audio: "",
                mark: "",
                code: 10
            }],
            //图片资源类型
            imgType: 1,
            imgTypeEffect: 1,
            imgList: [{
                image: "",
                mark: "",
                code: 1
            }, {
                image: "",
                mark: "",
                code: 2
            }, {
                image: "",
                mark: "",
                code: 3
            }, {
                image: "",
                mark: "",
                code: 4
            }, {
                image: "",
                mark: "",
                code: 5
            }, {
                image: "",
                mark: "",
                code: 6
            },{
                image: "",
                mark: "",
                code: 7
            },{
                image: "",
                mark: "",
                code: 8
            }, {
                image: "",
                mark: "",
                code: 9
            }, {
                image: "",
                mark: "",
                code: 10
            }
        ], //图片资源
            frequencyList: [{
                time: "",
                audio: "",
                options: [{
                    src: "",
                    position: "",
                    result: "1"
                }]
            }], //轮数
            countImg: "", //计数图片
            rightAudio: "", //答对语音反馈
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        },
        feedbackLists:[
          ...feedbackObjData1,
        ],
    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
    dynamicInstructions: [], //交互提示标签
};
Data.configData.feedbackLists.push(feedbackObjData1)

$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if(!Data.configData.tImg){
                Data.configData.tImg = '';
            }
            if(!Data.configData.tImgX){
                Data.configData.tImgX = 1340
            }
            if(!Data.configData.tImgY){
                Data.configData.tImgY = 15
            }
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '2' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
            if(!Data.configData.instructions){
                Data.configData.instructions = [{
                    commandId: '-1'
                }]
            }
            initializeFeedback(Data)
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    mounted: function() {
        this.getDynamicInstructions();
    },
    methods: {
        getDynamicInstructions: function() {
            var that = this;
            getDynamicInstructions(function(res, newIstructions) {
                that.dynamicInstructions = res;
                that.configData.instructions = newIstructions;
            }, that.configData.instructions);
        },
      addInstruction: function() {
        addInstruction(this.configData);
      },
      removeInstruction: function(index) {
        removeInstruction(index, this.configData);
      },
      validateInstructions: function() {
        return validateInstructions(this.configData);
      },

      feedBackChange(item) {
        feedBackChange(item)
      },
      // feedback 上传
      feedbackUpload:function(e, item, attr, fileSize) {
        console.log(e, item, attr, fileSize);
        const file = e.target.files[0];
        if (file.type === "image/png") {
          this.imageUpload(e, item, attr, fileSize);
        } else {
          this.lottieUpload(e, item, attr, fileSize);
        }
      },
        // lottie 图片上传
        lottieUpload:function(e, item, attr, fileSize) {
          var file = e.target.files[0],
            size = file.size,
            that = this
          if ((size / 1024).toFixed(2) > fileSize) {
            alert(
              "您上传的图片大小为" +
                (size / 1024).toFixed(2) +
                "KB, 超过" +
                fileSize +
                "K上限，请检查后上传！"
            );
            e.target.value = "";
            return;
          }
          const reader = new FileReader();
          reader.onload = async function (processEvent) {
            const jsonData = JSON.parse(processEvent.target.result);
            // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
            const naturalWidth = jsonData.w || jsonData.width;
            const naturalHeight = jsonData.h || jsonData.height;
            var check = that.sourceImgCheck(
              e.target,
              {
                height: naturalHeight,
                width: naturalWidth,
              },
              item,
              attr
            );
            if (check) {
              that.postData(file, item, attr);
            } else {
            }
          };
          reader.readAsText(file);
        },
        //辅助提示图片上传
        tImageUpload: function(e, attr, fileSize) {
            console.log("tImageUpload",e)
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;
            var item = this.configData;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.tImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        //辅助提示图片大小校检
        tImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && (height+1) > data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width+"*"+data.height);
            }
            return checkSize;
        },

        //图片上传
        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }

            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                });
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        //图片大小校检
        sourceImgCheck: function(input, data) {
            console.log(input)
            let dom = $(input),
                size = dom.attr("size").split(",");
            console.log(size)
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                alert("应上传图片大小为：" + size.join("或") + ", 上传图片尺寸为：" + data.width + "*" + data.height + "，请检查后上传！");
                // item[attr] = "";
                // alert("图片尺寸不符合要求！");
            }
            return checkSize;
        },
        //音频上传
        audioUpload: function(e, item, attr, fileSize) {
            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            var that = this;

            if ((size / 1024).toFixed(2) > 500) {
                console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            } else {
                console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            }
            if ((size / 1024).toFixed(2) > fileSize) {
                console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
                alert("您上传的音频大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }
            var url = URL.createObjectURL(file); //获取录音时长
            var audioElement = new Audio(url);
            var duration;
            audioElement.addEventListener("loadedmetadata", function(_event) {
                duration = audioElement.duration ? audioElement.duration : '';
                var check = that.sourceAudioCheck(e.target, {
                    duration: duration,
                });
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    audioElement = null;
                } else {
                    audioElement = null;
                }
            });
        },
        //音频长度校检
        sourceAudioCheck: function(input, data) {
            let dom = $(input),
                time = dom.attr("time");
            if (time == "" || time == undefined || data.duration == '') return true;
            let checkSize = false;
            if (data.duration <= time) {
                checkSize = true;
            } else {
                alert(`您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`);
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source;
            var checkMsg = false;
            // //验证是否选择图片类型
            // if (!data.imgType) {
            //     checkMsg = "未选择图片类型";
            //     return checkMsg;
            // }
            // //验证是否上传语音
            // var audioListLen = 10;
            // for (let i of data.audioList) {
            //     if (!i.audio) {
            //         audioListLen--;
            //     }
            // }
            // if (audioListLen == 0) {
            //     checkMsg = "请至少上传一条语音";
            //     return checkMsg;
            // }
            // //验证是否上传图片
            // var imgListLen = 10;
            // for (let i of data.imgList) {
            //     if (!i.image) {
            //         imgListLen--;
            //     }
            // }
            // if (imgListLen == 0) {
            //     checkMsg = "请至少上传一张图片";
            //     return checkMsg;
            // }
            // //验证是否选择题干声音
            // var rightLength = 0;
            // for (let i of data.frequencyList) {
            //     if (!i.audio || !data.audioList[i.audio - 1]) {
            //         checkMsg = "请选择题干声音";
            //         return checkMsg;
            //     }
            //     if (!data.audioList[i.audio - 1].audio) {
            //         checkMsg = "请选择有资源题干声音";
            //         return checkMsg;
            //     }
            //     // if (!i.time) {
            //     //     checkMsg = "请填写本轮作答时间";
            //     //     return checkMsg;
            //     // }
            //     // if (i.time < 4 || i.time > 30) {
            //     //     checkMsg = "本轮作答时间限制在4-30秒";
            //     //     return checkMsg;
            //     // }
            //     for (var k = 0; k < i.options.length; k++) {
            //         if (i.options[k].result == 1) {
            //             rightLength++;
            //         }
            //     }
            // }
            // if (rightLength == 0) {
            //     checkMsg = "每屏设计请至少保留一个正确选项";
            //     return checkMsg;
            // }
            // //验证是否选择题干声音
            // for (let i of data.frequencyList) {
            //     for (let k of i.options) {
            //         if (!k.src) {
            //             checkMsg = "请选择选项图片";
            //             return checkMsg;
            //         }
            //         // if (!k.position) {
            //         //     checkMsg = "请填写图片位置";
            //         //     return checkMsg;
            //         // }
            //     }
            // }
            // //验证是否上传计数图片了
            // // if (!data.countImg) {
            // //     checkMsg = "请上传计数图片";
            // //     return checkMsg;
            // // }
            console.log(this.configData.positiveFeedback)
            if(this.configData.positiveFeedback == "9"){
              if(this.configData.feedback == ""){
                checkMsg = "请上传自定义反馈动画/图片";
                return checkMsg;
              }
            }
        },
        onSend: function() {
            var data = this.configData;
            let feedbackStatus = feedbackAnimationSend(data);
            if(!feedbackStatus){
              return;
            }
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            console.log(_data)
            var val = this.validate();
            if (!val && this.validateInstructions()) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                alert(val);
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    console.log(res.data.key);
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },

        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({
                title: '',
                content: ''
            });
        },
        addL: function(item) {
            this.configData.level.low.push({
                title: '',
                content: ''
            });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addOptions: function(arr, item) {
            if (item) {
                arr.push(item)
            } else {
                arr.push('')
            }
        },
        delOption: function(arr, item) {
            arr.remove(item)
        },
        setAnswer: function(item) {
            this.configData.source.right = item;
        },
        inputPlayNum: function() {
            var reg = new RegExp("^([1][0-9]{0,1}|[2][0]{0,1})$");
            var regResult = reg.test(Data.configData.source.palyTime);
            if (!regResult && Data.configData.source.palyTime != "#") {
                Data.configData.source.palyTime = "";
            }
        }
    },
    watch: {
        'configData.source.imgType': function() {
            this.configData.source.imgList = [{
                image: "",
                mark: "",
                code: 1
            }, {
                image: "",
                mark: "",
                code: 2
            }, {
                image: "",
                mark: "",
                code: 3
            }, {
                image: "",
                mark: "",
                code: 4
            }, {
                image: "",
                mark: "",
                code: 5
            }, {
                image: "",
                mark: "",
                code: 6
            },{
                image: "",
                mark: "",
                code: 7
            }, {
                image: "",
                mark: "",
                code: 8
            }, {
                image: "",
                mark: "",
                code: 9
            }, {
                image: "",
                mark: "",
                code: 10
            }];
            this.configData.source.frequencyList = [{
                time: "",
                audio: "",
                options: [{
                    src: "",
                    position: "",
                    result: "1"
                }]
            }];
            // this.configData.source.frequencyList.forEach(function(item) {
            //     item.options.forEach(function(img) {
            //       img.src = "";
            //     })
            // })
        }
    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
