"use strict"
import '../../common/js/common_1v1.js'
import '../../common/js/drag.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
	var h5SyncActions = parent.window.h5SyncActions; 

	let staticData = configData.source;
	let seleList = staticData.seleList;
	let childPosArr = []; //存储所有被选中的元素相对于拖拽目标场景的位置坐标
	//判断是否使用默认背景图片
	if (configData.bg == '') {
		$(".container").css({ 'background-image': 'url(./image/defaultBg.jpg)' })
	}
	//目前简繁体文字完全一样

	//spell
	(function fnSpell() {
		//判断question是否有内容
		var textHtml = ''
		var imgHtml = ''
		if (staticData.question) {
			textHtml = `
				<div class="dis-img" style="width:100%;height:5.62rem;border-radius: 0.6rem;overflow:hidden">
					<img src="${staticData.img}" style="width:100%;height:100%;"/>
				</div>
				<div class='dis-text'>
					<p>${staticData.question}</p>
				</div>
			`
			$('.dis-area').html(textHtml)
		} else {
			imgHtml = ` <div class="dis-img" style="width:100%;height:100%;border-radius: 0.6rem;overflow:hidden"><img src="${staticData.img}" style="width:100%;height:100%;"/></div>`
			$('.dis-area').html(imgHtml)
			$('.btns').hide()
		}
		const pos = { l: 9.85, t: .35 };
		let ele = '', x, y, l_val = 2.8, t_val = 2.64;
		for (let i = 0, length = seleList.length; i < length; i++) {
			childPosArr.push({ t: -1, l: -1 })
			x = pos.l + (i % 3) * l_val;
			y = pos.t + (parseInt(i / 3)) * t_val;
			ele += '<div class="ansBot-area" style="left:' + x + 'rem;top:' + y + 'rem"></div><div class="ans-area scale" data_index="' + i + '" data-syncactions=item-' + i + ' style="left:' + x + 'rem;top:' + y + 'rem"><img src="' + seleList[i].img + '" style="left:' + x + 'rem;top:' + y + 'rem" /><i></i></div>'
		}
		$('.stage').append(ele);
		//判断拖入区图片的大小
		$(".dis-img img").on("load", function () {
			let dranAreaW = $(this).get(0).naturalWidth,
				dranAreaH = $(this).get(0).naturalHeight;
			$('.dis-img').attr("data_width", dranAreaW).attr("data_height", dranAreaH)
		});
		//图片尺寸大小判断
		$(".ans-area img").on("load", function () {
			contrImgSize.call(this);
		});

		function contrImgSize() {
			var _this = $(this),
				imgWidth = _this.get(0).naturalWidth,
				imgHeight = _this.get(0).naturalHeight,
				containerScale = imgWidth / imgHeight;   //容器的宽高比
			if (containerScale < 1) {//瘦高型
				if (imgHeight > 206) {
					_this.css({
						height: '100%',
						width: 'auto'
					});
					_this.data("size", { width: 'auto', height: '100%' })//记录图片开始位置尺寸
				} else {
					_this.css({
						height: imgHeight / 100 + "rem",
						width: 'auto'
					});
					_this.data("size", { width: 'auto', height: imgHeight / 100 + "rem" })
				}
				_this.parent('.ans-area').data("size", { width: imgWidth, height: imgHeight })//记录图片本身尺寸，用于拖动后父容器重新调整大小
			} else {//胖矮型
				if (imgWidth > 206) {
					_this.css({
						width: '100%',
						height: 'auto'
					});
					_this.data("size", { width: '100%', height: 'auto' })
				} else {
					_this.css({
						width: imgWidth / 100 + "rem",
						height: 'auto'
					});
					_this.data("size", { width: imgWidth / 100 + "rem", height: 'auto' })
				}
				_this.parent('.ans-area').data("size", { width: imgWidth, height: imgHeight })
			}
		}
	})()
	if (isSync && window.frameElement.getAttribute('user_type') == 'tea') {
		$('.ans-btn').text('完成');
	}
	// if( !configData.desc && !configData.source.title ) {
	// 	$('.commom').hide();
	// 	$('.stage').css('margin-top', '1.3rem');
	// }
	// if( configData.desc!='' && staticData.title=='' ) {
	// 	$('.commom .title').hide();
	// 	$('.stage').css('margin-top', '.4rem');
	// }

	if (configData.source.question) {
		if ($('.dis-text p').height() / window.base < 0.5) {
			$('.dis-text p').css({
				"height": "1.64rem",
				"line-height": "1.64rem",
				"text-align": "center"
			})
		}
	}
	//=================================
	//点击上下翻页按钮
	if (($('.dis-text p').height() / window.base - $('.dis-text').height() / window.base) <= 0) {
		$(".downBtn").addClass('noChange').find('img').css('opacity', '0.3')
	}
	let isReScrollUp = true;
	let contentReUp = 0;
	$(".upBtn").on("click touchstart", function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation();
		if (isReScrollUp) {
			isReScrollUp = false;
			if (contentReUp <= 0) {
				$(this).addClass('noChange').find('img').css('opacity', '0.3')
				isReScrollUp = true;
			} else {
				if (!isSync) {
					$(this).trigger("syncgoReUp");
					return;
				}
				SDK.bindSyncEvt({
					sendUser: '',
					receiveUser: '',
					index: $(e.currentTarget).data("syncactions"),
					eventType: 'click',
					method: 'event',
					syncName: 'syncgoReUp'
				});
			}
		}
	});
	$(".upBtn").on("syncgoReUp", function (e) {
		contentReUp -= 0.6;
		contentReUp = parseFloat(contentReUp);
		$(".downBtn").removeClass('noChange').find('img').css('opacity', '1')
		$('.dis-text p').css('top', -contentReUp + 'rem').css({
			"transition": "all 0.3s"
		});
		SDK.setEventLock();
		isReScrollUp = true;
	});
	let isScrollReDown = true;
	$(".downBtn").on("click touchstart", function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation();
		if (isScrollReDown) {
			isScrollReDown = false;
			let diffHeight = $('.dis-text p').height() / window.base - $('.dis-text').height() / window.base;
			if (contentReUp >= diffHeight) {
				$(this).addClass('noChange').find('img').css('opacity', '0.3')
				isScrollReDown = true;
			} else {
				if (!isSync) {
					$(this).trigger("syncgoRedown");
					return;
				}
				SDK.bindSyncEvt({
					sendUser: '',
					receiveUser: '',
					index: $(e.currentTarget).data("syncactions"),
					eventType: 'click',
					method: 'event',
					syncName: 'syncgoRedown'
				});
			}
		}
	});
	$(".downBtn").on("syncgoRedown", function (e) {
		contentReUp += 0.6;
		contentReUp = parseFloat(contentReUp);
		$(".upBtn").removeClass('noChange').find('img').css('opacity', '1')
		$('.dis-text p').css({ 'top': -contentReUp + 'rem', "transition": "all 0.3s" })
		SDK.setEventLock();
		isScrollReDown = true;
	});
	//鼠标按下改变状态
	$('.sameBtn').on('mousedown touchstart', function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation()
		if (!$(this).hasClass('noChange')) {
			$(this).css({
				"background": "rgba(0,0,0,0.08)",
			})
			$(this).find('img').css({
				opacity: 0.2
			})
		}
	})
	$('.sameBtn').on('mouseup touchend', function (e) {
		if (e.type == "touchstart") {
			e.preventDefault()
		}
		e.stopPropagation()
		if (!$(this).hasClass('noChange')) {
			$(this).css({
				"background": "none",
			})
			$(this).find('img').css({
				opacity: 1
			})
		}
	})
	//===============================
	//添加拖拽
	var dragBefore = true;
	var dragEnd = true;
	var dragProcess = true;
	var lock = true;
	let $audioDrag = document.getElementsByClassName('audioDrag')[0]

	$('.ans-area').drag({
		before: function (e) {
			// $audioDrag ? $audioDrag.play() : "";
			if($audioDrag) {
				SDK.playRudio({
					index: $audioDrag,
					syncName: $(".audioDrag").attr("data-syncaudio")
                })
			}
			//if(dragBefore) {
			//dragBefore = false;
			$(this).removeClass('scale')
			if (!isSync) {

				$(this).trigger('syncDragBefore', {
					left: $(this).data('startPos').left,
					top: $(this).data('startPos').top,
					pageX: '',
					pageY: '',
				});
				return;
			}

			SDK.bindSyncEvt({
				index: $(this).data('syncactions'),
				eventType: 'dragBefore',
				method: 'drag',
				left: $(this).data('startPos').left,
				top: $(this).data('startPos').top,
				pageX: '',
				pageY: '',
				syncName: 'syncDragBefore'
			});
		},
		process: function (e) {

			// if (!isSync) {
			// 	$(this).trigger('syncDragProcess', {
			// 		left: $(this).attr('data-left'),
			// 		top: $(this).attr('data-top'),
			// 		pageX: '',
			// 		pageY: '',
			// 	});
			// 	return;
			// }


			// if (lock) {
			// 	lock = false;
			// 	setTimeout(function() {
			// 		SDK.bindSyncEvt({
			// 			index: $(this).data('syncactions'),
			// 			eventType: 'dragProcess',
			// 			method: 'drag',
			// 			left: $(this).attr('data-left'),
			// 			top: $(this).attr('data-top'),
			// 			pageX: '',
			// 			pageY: '',
			// 			syncName: 'syncDragProcess'
			// 		});
			// 		lock = true;
			// 	}.bind(this), 300);
			// }
		},
		end: function (e) {
			$(this).addClass('scale')
			childPosArr[$(this).attr('data_index')] = {
				t: $(this).attr('data-top').substring(0, $(this).attr('data-top').length - 3) - 0.35,
				l: $(this).attr('data-left').substring(0, $(this).attr('data-left').length - 3) - 1.45, //1.45为.dis-area相对于stage的left距离
			}
			var startPos_1 = $(this).data('startPos');
			let criticlaValue_1 = $('.dis-area').position().left + $('.dis-area').width() - ($(this).width() / 2),
				nowLeft_1 = parseInt($(this).attr('data-left')) * window.base;
			if (nowLeft_1 < criticlaValue_1) { } else {
				childPosArr[$(this).attr('data_index')] = {
					t: -1,
					l: -1,
				}
			}
			if (!isSync) {
				$(this).trigger('syncDragEnd', {
					left: $(this).attr('data-left'),
					top: $(this).attr('data-top'),
					pageX: '',
					pageY: '',
				});
				return;
			}

			SDK.bindSyncEvt({
				index: $(this).data('syncactions'),
				eventType: 'dragEnd',
				method: 'drag',
				left: $(this).attr('data-left'),
				top: $(this).attr('data-top'),
				pageX: '',
				pageY: '',
				syncName: 'syncDragEnd',
				otherInfor: {
					childPosArr: childPosArr
				},
				recoverMode:'1'
			});
		}
	});
	//=======================================================
	var existNum = 0;
	$('.ans-area').on("syncDragBefore", function (e, pos) {

		//if($(this).attr('tag')!='true'){//定位层级控制
		$(this).css('z-index', existNum);
		existNum++;

		//}
		$(this).attr('tag', 'true');//是否可提交标记
		// $(this).data('startPos', {
		// 	left: pos.left,
		// 	top: pos.top
		// });
		SDK.setEventLock();
	});

	/*
	$('.ans-area').on("syncDragProcess", function(e, pos) {
		$(this).css({
			'left': pos.left,
			'top': pos.top
		});
		SDK.setEventLock();
		//lock = true;
	});
	*/
	$('.ans-area').on("syncDragEnd", function (e, pos) {
		if (isSync) {
			let obj = pos.otherInfor;
			childPosArr = obj.childPosArr
			if (pos == undefined || pos.operate == 1) {

			} else {
				//掉线直接恢复页面 
				recoverSelect(pos)
				SDK.setEventLock()
				return
			}
		}
		var startPos = $(this).data('startPos');
		let criticlaValue = $('.dis-area').position().left + $('.dis-area').width() - ($(this).width() / 2),
			nowLeft = parseInt(pos.left) * window.base;
		if (nowLeft < criticlaValue) {
			var _size = $(this).data('size');
			$(this).css({
				'width': _size.width / 100 + 'rem',
				'height': _size.height / 100 + 'rem',
				'border-radius': '0',
				'line-height': '0',
				'left': pos.left,
				'top': pos.top
			});

			$(this).find('img').css({ 'width': _size.width / 100 + 'rem', 'height': _size.height / 100 + 'rem' });
		} else {
			$(this).resetStart();
			// childPosArr[$(this).attr('data_index')] = {
			// 	t: -1,
			// 	l: -1,
			// }
		}

		//检测是否可提交
		if ($(".ans-area[tag=true]").length > 0) {
			if (isSync && window.frameElement.getAttribute('user_type') == 'stu') {
				$('.ans-btn').addClass('allowSub');
			} else if (!isSync) {
				$('.ans-btn').addClass('allowSub');
			}
			SDK.setEventLock();
		} else {
			$('.ans-btn').removeClass('allowSub');
			SDK.setEventLock();
		}
	});
	//提交
	var btnClickTimer = true;
	let $audioPhoto = document.getElementsByClassName('audioPhoto')[0]

	$('.ans-btn').on("click", function (e) {
		if (btnClickTimer) {
			btnClickTimer = false;
			if (!isSync) {
				$(this).trigger('syncBtnClick');

				return;
			}
			if (window.frameElement.getAttribute('user_type') == 'stu') {
				SDK.bindSyncEvt({
					sendUser: '',
					receiveUser: '',
					index: $(this).data('syncactions'),
					eventType: 'click',
					method: 'event',
					syncName: 'syncBtnClick',
					otherInfor: {
						childPosArr: childPosArr
					},
					recoverMode:'1'
				});
			}
		}
	});
	$('.ans-btn').on('syncBtnClick', function (e, message) {
		if (isSync) {
			let obj = message.data[0].value.syncAction.otherInfor;
			if (message == undefined || message.operate == 1) {
				childPosArr = obj.childPosArr
			} else {
				//直接恢复页面 提交答案后出现掉线
				setPhoto($(this), childPosArr, message)
				SDK.setEventLock()
				return
			}
		}
		// $audioPhoto ? $audioPhoto.play() : "";
		if($audioPhoto){
			SDK.playRudio({
				index: $audioPhoto,
				syncName: $(".audioPhoto").attr("data-syncaudio")
			})
		}
		setPhoto($(this), childPosArr)

		SDK.setEventLock();
		btnClickTimer = true;
	})

	//老师端响应
	let stuNameEle = '',
		userList = [];


	let userId = '';
	$('#container').on('syncResultClick', function (e, message) {
		var sendUserInfo = message.data[0].value.sendUserInfo;
		if (sendUserInfo.type == 'stu') {
			userId = sendUserInfo.id;
		}
		$('.stuNameList[id=' + userId + ']').addClass('stuName-active');
		SDK.setEventLock();
		btnClickTimer = true;
	})

	//拖拽选项过程中掉线恢复
	function recoverSelect(pos) {
		for (let i = 0; i < pos.otherInfor.childPosArr.length; i++) {
			if (pos.otherInfor.childPosArr[i].l !== -1 && pos.otherInfor.childPosArr[i].t !== -1) {
				$(".ans-area").eq(i).css({
					position: 'absolute',
					left: (pos.otherInfor.childPosArr[i].l) - 0 + 1.45 + 'rem',
					top: (pos.otherInfor.childPosArr[i].t) - 0 + 0.35 + 'rem'
				})
				var _size = $(".ans-area").eq(i).data('size');
				$(".ans-area").eq(i).css({
					'width': _size.width / 100 + 'rem',
					'height': _size.height / 100 + 'rem',
					'border-radius': '0',
					'line-height': '0',
				});

				$(".ans-area").eq(i).find('img').css({ 'width': _size.width / 100 + 'rem', 'height': _size.height / 100 + 'rem' });
			} else {
				$(".ans-area").eq(i).resetStart();
			}

			if (isSync && window.frameElement.getAttribute('user_type') == 'stu') {
				$('.ans-btn').addClass('allowSub');
			}
		}
	}
	//提交拍照  that:事件触发对象  childPosArr:被拖入场景区的道具相对于场景位置坐标
	function setPhoto(that, childPosArr, message) {
		that.text('完成').removeClass('allowSub');
		$('.cover').show();
		let dragW = $('.dis-img').attr("data_width"), dragH = $('.dis-img').attr("data_height");

		if (message == undefined || message.operate == 1) {
			$(".photoMask").css({
				display: 'block',
				// animation: 'camStyle .3s  1 forwards',
				// ' -webkit-animation': 'camStyle .3s  1 forwards',
			}).append($(".dis-area").clone())
			$(".showMask").css({
				animation: 'camStyle .3s  1 forwards',
				' -webkit-animation': 'camStyle .3s  1 forwards',
			})
			$(".showMask").on('animationend  webkitAnimationEnd', function () {
				$(this).css({ 'z-index': 10 })
			})
			$(".photoMask .dis-area").css({
				'z-index': '100',
				'border-radius': '0',
				width: dragW/100+'rem',
				height: dragH/100+'rem',
				position: 'absolute',
				left: '5.47rem',
				top: (10.8-dragH/100-0.5)/2-0.24+'rem',
				border: '.24rem solid #fff',
				'border-bottom': '0.8rem solid #fff',
				background: '#fff',
				animation: 'ptoStyle .5s  1 forwards',
				' -webkit-animation': 'ptoStyle .5s  1 forwards',
				'box-shadow': '0 0 .4rem rgba(255, 255, 255, 0.8)'
			})
		} else {
			$(".showMask").css({
				 'z-index': 10 
			})
			if(!$(".photoMask .dis-area").length>0){
				$(".photoMask").css({
					display: 'block',
					animation: 'camStyle 0s  1 forwards',
					' -webkit-animation': 'camStyle 0s  1 forwards',
				}).append($(".dis-area").clone())				
			}

			$(".photoMask .dis-area").css({
				'z-index': '100',
				'border-radius': '0',
				width: dragW/100+'rem',
				height: dragH/100+'rem',
				position: 'absolute',
				left: '5.47rem',
				top: (10.8-dragH/100-0.5)/2-0.24+'rem',
				border: '.24rem solid #fff',
				'border-bottom': '0.8rem solid #fff',
				background: '#fff',
				animation: 'ptoStyle 0.1s  1 forwards',
				' -webkit-animation': 'ptoStyle 0.1s  1 forwards',
				'box-shadow': '0 0 .4rem rgba(255, 255, 255, 0.8)'
			})
		}

		// $(".dis-area").on('animationend  webkitAnimationEnd', function () {
		// 	$(this).css({
		// 		transform: 'scale(1.1,1.1)'
		// 	});
		// });
		$('.ans-area').removeClass('scale')
		$(".dis-img").css({
			'border-radius': '0',
			height: '100%'
		})
		$(".dis-text").css({
			'display': 'none',
		})
		$(".showMask").css({'background':'rgba(51, 51, 51, 0.51)'})
		let zIndex = 0
		for (let i = 0; i < childPosArr.length; i++) {
			if (childPosArr[i].l !== -1 && childPosArr[i].t !== -1) {
				$(".stage .ans-area").eq(i).clone().appendTo($(".photoMask .dis-area"))
				$(".stage .ans-area").eq(i).css({ display: 'none' })
			}
			for (let j = 0; j < $(".photoMask .ans-area").length; j++) {
				if (i == $(".photoMask .ans-area").eq(j).attr('data_index')) {
					$(".photoMask .ans-area").eq(j).css({
						position: 'absolute',
						left: (childPosArr[i].l) + 'rem',
						top: (childPosArr[i].t) + 'rem'
					})
				}
			}
			$(".stage .dis-area").css({ display: 'none' })
		}
	}
});

jQuery.fn.extend({
	resetStart: function (size) {
		var thisObj = this;
		var startPos = $(this).data('startPos');
		var $left = startPos.left;
		var $top = startPos.top;
		thisObj.css({
			'left': $left,
			'top': $top,
			'width': '2.06rem',
			'height': '2.06rem',
			'border-radius': '.8rem'
		});
		var _img = thisObj.find('img');
		var _originSize = _img.data('size');
		_img.css({
			'width': _originSize.width,
			'height': _originSize.height
		})
		$(this).attr('tag', 'false');

	}
});