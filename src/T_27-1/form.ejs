<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TDR0001_拖拽_可选_中文版</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TDR0001_拖拽_可选_中文版</div>
			
			<% include ./src/common/template/common_head_cn %>

			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>拖入区域 </label>
					<div class="c-well">
						<div class="well-title">
							<p>图片（显示位置：3）<em>* </em>尺寸：750X750或750X562<em>(如有位置 4）</em></p>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label for="img-1" class="btn btn-show upload" v-if="configData.source.img==''?true:false">上传</label><label  for="img-1" class="btn upload re-upload" v-if="configData.source.img!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)</span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" id="img-1" size="750*750,750*562" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'img',50)">
							</div>
							<div class="img-preview" v-if="configData.source.img!=''?true:false">
								<img v-bind:src="configData.source.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="configData.source.img=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<label>问题 （显示位置：4）字符：≤48</label>
					<input type="text" class='c-input-txt' placeholder="请在此输入问题" maxlength='48' v-model="configData.source.question">
					<label>小图区域 <em>（注：与拖入区图片等比的透明PNG小图展示效果最佳。）</em></label>
					<div class="c-well" v-for="(item,index) in configData.source.seleList">
						<div class="well-title">
							<p>小图{{index+1}}（显示位置：5）<em>*</em></p>
							<span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.seleList.length>1?true:false'></span>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)</span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size=""  accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele()" v-show='configData.source.seleList.length<8?true:false'>+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
