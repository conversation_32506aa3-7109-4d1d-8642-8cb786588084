<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>T13FT_魔术时间FT</title>
	<link rel="stylesheet" href="form/css/style.css">
	<link rel="stylesheet" href="form/js/QuillEditor/quill.snow.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
	<script src='form/js/QuillEditor/quill.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">T13FT_魔术时间FT</div>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<div class="c-well">
						<div class="well-con">
							<label>选择魔术师</label>
							<!-- 上传动物图片 -->
							<div class="field-wrap">
								<label class="field-label">动物图片</label>
								<label for="animateId" class="btn btn-show upload" v-if="configData.source.animasteImg==''?true:false">上传</label>
								<label for="animateId" class="btn upload re-upload" v-if="configData.source.animasteImg!=''?true:false">重新上传</label>
								<span class='txt-info'>（尺寸：340X350，大小≤50KB）<em>*</em></span>
								<input type="file" v-bind:key="Date.now()" class="btn-file" id="animateId" size="340*350" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'animasteImg',50)">
							</div>
							<div class="img-preview" v-if="configData.source.animasteImg!=''?true:false">
								<img v-bind:src="configData.source.animasteImg" alt="" />
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="configData.source.animasteImg=''">删除</span>
								</div>
							</div>

							<div class="field-wrap">
									<label class="field-label">话泡图片</label>
									<label for="cloudImg" class="btn btn-show upload" v-if="configData.source.cloudImg==''?true:false">上传</label>
									<label for="cloudImg" class="btn upload re-upload" v-if="configData.source.cloudImg!=''?true:false">重新上传</label>
									<span class='txt-info'>（尺寸：宽度自适应X179,大小：≤50KB）<em>*</em></span>
									<input type="file" v-bind:key="Date.now()" class="btn-file" id="cloudImg" size="*179" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'cloudImg',50)">
								</div>
								<div class="img-preview" v-if="configData.source.cloudImg!=''?true:false">
									<img v-bind:src="configData.source.cloudImg" alt="" />
									<div class="img-tools">
										<span class="btn btn-delete" v-on:click="configData.source.cloudImg=''">删除</span>
									</div>
								</div>
							<!-- 上传声音 -->
							<div class="field-wrap">
								<label class="field-label" for="">上传声音</label>
								<div class="audio-preview" v-show="configData.source.audio!=''?true:false">
									<div class="audio-tools">
										<p v-show="configData.source.audio!=''?true:false">{{configData.source.audio}}</p>
										<img src="" alt="" v-show="configData.source.audio==''?true:false">
									</div>
									<span class="play-btn" v-on:click="play($event)">
										<audio v-bind:src="configData.source.audio"></audio>
									</span>
								</div>
								<span class="btn btn-audio-dele" v-show="configData.source.audio!=''?true:false" v-on:click="configData.source.audio=''">删除</span>
								<label for="audio-upload" class="btn btn-show upload" v-if="configData.source.audio==''?true:false">上传</label>
								<label for="audio-upload" class="btn upload re-upload mar" v-if="configData.source.audio!=''?true:false">重新上传</label>
								<span class='txt-info'>（大小：≤50KB,变出时的声音)<em>*</em></span>
								<input type="file" id="audio-upload" class="btn-file upload" size="" volume="50" accept=".mp3" v-on:change="audioUpload($event,configData.source,'audio')"
								 v-bind:key="Date.now()">
							</div>

							<div class="field-wrap">
									<label class="field-label" for="">上传声音</label>
									<div class="audio-preview" v-show="configData.source.sayAud!=''?true:false">
										<div class="audio-tools">
											<p v-show="configData.source.sayAud!=''?true:false">{{configData.source.sayAud}}</p>
											<img src="" alt="" v-show="configData.source.sayAud==''?true:false">
										</div>
										<span class="play-btn" v-on:click="play($event)">
											<audio v-bind:src="configData.source.sayAud"></audio>
										</span>
									</div>
									<span class="btn btn-audio-dele" v-show="configData.source.sayAud!=''?true:false" v-on:click="configData.source.sayAud=''">删除</span>
									<label for="sayAud" class="btn btn-show upload" v-if="configData.source.sayAud==''?true:false">上传</label>
									<label for="sayAud" class="btn upload re-upload mar" v-if="configData.source.sayAud!=''?true:false">重新上传</label>
									<span class='txt-info'>（大小：≤50K,说话时声音)<em>*</em></span>
									<input type="file" id="sayAud" class="btn-file upload" size="" volume="50" accept=".mp3" v-on:change="audioUpload($event,configData.source,'sayAud')"
									 v-bind:key="Date.now()">
								</div>
						</div>
					</div>
				</div>

			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
