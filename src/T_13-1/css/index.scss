@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
// .desc-visi{
// 	visibility: hidden;
// }
// .desc {
//     color: black;
// }
// .title h3{
//     color: black;
// }
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: url(../image/defaultBg.png) no-repeat;
    background-size: auto 100%;
}
.container{
	  position: relative;
    font-family:"ARLRDBD";

}
.stage{
	width: 100%;
    height: 100%;
    position: relative;
    height: 100%;
    #light {
        width: 17.3rem;
        height: 10.8rem;
        position: absolute;
        top: 0;
        right: 1rem;
        background: url(../image/light.png) no-repeat;
        background-size:contain;
        z-index:1;
        display: none;
    }
    .light_one{
        left: -4.5rem;
    }
    .light_two{
        left: 1rem;
    }
    .light_three{
        left: 6.5rem;
    }
    .light_four{
        left: 1rem;
    }
    .stage_one{
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        .person_two {
            position: absolute;
            width: 4.43rem;
            height: 6.76rem;
            left: 7.4rem;
            top: 2.86rem;
            background: url(../image/magic_1.png) no-repeat;
            background-size:contain;
            z-index: 4;
            filter: sepia(.2);
        }
        .choose {
            filter: sepia(0);
        }
        .tag{
            position: absolute;
            width: 1.8rem;
            height: 1.8rem;
            bottom: 1rem;
            right: 1.4rem;;
            // background: url(../image/button.png) no-repeat;
            // background-size:contain;
            z-index: 4;
            cursor: pointer;
        }
        .hand{
            width:1.8rem;
            height:1.8rem;
            background: url('../image/hands.png');
            background-size: 7.2rem 1.8rem;
            position: absolute;
            bottom: 0;
            right: 6.2rem;
            animation: handClick 1s steps(4) infinite;
            z-index: 4;
            cursor: pointer;
        }
        @keyframes handClick {
            0%{
                background-position: 0 0;
            }
            100%{
                background-position:-133% 0;
            }
        }
        @-webkit-keyframes handClick {
            0%{
                background-position: 0 0;
            }
            100%{
                background-position:-133% 0;
            }
        }
    }
    .stage_two{
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        display: none;
        .person{
            position: absolute;
            z-index: 4;
        }
        .per_two {
          left: 0.28rem;
          top: 2.4rem;
          width: 5.9rem;
          height: 8.28rem;
            background: url(../image/magic_1_change.png) no-repeat;
            background-size:contain;
            z-index: 5;
            .animate {
                width: 8rem;
                height: 5rem;
                // background: red;
                overflow: hidden;
                position: absolute;
                top: -0.9rem;
                left: 3.9rem;
                img {
                    width: 2rem;
                    height: auto;
                    position: absolute;
                    bottom: -0.9rem;
                    left:-.2rem;
                    z-index: 5;
                }
                @keyframes zoonScale {
                  0% {
                    width: 2rem;
                  }
                  100% {
                    width: 3.4rem;
                  }
                }
            }
        }
        .none{
            background: url('')
        }
        .table{
            position: absolute;
            bottom: 1.1rem;
            left: 5.51rem;
            width: 7.64rem;
            height: 5.52rem;
            background: url(../image/table.png) no-repeat;
            background-size:contain;
            z-index: 4;
            .box{
                position: absolute;
                width: 1rem;
                height: 1rem;
                top: 0.2rem;
                left: 2.8rem;
            }
        }
        .child {
            width: 3rem;
            height: 6rem;
            position: absolute;
            right: 2rem;
            bottom: 1.2rem;
        }
        .childAnt {
            animation: animate steps(3) 1s infinite;
        }
        @keyframes animate {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: -9rem 0;
            }
        }
        .cloud {
            width: auto;
            height: 1.79rem;
            position: absolute;
            left: 11.96rem;
            bottom: 6.4rem;
            opacity: 0;
            img{
              height: 1.79rem;
            }
            span{
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%,-50%);
            }
            .cloud-audio-icon {
              position: absolute;
              height: 1rem;
              width: 1rem;
              left: 0.13rem;
              top: 0.35rem;
            }
        }
    }
    .animationMove {
        animation: animationMove 2s forwards;
    }
  }
  .toMax {
    animation: toMax 2s forwards;
  }
  @keyframes animationMove {
      0% {
          left: 0;
          top: 0;
      }
      100% {
        top: 1.2rem;
      }
  }
  @keyframes toMax {
    0% {
        transform: scale(1);
        transform-origin: center;
    }
    100% {
      transform: scale(1.2);
      transform-origin: center;
    }
  }

