"use strict"
import '../../common/js/common_1v1.js'
import '../../common/js/lottie.js'

$(function () {
    window.h5Template = {
        hasPractice: '0'
    }
    // 音频动画
    let audioAnimations = 0;
    let h5SyncActions = parent.window.h5SyncActions;
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
    let addClassArr = ['light_one', 'light_three', 'light_two'];
    let personArr = ['person_one', 'person_three', 'person_two', 'person_three', 'person_one', 'person_three', 'person_two', 'person_three'];
    $('.cloud').append(`<img src="${configData.source.cloudImg}">`);
    $('.animate_img').attr('src', configData.source.animasteImg);
    $('.childAud').attr('src',configData.source.sayAud)
    if (configData.source.audio) {
      $('.stage').append('<audio src="' + configData.source.audio + '" class="audio" data-syncaudio="audio5"></audio>')
    }
    window.onload = async function () {
        let itemIndex = 0;
        let itemClick = true;
        // audioAnimations = await lottieAnimations.init(audioAnimations, '//cdn.51suyang.cn/apollo/public/json/laba.json', ".cloud-audio-icon");
        audioAnimations = await lottieAnimations.init(audioAnimations, './image/laba.json', ".cloud-audio-icon");
        $('.tag').on('click touchstart', function (e) {
            if (e.type == "touchstart") {
                e.preventDefault()
            }
            e.stopPropagation();
            if (itemClick) {
                itemClick = false;
                if (!isSync) {
                    $(this).trigger('buttonClick')
                    return;
                }
                SDK.bindSyncEvt({
                    index: $(e.currentTarget).data('syncactions'),
                    eventType: 'click',
                    method: 'event',
                    funcType: 'audio',
                    syncName: 'buttonClick'
                });
            }
        })
        $('.tag').on('buttonClick', function () {
            $('#light').fadeIn(1000);
            $('.hand').fadeOut(500);
            $('.person_two').addClass('choose');
            let starNum = 0;
            let time = setInterval(function () {
                $('#light').attr('class', addClassArr[starNum]);
                $('.' + personArr[starNum]).addClass('choose').siblings().removeClass('choose');
                // $('.' + personArr[starNum]).find('audio').get(0).play();
                SDK.playRudio({
                    index: $('.' + personArr[starNum]).find('audio').get(0),
                    syncName: $('.' + personArr[starNum]).find('audio').attr("data-syncaudio")
                })
                starNum++;
                if (starNum >= addClassArr.length) {
                    clearInterval(time);
                    $('#light').attr('class', 'light_two');
                    $('.person_two').addClass('choose').siblings().removeClass('choose');
                    setTimeout(function () {
                        switchStage()
                    }, 1000)
                }
            }, 500)
            SDK.setEventLock();
        })
        $('.cloud-audio-icon').on('click touchstart', function (e) {
          if (e.type == "touchstart") {
              e.preventDefault()
          }
          e.stopPropagation();
          if (!isSync) {
              $(this).trigger('cloudAudioIconClick')
              return;
          }
          SDK.bindSyncEvt({
              index: $(e.currentTarget).data('syncactions'),
              eventType: 'click',
              method: 'event',
              funcType: 'audio',
              syncName: 'cloudAudioIconClick'
          });
        })
        $('.cloud-audio-icon').on('cloudAudioIconClick', function () {
          // 重新播放音频
          $('.childAud').attr('src', '').attr('src', configData.source.sayAud);
          lottieAnimations.play(audioAnimations);
          SDK.playRudio({
            index: $('.childAud').get(0),
            syncName: $('.childAud').attr("data-syncaudio")
          })
          $('.childAud').on('ended', function (){
            lottieAnimations.stop(audioAnimations);
          })
          SDK.setEventLock();
        })
        // 第二个场景
        function switchStage() {
          $('#light').fadeOut(1000);
          $('.person').addClass('per_two');
          $('.stage_one').fadeOut(1000, function () {
            $('.stage_two').fadeIn(1000, function () {
              $('#light').fadeIn(1000, function () {
                $('.animate img').css({'animation': 'zoonScale 0.01s forwards'});
                let element = $('.animate_img').get(0);
                let target = $('.box').get(0);
                        let parabola = funParabola(element, target, {
                            curvature: '.4',
                            speed: 4 / window.base,
                            complete: function () {
                                if ($('.audio').length >= '1') {
                                    SDK.playRudio({
                                        index: $('.audio').get(0),
                                        syncName: $('.audio').attr("data-syncaudio")
                                    })
                                }
                                setTimeout(function () {
                                    $('#light').hide();
                                    $('.main').addClass('toMax');
                                    $('.stage_two').addClass('animationMove');
                                    setTimeout(function () {
                                        $('.cloud').css({ 'opacity': '1' })
                                        lottieAnimations.play(audioAnimations);
                                        SDK.playRudio({
                                            index: $('.childAud').get(0),
                                            syncName: $('.childAud').attr("data-syncaudio")
                                        })
                                        $('.child').addClass('childAnt');
                                        $('.childAud').get(0).onended = function () {
                                          $('.child').removeClass('childAnt');
                                          lottieAnimations.stop(audioAnimations);
                                        }
                                    },1000)
                                },1000)
                            }
                        })
                        parabola.init();

                    }).attr('class', 'light_four');

                });
            });
        }
    }
})
