<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SXMB0001FT_三星模板</title>
    <link rel="stylesheet" href="form/css/style.css">
    <script src='form/js/jquery-2.1.1.min.js'></script>
    <script src='form/js/vue.min.js'></script>
</head>
<body>
<div id="container">
    <div class="edit-form">
        <div class="h-title">SXMB0001FT_三星模板</div>

        <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>
        <% include ./src/common/template/multyDialog/form.ejs %>


                    <div class="c-group">
                        <div class="c-title">设置热区和点击元素（最多12个）</div>
                        <div class="c-area upload img-upload">
                            <ul>
                                <li v-for="(item, index) in configData.source.options">
                                    <div class="c-well">
                                        <span class="dele-tg-btn" style="z-index: 9;position: relative"
                                            v-show="configData.source.options.length > 1"
                                            v-on:click="deleConfig(item)"></span>
                                        <div class="field-wrap">
                                            <label class="field-label">热区图片{{index + 1}}*</label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-heatMap'+index" class="btn btn-show upload"
                                                    v-if="!item.heatMap">上传</label>
                                                <label :for="'content-pic-heatMap'+index" class="btn upload re-upload"
                                                    v-if="item.heatMap">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG格式，最大尺寸不超过800x800，小于等于80Kb </em></div>
                                            <input type="file" v-bind:key="Date.now()+'heatMap'" class="btn-file"
                                                :id="'content-pic-heatMap'+index" size="800*800" accept=".jpg,.png"
                                                @change="imageUpload($event,item,'heatMap',80)">
                                        </div>

                                        <div class="img-preview" v-if="item.heatMap">
                                            <img :src="item.heatMap" alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete" @click="delPrew(item,'heatMap')">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item" style="margin-top: 10px">
                                            <span>热图位置: &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.heatMapLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.heatMapLocation.y">
                                            <br>
                                        </div>

                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label">点击元素{{index + 1}}*</label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-image'+index" class="btn btn-show upload"
                                                    v-if="!item.image">上传</label>
                                                <label :for="'content-pic-image'+index" class="btn upload re-upload"
                                                    v-if="item.image">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG、JSON格式小于等于240Kb</em></div>
                                            <input type="file" v-bind:key="Date.now()+'image'" class="btn-file"
                                                :id="'content-pic-image'+index" size="" accept=".jpg,.png,.json"
                                                @change="feedbackUpload($event,item,'image',240)">
                                        </div>

                                        <div class="img-preview" v-if="item.image">
                                            <img :src="item.image.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.image" alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete" @click="delPrew(item,'image')">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item">
                                            <span>点击元素位置: &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.imageLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.imageLocation.y">
                                            <br>
                                        </div>

                                        <!-- 文字图片 -->
                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label">文字图片{{index + 1}}</label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-textImage'+index" class="btn btn-show upload"
                                                    v-if="!item.textImage">上传</label>
                                                <label :for="'content-pic-textImage'+index" class="btn upload re-upload"
                                                    v-if="item.textImage">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG格式，最大尺寸不超过800x800，小于等于80Kb </em></div>
                                            <input type="file" v-bind:key="Date.now()+'heatMap'" class="btn-file"
                                                :id="'content-pic-textImage'+index" size="800*800" accept=".jpg,.png"
                                                @change="imageUpload($event,item,'textImage',80)">
                                        </div>
                                        <div class="img-preview" v-if="item.textImage">
                                            <img :src="item.textImage" alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete"
                                                    @click="delPrew(item,'textImage')">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item" style="margin-top: 10px">
                                            <span>文字图位置: &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.textImageLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.textImageLocation.y">
                                            <br>
                                        </div>


                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label>初始大小：</label>
                                            <input class='c-input-txt' type="number"
                                                style="margin: 0 10px;width: 100px!important;display: inline-block;"
                                                oninput="if(value>200)value=200;if(value<0)value=0" placeholder=""
                                                v-model="item.initialSize">
                                            <span>1%-200%</span>
                                        </div>

                                        <!-- 上传声音 -->
                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label" for="">元素音频</label>
                                            <span>
                                                <label :for="'audio-upload-audio'+index" class="btn btn-show upload"
                                                    v-if="item.audio==''?true:false">上传</label>
                                                <label :for="'audio-upload-audio'+index"
                                                    class="btn upload re-upload mar"
                                                    v-if="item.audio!=''?true:false">重新上传</label>
                                            </span>
                                            <div style="color: red">mp3格式，小于等于50Kb</div>

                                            <div class="audio-preview" v-show="item.audio!=''?true:false">
                                                <div class="audio-tools">
                                                    <p v-show="item.audio!=''?true:false">{{item.audio}}</p>
                                                </div>
                                                <span class="play-btn" v-on:click="play($event)">
                                                    <audio v-bind:src="item.audio"></audio>
                                                </span>
                                            </div>
                                            <span class="btn btn-audio-dele" v-show="item.audio!=''?true:false"
                                                v-on:click="item.audio=''">删除</span>
                                            <input type="file" :id="'audio-upload-audio'+index" class="btn-file upload"
                                                size="" accept=".mp3" v-on:change="audioUpload($event,item,'audio',50)"
                                                v-bind:key="Date.now()+'audio'">
                                        </div>

                                        <button class="add-tg-btn" style="margin-top: 20px" v-on:click="addConfig"
                                            v-if="index < 11 && index === configData.source.options.length -1">+</button>

                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="c-group">
                        <div class="c-title">干扰项（最多6个）</div>
                        <div class="c-area upload img-upload">
                            <button class="add-gr-btn" @click="addDistraction"
                                v-if="configData.source.distraction.length === 0">添加
                            </button>
                            <ul v-else>
                                <li v-for="(item, index) in configData.source.distraction">
                                    <div class="c-well">
                                        <span class="dele-tg-btn" style="z-index: 9;position: relative"
                                            v-on:click="deleDistraction(item)"></span>
                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label">干扰点击元素{{index + 1}}*</label>
                                            <span class="field-wrap">
                                                <label :for="'content-pic-dis'+index" class="btn btn-show upload"
                                                    v-if="!item.image">上传</label>
                                                <label :for="'content-pic-dis'+index" class="btn upload re-upload"
                                                    v-if="item.image">重新上传</label>
                                            </span>
                                            <div class='txt-info'><em>JPG、PNG、JSON格式小于等于240Kb</em></div>
                                            <input type="file" v-bind:key="Date.now()" class="btn-file"
                                                :id="'content-pic-dis'+index" size="" accept=".jpg,.png,.json"
                                                @change="feedbackUpload($event,item,'image',240)">
                                        </div>

                                        <div class="img-preview" v-if="item.image">
                                            <img :src="item.image.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.image" alt="" />
                                            <div class="img-tools">
                                                <span class="btn btn-delete" @click="delPrew(item)">删除</span>
                                            </div>
                                        </div>
                                        <div class="field-wrap-item">
                                            <span>干扰元素位置: &nbsp;&nbsp;</span>
                                            X:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1920)value=1920;if(value<0)value=0"
                                                v-model="item.imageLocation.x">
                                            Y:<input type="number" class="c-input-txt "
                                                style="margin: 0 10px;width: 60px!important;display: inline-block;"
                                                oninput="if(value>1080)value=1080;if(value<0)value=0"
                                                v-model="item.imageLocation.y">
                                            <br>
                                        </div>

                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label>初始大小：</label>
                                            <input type="text" class='c-input-txt'
                                                oninput="if(value>200)value=200;if(value<0)value=0"
                                                style="margin: 0 10px;width: 100px!important;display: inline-block;"
                                                placeholder="" v-model="item.initialSize">
                                            <span>1%-200%</span>
                                        </div>

                                        <!-- 上传声音 -->
                                        <div class="field-wrap" style="margin-top: 20px">
                                            <label class="field-label" for="">元素音频</label>
                                            <span>
                                                <label :for="'audio-upload-dis'+index" class="btn btn-show upload"
                                                    v-if="item.audio==''?true:false">上传</label>
                                                <label :for="'audio-upload-dis'+index" class="btn upload re-upload mar"
                                                    v-if="item.audio!=''?true:false">重新上传</label>
                                            </span>
                                            <div style="color: red">mp3格式，小于等于50Kb</div>

                                            <div class="audio-preview" v-show="item.audio!=''?true:false">
                                                <div class="audio-tools">
                                                    <p v-show="item.audio!=''?true:false">{{item.audio}}</p>
                                                </div>
                                                <span class="play-btn" v-on:click="play($event)">
                                                    <audio v-bind:src="item.audio"></audio>
                                                </span>
                                            </div>
                                            <span class="btn btn-audio-dele" v-show="item.audio!=''?true:false"
                                                v-on:click="item.audio=''">删除</span>
                                            <input type="file" :id="'audio-upload-dis'+index" class="btn-file upload"
                                                size="" accept=".mp3" v-on:change="audioUpload($event,item,'audio',50)"
                                                v-bind:key="Date.now()">
                                        </div>

                                        <button class="add-tg-btn" style="margin-top: 20px"
                                            v-if="index < 5 && index === configData.source.distraction.length -1"
                                            v-on:click="addDistraction">+</button>

                                    </div>
                                </li>
                            </ul>
                        </div>

                    </div>
                    <div class="c-group">
                        <div class="c-title">小手设置</div>
                        <div class="c-area">
                            <div class="field-wrap">
                                <label class="field-label" style="width: 100px;">小手效果</label>
                                <select v-model="configData.source.handSet" style="width: 150px;">
                                    <option value="0">不显示</option>
                                    <option value="1">仅显示一次(在第一个点击元素上)</option>
                                    <option value="2">顺序出现在可点击元素上</option>
                                </select>

                            </div>
                        </div>
                    </div>
                    <!-- 反馈动画添加 -->
                    <% include ./src/common/template/feedbackAnimation/form %>
                        <button class="send-btn" v-on:click="onSend">提交</button>
        </div>
        <div class="edit-show">
            <div class="show-fixed">
                <div class="show-img">
                    <img src="form/img/preview.jpg?v=<%= new Date().getTime() %>" alt="">
                </div>
                <ul class="show-txt">
                    <li><em>图片格式：</em>JPG/PNG/GIF</li>
                    <li><em>声音格式：</em>MP3/WAV</li>
                    <li><em>视频格式：</em>MP4</li>
                    <li>带有" * "号为必填项</li>
                </ul>
            </div>
        </div>
    </div>
</body>
<script src='form/js/form.js?v=<%= new Date().getTime() %>'></script>
</html>
