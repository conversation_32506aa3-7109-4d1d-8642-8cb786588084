"use strict"
import '../../common/js/common_1v1.js'
import "../../common/template/multyDialog/index.js";
import "../../common/js/teleprompter.js"
import { USER_TYPE, CLASS_STATUS, TEACHER_TYPE, INTERACTION_TYPE, USERACTION_TYPE } from "../../common/js/constants.js";
import {feedbackAnimation} from "../../common/template/feedbackAnimation/index.js";

$(function () {
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      item: configData.source.options.length + configData.source.distraction.length,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.TEACHING_INPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.LISTEN
    },
  })

    // 全局配置和状态
  window.h5Template = {hasPractice: '1'}
  const options = configData.source.options;
  const distraction = configData.source.distraction;
  const h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  let start = true;
  let correctArr = []; //存储正确的选项的下标
  let clickIndex = 0;
  let roteAnimations;
  const lottieInstances = {}; // 存储所有Lottie动画实例

  // 设置背景
  if (configData.bg == '') {
    $(".container").css({'background-image': 'url(./image/bg_53.jpg)'})
  }

  // 动画管理模块
  const animationManager = {
    stopLottieAnimation(animId) {
      if (lottieInstances[animId]) {
        lottieAnimations.stop(lottieInstances[animId]);
        return true;
      }
      return false;
    },

    stopAllAnimations() {
      Object.keys(lottieInstances).forEach(id => {
        this.stopLottieAnimation(id);
      });
    },

    async initLottieAnimation(selector, jsonUrl, animId) {
      try {
        lottieInstances[animId] = lottieInstances[animId] ? lottieInstances[animId] : await lottieAnimations.init(
          lottieInstances[animId],
          jsonUrl,
          selector
        );
        lottieAnimations.play(lottieInstances[animId]);
        return animId;
      } catch (error) {
        console.error(`初始化Lottie动画失败: ${selector}`, error);
        return null;
      }
    },

    async initLabaAnimation(index) {
      lottieInstances[`laba${index}`] = await lottieAnimations.init(
        lottieInstances[`laba${index}`],
        './image/laba.json',
        `[data-syncactions="textAudio${index}"]`,
        false
      );
    },

    playLabaAnimation(index, needEnd = false) {
      const audio = $(`[data-syncaudio="audioOpt${index}"]`).get(0);
      if (audio) {
        lottieAnimations.play(lottieInstances[`laba${index}`]);
        audio.currentTime = 0;
        SDK.playRudio({
          index: audio,
          syncName: `audioOpt${index}`
        });

        audio.onended = () => {
          lottieAnimations.stop(lottieInstances[`laba${index}`]);
          SDK.setEventLock();
          if (needEnd) {
            end();
          }
        };
        SDK.reportTrackData({
          action:'CK_FT_INTERACTION_AUDIOPLAY', //事件名称
          data:{audio:Number(index)+1}, // 老师和学生  都需要上报的数据
          teaData:{},  // 只有老师端会上报的数据
          stuData:{},  // 只有学生端会上报的数据
        })
      }
    }
  };

  // 样式管理模块
  const styleManager = {
    isJsonFile(url) {
      if (!url) return false;
      const extension = url.split('.').pop().toLowerCase();
      return extension === 'json';
    },

    createStyleObject(width, height, left, top, scale, imageUrl, shouldSetBackground = true, zIndex) {
      const css = {
        position: 'absolute',
        width: `${width / 100}rem`,
        height: `${height / 100}rem`,
        left: left ? `${left / 100}rem` : '0rem',
        top: top ? `${top / 100}rem` : '0rem',
      };

      if (scale) {
        css.transform = `scale(${scale / 100})`;
      }

      if (shouldSetBackground && imageUrl) {
        css.backgroundImage = `url(${imageUrl})`;
        css.backgroundPosition = 'center';
        css.backgroundRepeat = 'no-repeat';
        css.backgroundSize = 'contain';
      }

      if (zIndex !== undefined) {
        css.zIndex = zIndex;
      }

      return css;
    },

    getImageDimensions(url) {
      return new Promise((resolve, reject) => {
        if (!url) {
          reject(new Error('无效的图片URL'));
          return;
        }

        if (this.isJsonFile(url)) {
          $.getJSON(url)
            .done(data => {
              const width = data.width || data.w;
              const height = data.height || data.h;
              resolve({width, height});
            })
            .fail(() => reject(new Error('JSON文件加载失败')));
        } else {
          const img = new Image();
          img.onload = () => resolve({width: img.width, height: img.height});
          img.error = () => reject(new Error('图片加载失败'));
          img.src = url;
        }
      });
    },

    async applyElementStyle(selector, dimensions, location, scale, imageUrl, animId, zIndex) {
      try {
        const isJson = this.isJsonFile(imageUrl);
        const shouldSetBackground = !isJson;
        const safeLocation = location || { x: 0, y: 0 };
        $(selector).css(
          this.createStyleObject(
            dimensions.width,
            dimensions.height,
            safeLocation.x,
            safeLocation.y,
            scale,
            imageUrl,
            shouldSetBackground,
            zIndex
          )
        );
        if (isJson) {
          await animationManager.initLottieAnimation(selector, imageUrl, animId);
        }
      } catch (error) {
        console.error(`设置元素样式失败: ${selector}`, error);
      }
    }
  };

  // 页面管理模块
  const pageManager = {
    async setStyle() {
      const stylePromises = [];

      // 处理选项元素
      options.forEach((item, i) => {
        if (item.heatMap) {
          stylePromises.push(
            styleManager.getImageDimensions(item.heatMap)
              .then(dimensions => {
                styleManager.applyElementStyle(
                  `[data-syncactions="heatMap${i}"]`,
                  dimensions,
                  item.heatMapLocation,
                  null,
                  item.heatMap
                );
              })
              .catch(error => console.error(`处理热区图片失败: ${i}`, error))
          );
        }

        if (item.image) {
          stylePromises.push(
            styleManager.getImageDimensions(item.image)
              .then(dimensions => {
                styleManager.applyElementStyle(
                  `[data-syncactions="image${i}"]`,
                  dimensions,
                  item.imageLocation,
                  item.initialSize,
                  item.image,
                  `option${i}`,
                  20 // 设置点击元素的z-index为20
                );
              })
              .catch(error => console.error(`处理选项图片失败: ${i}`, error))
          );
        }

        if (item.textImage) {
          stylePromises.push(
            styleManager.getImageDimensions(item.textImage)
              .then(dimensions => {
                styleManager.applyElementStyle(
                  `[data-syncactions="textImage${i}"]`,
                  dimensions,
                  item.textImageLocation,
                  null,
                  item.textImage
                );
                $(`[data-syncactions="textImage${i}"]`).css('display', 'none');

                if (options[i].audio) {
                  const textAudioWrapper = $(`<div class="textAudio" data-syncactions="textAudio${i}"></div>`);
                  $(`[data-syncactions="textImage${i}"]`).append(textAudioWrapper);

                  textAudioWrapper.css({
                    transform: 'translate(-50%, -50%)',
                    cursor: 'pointer',
                    width: '1.08rem',
                    height: '1.08rem'
                  });

                  const self = this;
                  (async function () {
                    await animationManager.initLabaAnimation(i);
                  })();

                  textAudioWrapper.syncbind('click touchstart', function (dom, next) {
                    if (!isSync) {
                      next(false);
                    } else {
                      next();
                    }
                  }, function () {
                    animationManager.playLabaAnimation(i);
                  });
                }
              })
              .catch(error => console.error(`处理文字图片失败: ${i}`, error))
          );
        }
      });

      // 处理干扰元素
      distraction.forEach((item, i) => {
        if (item.image) {
          stylePromises.push(
            styleManager.getImageDimensions(item.image)
              .then(dimensions => {
                styleManager.applyElementStyle(
                  `[data-syncactions="disImg${i}"]`,
                  dimensions,
                  item.imageLocation,
                  item.initialSize,
                  item.image,
                  `disImg${i}`,
                  10 // 设置干扰项的z-index为10
                );
              })
              .catch(error => console.error(`处理干扰图片失败: ${i}`, error))
          );
        }
      });

      try {
        await Promise.all(stylePromises);
        handManager.setHand(0);
      } catch (error) {
        console.error('设置样式时发生错误:', error);
      }
    },

    eleShow() {
      const elements = [
        ...options.map((item, i) => `
          <div data-syncactions="heatMap${i}"></div>
          <div data-syncactions="textImage${i}"></div>
          <audio class="audio" src="${item.audio || ''}" data-syncaudio="audioOpt${i}"></audio>
          <div class="images clickableEl" data-syncactions="image${i}" data-index="${i}"></div>
        `),
        ...distraction.map((item, i) => `
          <div class="disImg clickableEl" data-syncactions="disImg${i}" data-index="${i}"></div>
          <audio class="audio" src="${item.audio || ''}" data-syncaudio="audioDis${i}"></audio>
        `)
      ].join('');

      $(".mainArea").append(elements);
      this.setStyle();
    }
  };

  // 小手模块
  const handManager = {
    setHand(index) {
      if ((configData.source.handSet === '1' && clickIndex == 0) || configData.source.handSet === '2') {
        this.handStyle(index);
      } else {
        $('.hands').hide();
      }
    },

    handStyle(index) {
      let targetEl = $(`[data-syncactions="image${index}"]`);
      let targetCenterX = targetEl.position().left + (targetEl.outerWidth() / 2);
      let targetCenterY = targetEl.position().top + (targetEl.outerHeight() / 2);
      $('.hands').css({
        animation: 'hand 1s steps(4) infinite',
        '-webkit-animation': 'hand 1s steps(4) infinite',
        left: targetCenterX + 'px',
        top: targetCenterY + 'px'
      }).show();
    }
  };

  // 事件处理模块
  const eventManager = {
    init() {
      this.initHoverEvents();
      this.initClickEvents();
    },

    initHoverEvents() {
      $(document).on('mouseenter', '.clickableEl', function () {
        const flag = $(this).attr('data-syncactions');
        if (!correctArr.includes(flag)) {
          const currentTransform = $(this).css('transform');
          let currentScale = 1;

          if (currentTransform && currentTransform !== 'none') {
            const matrix = new WebKitCSSMatrix(currentTransform);
            currentScale = matrix.a;
          }
          $(this).css('transform', `scale(${currentScale * 1.1})`);
        }
      });

      $(document).on('mouseleave', '.clickableEl', function () {
        const flag = $(this).attr('data-syncactions');
        if (!correctArr.includes(flag)) {
          const currentTransform = $(this).css('transform');
          let currentScale = 1;

          if (currentTransform && currentTransform !== 'none') {
            const matrix = new WebKitCSSMatrix(currentTransform);
            currentScale = matrix.a;
          }

          $(this).css('transform', `scale(${currentScale / 1.1})`);
        }
      });
    },

    initClickEvents() {
      $(document).on('click touchstart', '.clickableEl', function (e) {
        if (e.type == 'touchstart') {
          e.preventDefault();
        }
        if (configData.source.handSet === '2' && $(this).attr('data-syncactions').includes('image') && $(this).attr('data-index') != clickIndex) {
          console.log('顺序错误');
          return;
        }
        if (start) {
          start = false;
          if (!isSync) {
            $(this).trigger('changePosSync');
            return;
          }
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data("syncactions"),
            eventType: 'click',
            method: 'event',
            syncName: 'changePosSync',
            recoveryMode: '1'
          });
        }
      });

      $(document).on('changePosSync', '.clickableEl', (e, message) => {
        if (correctArr.length >= options.length || correctArr.includes($(e.currentTarget).attr('data-syncactions'))) {
          return;
        }
        let flag = $(e.currentTarget).attr('data-syncactions');
        let index = $(e.currentTarget).attr('data-index');
        let that = e.currentTarget;
        if (flag.includes('image')) {
          correctArr.push(flag);
          clickIndex++;
          $('.hands').hide();
          this.moveAnimation(that, index);

          setTimeout(async () => {
            const startAnimationPromise = this.startAnimation(index);
            const correctAudioPromise = await this.playAudio($('#correctAudio'));

            Promise.all([startAnimationPromise, correctAudioPromise]).then(async () => {
              const optionAudio = $(`[data-syncaudio="audioOpt${index}"]`);
              const textImage = $(`[data-syncactions="textImage${index}"]`);

              if (optionAudio.attr('src')) {
                if (configData.source.options[index].textImage) {
                  textImage.fadeIn(300);
                  animationManager.playLabaAnimation(index, true);
                } else {
                  optionAudio.get(0).currentTime = 0;
                  SDK.playRudio({
                    index: optionAudio.get(0),
                    syncName: optionAudio.attr("data-syncaudio")
                  });
                  optionAudio.one('ended', function () {
                    end();
                  });
                  SDK.reportTrackData({
                    action:'CK_FT_INTERACTION_AUDIOPLAY', //事件名称
                    data:{audio:Number(index)+1}, // 老师和学生  都需要上报的数据
                    teaData:{},  // 只有老师端会上报的数据
                    stuData:{},  // 只有学生端会上报的数据
                  })
                }
              } else {
                end();
              }
            });
          }, 500);
          SDK.reportTrackData({
            action:'CK_FT_INTERACTION_ITEM', //事件名称
            data:{}, // 老师和学生  都需要上报的数据
            teaData:{},  // 只有老师端会上报的数据
            stuData:{
              item:Number(index)+1
            },  // 只有学生端会上报的数据
          },USER_TYPE.STU)
        } else {
          this.errorAnimationAudio(e.currentTarget);
          setTimeout(async () => {
            const distractionAudio = $(`[data-syncaudio="audioDis${index}"]`);

            if (distractionAudio.attr('src')) {
              await this.playAudio(distractionAudio);
              console.log('干扰项',Number(index)+1)
              SDK.reportTrackData({
                action:'CK_FT_INTERACTION_AUDIOPLAY', //事件名称
                data:{audio:Number(index)+1}, // 老师和学生  都需要上报的数据
                teaData:{},  // 只有老师端会上报的数据
                stuData:{},  // 只有学生端会上报的数据
              })
            }

            start = true;
            SDK.setEventLock();
          }, 1700);
          SDK.reportTrackData({
            action:'CK_FT_INTERACTION_ITEM', //事件名称
            data:{}, // 老师和学生  都需要上报的数据
            teaData:{},  // 只有老师端会上报的数据
            stuData:{
              error:Number(index)+1
            },  // 只有学生端会上报的数据
          },USER_TYPE.STU)
        }
      });
    },

    moveAnimation(that, index) {
      let targetEl = $(`[data-syncactions="heatMap${index}"]`);
      let heatMapWidth = targetEl.outerWidth();
      let heatMapHeight = targetEl.outerHeight();
      let initialScale = options[index].initialSize / 100;
      let targetLeft = targetEl.position().left + (heatMapWidth / 2) - ($(that).outerWidth() / 2);
      let targetTop = targetEl.position().top + (heatMapHeight / 2) - ($(that).outerHeight() / 2);
      $(that).animate({
        left: targetLeft,
        top: targetTop
      }, {
        duration: 500,
        step: function (now, fx) {
          let scaleValue = initialScale - (initialScale - 1) * (fx.pos);
          $(that).css('transform', 'scale(' + scaleValue + ')');
        },
        complete: function () {
          $(that).css('transform', 'scale(1)');
        }
      });
    },

    async startAnimation(index) {
      roteAnimations = roteAnimations ? roteAnimations : await lottieAnimations.init(
        roteAnimations,
        './image/start.json',
        '#startEl',
        false
      );
      let targetEl = $(`[data-syncactions="heatMap${index}"]`);
      let targetCenterX = targetEl.position().left + targetEl.outerWidth() / 2;
      let targetCenterY = targetEl.position().top + targetEl.outerHeight() / 2;
      const res = await $.get('./image/start.json');
      $('#startEl').css({
        position: 'absolute',
        left: targetCenterX,
        top: targetCenterY,
        height: `${res.h}px`,
        width: `${res.w}px`,
        transform: 'translate(-50%, -50%)',
        zIndex: 30
      });
      $('#startEl').show();
      lottieAnimations.play(roteAnimations);
      return new Promise((resolve, reject) => {
        try {
          roteAnimations.addEventListener("complete", () => {
            lottieAnimations.stop(roteAnimations);
            $('#startEl').hide();
            resolve();
          });
        } catch (error) {
          reject(error);
        }
      });
    },

    errorAnimationAudio(element) {
      $(element).addClass('shake');
      let elAudio = $('#mistakeSound').get(0);
      elAudio.currentTime = 0;
      SDK.playRudio({
        index: elAudio,
        syncName: $('#mistakeSound').attr("data-syncaudio")
      });
      $(element).on('animationend webkitAnimationEnd', function () {
        $(element).removeClass('shake');
      });
    },

    playAudio(el) {
      return new Promise(async (resolve, reject) => {
        let audio = el.get(0);
        audio.currentTime = 0;
        SDK.playRudio({
          index: audio,
          syncName: el.attr("data-syncaudio")
        });
        el.on('ended', () => {
          resolve();
        });
      });
    }
  };

  // 结束
  function end() {
    if (correctArr.length === options.length) {
      animationManager.stopAllAnimations();
      feedbackAnimation('feedKey1');
      SDK.reportTrackData({
        action:'CK_FT_INTERACTION_COMPLETE', //事件名称
        data:{result:'success'}, // 老师和学生  都需要上报的数据
        teaData:{},  // 只有老师端会上报的数据
        stuData:{},  // 只有学生端会上报的数据
      },USER_TYPE.TEA)
    } else {
      start = true;
      handManager.setHand(clickIndex);
      SDK.setEventLock();
    }
  }

  pageManager.eleShow();
  eventManager.init();
});



