var configData = {
  bg: '',
  desc: '',
  title: '',
  tg: [
    {
      content: "c",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
  ],
  level: {
    high: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      }],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "",
          audio: "",
        },
      ],
      messageLocationX: "", // 消息内容位置x
      messageLocationY: "", // 消息内容位置y
      roleLocationX: "100", // 角色位置x
      roleLocationY: "600", // 角色位置y
      roleImg: "", // 角色图片
      playAfterStauts: "2", // 播放完之后状态
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框
      hiddenStatus: "1", // 播放完是否应藏的状态
    },
    options: [
      {
        heatMap: 'assets/images/1.png',
        heatMapLocation: {
          x: '500',
          y: '500'
        },
        image: 'assets/images/Doll_turn1.json',
        imageLocation: {
          x: '100',
          y: '100'
        },
        textImage: 'assets/images/text.png',
        textImageLocation: {
          x: '150',
          y: '150'
        },
        audio: 'assets/audios/01.mp3',
        initialSize: '120'
      },
      {
        heatMap: 'assets/images/2.png',
        heatMapLocation: {
          x: '600',
          y: '600'
        },
        image: 'assets/images/22.png',
        imageLocation: {
          x: '200',
          y: '200'
        },
        textImage: '',
        textImageLocation: {
          x: '200',
          y: '200'
        },
        audio: 'assets/audios/01.mp3',
        initialSize: '120'
      },
      {
        heatMap: 'assets/images/3.png',
        heatMapLocation: {
          x: '700',
          y: '700'
        },
        image: 'assets/images/33.png',
        imageLocation: {
          x: '300',
          y: '300'
        },
        textImage: 'assets/images/33.png',
        textImageLocation: {
          x: '300',
          y: '300'
        },
        audio: 'assets/audios/01.mp3',
        initialSize: '100'
      },
    ],
    distraction: [
      {
        image: 'assets/images/Doll_turn1.json',
        imageLocation: {
          x: '1000',
          y: '400'
        },
        audio: 'assets/audios/01.mp3',
        initialSize: '100'
      },
      {
        image: 'assets/images/5.png',
        imageLocation: {
          x: '900',
          y: '300'
        },
        audio: 'assets/audios/01.mp3',
        initialSize: '100'
      }
    ],
    handSet: '1'
  },
  feedbackLists: [
    {
      positiveFeedback: '2',
      feedbackList: [
        {id: '-1', json: '', mp3: ''},
        {id: '0', json: './image/prefect.json', mp3: './audio/prefect.mp3'},
        {id: '1', json: './image/goldCoin.json', mp3: './audio/goldCoin.mp3'},
        {id: '2', json: './image/FKJB.json', mp3: './audio/resultWin.mp3'},
        {id: '9', json: './image/feedback.json', mp3: './audio/feedback01.mp3'},
      ],
      feedbackObj: {id: '0', json: './image/prefect.json', mp3: './audio/prefect.mp3'},
      feedback: '',
      feedbackAudio: '',
      feedbackName: '整体反馈',
      key: 'feedKey1',
    }
  ]
};
