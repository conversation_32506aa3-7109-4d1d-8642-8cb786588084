// var domain = 'http://172.16.0.107:9011/pages/1152/';
var domain = '';

var Data = {
    configData: {
        bg: '',
        desc: '',
        title: "",
        tg: [{
            title: '',
            content: ''
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {
          fristModule: 0, //0：仅老师  1：老师/学生
          moduleList:[
            {
              twoModule: 11, //11：仅老师   12：仅学生  1：老师/学生
              default: 13, //13：默认老师  14：默认学生
            }
          ]
        }
    }
}
$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    methods: {
        //图片上传
        imageUpload: function(e, item, attr) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;

            if ((!isNaN(parseInt($(e.target).attr('volume')))) && (size / 1024).toFixed(2) > parseInt($(e.target).attr('volume'))) {
                console.error("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB");
                alert("您上传的图片大小为" + (size / 1024).toFixed(2) + "KB, 超过" + $(e.target).attr('volume') + "K上限，请检查后上传！");
                e.target.value = '';
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            var img = new Image();
            img.onload = function() {
                naturalWidth = img.naturalWidth;
                naturalHeight = img.naturalHeight;
                var check = that.sourceImgCheck(e.target, {
                    height: naturalHeight,
                    width: naturalWidth
                }, item, attr);
                if (check) {
                    that.postData(file, item, attr);
                    img = null;
                } else {
                    img = null;
                }
            }
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        //图片大小校检
        sourceImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                //console.error("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
            }
            return checkSize;
        },
        //音频上传
        audioUpload: function(e, item, attr, fileSize) {
            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            that = this;

            if ((size / 1024).toFixed(2) > 500) {
                console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            } else {
                console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            }
            if ((size / 1024).toFixed(2) > fileSize) {
                console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
                alert("您上传的音频大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
                return;
            }
            var url = URL.createObjectURL(file); //获取录音时长
            var audioElement = new Audio(url);
            var duration;
            audioElement.addEventListener("loadedmetadata", function(_event) {
                duration = audioElement.duration ? audioElement.duration : '';
                var check = that.sourceAudioCheck(e.target, {
                    duration: duration,
                });
                if (check) {
                    item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
                    audioElement = null;
                } else {
                    audioElement = null;
                }
            });
        },
        //音频长度校检
        sourceAudioCheck: function(input, data) {
            let dom = $(input),
                time = dom.attr("time");
            if (time == "" || time == undefined || data.duration == '') return true;
            let checkSize = false;
            if (data.duration <= time) {
                checkSize = true;
            } else {
                alert(`您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`);
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source;
            var checkMsg = false;

            //验证游戏各轮元素 是否齐全
            for (let i of data.moduleList) {
              if (!i.twoModule) {
                checkMsg = "请选择权限分配类型";
                return checkMsg;
              }
              if(i.twoModule == 1 && !i.default)
              if (!i.audioTime) {
                checkMsg = "请选择默认权限";
                return checkMsg;
              }
            }
        },

        onSend: function() {
            var data = this.configData;
            var _data = JSON.stringify(data);
            console.log(_data)
            var val = this.validate();
            if (!val) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                alert(val);
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            bg = arguments.length > 2 ? arguments[2] : null;
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    console.log(res.data.key);
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },

        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({
                title: '',
                content: ''
            });
        },
        addL: function(item) {
            this.configData.level.low.push({
                title: '',
                content: ''
            });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        addOptions: function(arr, item) {
            if (item) {
                arr.push(item)
            } else {
                arr.push('')
            }
        },
        delOption: function(arr, item) {
            arr.remove(item)
        },
        setAnswer: function(item) {
            this.configData.source.right = item;
        },
        inputPlayNum: function() {
            var reg = new RegExp("^([1][0-9]{0,1}|[2][0]{0,1})$");
            var regResult = reg.test(Data.configData.source.palyTime);
            if (!regResult && Data.configData.source.palyTime != "#") {
                Data.configData.source.palyTime = "";
            }
        }
    },
    watch: {

    }
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
