"use strict"
import '../../common/js/common_1v1.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import {
    resultWin,
    resultHide
} from '../../common/template/ribbonWin/index.js';
import {
    templateScale,
    handClick,
    handHide
} from "../../common/template/hint/index.js";
$(function() {
    //是否在控制器显示功能按钮
    window.h5Template = {
        hasDemo: '1', //0 默认值，无提示功能  1 有提示功能
        hasPractice: '3' //0 无授权功能  1  默认值，普通授权模式  2 start授权模式  3 新授权模式
    }
    let staticData = configData.source,
        fristModule = staticData.fristModule,
        moduleList = staticData.moduleList; //列表数据
    let userType = window.frameElement && window.frameElement.getAttribute('user_type'); //用户身份学生还是老师


    //判断是否使用默认背景图片
    let isFirst = true;
    if (configData.bg == '') {
        $(".container").css({
            'background-image': 'url(./image/bg.jpg)',
        })
    }

    /**
     * 初始化
    */
   moduleListDraw()
    function moduleListDraw() {
      moduleList.forEach(function(item, index) {
        let str = '';
        str = $(`<li data-syncactions="onLine-${index}" data-twoModule="${item.twoModule}" data-default="${item.default}">`
              + `<span>按钮${item.twoModule}</span>`
              +`<span>默认权限：${item.default}</span>`
              + '</li>');
        $('.moduleList .moduleUl').append(str)
      })
    }


    /**
     * 授权模式方法
     * @param {*} type: 方法
     * @param {*} value：权限
     * 11:仅老师有权限  12:仅学生有权限  13:S/T，默认老师权限  14:S/T，默认学生权限
    */
    function moduleListFn(type, value) {
      console.log(1111)
      SDK.bindSyncCtrl({
        'type': type,
        'tplAuthorization':"tpl",
        'data': {
            CID: SDK.getClassConf().course.id + '', //教室id 字符串
            operate: '1',
            data: [{
                key: 'classStatus',
                value: value,
                ownerUID: SDK.getClassConf().user.id
            }]
        }
      });
    }

    /**
     * 事件event
    */
    $(".moduleUl").on("click touchstart", 'li', function(e) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation(); //阻止事件进一步传播

      if (!isSync) {
        return;
      }

      let dataTwoModule = $(this).attr('data-twoModule'),
      dataDefault = $(this).attr('data-default');
      console.log('dataTwoModule----', dataTwoModule)
      console.log('dataDefault----', dataDefault)
      switch(dataTwoModule){
        case '11' :
          moduleListFn('teaOnly', '11')
          break;
        case '12' :
          moduleListFn('stuOnly', '12')
          break;
        case '1' :
          if(dataDefault == 13) {
            moduleListFn('teaRoot', '13')
          }
          if(dataDefault == 14) {
            moduleListFn('stuRoot', '14')
          }
          break;
      }

    });


    /**
     *初始化 老师 or 老师/学生
    */
    // 只有老师
     if(fristModule == 0) {
      if (!isSync) {
        return;
      }
      console.log('SDK.getClassConf().h5Course.classStatus----', SDK.getClassConf().h5Course.classStatus)
      if(SDK.getClassConf().h5Course.classStatus == 11) {
        // moduleListFn('tplAuthorization', '11')
        moduleListFn('teaOnly', '11')
      }
     }
    //S/T 老师有权限
     if(fristModule == 1) {
      if (!isSync) {
        return;
      }
      if(SDK.getClassConf().h5Course.classStatus == 13) {
        // moduleListFn('tplAuthorization', '13')
        moduleListFn('teaRoot', '13')
      }

     }
});


/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
    var def = $.Deferred(),
        len = list.length;
    $(list).each(function(i, e) {
        var img = new Image();
        img.src = e.image;
        if (img.complete) {
            imgs[i] = img;
            len--;
            if (len == 0) {
                def.resolve();
            }
        } else {
            img.onload = (function(j) {
                return function() {
                    imgs[j] = img
                    len--;
                    if (len == 0) {
                        def.resolve();
                    }
                };
            })(i);
            img.onerror = function() {
                len--;
            };
        }
    });
    return def.promise();
};
