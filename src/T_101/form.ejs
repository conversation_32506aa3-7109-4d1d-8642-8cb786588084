<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>NRC0001_新授权模式</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src='./form/js/vue.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">NRC0001_新授权模式</h3>
      <!-- 编辑器 -->
      <div class="c-group">
        <div class="c-title">第一个阶段模块</div>
        <div class="c-area upload img-upload radio-group">
          <div class="c-well">
            <span class="fixed-width">权限分配类型</span>
            <label class="inline-label" for="fristModule"><input type="radio" name="fristModule" value="0"
                v-model="configData.source.fristModule">仅老师</label>
            <label class="inline-label" for="fristModule"><input type="radio" name="fristModule" value="1"
                v-model="configData.source.fristModule">老师/学生</label>
          </div>
        </div>
      </div>
      <div class="c-group">
        <div class="c-title">第二个阶段模块</div>
        <div class="c-area upload img-upload radio-group">
          <div class="c-well"  v-for="(item,index) in configData.source.moduleList">
            <div>
              <span class="fixed-width">权限分配类型{{index+1}}</span>
              <span class="dele-tg-btn" v-on:click="delOption(configData.source.moduleList, item)" v-show="configData.source.moduleList.length>1"></span>
              <label for="twomodule" class="inline-label">
                <input type="radio" :name="'twomodule' + index" value="11" v-model="item.twoModule">&nbsp;仅老师
              </label>
              <label for="twomodule" class="inline-label">
                <input type="radio" :name="'twomodule' + index" value="12" v-model="item.twoModule">&nbsp;仅学生
              </label>
              <label for="twomodule" class="inline-label">
                <input type="radio" :name="'twomodule' + index" value="1" v-model="item.twoModule">&nbsp;老师/学生
              </label>
            </div>
            <div v-if="item.twoModule == 1">
              <span class="fixed-width">默认权限</span>
              <label for="default" class="inline-label">
                <input type="radio" :name="'default' + index" value="13" v-model="item.default">&nbsp;默认老师
              </label>
              <label for="default" class="inline-label">
                <input type="radio" :name="'default' + index" value="14" v-model="item.default">&nbsp;默认学生
              </label>
            </div>
          </div>
          <button type="button" class="add-tg-btn text-add-btn add-btn-61"
          v-on:click="addOptions(configData.source.moduleList, {
            twoModule: '',
            default:''
          })">添加一轮</button>
        </div>
      </div>
      <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.webp?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>

</html>
