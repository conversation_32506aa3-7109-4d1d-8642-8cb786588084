var configData = {
  bg: "",
  pannel:{
    pannelBottomBg: undefined,
    pannelWheelBg: "",
    pannelBottonLocationX:102,
    pannelBottonLocationY:151,
    contentBg:'',
    pipeTopJson:'',
    pipeBottomJson:'',
    coinJson:'',
    hasRotateMusic:'1',
  },
  carousel: {
    content:[
      {
        img:'./assets/images/001.jpeg',
      },
      {
        img:'./assets/images/002.jpeg',
      },
      {
        img:'./assets/images/003.jpeg',
      },
      {
        img:'./assets/images/003.jpeg',
      }
    ]
  },
  feedback:'',
  desc: "qw23213234",
  title: "fdfg fgfh gthrt",
  tg: [
    {
      content:
        "AHI0002_句子霓虹灯eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
  ],
  level: {
    high: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
    low: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
  },
  source: {
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "./assets/images/e41570807174c4a7617e2a75455838d1.png",
          audio: "./assets/audios/01.mp3"
        },
        {
          text:"./assets/images/e41570807174c4a7617e2a75455838d1.png",
          audio: "./assets/audios/4a5ec638c60f9afcc25e797e006919ab.mp3"
        }
      ],
      messageLocationX: '550', // 消息内容位置x
      messageLocationY: '100', // 消息内容位置y
      roleLocationX: '100', // 角色位置x
      roleLocationY: '100', // 角色位置y
      roleImg: "./assets/images/Doll_turn1.json", // 角色图片
      prevBtn:'./assets/images/cd28b2d464fd49af8fba357129e92873.png',
      nextBtn:'./assets/images/9aba8b13d42b1e7c51f03ffc923e6e05.png',
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框 1不自动播放  2自动播放
      hiddenStatus: "3", // 播放完是否应藏的状态 1不隐藏  2气泡隐藏  3IP和气泡都隐藏
    },
  },
  tImg: '',
	tImgX: 1340,
	tImgY: 15,
};
