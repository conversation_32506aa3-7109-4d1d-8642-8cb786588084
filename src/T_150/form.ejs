<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>FTG0001_转盘游戏</title>
    <link rel="stylesheet" href="form/css/style.css" />
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
  </head>
  <body>
    <div id="container">
      <div class="edit-form">
        <div class="h-title">FTG0001_转盘游戏</div>
        <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>

        <!-- IP对话组件 -->
        <% include ./src/common/template/multyDialog/form %>

        <div class="c-group">
          <div class="c-title">转盘配置</div>

          <div class="c-area upload img-upload">
            <div>中奖规则：（最后一轮必须经验值加满）</div>
            <ul>
              <li>
                - 宝箱分为两种：大奖和小奖，大奖宝箱显示 x4，小奖宝箱显示 x2
              </li>
              <li>- 大奖：0 度 180度</li>
              <li>- 小奖：90度 270度</li>
              <li>- 不中奖：45度 135度 225度 315度</li>
            </ul>
            <br />
            <div>大奖100小奖60，首轮中奖，最后一轮中奖</div>
            <ul>
              <li>- 3轮，总分150，中奖2次，奖励一大一小随机分</li>
              <li>- 4轮，总分150，中奖2次，奖励一大一小随机分</li>
              <li>- 5轮，总分210，中奖3次，奖励一大二小随机分</li>
              <li>- 6轮，总分210，中奖3次，奖励一大二小随机分</li>
            </ul>
            <br>
            <div class="c-well">
              <div>转盘扇面</div>
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">图片</label>
                  <span class="txt-info"
                    ><em
                      >PNG格式，固定尺寸：560 x 560，大小不超过100KB
                      非必填，展示默认样式
                    </em></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    size="560*560"
                    :id="'pannel-wheel'"
                    accept=".png"
                    @change="imageUpload($event,configData.pannel,'pannelWheelBg',100)"
                  />
                </div>
                <div class="field-wrap">
                  <label
                    :for="'pannel-wheel'"
                    class="btn btn-show upload"
                    v-if="!configData.pannel.pannelWheelBg"
                    >上传</label
                  >
                  <label
                    :for="'pannel-wheel'"
                    class="btn upload re-upload"
                    v-if="configData.pannel.pannelWheelBg"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="configData.pannel.pannelWheelBg">
                  <img :src="configData.pannel.pannelWheelBg" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGamePrew(configData.pannel, 'pannelWheelBg')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>转盘扇底</div>
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">图片</label>
                  <span class="txt-info"
                    ><em>PNG格式，大小不超过100KB</em></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    size=""
                    :id="'pannel-bottom'"
                    accept=".png"
                    @change="imageUpload($event,configData.pannel,'pannelBottomBg',100)"
                  />
                </div>
                <div class="field-wrap">
                  <label
                    :for="'pannel-bottom'"
                    class="btn btn-show upload"
                    v-if="!configData.pannel.pannelBottomBg"
                    >上传</label
                  >
                  <label
                    :for="'pannel-bottom'"
                    class="btn upload re-upload"
                    v-if="configData.pannel.pannelBottomBg"
                    >重新上传</label
                  >
                </div>
                <div
                  class="img-preview"
                  v-if="configData.pannel.pannelBottomBg"
                >
                  <img :src="configData.pannel.pannelBottomBg" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGameIPrew(configData.pannel, 'pannelBottomBg')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">坐标位置</label><br />
                  X:<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1920)value=1920;if(value<0)value=0"
                    v-model="configData.pannel.pannelBottonLocationX"
                  />
                  Y:<input
                    type="number"
                    class="c-input-txt c-input-txt-inline"
                    oninput="if(value>1080)value=1080;if(value<0)value=0"
                    v-model="configData.pannel.pannelBottonLocationY"
                  />
                  <span class="txt-info"
                    ><em class="game_em">数字,0<=x<=1920,0<=y<=1080</em></span
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="c-area upload img-upload">
            <div class="c-well">
              <!-- <div>图片框</div> -->
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">图片框</label>
                  <span class="txt-info"
                    ><em
                      >PNG格式，尺寸：910 x 626, 大小不超过100KB
                      非必填，展示默认样式</em
                    ></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    size="910*626"
                    :id="'carousel-bg'"
                    accept=".png"
                    @change="imageUpload($event,configData.pannel,'contentBg',100)"
                  />
                </div>
                <div class="field-wrap">
                  <label
                    :for="'carousel-bg'"
                    class="btn btn-show upload"
                    v-if="!configData.pannel.contentBg"
                    >上传</label
                  >
                  <label
                    :for="'carousel-bg'"
                    class="btn upload re-upload"
                    v-if="configData.pannel.contentBg"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="configData.pannel.contentBg">
                  <img :src="configData.pannel.contentBg" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGameIPrew(configData.pannel, 'contentBg')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">上管道动画</label>
                  <span class="txt-info"
                    ><em>文件大小≤100KB,格式.json </em></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    id="pipe-top"
                    size=""
                    accept=".json"
                    @change="lottieUpload($event,configData.pannel,'pipeTopJson',100)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    for="pipe-top"
                    class="btn btn-show upload"
                    v-if="!configData.pannel.pipeTopJson"
                    >上传</label
                  >
                  <label
                    for="pipe-top"
                    class="btn upload re-upload"
                    v-if="configData.pannel.pipeTopJson"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="configData.pannel.pipeTopJson">
                  <!-- <img
                    src="//cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                    alt=""
                  /> -->
                  <img
                    src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                    alt=""
                  />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGamePrew(configData.pannel, 'pipeTopJson')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">下管道动画</label>
                  <span class="txt-info"
                    ><em>文件大小≤100KB,格式.json </em></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    id="pipe-bottom"
                    size=""
                    accept=".json"
                    @change="lottieUpload($event,configData.pannel,'pipeBottomJson',100)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    for="pipe-bottom"
                    class="btn btn-show upload"
                    v-if="!configData.pannel.pipeBottomJson"
                    >上传</label
                  >
                  <label
                    for="pipe-bottom"
                    class="btn upload re-upload"
                    v-if="configData.pannel.pipeBottomJson"
                    >重新上传</label
                  >
                </div>
                <div
                  class="img-preview"
                  v-if="configData.pannel.pipeBottomJson"
                >
                  <img
                    src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                    alt=""
                  />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGamePrew(configData.pannel, 'pipeBottomJson')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">金币掉落动画</label>
                  <span class="txt-info"
                    ><em>文件大小≤100KB,格式.json </em></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    id="pipe-coin"
                    size=""
                    accept=".json"
                    @change="lottieUpload($event,configData.pannel,'coinJson',100)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    for="pipe-coin"
                    class="btn btn-show upload"
                    v-if="!configData.pannel.coinJson"
                    >上传</label
                  >
                  <label
                    for="pipe-coin"
                    class="btn upload re-upload"
                    v-if="configData.pannel.coinJson"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="configData.pannel.coinJson">
                  <img
                    src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                    alt=""
                  />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGamePrew(configData.pannel, 'coinJson')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>


          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">是否开启转盘音乐</label>
                  <select id="teachTime" v-model="configData.pannel.hasRotateMusic" style="width: 170px;">
                    <option name="optive" value="1">是</option>
                    <option name="optive" value="2">否</option>
                </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">用户跟读内容设置(最少3张，最多6张)</div>
          <div class="c-area upload img-upload">
            <div
              class="c-well"
              v-for="(item, index) in configData.carousel.content"
              v-bind:key="index"
            >
              <div>
                <span
                  class="dele-tg-btn"
                  style="position: relative; z-index: 10"
                  @click="delWheelGameCarouselContent(item)"
                  v-show="configData.carousel.content.length>3"
                ></span>
                <div class="field-wrap">
                  <label
                    class="field-label"
                    for=""
                    >{{`内容图片${index+1}* `}}</label
                  >
                  <span class="txt-info"
                    ><em>尺寸：686 * 426,文件大小≤80KB,格式.jpg.png *</em></span
                  >
                  <input
                    type="file"
                    class="btn-file"
                    :id="`carousel-content-${index}`"
                    size="686*426"
                    accept=".jpg,.jpeg,.png"
                    @change="imageUpload($event,item,'img',80)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    :for="`carousel-content-${index}`"
                    class="btn btn-show upload"
                    v-if="!item.img"
                    >上传</label
                  >
                  <label
                    :for="`carousel-content-${index}`"
                    class="btn upload re-upload"
                    v-if="item.img"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="item.img">
                  <img
                    :src="item.img"
                    alt=""
                  />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGamePrew(item, 'img')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
            <button
              v-if="configData.carousel.content.length<6"
              type="button"
              class="text-add-btn add-tg-btn add-tg-btn-dialog"
              @click="addCarouselContent"
            >
              添加跟读图片
            </button>
          </div>
        </div>

        <div class="c-group">
          <div class="c-title">整体反馈配置</div>
          <div class="c-area upload img-upload">
            <div class="c-well">
              <div>
                <div class="field-wrap">
                  <label class="field-label" for="">上传文件</label>
                  <span class="txt-info"
                    ><em>文件大小≤200KB,格式.json.png</em></span
                  >
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    id="feedback"
                    size=""
                    accept=".json,.png"
                    @change="feedbackUpload($event,configData,'feedback',200)"
                  />
                </div>

                <div class="field-wrap">
                  <label
                    for="feedback"
                    class="btn btn-show upload"
                    v-if="!configData.feedback"
                    >上传</label
                  >
                  <label
                    for="feedback"
                    class="btn upload re-upload"
                    v-if="configData.feedback"
                    >重新上传</label
                  >
                </div>
                <div class="img-preview" v-if="configData.feedback">
                  <img
                    src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                    alt=""
                  />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delWheelGamePrew(configData, 'feedback')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <button class="send-btn" v-on:click="onSend">提交</button>
      </div>
      <div class="edit-show">
        <div class="show-fixed">
          <div class="show-img">
            <img
              src="form/img/preview.jpg?v=<%=new Date().getTime()%>"
              alt=""
            />
          </div>
          <ul class="show-txt">
            <li><em>图片格式：</em>JPG/PNG/GIF</li>
            <li><em>声音格式：</em>MP3/WAV</li>
            <li><em>视频格式：</em>MP4</li>
            <li>带有“ * ”号为必填项</li>
          </ul>
        </div>
      </div>
    </div>
  </body>
  <script src="form/js/form.js?v=<%=new Date().getTime()%>"></script>
</html>
