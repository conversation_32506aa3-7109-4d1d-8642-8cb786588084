<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="FTG0001_转盘游戏"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult='show-result-1'>
    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>
    
    <section class="main">
        <audio id="effect-audio" class="audioSrc" webkit-playsinline controls src="" data-syncaudio="effectAudio"></audio>
        <audio id="wheel-audio" class="audioSrc" webkit-playsinline controls src="" data-syncaudio="effectAudio"></audio>
        <div class="game-container"  data-syncactions='syncInitRule'>
            <div class="pannel-bottom"></div>
            <div class="pannel-content">
                <div class="pannel-wheel"></div>
                <div class="pannel-pointer"></div>
                <div class="stars-json"></div>
                <div class="emoji-json"></div>
            </div>
            <div class="carousel-wrapper">
                <div class="experience-bar">
                    <div class="experience-bar-fill"></div>
                </div>
                <div class="chest"></div>
                <div class="carousel-container">
                    <div class="carousel">
                        <div class="carousel-inner">
                        </div>
                    </div>
                    <span class="carousel-control prev not-allowed" data-syncactions='sycnContentPrevBtn'></span>
                    <span class="carousel-control next not-allowed" data-syncactions='sycnContentNextBtn'></span>
                </div>
                <div class="carousel-counter"></div>
                <div class="start" data-syncactions='syncStartBtn'></div>
                <div class="great" data-syncactions='syncGreatBtn'></div>  
                <!-- <div class="handle-top handle" data-syncactions='syncHandleTopBtn'>
                    <div class="handsss"></div>
                </div>
                <div class="handle-bottom handle"></div> -->
            </div>
            <div class="mic-json"></div>
            <div class="coin">
                <div class="coin-bottom"></div>
                <div class="coin-json"></div> 
            </div>
            <div class="pipe-top"></div>
            <div class="pipe-bottom"></div>
            <div class="mask">
                <div class="chest-json"></div>
                <div class="feedback"></div>
            </div>
        </div>
    </section>
</div>
<%include ./src/common/template/index_bottom %>
<%include ./src/common/template/lottie %>
</body>
</html>
