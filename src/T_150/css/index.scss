@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/multyDialog/style.scss';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.container{
	// background-image: url(../image/1.jpg);
	position: relative;
    // font-family:"ARLRDBD";

}
audio{
	width: 0;
	height: 0;
	opacity: 0;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    .audioSrc {
        position: absolute;
    }
}
.mainArea{
	width: 16.4rem;
	height: 7.45rem;
	// position: relative;	
	top:2.15rem;
	left: 50%;
	margin-left: -8.2rem;
	.list{
		width: 1.64rem;
		height: 1.49rem;
		// float: left;
		box-sizing: border-box;
		// position: relative;
        position: absolute;
        z-index: 10;
		.audioList{
            position: absolute;
            width: 1.45rem;
            height: 1.34rem;
            background: url('../image/btn-audio-bg.png') no-repeat;
            background-size: 100% 100%;
            z-index: 10;
			left: 50%;
			top: 50%;
            transform: translateX(-50%) translateY(-50%);
            cursor: pointer;
            img{
                position: absolute;
                top: 0.3rem;
                left: 0.3rem;
                width: 0.83rem;
                height: 0.8rem;
            }
            audio{
                width:0;
                height: 0;
                opacity: 0;
                position:absolute;
            }
        }
	}
}

.game-container{
    // background-color: red;
    width: 100%;
    height: 100%;
    position: relative;
    .pannel-bottom  {
        position: absolute;
        z-index: 5;
        background-size: cover;
    }
    .pannel-content {
        .pannel-wheel {
            position: absolute;
            z-index: 6;
            top: 2.34rem;
            left: 2.1rem;
            width: 5.6rem;
            height: 5.6rem;
            background-size: cover;
            transition: transform 3.5s ease-in-out; 
            // animation:  ease 0s 1 normal none running none;  
        }
        .pannel-pointer {
            position: absolute;
            z-index: 7;
            top: 4.01rem;
            left: 4.14rem;
            background-size: cover;
            width: 1.5rem;
            height: 1.88rem;
        }
        .stars-json {
            position: absolute;
            z-index: 8;
            top: -2rem;
            left: -.1rem;
            display: none;
        }
        .emoji-json {
            position: absolute;
            z-index: 8;
            // top: -2rem;
            // left: -.1rem;
            display: none;
            left: 2.9rem;
            top: 2.2rem;
        }
    }
    .carousel-wrapper {
        position: absolute;
        z-index: 5;
        right: 0.52rem;
        top: 2.09rem;
        width: 9.02rem;
        height: 6.2rem;
        background-size: cover;
        .experience-bar {
            position: absolute;
            z-index: 7;
            left: 1.03rem;
            top: 0.26rem;
            width: 5.96rem;
            height: 0.24rem;
            background-color: #543F89;
            border-radius: .24rem;
            overflow: hidden;   
            padding: 2px;
            .experience-bar-fill {
                height: 100%;
                width: 0;    
                transition: all 1s ease;
                background: url('../image/experience.png') repeat  center;
                background-size: cover;
                border-radius: .24rem;
            }
        }
        .chest {
            position: absolute;
            z-index: 8;
            right: 1.34rem;
            top: -0.39rem;
            background: url('../image/chest.png') no-repeat center;
            background-size: cover;
            width: 0.97rem;
            height: 0.92rem;
        }
        .carousel-container {
            width: 6.86rem;
            height: 4.26rem;
            margin-top: .62rem;
            margin-left: .78rem;
            position: relative;
            .carousel-control {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 0.72rem;
                height: 0.9rem;
                padding: 10px;
                cursor: pointer;
                background-size: 100% 100%;
                background-position: center;
                background-repeat: no-repeat;
                transition: transform 0.3s ease;
                z-index: 7;
                &.prev {
                    left: -0.44rem;
                    background-image: url("../image/pannel-control-left-active.png");
                    &:hover {
                        transform: translateY(-50%) scale(1.05);
                    }
                    &.not-allowed {
                        background-image: url("../image/pannel-control-left.png");
                        cursor: not-allowed ;
                        &:hover {
                            transform: translateY(-50%)
                        }
                    }
                }
                &.next {
                    right: -0.44rem;
                    background-image: url("../image/pannel-control-right-active.png");
                    &:hover {
                        transform: translateY(-50%) scale(1.05);
                    }
                    &.not-allowed {
                        background-image: url("../image/pannel-control-right.png");
                        cursor: not-allowed ;
                        &:hover {
                            transform: translateY(-50%)
                        }
                    }

                }
            }
        }
        .carousel {
            width: 100%;
            height: 100%;
            position: relative;
            // border-radius: .72rem;
            border-radius: .36rem;
            overflow: hidden;
            
        }
        .carousel-inner {
            display: flex;
            transition: transform 0.5s ease-in-out;
            width: 100%;
            height: 100%;
            .carousel-item {
                min-width: 100%;
                height: 100%;
                border-radius: .36rem;
                overflow: hidden;
                img {
                    display: block;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
        .carousel-counter {
            line-height: 0.3rem;
            font-weight: 600;
            font-size: 0.28rem;
            color: #6C68DE;
            position: absolute;
            z-index: 7;
            right: 0.85rem;
            bottom: 1.84rem;
        }
        .start ,.great{
            position: absolute;
            z-index: 6;
            width: 3.18rem;
            height: 1.16rem;
            left: 2.65rem;
            bottom:0.56rem;
            background: url('../image/start.png') no-repeat center;
            background-size: cover;
            cursor: pointer;
        }
        .great {
            background: url('../image/great.png') no-repeat center;
            background-size: cover;
            display: none;
        }
        .handle {
            position: absolute;
        }
        .handle-top {
            background: url('../image/handle-top.png') no-repeat center;
            background-size: cover;
            width: 1.23rem;
            height: 2.17rem;
            right: 0;
            top: 1.39rem;
            z-index: 6;
            .handsss {
                width:1.8rem;
                height:1.8rem;
                background: url('../image/hands.png');
                background-size: 7.2rem 1.8rem;
                position: absolute;
                top: 80%;
                left: 70%;
                transform: translate(-50%, -50%) scale(0.7);
                animation: handClick 0.8s steps(4) infinite;
                opacity: 1;
                cursor: pointer;
                z-index: 6;
                display: none;
            }
        }
        .handle-bottom {
            background: url('../image/handle-bottom.png') no-repeat center;
            background-size: cover;
            width: 1.23rem;
            height: 1.83rem;
            right: 0;
            top: 1.89rem;
            z-index: 6;
            display: none;
        }
    }
    .coin {
        .coin-bottom {
            position: absolute;
            z-index: 6;
            background: url('../image/coin-bottom-change.png') no-repeat center;
            background-size: cover;
            width: 3.14rem;
            height: 1.76rem;
            bottom: 1.04rem;
            right: 3.76rem;
        }
        .coin-json {
            position: absolute;
            z-index: 7;
            bottom: 0.8rem;
            right: 3.15rem;
            display: none;
        }
    }
    .pipe-top {
        // left: 4.81rem;
        // top: 2.75rem;
        // 根据动画尺展现的寸修改位置
        left: 4.81rem;
        top: 2.3rem;
        position: absolute;
        z-index: 4;
        // width: 5.5rem;
        // height: .48rem;
        // background: url('../assets/images/pipe-top.png') no-repeat center;
        // background-size: cover;
    }
    .pipe-bottom {
        // left: 4.8rem;
        // bottom: 1.71rem;
        // 根据动画尺展现的寸修改位置
        left: 4.8rem;
        bottom: 2.16rem;
        position: absolute;
        z-index: 4;
        // width: 8.09rem;
        // height: 0.48rem;
        // background: url('../assets/images/pipe-bottom.png') no-repeat center;
        // background-size: cover;
    }

    .mic-json {
        position: absolute;
        z-index: 10;
        right: 2.95rem;
        bottom: 0;
        display: none;
    }

    .mask {
        position: absolute;
        z-index: 1000;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,.7);
        display: none;
        .chest-json {
            position: absolute;
            z-index: 12;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            display: none;
        }
        .feedback {
            position: absolute;
            z-index: 13;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            display: none;
        }
    }
}






@keyframes shake {
    0%, 100% { transform: translateX(0) rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px) rotate(-5deg); }
    20%, 40%, 60%, 80% { transform: translateX(10px) rotate(5deg); }
}


@keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}
@-webkit-keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}