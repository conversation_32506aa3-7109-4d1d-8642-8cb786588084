@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../record/css/record.css';
@mixin setEle($l:0rem, $t:0rem, $w:0rem, $h:0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.hide {
  display: none;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  .try-again{
    display: none;
    background: rgba(0,0,0,0.7);
    height: 100%;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    .try-again-content{
      display: flex;
      align-items: center;
      flex-direction: column;
      border-radius: 0.6rem;
      position: absolute;
      left: 50%;
      top: 45%;
      animation: fade-in 0.6s cubic-bezier(0.390, 0.575, 0.565, 1.000) both;
      transform: translate(-50%, -50%);
    }
    img{
      display: block;
    }
    .try-again-icon{
      width: 5.3rem;
      height: 5.3rem;
      margin-bottom: 0.5rem;
    }
    .try-again-btn{
      cursor: pointer;
      width: 4.4rem;
      height: 1.1rem;
    }
  }
  .gold-coin{
    display: none;
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    background: rgba(0,0,0,0.7);
    .gold-coin-animation{
      display: block;
      width: 100%;
      height: 100%;
    }
  }
  .cardArea {
    width: 15.5rem;
    height: 9.33rem;
    margin: 1.5rem 0 0 1.65rem;
    background: url('../image/cardBgs-2.png') no-repeat 0 bottom;
    background-size: 15.5rem 9.33rem;
    position: relative;
    .handle {
      width: 1.58rem;
      height: 3rem;
      position: absolute;
      top: 3rem;
      right: -1.54rem;
    }
    .cardTransform {
      z-index: 100;
    }
    .cardPos,.defaultCardPos,
    .cardTransform {
      position: absolute;
      height: 4.2rem;
      width: 13.58rem;
      top: 0.8rem;
      left: 50%;
      transform: translateX(-50%);
      box-sizing: border-box;
    }
    .cardPos{
      display: none;
    }
    ul {
      position: absolute;
      height: 3.52rem;
      width: 100%;
      top: 0.35rem;
      overflow: hidden;
      padding: 0 0.11rem;
      display: flex;
      gap: 0.48rem;
      .card,
      .cardGif {
        width: 2.98rem;
        height: 3.52rem;
        line-height: 3.52rem;
        text-align: center;
        float: left;
        font-size: 1.52rem;
        border-radius: 0.24rem;
        overflow: hidden;
      }
      .card:last-child,
      .cardGif:last-child{
        margin-right: 0;
      }
      .cardGif {
        height: 3.52rem;
      }
      .card {
        top: -3.52rem;
        img{
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      .pic-img{
        width: 2.96rem;
        height: 3.52rem;
      }
      li{
        animation: goTop 0.3s ease-in-out forwards;
        -webkit-animation: goTop 0.1s ease-in forwards;
      }
      // &:nth-child(1) {
      //   li{
      //     animation: goTop 0.3s ease-in-out forwards;
      //     -webkit-animation: goTop 0.1s ease-in forwards;
      //   }
      // }
    }
    .content{
      position: absolute;
      height: 3.1rem;
      width: 14.54rem;
      left: 0.48rem;
      bottom: 1.02rem;
      display: flex;
      align-items: center;
      justify-content: center;
      .content-sentence{
        height: 2rem;
        width: 13.14rem;
        img{
          display: block;
          height: 100%;
          width: 100%;
        }
      }
    }
    .operate-box{
      position: absolute;
      left: 50%;
      bottom: 0;
      transform: translateX(-50%);
      height: 1.56rem;
    }
    .operate-great{
      display: none;
      width: 5.86rem;
      .audio-icon{
        height: 1.73rem;
        width: 3.05rem;
        display: none;
        float: left;
        position: absolute;
        left: 0;
        bottom: 0.04rem;
      }
      .great{
        height: 1.22rem;
        width: 2.59rem;
        display: block;
        background: url('../image/great.png');
        background-size: 100% 100%;
        cursor: pointer;
        margin-top: 0.2rem;
        float: right;
      }
    }
    .operate-reception{
      height: 1.22rem;
      width: 2.59rem;
      background: url('../image/start.png');
      background-size: 100% 100%;
      cursor: pointer;
      margin-top: 0.14rem;
    }
  }
}

.emptyCard {
	background: #AEB4B4;
	border-radius: .2rem;
}

.container {
  background-size: auto 100%;
  position: relative;
  // font-family:"ARLRDBD";
}

.lookTitle{
	width:100%;
	height:1.5rem;
	font-size:0.8rem;
	text-align:center;
	font-weight:700;
	color:#333333;
	span{
		display:inline-block;
		margin-top:0.45rem;
	}
}
.isRed{
  color: red;
}
.gif{
	background: url('../image/scroll.gif') no-repeat center;
	background-size: 2.98rem 3.52rem;
}
.btn{
	width: 0.68rem;
	height: 0.86rem;
	position: absolute;
	top: 6.25rem;
	img{
		width: 100%;
		height:100%;
		position: absolute;
	}
  .disalble{
    display: none;
  }
  .normal{
    display: block;
  }
}
.btn-disable{
	.normal{
		display: none;
	}
  .disalble{
    display: block;
  }
}

.btns:active{
	transform: scale(0.9,0.9);
}
.lastBtn{
	left: -0.1rem;
}
.nextBtn{
	right: -0.1rem;
}


.page{
	background: #FFB44C;
  border: 3px solid #e0532a;
  box-sizing: content-box;
	width: 1.2rem;
  height: 0.54rem;
  line-height: 0.54rem;
	position: absolute;
	bottom: 3.9rem;
	left: 50%;
	margin-left: -0.6rem;
	font-size: 0.38rem;
	color: #fff;
	text-align: center;
	border-radius: .1rem;
}

@keyframes hide{
	100%{
		display: none;
	}
}
@keyframes goTop{
	0%{
		top: -3.52rem
	}
	30%{
		top: 1rem;
	}
	100%{
		top: 0;
	}
}
@-webkit-keyframes goTop{
	0%{
		top: -3.52rem
	}
	30%{
		top: 1rem;
	}
	100%{
		top: 0;
	}
}
@-webkit-keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
