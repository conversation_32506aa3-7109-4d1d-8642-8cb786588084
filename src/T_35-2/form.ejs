<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>TRA0001FT_随机音素FT</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TRA0001FT_随机音素FT</div>

			<% include ./src/common/template/common_head %>

			<!-- 添加选项 -->
			<div class="c-group">
				<div class="c-title">添加选项</div>
				<div class="c-area upload img-upload">
          <div class="pic-title">
            <p class="title">可设置2-10组</p>
          </div>

          <!-- 图片类型 -->
          <div class="pic-list">
            <div class="c-well" v-for="(item,index) in configData.source.optionsData">
              <div class="field-wrap">
                <div class="add-field-content">
                  <label class="field-label"  for="">第{{index+1}}组</label>
                  <span class="txt-info" v-if="configData.source.optionsData.length-1 == index"><em>最后一轮必须设置中奖，并且四张旋转图片相同</em></span>
                  <span class="dele-tg-btn" v-on:click="delOptionImg(item)" v-show="configData.source.optionsData.length>2"></span>
                </div>
                <div class="field-wrap-item">
                  <span class="fixed-width">是否中奖</span>
                  <label class="inline-label" :for="`is-win${index}2`"><input type="radio" @change="changeIsWin(item)" :name="`is-win${index}2`" :value="false"
                      v-model="item.picsList.isWin"> 不中奖</label>
                  <label class="inline-label" :for="`is-win${index}1`"><input type="radio" @change="changeIsWin(item)" :name="`is-win${index}1`" :value="true"
                      v-model="item.picsList.isWin"> 中奖（四张旋转图片相同）</label>
                </div>
                <div class="field-wrap-item">
                  <span class='txt-info'>读题图片</em>
                  <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.picsList.questionImg">上传</label>
                  <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.picsList.questionImg!=''?true:false">重新上传</label>
                  <span class='txt-info'><em>&nbsp;&nbsp;尺寸：1314*200 大小:≤30KB </em></span>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="1314*200" isKey="1" :id="'content-pic-'+index" @change="imageUpload($event,item.picsList,'questionImg',30)">


                  <div class="img-preview" v-if="item.picsList.questionImg">
                    <img v-bind:src="item.picsList.questionImg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.picsList.questionImg=''">删除</span>
                    </div>
                  </div>
                </div>

                <div class="field-wrap-item" v-for="(arr, i) in item.picsList.letter">
                  <span class='txt-info'>旋转图片{{item.picsList.letter.length> 1 ? i+1 : ''}}</span>
                  <label :for="'content-pic-'+index+i" class="btn btn-show upload" v-if="!arr.w">上传</label>
                  <label :for="'content-pic-'+index+i" class="btn upload re-upload" v-if="arr.w!=''?true:false">重新上传</label>
                  <span class='txt-info'><em>&nbsp;&nbsp;尺寸：298*352 大小:≤30KB </em></span>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="298*352" isKey="1" :id="'content-pic-'+index+i" @change="imageUpload($event,arr,'w',30)">


                  <div class="img-preview" v-if="arr.w">
                    <img v-bind:src="arr.w" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="arr.w=''">删除</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button type="button" class="add-tg-btn" v-show="configData.source.optionsData.length<10" v-on:click="addOptionImg">+</button>
          </div>
        </div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/bg.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
