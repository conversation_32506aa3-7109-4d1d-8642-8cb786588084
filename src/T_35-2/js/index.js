"use strict"
import '../../common/js/lottie.js'
import '../../common/js/common_1v1.js'
import './drag.js'
import '../record/js/record.js'
import "../../common/js/teleprompter.js"

$(function () {
  window.h5Template = {
    hasPractice: '0'// 无授权功能，所有由老师操作
  }
  const h5SyncActions = parent.window.h5SyncActions;
  const isSync = h5SyncActions && h5SyncActions.isSync;
  const classStatus = h5SyncActions && h5SyncActions.classConf.h5Course.classStatus;
  if (configData.bg == '') {
    $(".container").css({ 'background-image': 'url(./image/defaultBg.jpg)' })
  }
  // 用户属性
  const userType = window.frameElement && window.frameElement.getAttribute('user_type');
  let currentPage = 1; //当前页码
  let optionsData = configData.source.optionsData;
  // 录音动效
  let microphoneAnimation = 0;
  // 把手动效
  let handleAnimation = 0;
  // 金币动效
  let goldAnimation = 0;

  function getCardEl(index) {
    var html = '';
    const picsList = optionsData[index].picsList;
    for (let i = 0; i < 4; i++) {
      const cardImg = picsList.isWin ? picsList.letter[0].w : picsList.letter[i].w;
      html += `<li class="card"><img src="${cardImg}" alt="" class="pic-img"></li>`
    }
    return `<ul class="index${index}">` + html + `</ul>`
  }
  //当前显示第几页内容
  function showCurrentPage(currentPage) {
    for (let i = 0; i < optionsData.length; i++) {
      if (i == currentPage - 1) {
        $('.cardPos ul').eq(i).css({
          'display': 'flex'
        })
      } else {
        $('.cardPos ul').eq(i).css({
          'display': 'none'
        })
      }
    }
    $('.content-sentence img').attr('src', optionsData[currentPage - 1].picsList.questionImg);
    $(".page").html(currentPage + "/" + optionsData.length);
  }
  //点击上一个按钮
  let lastLock = false;
  // 点击下一个按钮
  let nextLock = false;
  // 点击great
  let greatLock = false;
  // 点击reception
  let receptionLock = false;
  // 设置滚动动画锁
  function setScrllGifLock(value) {
    lastLock = value;
    nextLock = value;
    greatLock = value;
    receptionLock = value;
  }
  function scrollGif(callback) {
    let i = 0
    let lis = $(".cardTransform ul").find('li')
    $(lis).addClass('gif');
    const audio = $('.runRandom')[0];
    audio.currentTime = 0;
    SDK.playRudio({
      index: audio,
      syncName: $('.runRandom').attr("data-syncaudio")
    })
    $(".cardTransform ul").delay(500).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(300).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(300).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(300).queue(function () {
      $(lis).eq(i).removeClass('gif')
      i++
      $(this).dequeue();
    }).delay(1500).queue(function () {
      $(this).dequeue();
      setScrllGifLock(false);
      SDK.pauseRudio({
        index: audio,
        syncName: $('.runRandom').attr("data-syncaudio")
      })
      callback && callback();
    })
  }
  // 填充内容
  const page = {
    // 填充默认卡片
    addDefaultCard: function () {
      let cardLi = '';
      for (let i = 0; i < 4; i++) {
        if(i%2 == 0) {
          cardLi += `<li class="card"><img src="./image/default-card-1.png"></li>`;
        } else {
          cardLi += `<li class="card"><img src="./image/default-card-2.png"></li>`;
        }
      }
      $(".defaultCardPos").append(`<ul>${cardLi}</ul>`);
    },
    addCard: function () {
      for (let i = 0; i < optionsData.length; i++) {
        $(".cardPos").append(getCardEl(i))
      }
      showCurrentPage(currentPage) //默认显示第一页
    },
    btnInit: function () {
      if (!isSync) {
        return
      }
      // 学生端上课期间左右按钮隐藏
      if(classStatus != '0' && userType == 'stu') {
        $('.nextLastBtnBox').hide();
      }
      if (userType == 'stu') {
        // 如果是学生话筒居中，great按钮隐藏
        $('.great').hide();
        $('.audio-icon').css({
          left: '50%',
          transform: 'translateX(-50%)'
        })
      }
    },
    // 当前页初始为最初状态
    resetPage() {
      $(".cardPos").hide();
      $(".defaultCardPos").show();
      $('.operate-great').hide();
      $('.operate-reception').show();
      $('.try-again').hide();
      $('.audio-icon').hide();
    },
    // 初始化lottle
    async initPage() {
      microphoneAnimation = await lottieAnimations.init(microphoneAnimation, './image/microphone.json', '.audio-icon');
      microphoneAnimation.play();
      handleAnimation = await lottieAnimations.init(handleAnimation, './image/handle.json', '.handle', false);
      goldAnimation = await lottieAnimations.init(goldAnimation, './image/zhongjiang.json', '.gold-coin-animation', false);
      $('.content-sentence').html(`<img src="${optionsData[0].picsList.questionImg}"/>`)
    },
    init: function () {
      this.addDefaultCard();
      this.addCard();
      this.btnInit();
      this.initPage();
      this.resetPage();
    }
  }
  page.init();
  // 点击try-again
  $('.try-again-btn').on('click', function () {
    if (!isSync) {
      $(this).trigger('tryAgainClickSync')
      return
    }
    SDK.bindSyncEvt({
      index: $('.try-again-btn').data('syncactions'),
      eventType: 'click',
      method: 'event',
      syncName: 'tryAgainClickSync',
      recoveryMode: 1
    })
  })
  $('.try-again-btn').on('tryAgainClickSync', function () {
    $('.try-again').fadeOut();
    if(currentPage !== optionsData.length) $('.nextBtn').trigger('click');
    SDK.setEventLock();
  })
  // 老师点击收音
  $(".operate-reception").on('click', function (e) {
    if(receptionLock) return;
    if (!isSync) {
      $(this).trigger('receptionClickSync')
      return
    }
    if(userType == 'tea') {
      SDK.bindSyncEvt({
        index: $('.operate-reception').data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'receptionClickSync',
        recoveryMode: 1
      });
    }
  })
  $(".operate-reception").on('receptionClickSync', function (e) {
    $(this).hide();
    $('.operate-great').fadeIn();
    $('.audio-icon').slideDown(1000);
    const audio = $('.runStart');
    audioPlay(audio);
    SDK.setEventLock();
  })
  // 老师点击great
  $(".great").on('click touchstart', function (e) {
    if(greatLock) return;
    if (!isSync) {
      $(this).trigger('greatClickSync')
      return
    }
    SDK.bindSyncEvt({
      index: $('.great').data('syncactions'),
      eventType: 'click',
      method: 'event',
      syncName: 'greatClickSync',
      recoveryMode: 1
    });
  })
  // 处理音频播放
  function audioPlay(audio) {
    SDK.pauseRudio({
      index: audio[0],
      syncName: audio.attr("data-syncaudio")
    })
    audio[0].currentTime = 0;
    SDK.playRudio({
      index: audio[0],
      syncName: audio.attr("data-syncaudio")
    })
  }
  const winAudio = $('.runWin');
  const failAudio = $('.runFail');
  const handleAudio = $('.runHandle');
  $(".great").on('greatClickSync', function (e) {
    // 隐藏great
    $('.operate-great').fadeOut(1000);
    $('.audio-icon').slideUp(1000);
    // 设置滚动动画锁
    setScrllGifLock(true);
    // 播放handle动画
    handleAnimation.play();
    audioPlay(handleAudio);
    // 播放完把手动画之后再执行下一步
    handleAnimation.removeEventListener('complete', handlePrizeDraw);
    handleAnimation.addEventListener('complete', handlePrizeDraw);
    SDK.setEventLock();
  })
  // 执行抽奖逻辑
  function handlePrizeDraw() {
    handleAnimation.stop();
    scrollGif(()=> {
      // 判断是否中奖执行对应逻辑
      if(optionsData[currentPage - 1].picsList.isWin){
        $('.gold-coin').show();
        goldAnimation.play();
        audioPlay(winAudio);
        if (goldAnimation._completeEventBound) return; // 如果已经绑定，直接返回
        goldAnimation.addEventListener('complete', function() {
          goldAnimation.stop();
          $('.gold-coin').hide();
          // 判断是否是最后一页，不是最后一页进入下一页
          if(currentPage !== optionsData.length) $('.nextBtn').trigger('click');
          goldAnimation._completeEventBound = true; // 设置标志位
        });
      } else {
        audioPlay(failAudio);
        $('.try-again').fadeIn();
      }
    });
    $('.cardPos').show();
    $('.defaultCardPos').hide();
    SDK.setEventLock();
  }

  $(".lastBtn").on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    if (currentPage <= 1 || lastLock) {
      return
    }
    if (!isSync) {
      $(this).trigger('lastClickSync')
      return
    }
    // 老师或者学生预览模式可点击
    if (userType == 'tea' || classStatus == '0') {
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'lastClickSync',
        recoveryMode: 1,
        otherInfor: {
          currentPage: currentPage - 1
        }
      });
    }
  })

  $(".lastBtn").on('lastClickSync', function (e, message) {
    page.resetPage();
    $(".nextBtn").removeClass('btns')
    $(".nextBtn").removeClass("btn-disable")
    $(this).removeClass('btns')
    if (currentPage == 2) {
      $(this).addClass("btn-disable")
    }
    currentPage--
    let obj = '';
    if (!isSync) {
      currentPage = currentPage
    } else {
      obj = message.data[0].value.syncAction.otherInfor;
      if (message == undefined || message.operate == '1') {
        currentPage = obj.currentPage
        if (currentPage > 1) {
          $(".lastBtn").removeClass('btns')
          $(".lastBtn img").removeClass("hide")
        }
      } else {
        if (obj.currentPage > 1) {
          $(".lastBtn").removeClass('btns')
          $(".lastBtn img").removeClass("hide")
        }
        //直接恢复页面
        rebackPage(obj, message.operate)
      }
    }
    if (isSync && message.operate != '1') {
      currentPage = obj.currentPage
    }
    showCurrentPage(currentPage)
    SDK.setEventLock()
  })
  $(".nextBtn").on('click touchstart', function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    var optionsNum = optionsData.length - 1;
    if (currentPage > optionsNum || nextLock) {
      return
    }
    if (!isSync) {
      $(this).trigger('nextClickSync')
      return
    }
    if (userType == 'tea' || classStatus == '0') {
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'nextClickSync',
        recoveryMode: 1,
        otherInfor: {
          currentPage: currentPage + 1
        }
      });
    }
  })

  $(".nextBtn").on('nextClickSync', function (e, message) {
    page.resetPage();
    $(".lastBtn").removeClass('btns')
    $(".lastBtn").removeClass("btn-disable")
    $(this).removeClass('btns')

    if (currentPage == optionsData.length - 1) {
      $(this).addClass("btn-disable")
    }
    currentPage++
    if (!isSync) {
      currentPage = currentPage
    } else {
      let obj = message.data[0].value.syncAction.otherInfor;
      currentPage = obj.currentPage
      if (message == undefined || message.operate == 1) {
      } else {
        //直接恢复页面
        rebackPage(obj, message.operate)
      }
    }
    showCurrentPage(currentPage)
    SDK.setEventLock()
  })

  //恢复页面
  function rebackPage(page, operate) {
    let pageIndex = page.currentPage
    showCurrentPage(pageIndex)
  }
});
