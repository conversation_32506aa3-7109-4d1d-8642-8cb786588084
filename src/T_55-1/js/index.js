"use strict"
import '../../common/js/common_1v1.js'


function countDown(callback) {
  let num = 3;
  $(".shade img").attr("src", `image/num${num}.png`);

  function numAnimate() {
    if (num === 0) {
      // $("#soundone")[0].play();
      SDK.playRudio({
        index: $("#soundone")[0],
        syncName: $("#soundone").attr("data-syncaudio")
      })
    } else {
      // $("#soundtwo")[0].play();
      SDK.playRudio({
        index: $("#soundtwo")[0],
        syncName: $("#soundtwo").attr("data-syncaudio")
      })
    }
    $(".shade img").css({ "height": "2rem", opacity: 1 });
    $(".shade img").animate({ "height": "0rem", opacity: 0 }, 900, function() {
      num--;
      if (num > -1) {
        $(".shade img").attr("src", `image/num${num}.png`);
      }
      if (num === -1) {
        callback && callback();
      } else {
        setTimeout(function() {
          numAnimate();
        }, 100);
      }
    });
  }
  numAnimate();
}

$(function() {
  window.h5Template = {
    hasPractice: '0'
  }
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  if(configData.bg==''){
    $(".container").css({'background-image': 'url(./image/defaultBg.jpg)'})
  }
  let classStatus = "0"; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = h5SyncActions.classConf.h5Course.classStatus;
  }


    //多语言
    const lang1 = configData.lang == 1 ?'學生讀對時，點擊字詞。':'学生读对时，点击字词。' ;
    const lang2 = configData.lang == 1 ?'開始':'开始' ;
    $("#lang1").text(lang1);
    $("#btnstart").text(lang2);

  //业务逻辑
  let source = configData.source;
  let words = source.words;
  let allWord = []; //显示word的队列
  for (let i = 0; i < source.repeats; i++) {
    allWord = allWord.concat(words);
  }

  //填充内容
  const page = {
    showWords: function() {
      let html = '';
      for (let i = 0; i < 9; i++) {
        html += `<div class="word word-${i} hide" data-syncactions="word-${i}"></div>`;
      }
      $(".word-container").append(html);
    },
    drawBg: function(){
      if(source.basepic.big){
        $(".word-1").css({"background":`url(${source.basepic.big}) no-repeat`,"background-size": "auto 100%"});
        $(".word-8").css({"background":`url(${source.basepic.big}) no-repeat`,"background-size": "auto 100%"});
      }
      if(source.basepic.middle){
        $(".word-4").css({"background":`url(${source.basepic.middle}) no-repeat`,"background-size": "auto 100%"});
        $(".word-5").css({"background":`url(${source.basepic.middle}) no-repeat`,"background-size": "auto 100%"});
        $(".word-6").css({"background":`url(${source.basepic.middle}) no-repeat`,"background-size": "auto 100%"});
        $(".word-0").css({"background":`url(${source.basepic.middle}) no-repeat`,"background-size": "auto 100%"});
      }
      if(source.basepic.small){
        $(".word-2").css({"background":`url(${source.basepic.small}) no-repeat`,"background-size": "auto 100%"});
        $(".word-3").css({"background":`url(${source.basepic.small}) no-repeat`,"background-size": "auto 100%"});
        $(".word-7").css({"background":`url(${source.basepic.small}) no-repeat`,"background-size": "auto 100%"}); 
        $(".countimg").attr("src",source.basepic.small);
        $(".totalimg").attr("src",source.basepic.small);
      }

      //这个是为了实现只传图片不写单词的场景，属于临时折中方案，
      //老师把单词做到了图片上，后续如果有扩展需求,需要单独开一个模板做专门的图片随机出的实现
      if(source.respic){
        $(".total-container").css({
          "justify-content":"left",
          "align-items":"left",
          width: "2.4rem",
          height: "1rem",
          left: "2.25rem",
          bottom: "1.13rem",
          "font-size": "0.72rem"
        });
        $(".total-container .connect").css({ 
          "margin-left":"0.1rem",
          "margin-right":"0.1rem",
          "font-size": "0.72rem"
        });

        $(".score-container .connect").css({ 
          "margin-left":"0.4rem",
          "margin-right":"0.4rem",
          "font-size": "0.72rem"
        });
        $(".countimg").attr("src",source.respic).addClass("respic"); 
        $(".totalimg").attr("src",source.respic).addClass("respic");
      }
    },
    showBegin: function() {
      if (!isSync || window.frameElement.getAttribute('user_type') == 'tea') {
        $(".start-page").show();
      }
    },
    init: function() {
      this.showWords(); //渲染卡片
      this.drawBg();  //渲染单词背景图片
      this.showBegin(); //老师端显示开始界面
    }
  }
  page.init();
  
  SDK.recover = function(data){
    if(data.start&&!data.gameOver){  //游戏进行中
      $(".start-page").hide();
      $("#total").text(data.clickWord);
      for(let key in data.showWords){
        if(data.showWords[key]){
          $("[data-syncactions=" + key + "]").text(data.showWords[key]).show();
        }
      }
      if (window.frameElement.getAttribute('user_type') == 'tea') {
        randomWords();
      }
    }else if(data.gameOver===true){
      $("#total").text(data.clickWord);
      $(".start-page .text").hide();
      $("#btnstart").hide();
      $(".shade").hide();
      $("#score").text(allWord.length);
      $(".start-page").fadeIn();
      $(".score-container").show().animate({opacity:1});
    }
 
    SDK.setEventLock();
  }

  //游戏暂停标识
  var gamePauseFlag = false;
  SDK.memberChange = function(message) {
    let stuStatus;
    if (message.role === "stu") {
      stuStatus = message.state;
    } 
    //上课状态中，学生退出了，游戏暂停
    if (classStatus == "1" && stuStatus == "out") { 
      gamePauseFlag = true;
    } else { 
      gamePauseFlag = false;
    }  
  }

  function getRandomPos() {
    let empWords = $('.word:hidden');
    if (empWords && empWords.length > 0) {
      let random = Math.floor(Math.random() * empWords.length);
      return empWords.eq(random);
    } else {
      return null;
    }
  }

  function randomWords() {
    let addWord = function(){
      if(gamePauseFlag){
        //游戏暂停标识触发，说明是学生端断线了
        return;
      }
      if (allWord&&allWord[SDK.syncData.wordIndex]) {
        let dom = getRandomPos();
        if (dom) { 
          let index = dom.data("syncactions");
          SDK.syncData.showWords[index] = allWord[SDK.syncData.wordIndex];
          SDK.syncData.wordIndex++;
          if(!isSync){
            dom.trigger("appendText");
            return;
          }
          SDK.bindSyncEvt({
            index: index,
            eventType: "sendmessage",
            method: 'event',
            syncName: "appendText"
          }); 
        }
      } else {
        //单词都显示完了
        clearInterval(itv);
      }
    }
    addWord();
    let itv = setInterval(addWord, 3000);
  }

  $(".word").on("appendText",function(){
    $(this).text(allWord[SDK.syncData.wordIndex-1]); 
    $(this).fadeIn(function(){
      SDK.setEventLock();
    }); 
  });

  //判断单词是否都点完了
  function gameOver() {
    //说明单词都显示完了并且页面上就剩一个可点击的萝卜
    if (allWord.length === SDK.syncData.wordIndex && $('.word:hidden').length === 8) {
      return true;
    } else {
      return false;
    }
  }

  let wordClick = false;
  $(".word").syncbind('click touchstart', function(dom, next) {
    if (wordClick) {
      return;
    }
    wordClick = true;
    //修改切面信息
    let index = dom.data("syncactions");
    SDK.syncData.showWords[index] = "";
    SDK.syncData.clickWord ++ ;
    let end = gameOver();
    if (end) {
      SDK.syncData.gameOver = end;
    }  

    if (!isSync) {
      next(false);
      return
    }
    if (window.frameElement.getAttribute('user_type') == 'tea') {
      next();
    }
  }, function() {
    $("#dong")[0].currentTime = 0;
    // $("#dong")[0].play();
    SDK.playRudio({
      index: $("#dong")[0],
      syncName: $("#dong").attr("data-syncaudio")
    })
    $(this).text("").hide();
    $("#total").text(SDK.syncData.clickWord); 
    wordClick = false;
    //游戏结束
    if(SDK.syncData.gameOver){
      $(".start-page .text").hide();
      $("#btnstart").hide();
      $(".shade").hide();
      $("#score").text(allWord.length);
      $(".start-page").fadeIn();
      $(".score-container").show().animate({opacity:1});
    }
    SDK.setEventLock();
  });


  let startBtnClick = false;
  $("#btnstart").syncbind("click touchstart", function(dom, next) {
    if (startBtnClick) {
      return;
    }
    startBtnClick = true;
    //存储切面信息
    SDK.syncData.start = true; //记录切面，游戏已经开始
    SDK.syncData.wordIndex = 0; //记录显示到第几个单词了
    SDK.syncData.clickWord = 0; //记录点击了几个单词
    SDK.syncData.showWords = {}; //记录萝卜坑对应的单词

    if (!isSync) {
      next(false);
      return
    }
    if (window.frameElement.getAttribute('user_type') == 'tea') {
      next();
    }
  }, function() {
    $(this).hide();
    $(".shade").css("display","flex");
    $(".start-page .text").hide();
    $(".score-container").hide();
    $(".start-page").show();
    countDown(function() {
      $(".start-page").hide();
      if (!isSync || window.frameElement.getAttribute('user_type') == 'tea') {
        randomWords();
      }
      startBtnClick = false;
      SDK.setEventLock();
    });
  });


})