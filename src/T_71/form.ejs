<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>VDO0001_播放视频</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src="./form/js/acp.umd.min.js"></script>
  <script src='./form/js/vue.min.js'></script>

</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">VDO0001_播放视频</h3>

      <% include ./src/common/template/common_head.ejs %>

        <!-- 编辑视频 -->
        <div class="c-group">
          <div class="c-title">编辑视频</div>
          <div class="c-area upload img-upload editor-upload">
            <div class="field-wrap" v-for="(item,index) in configData.source.options">
              <div class="field-sub-tilte">选项{{index+1}}</div>
              <span class="dele-tg-btn delOption-tg-btn" v-on:click="delOption(item)"
                v-show="configData.source.options.length>1"></span>
              <!-- 选择monitor -->
              <div class="c-well">
                <label><em>*</em>请选择位于AC Monitor中的视频资源</label>
                <div class="upload-content" v-if="!item.originMd5">
                  <div class="btn btn-show upload" v-on:click="selectFn(index)">选择</div>
                </div>
                <div class="retranslating" v-if="item.originMd5">
                  <label>
                    <span class="name">视频名称</span>
                    <span class="video-name">{{item.name}}</span>
                    <div class="btn btn-show upload" v-on:click="selectFn(index)">重新上传</div>
                  </label>
                </div>
              </div>
              <!-- 视频播放按钮位置 -->
              <div class="c-well">
                <label><em>*</em>设置视频播放按钮位置<em>（以按钮的左上角为基准）</em></label>
                <div class="details">
                  <label class="title-label">
                    X:<input type="number" class="tiggerCls distance" v-model="item.videoBtnX"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" class="tiggerCls distance" v-model="item.videoBtnY"
                      oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080< /span>
                  </label>
                </div>
              </div>
              <!-- 视频尺寸与位置 -->
              <div class="c-well">
                <label><em>*</em>请设置视频的尺寸与位置<span>（视频比例为16:9）</span></label>
                <div class="details">
                  <label class="title-label">
                    <span>视频位置</span>&nbsp;&nbsp;
                    X:<input type="number" class="tiggerCls distance" v-model="item.videoX"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" class="tiggerCls distance" v-model="item.videoY"
                      oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080< /span>
                  </label>
                </div>
                <div class="details">
                  <label class="title-label">
                    <span>视频宽高</span>&nbsp;&nbsp;
                    W:<input type="number" class="tiggerCls distance" v-model="item.videoWidth"
                      oninput="if(value>1920)value=1920;if(value<0)value=0">
                    H:<input type="number" class="tiggerCls distance" v-model="item.videoHeight"
                      oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=W<=1920, <br> 0<=H<=1080< /span>
                  </label>
                </div>
              </div>
            </div>
            <div class="c-area c-new-area">
              <label><em>若全屏视频，请设置视频位置X=0 Y=0 视频宽高W=1920 H=1080</em></label>
            </div>
            <button type="button" class="add-tg-btn" v-on:click="addOption({
              videoBtnX: '',
              videoBtnY: '',
              videoX: '',
              videoY: '',
              videoWidth: '',
              videoHeight: '',
              videoMd5: '',
              originMd5: '',
              name: '',
            })">+</button>
          </div>
        </div>

        <button class="send-btn" v-on:click="onSend">提交</button>

    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.png?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>

</html>
