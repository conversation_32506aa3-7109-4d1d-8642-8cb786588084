var configData = {
  bg: '',
  desc: '',
  title: '',
  sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
  tg: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "1111111111111111111"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
  ],
  level: {
    high: [{
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      }
    ],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    options:[
      {
        videoBtnX: '50', //视频按钮的横向位置
        videoBtnY: '50', //视频按钮的纵向位置
        videoX: '1', //视频的横向位置
        videoY: '1', //视频的纵向位置
        videoWidth: '1', //视频的宽度
        videoHeight: '1', //视频的高度
        videoMd5: '1', //视频资源内容
      },
      {
        videoBtnX: '500', //视频按钮的横向位置
        videoBtnY: '200', //视频按钮的纵向位置
        videoX: '2', //视频的横向位置
        videoY: '2', //视频的纵向位置
        videoWidth: '2', //视频的宽度
        videoHeight: '2', //视频的高度
        videoMd5: '2', //视频资源内容
      }
    ]
  }
};
(function(pageNo) { configData.page = pageNo })(0)
