"use strict"
import '../../common/js/common_1v1.js'
// import '../../common/js/commonFunctions.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: '0' // 是否有授权按钮 1：是 0：否
  }

  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/bj.jpg)'
    })
  }

  let options = configData.source.options; //素材地址

  /**
    *初始化音频列表
  */
  optionsList()
  function optionsList() {
    console.log("初始化状态")
    options.forEach(function (item, index) {
      let liEle = '';
      // 触发按钮和图片的位置
      let moveLeft = item.videoBtnX / 100 + 'rem',
        moveTop = item.videoBtnY / 100 + 'rem';

      liEle = $(`<li data-md5=${item.videoMd5} data-syncactions="video-${index}" data-origin-md5=${item.originMd5} data-left=${item.videoX} data-top=${item.videoY} data-width=${item.videoWidth} data-height=${item.videoHeight}>`
        + `<img src='./image/play.png?v=1'>`
        + '</li>')

      // 初始化样式
      liEle.css({
        left: moveLeft,
        top: moveTop
      })

      $(".video-list ul").append(liEle);

      if (!isSync) {
        return;
      }

      // 是否有允许播放视频通知协议
      console.log('SDK.getClassConf().h5Course.isPlaying--------', SDK.getClassConf().h5Course.isPlaying)
      if (SDK.getClassConf().h5Course.isPlaying) {
        $(".video-list").show()
      } else {
        $(".video-list").hide()
      }

    })
  }

  /**
   * 触发按钮列表事件
  */
  $(".video-list ul").on("click touchstart", "li", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("synTriggerClick");
      return;
    }

    console.log('this is new a video')
    let arr = [],
      originArr = [];
    originArr.push($(this).attr('data-origin-md5'))
    arr.push($(this).attr('data-md5'))
    let videoImg = {
      "md5": originArr,
      "left": $(this).attr('data-left'),
      "top": $(this).attr('data-top'),
      "width": $(this).attr('data-width'),
      "height": $(this).attr('data-height')

    }
    playVideoSync(videoImg)
    SDK.setEventLock();

    // SDK.bindSyncEvt({
    //   sendUser: '',
    //   receiveUser: '',
    //   index: $(e.currentTarget).data("syncactions"),
    //   eventType: 'click',
    //   method: 'event',
    //   syncName: 'synTriggerClick',
    //   otherInfor: {

    //   },
    //   recoveryMode: '2'
    // });
  })

  $(".video-list ul").on("synTriggerClick", "li", function (e, message) {
    console.log('this is new old a video')
    let arr = [],
      originArr = [];
    originArr.push($(this).attr('data-origin-md5'))
    arr.push($(this).attr('data-md5'))
    let videoImg = {
      "md5": originArr,
      "left": $(this).attr('data-left'),
      "top": $(this).attr('data-top'),
      "width": $(this).attr('data-width'),
      "height": $(this).attr('data-height')

    }
    playVideoSync(videoImg)
    SDK.setEventLock();

  });

  //启动视频播放
  function playVideoSync(videoImg) {
    if (!isSync) {
      return;
    }
    SDK.bindSyncNormalData({
      'type': 'js2cpp_playVideo',
      'data': {
        "video_status": "open",
        "video_msg": videoImg
      }
    });
  }

})



/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
  var def = $.Deferred(),
    len = list.length;
  $(list).each(function (i, e) {
    var img = new Image();
    img.src = e.image;
    if (img.complete) {
      imgs[i] = img;
      len--;
      if (len == 0) {
        def.resolve();
      }
    } else {
      img.onload = (function (j) {
        return function () {
          imgs[j] = img
          len--;
          if (len == 0) {
            def.resolve();
          }
        };
      })(i);
      img.onerror = function () {
        len--;
      };
    }
  });
  return def.promise();
};
