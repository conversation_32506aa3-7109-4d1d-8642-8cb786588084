@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';



@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}

.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 2.2rem;
	position: absolute;
    right: 0px;
	.desc {
		top: 0.6rem;
	}
	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
}

.video-list{
  ul{
    position: relative;
    li{
      position: absolute;
      img{
        cursor: pointer;
        width: 2.21rem;
        height: 2.21rem;
      }
    }
  }
}

@keyframes handClick {
	0%{
		background-position: 0 0;
  }
	100%{
		background-position: 400% 0;
  }
}
