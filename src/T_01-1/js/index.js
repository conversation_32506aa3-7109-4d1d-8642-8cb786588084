"use strict";
import "../../common/js/common_1v1.js";

$(function () {
  window.h5Template = {
    hasPractice: "1",
  };
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  if (configData.bg == "") {
    $(".container").css({ "background-image": "url(./image/defaultBg.png)" });
  }
  let itemClick = true;
  $(".main_box").prepend(
    '<img src="' + configData.source.img + '" class="catch_doll">'
  );
  $(".rightBox").append(
    `<div class='rightAniBox'><img src='./image/light2.png' class='rightLight'><img src='${configData.source.img}' class='optionInlight'></div>`
  );
  $(".rod").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (itemClick) {
      itemClick = false;
      trackify.pind_send({
        isAuto: false,
      });
      if (!isSync) {
        $(this).trigger("rodClick");
        return;
      }
     
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        funcType: "audio",
        syncName: "rodClick",
      });
    }
  });
  $(".rod").on("rodClick", function () {
    $(".one_mp3").get(0).currentTime = 0;
    // $('.one_mp3').get(0).play();
    SDK.pauseRudio({
      index: $(".one_mp3").get("0"),
      syncName: $(".one_mp3").attr("data-syncaudio"),
    });
    SDK.playRudio({
      index: $(".one_mp3").get("0"),
      syncName: $(".one_mp3").attr("data-syncaudio"),
    });
    $(".hand").removeClass("handAnimate").hide();
    $(".rod").addClass("rodDown");
    $(".billot")
      .addClass("billotAn")
      .on("animationend webkitAnimationEnd", function () {
        setTimeout(function () { //2

          $(".billot").off("animationend webkitAnimationEnd");
          $(".rod").addClass("rodLeft").removeClass("rodDown");
          $(".moveItem")
            .addClass("moveItemAn")
            .on("animationend webkitAnimationEnd", function () {
              $(".button").addClass("buttonAn");
              $(".moveItem").off("animationend webkitAnimationEnd");
              setTimeout(function () { //3
                setTimeout(function () {
                  $(".behind_claw").hide();
                }, 900);
                $(".line")
                  .addClass("lineAn")
                  .on("animationend webkitAnimationEnd", function () {
                    $(".line").off("animationend webkitAnimationEnd");
                    setTimeout(function () { //4
                      $(".left_claw").addClass("catchLe");
                      $(".right_claw").addClass("catchRi");
                      setTimeout(function () {
                        $(".catch_doll").addClass("upDoll");
                        $(".line")
                          .removeClass("lineAn")
                          .addClass("lineAn_2")
                          .on("animationend webkitAnimationEnd", function () {
                            setTimeout(function () { //5
                              $(".moveItem").off(
                                "animationend webkitAnimationEnd"
                              );
                              $(".moveItem1").addClass("moveItemAn_2");
                              $(".catch_doll").addClass("leftDoll");
                              setTimeout(function () {
                                $(".left_claw").removeClass("catchLe");
                                $(".right_claw").removeClass("catchRi");
                                $(".catch_doll").addClass("downDoll");
                                $("#behind_claw").show();
                                
                                setTimeout(() => {
                                  //光环特效
                                  $(".rightBox").show();
                                  $(".blackShadow").show();
                                  setTimeout(function () {
                                    //娃娃机结束
                                    // $(".rightBox").hide();
                                    // $(".blackShadow").hide();
                                  }, 3000);
                                  $('.billot').hide();//隐藏娃娃机滑杆
                                }, 500);
                              }, 500);
                            }, 500);
                          });
                         
                      }, 500);
                      console.log("over");
                      if (isSync) {
                        console.log("over");
                        SDK.bindSyncEvt({
                          method: "ai_event",
                          syncName: "over",
                        });
                      }
                    }, 500);
                  });
                  
              }, 500);
            });
        }, 500);
      });
    // itemClick = true;
    SDK.setEventLock();
  });
  $(".hand").hide();
  // 指令接受
  window.generalTplData = function (message) {
    if (message.actionType == "fingerDome") {
      $(".hand").show();
    }
  }
});
