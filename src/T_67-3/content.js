var configData = {
    bg: "",
    desc: "LCH0003FT_对话听音弹窗FT",
    title: "",
    tg: [{
        title: "1",
        content: "1"
    }],
    level: {
        high: [{
            title: "1",
            content: "1"
        }],
        low: [{
            title: "1",
            content: "1"
        }]
    },
    teleprompter: '简版辅助提示-文案格式',
    tImg: '',
    tImgX: 1340,
    tImgY: 15,
    source: {
        hasPractice: "1",  //是否展示授权按钮 0 无授权功能  1  默认值，普通授权模式
        hasHandAnimation: '1',
        dialogBg: '',
        dialogBtnActiveR: 'assets/images/dialog-btn-activeR.png',
        dialogBtnActiveL: 'assets/images/dialog-btn-activeL.png',
        //标志物
        isNeedLandMark: 2, //是否需要标志物 1不需要 2需要
        landMarkImg: "", //标志物图片
        landMarkShowType: 2, //标志物显示类型  1  原地显示  2.飞入
        landMarkPositionX: '129', //标志物位置X
        landMarkPositionY: '142', //标志物位置Y
        showPopup: "large", // 默认大弹窗 large为大弹窗 small为小弹窗
        dialogs: {
          // 对话框信息列表
          messages: [
            {
              text: "assets/images/dialog-text.png",
              audio: "assets/audios/dialog.mp3"
            }
          ],
          messageLocationX: '550', // 消息内容位置x
          messageLocationY: '100', // 消息内容位置y
          roleLocationX: '100', // 角色位置x
          roleLocationY: '100', // 角色位置y
          roleImg: "", // 角色图片
          // playAfterStauts: "2", // 播放完之后状态, 1显示2隐藏
          prevBtn:'assets/images/dialog-btn-activeR.png',
          nextBtn:'assets/images/dialog-btn-activeL.png',
          scale: 100, //缩放比例  1-500
          autoNext: "2", // 是否自动播放下一条对话框 1不自动播放  2自动播放
          hiddenStatus: "1", // 播放完是否应藏的状态 1不隐藏  2气泡隐藏  3IP和气泡都隐藏
        },
        //游戏资源
        gameList: [


          {
                audio: "assets/audios/hippo.mp3", //河马 题干音频
                audioTime:2000, // 音频时长
                rightImg: "assets/images/right.json", //正确图片
                rightPositionStartX: "489", //正确图片初始位置X
                rightPositionStartY: "352", //正确图片初始位置Y
                rightPositionEndX: "649", //正确图片最终位置X
                rightPositionEndY: "436", //正确图片最终位置Y
                shadeImg: "assets/images/z1.png", //遮罩图片
                shadePositionX: "369", //遮罩图片位置X
                shadePositionY: "394", //遮罩图片位置Y
                landMarkPositionIngameX: "489", //游戏中标志物位置X
                landMarkPositionIngameY: "604", //游戏中标志物位置Y
                modalList: [
                  {
                    title: "./assets/images/modal.png",
                    content: "./assets/images/68076321475941c554000108.mp4",
                    // content: "./assets/images/p2.png",
                    modalAudio:"./assets/audios/dialog.mp3"
                },
                  ],
                modalMode:"2"
            },
            {
                audio: "assets/audios/gorilla.mp3", //猩猩 题干音频
                audioTime:2000, // 音频时长
                rightImg: "assets/images/right.json", //正确图片
                rightPositionStartX: "1569", //正确图片初始位置X
                rightPositionStartY: "184", //正确图片初始位置Y
                rightPositionEndX: "1209", //正确图片最终位置X
                rightPositionEndY: "142", //正确图片最终位置Y
                shadeImg: "assets/images/z2.png", //遮罩图片
                shadePositionX: "1409", //遮罩图片位置X
                shadePositionY: "142", //遮罩图片位置Y
                landMarkPositionIngameX: "1449", //游戏中标志物位置X
                landMarkPositionIngameY: "226", //游戏中标志物位置Y
                modalList: [
                  {
                      title: "./assets/images/modal.png",
                      content: "./assets/images/p2.png",
                      modalAudio:"./assets/audios/dialog.mp3"
                  },
                  {
                    title: "./assets/images/modal.png",
                    content: "./assets/images/68076321475941c554000108.mp4",
                    modalAudio:"./assets/audios/dialog.mp3"
                  },
                  {
                    title: "",
                    content: "",
                    modalAudio:""
                  },
                  // {
                  //   title: "",
                  //   content: "//cdn.51suyang.cn/apollo/public/mp4/ad0ad33ed72e642ab18bb917de5edbb8.mp4",
                  //   modalAudio:""
                  // },
                  ]
            },
            // {
            //     audio: "assets/audios/goat.mp3", //羊 题干音频
            //     audioTime:2000, // 音频时长
            //     rightImg: "//cdn.51suyang.cn/apollo/public/json/data.json", //正确图片
            //     rightPositionStartX: "1289", //正确图片初始位置X
            //     rightPositionStartY: "478", //正确图片初始位置Y
            //     rightPositionEndX: "1529", //正确图片最终位置X
            //     rightPositionEndY: "310", //正确图片最终位置Y
            //     shadeImg: "assets/images/z3.png", //遮罩图片
            //     shadePosition: "424", //遮罩图片位置
            //     shadePositionX: "1049", //遮罩图片位置X
            //     shadePositionY: "520", //遮罩图片位置Y
            //     landMarkPositionIngameX: "1689", //游戏中标志物位置X
            //     landMarkPositionIngameY: "688", //游戏中标志物位置Y
            // },

          //   {
          //     audio: "assets/audios/rabbit.mp3", //兔子 题干音频
          //     audioTime:2000, // 音频时长
          //     rightImg: "//cdn.51suyang.cn/apollo/public/json/data.json", //正确图片
          //     rightPositionStartX: "649", //正确图片初始位置X
          //     rightPositionStartY: "814", //正确图片初始位置Y
          //     rightPositionEndX: "849", //正确图片最终位置X
          //     rightPositionEndY: "730", //正确图片最终位置Y
          //     shadeImg: "assets/images/z4.png", //遮罩图片
          //     shadePositionX: "249", //遮罩图片位置X
          //     shadePositionY: "772", //遮罩图片位置Y
          //     landMarkPositionIngameX: "689", //游戏中标志物位置X
          //     landMarkPositionIngameY: "730", //游戏中标志物位置Y
          // },

        ],
    }
};
(function(pageNo) {
    configData.page = pageNo
})(0)
