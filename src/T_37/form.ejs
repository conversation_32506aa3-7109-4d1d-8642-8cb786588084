<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TSL0001_拓展</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TSL0001_拓展</div>
			
			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>遮罩背景图 <em>( 必填 ) *</em></label>
					<div class="field-wrap">
						<label class="field-label"  for="">上传图片</label><label class="btn btn-show upload" for="card_Bg" v-if="configData.source.maskBg==''?true:false">上传</label><label for="card_Bg" class="btn upload re-upload" v-if="configData.source.maskBg!=''?true:false">重新上传</label><span class='txt-info'>（尺寸：1920X1080，大小：≤100KB)</span>
						<input type="file"  v-bind:key="Date.now()" class="btn-file" id="card_Bg" size="1920*1080" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'maskBg',100)">
					</div>
					<div class="img-preview" v-if="configData.source.maskBg!=''?true:false">
						<img v-bind:src="configData.source.maskBg" alt=""/>
						<div class="img-tools">
							<span class="btn btn-delete" v-on:click="configData.source.maskBg=''">删除</span>
						</div>
					</div>

					<!-- 上传声音 -->

					<div class="field-wrap">
						<label class="field-label"  for="">内容声音<em>（选填）</em></em></label>
						<div>
							<em>上传声音</em>
							<label for="audio-upload" class="btn btn-show upload" v-if="configData.source.audio==''?true:false">上传</label>
							<label  for="audio-upload" class="btn upload re-upload mar" v-if="configData.source.audio!=''?true:false">重新上传</label>
							<em>文件大小≤150KB</em>
						</div>
						<div class="audio-preview" v-show="configData.source.audio!=''?true:false">
							<div class="audio-tools">
								<p v-show="configData.source.audio!=''?true:false">{{configData.source.audio}}</p>
							</div>
							<span class="play-btn" v-on:click="play($event)">
								<audio v-bind:src="configData.source.audio"></audio>
							</span>
						</div>
						<span class="btn btn-audio-dele" v-show="configData.source.audio!=''?true:false" v-on:click="configData.source.audio=''">删除</span>
						<input type="file" id="audio-upload" class="btn-file upload" size="" volume="150" accept=".mp3" v-on:change="audioUpload($event,configData.source,'audio',150)" v-bind:key="Date.now()">
					</div>	
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
