<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>THM0001FT_打地鼠_FT螃蟹版</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="module-title">THM0001FT_打地鼠_FT螃蟹版</div>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<div class="c-well" v-for="(item,index) in configData.source.cardList">
							<div class="well-title">
								<p>图片{{index+1}}<em style="color:red;">*</em></p>
								<span class="dele-tg-btn" v-on:click="deleCards(item)" v-show='configData.source.cardList.length>2?true:false'></span>
							</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label" for="">上传图片</label><label v-bind:for="'img-upload-no'+index" class="btn btn-show upload"
									v-if="item.img==''?true:false">上传</label><label v-bind:for="'img-upload-no'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span
									class='txt-info'>（尺寸：408X300，大小：≤50KB)</span>
								<input type="file" v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload-no'+index" size="408*300" accept=".gif,.jpg,.jpeg,.png"
									v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt="" />
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addCards()" v-if='configData.source.cardList.length<6?true:false'>+</button>
				</div>


				<div class="c-title">计时器</div>
				<div class="c-area upload img-upload">
					<div class="c-well">
						时长：<input type="name" style="height:25px; width:100px" v-model="configData.source.time"> 秒 <em style="margin-left:30px;">数值范围：1~999</em>
					</div>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
