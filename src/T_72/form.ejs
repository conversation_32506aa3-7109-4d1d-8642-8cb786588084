<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>PSO0001AI_个性化总结AI</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="module-title">PSO0001AI_个性化总结AI</div>

			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">设置图片内容</div>
				<div class="c-areAtation img-upload">
					<font>注:图片数量1～4个，显示顺序从左至右，从上之下。</font>
				</div>
				<div class="c-area upload img-upload">
					<div class="c-well" v-for="(item, index) in configData.source.cards">
						<span class="dele-tg-btn" @click="delCards(item)" v-show="configData.source.cards.length>1"></span>
						<div class="field-wrap">
							<label class="field-label"  for="">图片</label>
							<span class='txt-info'><em>文件大小≤50KB,尺寸566*626 * </em></span>
							<input type="file"  v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size="566*626" @change="imageUpload($event,item,'img',50)">
						</div>

						<div class="field-wrap">
							<label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
							<label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img">重新上传</label>
						</div>
						<div class="img-preview" v-if="item.img">
							<img :src="item.img" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(item)">删除</span>
							</div>
						</div>
						<label>句子 <em>字符：≤1000 * </em></label>
						<input type="text" class='c-input-txt' placeholder="请在此输入句子" v-model="item.sentence" maxlength="1000">
					</div>
					<button v-if="configData.source.cards.length<4" type="button" class="add-tg-btn" @click="addCards" >+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>
