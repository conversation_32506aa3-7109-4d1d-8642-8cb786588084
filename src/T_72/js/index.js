"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/drag.js";

$(function () {
  // var script = document.createElement("script"); //创建新的script节点
  // script.setAttribute("type", "text/javascript");
  // script.setAttribute("src", "//cdn.51talk.com/apollo/public/js/vconsole.min.3.3.0.js");
  // document.body.appendChild(script); //添加到body节点的末尾
  // script.addEventListener('load', function() {
  //     var vConsole = new VConsole();
  //     console.log('vconsole已正常启用');
  // }, false);
  window.h5Template = {
    hasPractice: "0",
  };
  let h5SyncActions = parent.window.h5SyncActions;
  const isSync =
    parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
  if (configData.bg == "") {
    $("#container-cnavas").css({
      "background-image": "url(./image/defaultBg.png)",
    });
  }

  //业务逻辑
  let source = configData.source;
  let pageData = source.cards;

  //填充内容
  if (isSync) {
    let appoint_id = h5SyncActions.classConf.course.id;
    let user_id = h5SyncActions.classConf.user.id;
    getdata(user_id, appoint_id);
  }
  function initPage() {
    let html = "";
    for (let i = 0; i < pageData.length; i++) {
      html += `

      <div class="card-container">
        <div class="card" >
          <img class='img1' src="${pageData[i].img}" />
          <img class='img2'  id="card-ride${[i]}" src="image/ride.png" />
          <img class='img3 hide'  id="card${[i]}" src="image/Selected.png" />
        </div>
				</div>`;
    }
    $(".cards").html(html);
    let html2 = "";
    for (let i = 0; i < pageData.length; i++) {
      html2 += `<div class="sentence-container">
        <div class="sentence" >
          <p>- ${pageData[i].sentence}</p>
          <img class='img2' id="sentence-ride${[i]}" src="image/ride.png" />
          <img class='img3 hide' id="sentence${[i]}" src="image/Groupci1.png" />
        </div>
				</div>`;
    }
    $(".sentences").html(html2);
  }

  initPage();

  //从接口获取数据
  function getdata(user_id, appoint_id) {
    let sign = window.CryptoJS.MD5(
      `${user_id}vEKP8L7RAvfM0y1kIjbIW8BQj7Eotl11IEYUoi9ePdLSFftnknTKfqm09QS5ZDo2`
    ).toString();
    let timestamp = new Date().getTime();
    console.log(user_id, appoint_id, sign);
    let url = `https://igateway.51talkjr.com/talkplatform_ai_teacher/api/ai_textbook/wrap_up?appkey=h5_template&timestamp=${timestamp}&user_id=${user_id}&appoint_id=${appoint_id}&sign=${sign}`;

    $.get(url, {}, function (res) {
      let resJson = JSON.parse(res);
      console.log("222", resJson);
      if (resJson.code == "10000") {
        if(resJson.res && resJson.res.length > 0) {
          pageData = resJson.res;
          initPage(); //重新初始化
        }
      } else {
        console.log("接口请求失败");
      }
    });
  }

  //断线重连恢复结果切面信息
  SDK.recover = function (data) {
    let index = data.stole;
    SDK.setEventLock();
  };

  //拍照+奖章
  // $('.done-btn').on("click touchstart", function (e) {
  //   setPhoto();
  // });
  //提交拍照
  function setPhoto() {
    $("#main").appendTo("#contentMask");

    if (configData.bg == "") {
      $(".contentMask").css({
        "background-image": "url(./image/defaultBg.png)",
      });
    } else {
      $(".contentMask").css({
        "background-image": `url(${configData.bg})`,
      });
    }
    $(".winResult").show();
    $(".resultMask").css({
      "z-index": "100",
    });
    $(".resultMask").show().find(".resultWin").show();
    SDK.playRudio({
      index: $(".rightAudio")[0],
      syncName: $(".rightAudio").attr("data-syncaudio"),
    });
  }
  window.generalTplData = function (message) {
    //根据不同协议调不同方法
    if (message.actionType == "word") {
      //单词标记完成
      console.log("word", message);
      $(`#card${message.num}`).show();
      $(`#card-ride${message.num}`).hide();
      // 上报埋点
      trackify.pind_send({
        num: message.num,
        type: "word",
        time: new Date().getTime(),
      });
    } else if (message.actionType == "sentence") {
      //句子标记完成
      console.log("sentence", message);
      $(`#sentence-ride${message.num}`).hide();
      $(`#sentence${message.num}`).show();
      // 上报埋点
      trackify.pind_send({
        num: message.num,
        type: "sentence",
        time: new Date().getTime(),
      });
    } else if (message.actionType == "openSumUp") {
      //拍照打卡
      setPhoto();
    }
    SDK.setEventLock();
  };
});

/**
 * window.generalTplData({actionType: 'word', num:0})
 * window.generalTplData({actionType: 'sentence', num:0})
 * window.generalTplData({actionType: 'openSumUp'})
 */
