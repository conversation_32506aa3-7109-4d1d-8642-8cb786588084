@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import "../../common/template/ribbonWin/style.scss";
@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}
* {
  box-sizing: border-box;
}
.desc-visi {
  visibility: hidden;
}
.hide {
  display: none;
}
.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.container {
  // background: url(../image/defaultBg.jpg) no-repeat;
  background-size: auto 100%;
  position: relative;
  // font-family:"ARLRDBD";
}
#container-cnavas {
  width: 100%;
  height: 100%;
  background-size: auto 100%;
  position: relative;
}
.cards—list {
  .cards-img {
    width: 3.51rem;
    height: 100%;
    background-image: url('../image/words.png');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 3.51rem 100%;
    margin-right: 0.9rem;
    
  }
  width: 16.94rem;
  height: 3.76rem;
  flex-shrink: 0;
  background-image: url('../image/bjStae.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 20;
  margin-top: 2.26rem;
  margin-left: 1.47rem;
  display: flex;
  align-items: center;
  overflow: hidden;

}

.cards {
  
  display: flex;
  height: 100%;
  // flex-wrap: wrap;
  // flex-direction: row;
  align-items: center;
 
  
  .card-container {
    width: 2.78rem;
    // height: 3.04rem;
    // margin: 0 0.22rem;
    margin-right: 0.34rem;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    text-align: center;
    .card {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      // background: url(../image/tmp_cards_bg.png) no-repeat;
      background-size: auto 100%;
      position: relative;

      .img1 {
        width: 100%;
        height: 100%;
      }
      .img2 {
        position: absolute;
        width: 0.7rem;
        height: 0.7rem;
        right: 0;
        bottom: 0.01rem;
      }
      .img3{
        position: absolute;
        width: 0.9rem;
        height: 0.7rem;
        right: 0;
        bottom: 0.01rem;
      }
    }
  }
}
.sentences-list {
  margin-left: 1.47rem;
  margin-top: 0.42rem;
  width: 16.94rem;
  flex-shrink: 0;
  background-image: url('../image/bjStae.png');
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  padding-bottom: 0.3rem;
  padding-top: 0.3rem;
  .sentences-img {
    width: 3.51rem;
    height: 1.65rem;
    display: flex;
    align-items: center;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.sentences {
  z-index: 20;

  width: 70%;
  // height: 4rem;
  // border: 1px solid rebeccapurple;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  .sentence-container {
    width: 100%;
    // height: .8rem;
    margin-left: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    text-align: center;
    margin-top: 0.3rem;
    .sentence {
      font-size: 0.5rem;
      width: 100%;
      height: 100%;
      display: flex;
      // justify-content: space-between;
      align-items: center;
      text-align: center;
      background-size: auto 100%;
      position: relative;
      font-weight: 800;
     

      .img3 {
        width: 0.66rem;
        height: 0.51rem;
        margin-left: auto;
        margin-right: -0.1rem;
      }
      .img2 {
        width: 0.5rem;
        height: 0.5rem;
        margin-left: auto;
      }
    }

  }
  // 选中元素sentence-container第一个元素
  .sentence-container:first-child {
    margin-top: 0;
  }
  // .sentence-container
}

.doneTip {
  width: 7.91rem;
  height: 1rem;
  border-radius: 0.2rem 0.2rem 0 0;
  position: absolute;
  margin-left: -3.5rem;
  left: 50%;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: none;
  font-size: 0.3rem;
  p {
    height: 100%;
    width: 5.14rem;
    line-height: 1rem;
    text-align: center;
    padding-left: 0.53rem;
  }
  .btn {
    position: absolute;
    top: 0.2rem;
    height: 0.23rem;
    padding: 0.17rem 0.26rem;
    color: #fff;
    text-align: center;

    line-height: 0.23rem;
    border-radius: 0.3rem;
    cursor: pointer;
  }
  .hint-btn {
    background: #f1a91e;
    left: 5.6rem;
  }
}
.noTip {
  position: absolute;
  right: 1rem;
  bottom: 0rem;
  img {
    width: 2.11rem;
    height: 0.6rem;
  }
  .done-btn {
    cursor: pointer;
  }
}

// 拍照
.resultMask {
  .contentMask {
    // transform: rotate(-12deg);
    // border: 1px solid red;
    z-index: 2000;
    position: absolute;
    left: 3rem;
    top: 1.2rem;
    width: 13.2rem;
    height: 8.03rem;
    background: #fff;
    border: 0.7rem solid #fff;
    // transform: translate(-50%,-50%);
    // overflow: hidden;
    animation: ptoStyle 0.5s 1 forwards;
    -webkit-animation: ptoStyle 0.5s 1 forwards;
    // box-shadow: 0 0 .4rem rgba(255, 255, 255, 0.8);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // img {
    //   width: 12.2rem;
    //   height: 7.03rem;
    // }
    .sentences-list {
      margin-left: 0.47rem;
      margin-top: 0.12rem !important;
      width: 10.94rem;
      height: 2.5rem;
      flex-shrink: 0;
      background-image: url('../image/bjStae.png');
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding-bottom: 0rem !important;
      padding-top: 0rem !important;
      .sentences-img {
        width: 3.51rem;
        height: 1.65rem;
        display: flex;
        align-items: center;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    
    .main {
      // position: fixed;
      .cards—list {
        .cards-img {
          width: 2rem;
          height: 100%;
          background-image: url('../image/words.png');
          background-position: center;
          background-repeat: no-repeat;
          background-size: 2rem 100%;
          margin-right: 0.3rem;
          
        }
        width: 10.94rem;
        height: 2.76rem;
        flex-shrink: 0;
        background-image: url('../image/bjStae.png');
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        z-index: 20;
        margin-top: 1.16rem;
        margin-left: 0.47rem;
        display: flex;
        align-items: center;
        overflow: hidden;
      
      }
      .sentences {
        scale: 0.614;
        width: 100%;
        left: 1.5rem;
        bottom: -0.2rem;
      }
      .cards {
  
        display: flex;
        height: 100%;
        // flex-wrap: wrap;
        // flex-direction: row;
        align-items: center;
       
        
        .card-container {
          width: 1.78rem;
          // height: 3.04rem;
          // margin: 0 0.22rem;
          margin-right: 0.34rem;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          text-align: center;
          .card {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            // background: url(../image/tmp_cards_bg.png) no-repeat;
            background-size: auto 100%;
            position: relative;
      
            .img1 {
              width: 100%;
              height: 100%;
            }
            .img2 {
              position: absolute;
              width: 0.7rem;
              height: 0.7rem;
              right: 0;
              bottom: 0.01rem;
            }
            .img3{
              position: absolute;
              width: 0.9rem;
              height: 0.7rem;
              right: 0;
              bottom: 0.01rem;
            }
          }
        }
      }
      .sentences {
        z-index: 20;
      
        width: 70%;
        // height: 4rem;
        // border: 1px solid rebeccapurple;
        display: flex;
        flex-wrap: wrap;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .sentence-container {
          width: 100%;
          // height: .8rem;
          margin-left: 0.5rem;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          text-align: center;
          margin-top: 0.3rem;
          .sentence {
            font-size: 0.3rem;
            width: 100%;
            height: 100%;
            display: flex;
            // justify-content: space-between;
            align-items: center;
            text-align: center;
            background-size: auto 100%;
            position: relative;
            font-weight: 800;
           
      
            .img3 {
              width: 0.66rem;
              height: 0.51rem;
              margin-left: auto;
              margin-right: -0.1rem;
            }
            .img2 {
              width: 0.5rem;
              height: 0.5rem;
              margin-left: auto;
            }
          }
      
        }
      }
    }
  }
}

.winResult {
  position: absolute;
  width: 4.12rem;
  height: 2.72rem;
  bottom: 0.7rem;
  left: 0.5rem;
  z-index: 3015;
  img {
    width: 100%;
  }
}

@keyframes ptoStyle {
  0% {
    transform: scale(1.2, 1.2) rotate(0deg);
    // transform-origin:left top;
  }
  100% {
    transform: scale(1.1, 1.1) rotate(-3deg);
    // transform-origin:left top;
  }
}

@-webkit-keyframes ptoStyle {
  0% {
    transform: scale(1.2, 1.2) rotate(0deg);
    // transform-origin:left top;
  }
  100% {
    transform: scale(1.1, 1.1) rotate(-3deg);
    // transform-origin:left top;
  }
}
