@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../record//css/record.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.hide{
    display: none;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.container{
    background-size: auto 100%;
	position: relative;
} 

.point{
	position: absolute;
	width: 0.05rem;
	height: 0.05rem;
	background: red;
}

.sec-middle{
	width: 100%;
	height: 6.62rem;
}
audio{
	width:0;
	height: 0;
	opacity: 0;
	position:absolute;
}
.lookTitle{
	width:100%;
	height:1.5rem;
	font-size:0.8rem;
	text-align:center;
	font-weight:700;
	color:#333333;
	span{
		display:inline-block;
		margin-top:0.45rem;
	}
}
.submit{
	position: absolute;
	width: 2.62rem;
	height: 1.22rem;
	bottom: -0.59rem;
	left: 50%;
	margin-left: -1.31rem;
	z-index: 100;
	background:url('../image/submit.png') no-repeat center center;
	background-size:cover;
 }
 .tea .submit{
 	opacity: 0
 }
.main{
	width: 19.2rem;
	height: 10.8rem;
	position: absolute;
	top: 0;
	left: 0;
	padding-top: 1.5rem;
	box-sizing: border-box;
	.title{
		width: 100%;
		height: 1rem;
		line-height: 1rem;
		text-align: center;
		font-size: 0.36rem;
	}
	.btn{
		width: 1.36rem;
		height: 1.36rem;
		position: absolute;
		top: 6.56rem;
		z-index: 80;
	}
	.left{
		left: 0.92rem;
		background:url('../image/left.png') no-repeat;
		background-size: contain;
		opacity: 0.4;
	}
	.right{
		right: 0.92rem;
		background:url('../image/right.png') no-repeat;
		background-size: contain;		
	}
	.page{
		position: absolute;
		top: 0rem;
		left: 0;
		width: 100%;
		height:100%;
		.showAns{
			width: 14.01rem;
			height: 1.92rem;
			line-height: 1.92rem;
			background:url('../image/ansBg.png') no-repeat;
			background-size: contain; 
			position: absolute;
			top: 2.72rem;
			left: 50%;
			margin-left: -7rem;
			text-align: center;
			color: #03a011;
		}
		.wordsArea{
			width: 19.2rem;
			height: 4.3rem;
		}
		.answer{
		    font-size: 0;
		    width: 14rem;
		    height: 3.76rem;
			margin: 0 auto;
		    position: absolute;
		    bottom:1.6rem;
		    left: 50%;
		   	transform: translate(-50%);
		   	border: 0.06rem dashed #fff;
		   	border-radius: 0.3rem;
		   	text-align: center;
		   	box-sizing: border-box;
		   	background: rgba(51, 51, 51, 0.15);
			.audioList{
    			position: absolute;
    			width: 1.45rem;
    			height: 1.34rem;
    			background: url('../image/btn-audio-bg.png') no-repeat;
    			background-size: 100% 100%;
    			z-index: 10;
    			left: 50%;
    			transform: translateX(-50%);
    			top: -0.7rem;
    			img{
    				position: absolute;
    				top: 0.3rem;
    				left: 0.3rem;
    				width: 0.83rem;
    				height: 0.8rem;
    			}
    		}
			.list{
				width: 2.9rem;
				height: 2.52rem;
				background: blue;
				display: inline-block;
				position: relative;
				background: url('../image/home.png') no-repeat;
				background-size: contain;
				margin-top: 0.45rem;
			}
		}
		.card{
			position: absolute;
			width: 2.6rem;
			height: 2.26rem;
			line-height:2.6rem;
			text-align: center;
			background: red;
			top: 2.48rem;
			font-size: 0.9rem;
				.bling{
					position: absolute;
					width: 100%;
					height: 100%;
					background:url('../image/bling.png') no-repeat; ;
					background-size:contain;
					opacity: 0; 
					top: 0;
					left: 0;
				}
		}
		.index0{
			left: 0;
			background: url('../image/home1.png') no-repeat;
			background-size: contain;
		}
		.index1{
			left: 2.92rem;
			background: url('../image/home2.png') no-repeat;
			background-size: contain;		
		}
		.index2{
			left: 5.84rem;			
			background: url('../image/home3.png') no-repeat;
			background-size: contain;						
		}
		.index3{
			left: 8.76rem;						
			background: url('../image/home4.png') no-repeat;
			background-size: contain;
		}
		.index4{
			left: 11.68rem;			
			background: url('../image/home5.png') no-repeat;
			background-size: contain;
		}
		.index5{
			left:14.6rem;				
			background: url('../image/home6.png') no-repeat;
			background-size: contain;
		}		
	}

}
@keyframes shake{
	0% {
        transform: translateX(2px) ;
        
    }
    20% {
        transform: translateX(-2px);

    }
    40% {
        transform: translateX(2px);
    }
    60% {
        transform: translateX(-2px);
    }
    80% {
        transform: translateX(2px);
    }
    100% {
        transform: translateX(0px);
    }
}
@-webkit-keyframes shake{
	0% {
        transform: translateX(2px) ;
        
    }
    20% {
        transform: translateX(-2px);

    }
    40% {
        transform: translateX(2px);
    }
    60% {
        transform: translateX(-2px);
    }
    80% {
        transform: translateX(2px);
    }
    100% {
        transform: translateX(0px);
    }
}
@keyframes shakeUp{
	0% {
    	transform: rotate(10deg);
    }
    20% {
        transform: rotate(-10deg);
    }
    40% {
        transform: rotate(10deg);
    }
    60% {
        transform: rotate(-10deg);
    }
    70%{
    	transform: rotate(10deg);
    }
    80% {
        transform: rotate(10deg) translateY(8px);
    }
    100% {
        transform:rotate(10deg) translateY(16px);
    }
}
@-webkit-keyframes shakeUp{
	0% {
    	transform: rotate(10deg);
    }
    20% {
        transform: rotate(-10deg);
    }
    40% {
        transform: rotate(10deg);
    }
    60% {
        transform: rotate(-10deg);
    }
    70%{
    	transform: rotate(10deg);
    }
    80% {
        transform: rotate(10deg) translateY(8px);
    }
    100% {
        transform:rotate(10deg) translateY(16px);
    }
}

@keyframes bling{
	0% {
    	transform:scale(1,1);
    	opacity: 0;
    }
    70% {
    	transform:scale(1.2,1.2);
    	opacity: 1;
    }
    80% {
    	transform:scale(1.4,1.4);
    	opacity: 0.75;
    }
    90% {
    	transform:scale(1.7,1.7);
    	opacity: 0.35;
	}
	99% {
    	transform:scale(1.7,1.7);
    	opacity: 0;
    }
    100% {
    	transform:scale(1,1);
		opacity: 0;
    }
}
@-webkit-keyframes bling{
	0% {
    	transform:scale(1,1);
    	opacity: 0;
    }
    25% {
    	transform:scale(1.2,1.2);
    	opacity: 1;
    }
    10% {
    	transform:scale(1.4,1.4);
    	opacity: 0.75;
    }
    30% {
    	transform:scale(1.7,1.7);
    	opacity: 0.35;
    }
    90% {
    	transform:scale(1.7,1.7);
    	opacity: 0.35;
	}
	99% {
    	transform:scale(1.7,1.7);
    	opacity: 0;
    }
    100% {
    	transform:scale(1,1);
		opacity: 0;
    }
}


