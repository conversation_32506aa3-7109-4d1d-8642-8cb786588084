"use strict"
import '../../common/js/common_1v1.js'
import {feedbackAnimation} from "../../common/template/feedbackAnimation";
import {CLASS_STATUS, INTERACTION_TYPE, TEACHER_TYPE, USER_TYPE, USERACTION_TYPE} from "../../common/js/constants";
import "../../common/js/teleprompter.js"

$(function () {
  SDK.reportTrackData({
    action: 'PG_FT_INTERACTION_LIST',
    data: {
      roundcount: configData.source.roundArr.length,
    },
    teaData: {
      teacher_type:TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type:INTERACTION_TYPE.CLICK,
      useraction_type:USERACTION_TYPE.SPEAK
    },
  })
    window.h5Template = {
      hasPractice: '0'
    }
    let h5SyncActions = parent.window.h5SyncActions;
    const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
    if (configData.bg == '') {
      $(".container").css({'background-image': 'url(./image/defaultBg.png)'})
    }
    // 角色动画图
    let chaseAnimations //追击
    let beChaseAnimations //被追击
    let arrowAnimations //箭头
    let coloredRibbonAnimations //彩带动画
    let source = configData.source;
    let pics = source.roundArr;
    let endLeft = 13.5 //终点的坐标
    let stepCount = 5 //移动步数
    let stepWidth = (endLeft - 0.4) / 5 //步长(每次移动的距离，0.4是追击元素左侧距离)
    let rolestep = 0; //角色前进步数
    let rewardCount = 0; //获得奖励次数 可以判断第几轮
    //填充内容
    let retreatAnimationId  //后退元素动画id
    let currentPic = 0 //图片页码
    const page = {
      showPics: async function () {
        let html = '';
        for (let i = 0; i < pics.length; i++) {
          let cls = "hide";
          if (i === 0) {
            cls = "";
          }
          html += `<img id="pic-${i}" class="${cls}" src="${pics[i].contentImg}" />`;
        }
        $(".pic").append(html);
        // 被追击元素Y轴位置
        let beChaseY = source.beChaseY;
        if(beChaseY){
          $(".beChaseJson").css({
            top: beChaseY / 100 + "rem"
          });
        }
        // 初始化动画
        chaseAnimations = await lottieAnimations.init(chaseAnimations, source.chaseJson, ".chaseJson");
        beChaseAnimations = await lottieAnimations.init(beChaseAnimations, source.beChaseJson, ".beChaseJson");
        arrowAnimations = await lottieAnimations.init(arrowAnimations, './image/arrows.json', ".arrow");
        // 下面是蒙版上的动画 动画初始化完成后再克隆元素
        await new Promise(resolve => setTimeout(resolve, 100)); // 添加短暂延迟确保初始化完成
        // 克隆chaseJson 和 beChaseJson 元素
        let $chaseJson = $(".chaseJson").clone();
        let $beChaseJson = $(".beChaseJson").clone();
        $chaseJson.css({
          left: "3rem",
          top: "3rem",
          display: "block",
          scale: "1.2"
        });
        $beChaseJson.css({
           left: "12.5rem",
          top: "3rem",
          display: "block",
          scale: "1.2"
        });
        $(".modal").append($chaseJson);
        $(".modal").append($beChaseJson);
        let arrowmaskAnimations = await lottieAnimations.init(arrowmaskAnimations, './image/arrows.json', ".arrowMask");
        lottieAnimations.play(arrowmaskAnimations);
      },
      initPage: function () {
        $("#picpage").html("1/" + pics.length);
        $("#btnstart").show();
        if (!isSync) {
          $(".lrbtn-bg").css("display", "flex");
        } else {
          const classStatus = SDK.getClassConf().h5Course.classStatus;
          if (classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU) {
            $(".lrbtn-bg").css("display", "flex");
          }
          if (window.frameElement.getAttribute('user_type') == USER_TYPE.TEA) {
            $(".lrbtn-bg").css("display", "flex");
          }
        }
        $('#bgm').attr('src', source.bgm);
      },
      initRetreat() {
        // 遍历 retreatArr 数组
        $.each(source.retreatArr, function (index, item) {
          // 创建 <img> 元素（暂不设置 src，避免因加载过快错过事件）
          var $img = $("<img>", {
            alt: "",
            class: "retreat",
            id: "retreat" + index
          });

          // 监听图片加载完成事件
          $img.on("load", function () {
            // 获取图片原始宽高
            let naturalWidth = this.naturalWidth;  // 原始宽度（像素）
            let naturalHeight = this.naturalHeight; // 原始高度（像素）

            // 将宽高转换为 1/100 rem（例如：100px → 1rem）
            $img.css({
              width: naturalWidth / 100 + "rem",
              height: naturalHeight / 100 + "rem"
            });
          });

          // 设置图片路径（必须在绑定 load 事件后设置 src，确保事件生效）
          $img.attr("src", item.img);

          // 设置绝对定位坐标（单位默认像素 px）
          $img.css({
            position: "absolute",
            left: item.x ? item.x / 100 + "rem" : "0rem",
            top: item.y ? item.y / 100 + "rem" : "0rem",
            display: "block"
          });
          $(".container").append($img);
        });
      },
      initRewardIcon() {
        $.each(source.roundArr, function (index, item) {
          let $img = $("<img>", {
            alt: "",
            class: "rewardIcon" + index
          });
          // 监听图片加载完成事件
          $img.on("load", function () {
            // 获取图片原始宽高
            let naturalWidth = this.naturalWidth;  // 原始宽度（像素）
            let naturalHeight = this.naturalHeight; // 原始高度（像素）
            // 将宽高转换为 1/100 rem（例如：100px → 1rem）
            $img.css({
              width: naturalWidth / 100 + "rem",
              height: naturalHeight / 100 + "rem"
            });
          });
          // 设置图片路径（必须在绑定 load 事件后设置 src，确保事件生效）
          $img.attr("src", item.rewardIcon);
          $(".reward-icon-box").append($img);
        });
      },
      async initMicrophone() {
        let microphoneAnimation = await lottieAnimations.init(microphoneAnimation, './image/microphone.json', ".microphone");
        lottieAnimations.play(microphoneAnimation);
      },
      //重置游戏界面
      resetGame: function () {
        //后退元素 追击元素 被追击元素 位置重置
        source.retreatArr.forEach((item, i) => {
          $('#retreat' + i).css("left", item.x ? item.x / 100 + "rem" : "0rem");
        })
        $(".chaseJson").css({"left": "0.4rem"});
        $(".beChaseJson").css({"left": "7.68rem"});
        $("#btngogo").hide();
        //移除麦克风位置样式
        $(".microphone").attr("class", "microphone");
        //重置必要的切面信息
        rolestep = 0; //角色前进步数
      },
      //设置左右图片切换按钮状态样式
      setLRBtn: function () {
        if (currentPic === pics.length - 1) {
          $(".rightBtn").addClass("disable-right");
        } else {
          $(".rightBtn").removeClass("disable-right");
        }
        if (currentPic === 0) {
          $(".leftBtn").addClass("disable-left");
        } else {
          $(".leftBtn").removeClass("disable-left");
        }
      },
      //设置麦克风位置
      setMicLocation: function () {
        $(".microphone").addClass(
          `microphone${source.roundArr[rewardCount].micLocation}`
        )
      },
      //玩游戏
      playGame: function () {
        let that = this;
        gogoBtn = false;
        $(".arrow").show();
        $(".chaseJson").show();
        $(".beChaseJson").show();
        lottieAnimations.play(chaseAnimations);
        lottieAnimations.play(beChaseAnimations);
        $(".arrow").show();
        lottieAnimations.play(arrowAnimations);

        if (!isSync) {
          $("#btngogo").show();
        } else {
          const classStatus = SDK.getClassConf().h5Course.classStatus;
          if (classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU) {
            $("#btngogo").show();
          }
          if (window.frameElement.getAttribute('user_type') == USER_TYPE.TEA) {
            $("#btngogo").show();
          }
        }
        page.setMicLocation()
        $('.microphone').show()
        retreatAnimation();
        $(".beChaseJson").animate({
          left: `${endLeft}rem` // 目标位置（支持rem单位）
        }, 2000);
        SDK.playRudio({
          index: $("#bgm")[0],
          syncName: $("#bgm").attr("data-syncaudio")
        })
      },
      gameOver: async function () {
        //暂停背景音
        SDK.pauseRudio({
          index: $('#bgm').get('0'),
          syncName: $('#bgm').attr("data-syncaudio")
        })
        // 停止后退元素和追击被追击元素动画
        lottieAnimations.stop(chaseAnimations);
        lottieAnimations.stop(beChaseAnimations);
        cancelAnimationFrame(retreatAnimationId)
        // 隐藏麦克风
        $('.microphone').hide()
        //彩带动画
        coloredRibbonAnimation()
        //奖励图标替换
        rewardIconChange()
        //追击成功音效
        await $('#succeed').playAudioSync()
        rewardCount++
        //重置游戏
        page.resetGame()
        //判断轮次等于奖励次数 整个游戏结束
        if (rewardCount === source.roundArr.length) {
          await feedbackAnimation('feedKey1')
          SDK.reportTrackData({
            action:'CK_FT_INTERACTION_COMPLETE', //事件名称
            data:{}, // 老师和学生  都需要上报的数据
            teaData:{result:'success'},  // 只有老师端会上报的数据
            stuData:{},  // 只有学生端会上报的数据
          },USER_TYPE.TEA)
        } else {
          //图片切换下一张
          setShowPic(rewardCount)
          page.playGame()
        }
        SDK.setEventLock();
      },
      init: function () {
        this.showPics(); //渲染卡片
        this.initPage(); //初始化页面其他数据
        this.initRetreat() //初始化倒退元素
        this.initRewardIcon() //初始化奖励图标
        this.initMicrophone() //初始化奖励图标
        this.setMicLocation() //初始化麦克风位置
      }
    }
    page.init();

    let leftBtnClick = false;
    $(".leftBtn").syncbind("click touchstart", function (dom, next) {
      if (leftBtnClick) {
        return;
      }
      if (dom.is(".disable-left")) {
        return;
      }
      leftBtnClick = true;
      if (!isSync) {
        next(false);
      } else {
        next()
      }
    }, function () {
      currentPic--;
      page.setLRBtn(); //设置左右翻页按钮
      $(`#pic-${currentPic + 1}`).hide();
      $(`#pic-${currentPic}`).show();
      $("#picpage").html((currentPic + 1) + "/" + pics.length);
      leftBtnClick = false;
      SDK.setEventLock();
    });

    let rightBtnClick = false;
    $(".rightBtn").syncbind("click touchstart", function (dom, next) {
      if (rightBtnClick) {
        return;
      }
      if (dom.is(".disable-right")) {
        return;
      }
      rightBtnClick = true;
      if (!isSync) {
        next(false);
      } else {
        next()
      }
    }, function () {
      currentPic++;
      page.setLRBtn(); //设置左右翻页按钮
      $(`#pic-${currentPic - 1}`).hide();
      $(`#pic-${currentPic}`).show();
      $("#picpage").html((currentPic + 1) + "/" + pics.length);
      rightBtnClick = false;
      SDK.setEventLock();
    });

    let gameStartBtn = false;
    $("#btnstart").syncbind("click touchstart", function (dom, next) {
      if (gameStartBtn) {
        return;
      }
      let classStatus
      if (isSync) {
        classStatus = SDK.getClassConf().h5Course.classStatus;
      }
      // 上课中 角色是学生 直接返回
      if (classStatus == CLASS_STATUS.IN_CLASS && window.frameElement.getAttribute('user_type') == USER_TYPE.STU) {
        return;
      }
      gameStartBtn = true;
      //游戏开始，禁止切换卡片
      $(".leftBtn").hide();
      $(".rightBtn").hide();
      setShowPic(0)
      SDK.reportTrackData({
        action: 'CK_FT_INTERACTION_STARTBUTTON',
        data:{}, // 老师和学生  都需要上报的数据
        teaData:{},  // 只有老师端会上报的数据
        stuData:{},  // 只有学生端会上报的数据
      },USER_TYPE.TEA)
      if (!isSync) {
        next(false);
        return
      } else {
        const classStatus = SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU) {
          next();
        }
        if (window.frameElement.getAttribute('user_type') == USER_TYPE.TEA) {
          next();
        }
      }
    }, function () {
      //销毁蒙版
      $(".modal").remove();
      threeTwoOne(function () {
        page.playGame();
      });
      gameStartBtn = false;
      SDK.setEventLock();
    });

    let gogoBtn = false;
    $("#btngogo").syncbind("click touchstart", function (dom, next) {
      if (gogoBtn) {
        return;
      }
      SDK.reportTrackData({
        action:'CK_FT_INTERACTION_SPOKEBUTTON', //事件名称
        data:{}, // 老师和学生  都需要上报的数据
        teaData:{
          roundid: rewardCount+1
        },  // 只有老师端会上报的数据
        stuData:{},  // 只有学生端会上报的数据
      },USER_TYPE.TEA)
      if (!isSync) {
        next(false);
        return
      } else {
        const classStatus = SDK.getClassConf().h5Course.classStatus;
        if (classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU) {
          next();
        }
        if (window.frameElement.getAttribute('user_type') == USER_TYPE.TEA) {
          next();
        }
      }
    }, function () {
      gogoBtn = true;
      //隐藏箭头
      lottieAnimations.stop(arrowAnimations);
      $(".arrow").hide();
      //存储切面信息，角色前进步数
      rolestep++;
      let left = $(".chaseJson").position().left / window.base + stepWidth;
      $(".chaseJson").animate({"left": left + 'rem'}, 300, "linear", function () {
        gogoBtn = false;
        if (rolestep === stepCount) {
          gogoBtn = true;
          page.gameOver();
        }
        SDK.setEventLock();
      });
    });


    function threeTwoOne(callback) {
      let q = 1;
      $(".timeChangeBox").show().find(".numberList");
      SDK.playRudio({
        index: $(".timeLowAudio_" + q).get(0),
        syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
      });
      let audioPlay = setInterval(function () {
        q++;
        if (q > 4) {
          callback()
          clearInterval(audioPlay);
          $(".timeChangeBox").hide();
        } else {
          SDK.playRudio({
            index: $(".timeLowAudio_" + q).get(0),
            syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
          });
          $(".numberList").css({
            "background-position-x": -(1.5 * (q - 1)) + "rem",
          });
        }
      }, 1000);
    }


    function retreatAnimation() {
      // container容器 在这个元素中播放动画
      const $container = $('.container');

      // 获取容器宽度（以rem为单位）
      const containerWidth = $container.width() / window.base;

      // 定义1像素对应的rem值，用于消除轮播间的缝隙
      const onePixelInRem = 1 / window.base;

      // 存储每个后退元素的轮播容器和速度信息
      const retreatElements = [];

      // 为每个后退元素创建轮播容器
      source.retreatArr.forEach((item, index) => {
        const $element = $('#retreat' + index);

        // 从DOM中移除原始元素，后续会添加到新创建的轮播容器中
        $element.detach();

        // 获取后退速度，先取item的值，再取全局值，最后使用默认值1
        // 速度系数降低，使移动更平滑
        const speed = (item.retreatSpeed || 2) * 0.02;

        // 创建两个相同的轮播容器（一个在视图内，一个在右侧待命）
        for (let i = 0; i < 2; i++) {
          // 创建与container同样大小的div容器
          const $carouselDiv = $('<div>', {
            class: `retreat-carousel retreat-${index}-${i}`,
            css: {
              position: 'absolute',
              left: i === 0 ? '0rem' : (containerWidth - onePixelInRem) + 'rem',  // 第二个容器减去1像素宽度避免缝隙
              top: '0',
              width: containerWidth + 'rem',
              height: '100%'
            }
          });

          // 复制元素并调整位置
          const $clonedElement = $element.clone();
          // 保持元素在容器中的相对位置
          $clonedElement.css({
            left: (item.x ? item.x / 100 : 0) + 'rem',
            top: (item.y ? item.y / 100 : 0) + 'rem'
          });

          // 将复制的元素添加到轮播容器中
          $carouselDiv.append($clonedElement);

          // 将轮播容器添加到主容器中
          $container.append($carouselDiv);

          // 记录元素信息
          retreatElements.push({
            $carousel: $carouselDiv,
            left: i === 0 ? 0 : (containerWidth - onePixelInRem),
            speed: speed,
            index: index,
            carouselId: i
          });
        }
      });

      // 记录上一帧的时间戳
      let lastTimestamp = null;

      // 动画函数
      function animate(timestamp) {
        // 请求下一帧动画
        retreatAnimationId = requestAnimationFrame(animate);

        // 计算时间增量
        if (!lastTimestamp) {
          lastTimestamp = timestamp;
          return;
        }

        const deltaTime = timestamp - lastTimestamp;
        lastTimestamp = timestamp;

        // 确保时间增量在合理范围内，避免页面不活跃时的大跳变
        if (deltaTime > 100) {
          return;
        }

        // 按照元素索引分组，确保同一元素的两个轮播容器一起处理
        const elementGroups = {};
        retreatElements.forEach(element => {
          if (!elementGroups[element.index]) {
            elementGroups[element.index] = [];
          }
          elementGroups[element.index].push(element);
        });

        // 分组处理每个元素的轮播容器
        Object.values(elementGroups).forEach(group => {
          // 获取同一元素的两个轮播容器
          const container1 = group[0];
          const container2 = group[1];

          // 计算新位置
          container1.left -= container1.speed * (deltaTime / 16);
          container2.left -= container2.speed * (deltaTime / 16);

          // 更新DOM位置
          container1.$carousel.css('left', container1.left + 'rem');
          container2.$carousel.css('left', container2.left + 'rem');

          // 无缝衔接处理：当一个容器完全离开视口时，将其重置到另一个容器的右侧
          if (container1.left + containerWidth <= 0) {
            container1.left = container2.left + containerWidth - onePixelInRem; // 减去1像素避免缝隙
            container1.$carousel.css('left', container1.left + 'rem');
          }

          if (container2.left + containerWidth <= 0) {
            container2.left = container1.left + containerWidth - onePixelInRem; // 减去1像素避免缝隙
            container2.$carousel.css('left', container2.left + 'rem');
          }
        });
      }

      // 启动动画
      retreatAnimationId = requestAnimationFrame(animate);
    }

    function setShowPic(index) {
      source.roundArr.forEach((item, i) => {
        if (index !== i) {
          $(`#pic-${i}`).hide();
        } else {
          $(`#pic-${i}`).show();
        }
      })
      $("#picpage").html((index + 1) + "/" + pics.length);
    }

    async function coloredRibbonAnimation() {
      if (!coloredRibbonAnimations) {
        coloredRibbonAnimations = await lottieAnimations.init(null, './image/coloredRibbon.json', ".coloredRibbon", false);
      }
      lottieAnimations.play(coloredRibbonAnimations);
      await new Promise(resolve => {
        coloredRibbonAnimations.addEventListener("complete", () => {
          lottieAnimations.stop(coloredRibbonAnimations);
          resolve();
        });
      });
    }

    function rewardIconChange() {
      let index = rewardCount
      $(`.rewardIcon${index}`).attr('src', source.roundArr[index].rewardIconMask);
    }
  }
)
