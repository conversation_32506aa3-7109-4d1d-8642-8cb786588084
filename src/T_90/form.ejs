<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>ZJYX0001FT_追击游戏FT</title>
    <link rel="stylesheet" href="form/css/style.css" />
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
  </head>

  <body>
    <div id="container">
      <div class="edit-form">
        <div class="module-title">ZJYX0001FT_追击游戏FT</div>
        <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %>

        <div class="c-group">
          <div class="c-title">上传追击元素</div>
          <div class="c-area upload img-upload">
            <div class="c-well">
              <div class="field-wrap" style="display: flex">
                <label class="field-label" style="width: 80px; margin: 0" for=""
                  >追击元素<em>*</em></label
                >
                <div style="flex: 1">
                  <div class="field-wrap">
                    <label
                      for="chaseJson"
                      class="btn btn-show upload"
                      v-if="!configData.source.chaseJson"
                      >上传</label
                    >
                    <label
                      for="chaseJson"
                      class="btn upload re-upload"
                      v-if="configData.source.chaseJson"
                      >重新上传</label
                    >
                  </div>
                  <div style="margin-left: 10px">
                    <div class="txt-info">
                      <em>json格式，最大尺寸不超过384x384，小于等于240Kb</em>
                    </div>
                    <input
                      type="file"
                      v-bind:key="Date.now()"
                      class="btn-file"
                      id="chaseJson"
                      size="384*384"
                      accept=".json"
                      @change="lottieUpload($event,configData.source,'chaseJson',240)"
                    />
                  </div>
                </div>
              </div>

              <div class="img-preview" v-if="configData.source.chaseJson">
                <img
                  src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                  alt=""
                />
                <div class="img-tools">
                  <span
                    class="btn btn-delete"
                    @click="delPrew(configData.source,'chaseJson')"
                    >删除</span
                  >
                </div>
              </div>

              <div class="field-wrap" style="display: flex; margin-top: 20px">
                <label class="field-label" style="width: 80px; margin: 0" for=""
                  >被追击元素<em>*</em></label
                >
                <div style="flex: 1">
                  <div class="field-wrap">
                    <label
                      for="beChaseJson"
                      class="btn btn-show upload"
                      v-if="!configData.source.beChaseJson"
                      >上传</label
                    >
                    <label
                      for="beChaseJson"
                      class="btn upload re-upload"
                      v-if="configData.source.beChaseJson"
                      >重新上传</label
                    >
                  </div>
                  <div style="margin-left: 10px">
                    <span class="txt-info"
                      ><em
                        >json格式，最大尺寸不超过384x384，小于等于240Kb</em
                      ></span
                    >
                    <input
                      type="file"
                      v-bind:key="Date.now()"
                      class="btn-file"
                      id="beChaseJson"
                      size="384*384"
                      accept=".json"
                      @change="lottieUpload($event,configData.source,'beChaseJson',240)"
                    />
                  </div>
                </div>
              </div>
              <div class="img-preview" v-if="configData.source.beChaseJson">
                <img
                  src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg"
                  alt=""
                />
                <div class="img-tools">
                  <span
                    class="btn btn-delete"
                    @click="delPrew(configData.source,'beChaseJson')"
                    >删除</span
                  >
                </div>
              </div>
              <div style="margin-top: 20px">
                <div style="margin-top: 10px; display: flex">
                  <div>被追击元素Y轴位置&nbsp;&nbsp;</div>
                  <div>
                    <input
                      type="number"
                      class="c-input-txt"
                      style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                      @change="validateBeChaseY"
                      v-model="configData.source.beChaseY"
                    />
                    <div><em>y:500-1080，默认和追击元素高度一致</em></div>
                  </div>
                </div>
              </div>

              <div
                v-for="(item,index) in configData.source.retreatArr"
                :key="index"
                style="margin-top: 20px"
              >
                <div class="field-wrap" style="display: flex">
                  <div>
                    <label
                      class="field-label"
                      style="width: 80px; margin: 0"
                      for=""
                      >后退元素{{ index + 1 }}<em>*</em></label
                    >
                  </div>
                  <div style="flex: 1">
                    <div class="field-wrap">
                      <label
                        :for="'retreat'+index"
                        class="btn btn-show upload"
                        v-if="!item.img"
                        >上传</label
                      >
                      <label
                        :for="'retreat'+index"
                        class="btn upload re-upload"
                        v-if="item.img"
                        >重新上传</label
                      >
                      <span
                        class="dele-tg-btn"
                        v-on:click="deleRetreat(item)"
                        v-show="index>0"
                      ></span>
                    </div>
                    <div style="margin-left: 10px">
                      <span class="txt-info"
                        ><em>JPG、PNG格式，小于等于60Kb</em></span
                      >
                      <input
                        type="file"
                        v-bind:key="Date.now()+index"
                        class="btn-file"
                        :id="'retreat'+index"
                        size=""
                        accept=".jpg,.png"
                        @change="imageUpload($event,item,'img',60)"
                      />
                      <div style="margin-top: 10px">
                        <span>初始位置&nbsp;&nbsp;</span>
                        X:<input
                          type="number"
                          class="c-input-txt"
                          style="
                            margin: 0 10px;
                            width: 60px !important;
                            display: inline-block;
                          "
                          oninput="if(value>1920)value=1920;if(value<0)value=0"
                          v-model="item.x"
                        />
                        Y:<input
                          type="number"
                          class="c-input-txt"
                          style="
                            margin: 0 10px;
                            width: 60px !important;
                            display: inline-block;
                          "
                          oninput="if(value>1080)value=1080;if(value<0)value=0"
                          v-model="item.y"
                        />
                        <div><em>x:0-1920. y:0-1080</em></div>
                      </div>
                      <div style="margin-top: 10px">
                        <span>移动速度&nbsp;&nbsp;</span
                        ><input
                          type="number"
                          class="c-input-txt"
                          style="
                            margin: 0 10px;
                            width: 80px !important;
                            display: inline-block;
                          "
                          min="1"
                          max="10"
                          step="1"
                          oninput="if(value>10)value=10;if(value<1)value=1"
                          v-model.number="item.retreatSpeed"
                        />
                        <div><em>速度范围: 1-10，数值越大速度越快</em></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="img-preview" v-if="item.img">
                  <img :src="item.img" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" @click="delPrew(item,'img')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
              <button
                type="button"
                class="add-tg-btn"
                style="
                  display: block;
                  margin: 20px auto 0;
                  width: auto;
                  font-size: 18px;
                  padding: 0 5px;
                "
                v-on:click="addRetreat"
                v-show="configData.source.retreatArr.length < 6"
              >
                添加后退元素
              </button>
            </div>
          </div>
        </div>
        <div class="c-group">
          <div class="c-title">轮次设置(最多6轮)</div>
          <div class="c-area upload img-upload">
            <div
              class="c-well"
              v-for="(item, index) in configData.source.roundArr"
              :key="index"
            >
              <span
                class="dele-tg-btn"
                @click="delRound(item)"
                v-show="configData.source.roundArr.length>2 && index > 1"
              ></span>
              <span>第{{ index + 1 }}轮设置</span>
              <div style="margin-top: 20px">
                <div class="field-wrap" style="display: flex">
                  <div style="width: 80px">
                    <label class="field-label" for="">内容图片<em>*</em></label>
                  </div>
                  <div style="flex: 1">
                    <div class="field-wrap">
                      <label
                        :for="'contentImg-'+index"
                        class="btn btn-show upload"
                        v-if="!item.pic"
                        >上传</label
                      >
                      <label
                        :for="'contentImg-'+index"
                        class="btn upload re-upload"
                        v-if="item.pic"
                        >重新上传</label
                      >
                    </div>
                    <div class="txt-info" style="margin-left: 10px">
                      <em
                        >JPG、PNG格式，最大尺寸不超过1516x472，小于等于50Kb</em
                      >
                      <input
                        type="file"
                        v-bind:key="'contentImg-'+Date.now()"
                        class="btn-file"
                        :id="'contentImg-'+index"
                        size="1516*472"
                        accept=".jpg,.png"
                        @change="imageUpload($event,item,'contentImg',50)"
                      />
                    </div>
                  </div>
                </div>
                <div class="img-preview" v-if="item.contentImg">
                  <img :src="item.contentImg" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delPrew(item,'contentImg')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
              <div style="margin-top: 20px">
                <div class="field-wrap" style="display: flex">
                  <div style="width: 80px">
                    <label class="field-label" for=""
                      >奖励图标初始<em>*</em></label
                    >
                  </div>
                  <div style="flex: 1">
                    <div class="field-wrap">
                      <label
                        :for="'rewardIcon-'+index"
                        class="btn btn-show upload"
                        v-if="!item.rewardIcon"
                        >上传</label
                      >
                      <label
                        :for="'rewardIcon-'+index"
                        class="btn upload re-upload"
                        v-if="item.rewardIcon"
                        >重新上传</label
                      >
                    </div>
                    <div class="txt-info" style="margin-left: 10px">
                      <em>JPG、PNG格式，最大尺寸不超过80x80，小于等于20Kb</em>
                      <input
                        type="file"
                        v-bind:key="'rewardIcon-'+Date.now()"
                        class="btn-file"
                        :id="'rewardIcon-'+index"
                        size="80*80"
                        accept=".jpg,.png"
                        @change="imageUpload($event,item,'rewardIcon',20)"
                      />
                    </div>
                  </div>
                </div>
                <div class="img-preview" v-if="item.rewardIcon">
                  <img :src="item.rewardIcon" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delPrew(item,'rewardIcon')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
              <div style="margin-top: 20px">
                <div class="field-wrap" style="display: flex">
                  <div style="width: 80px">
                    <label class="field-label" for=""
                      >励图标点亮<em>*</em></label
                    >
                  </div>
                  <div style="flex: 1">
                    <div class="field-wrap">
                      <label
                        :for="'rewardIconMask-'+index"
                        class="btn btn-show upload"
                        v-if="!item.rewardIconMask"
                        >上传</label
                      >
                      <label
                        :for="'rewardIconMask-'+index"
                        class="btn upload re-upload"
                        v-if="item.rewardIconMask"
                        >重新上传</label
                      >
                    </div>
                    <div class="txt-info" style="margin-left: 10px">
                      <em>JPG、PNG格式，最大尺寸不超过80x80，小于等于20Kb</em>
                      <input
                        type="file"
                        v-bind:key="'rewardIconMask-'+Date.now()"
                        class="btn-file"
                        :id="'rewardIconMask-'+index"
                        size="80*80"
                        accept=".jpg,.png"
                        @change="imageUpload($event,item,'rewardIconMask',20)"
                      />
                    </div>
                  </div>
                </div>
                <div class="img-preview" v-if="item.rewardIconMask">
                  <img :src="item.rewardIconMask" alt="" />
                  <div class="img-tools">
                    <span
                      class="btn btn-delete"
                      @click="delPrew(item,'rewardIconMask')"
                      >删除</span
                    >
                  </div>
                </div>
              </div>
              <div style="margin-top: 20px">
                <span>麦克风位置</span>
                <label
                  class="inline-label"
                  :for="'micLocation'+index"
                  style="display: inline-block; margin-left: 15px"
                >
                  <input
                    type="radio"
                    :name="'micLocation'+index"
                    value="Left"
                    v-model="item.micLocation"
                  />左侧</label
                >
                <label
                  class="inline-label"
                  :for="'micLocation'+index"
                  style="display: inline-block; margin-left: 15px"
                >
                  <input
                    type="radio"
                    :name="'micLocation'+index"
                    value=""
                    v-model="item.micLocation"
                  />中间</label
                >
                <label
                  class="inline-label"
                  :for="'micLocation'+index"
                  style="display: inline-block; margin-left: 15px"
                >
                  <input
                    type="radio"
                    :name="'micLocation'+index"
                    value="Right"
                    v-model="item.micLocation"
                  />右侧</label
                >
              </div>
            </div>
            <button
              v-if="configData.source.roundArr.length<6"
              type="button"
              class="add-tg-btn"
              @click="addRound"
            >
              +
            </button>
          </div>
        </div>
        <!-- 反馈动画添加 -->
        <% include ./src/common/template/feedbackAnimation/form %>
        <div class="c-group img-upload">
          <div class="c-title">背景音乐</div>
          <div class="c-well upload c-area">
            <div class="field-wrap" style="display: flex">
              <div style="width: 80px">
                <label class="field-label" for="">音效</label>
              </div>
              <div style="flex: 1" class="field-wrap">
                <div class="field-wrap">
                  <div>
                    <label
                      for="pursuitAudio"
                      class="btn btn-show upload"
                      v-if="!configData.source.bgm"
                      >上传</label
                    >
                    <label
                      for="pursuitAudio"
                      class="btn upload re-upload"
                      v-if="configData.source.bgm"
                      >重新上传</label
                    >
                    <label
                      class="btn upload btn-audio-dele"
                      v-if="configData.source.bgm"
                      @click="configData.source.bgm=''"
                      >删除</label
                    >
                  </div>
                  <div><em>mp3格式，小于等于50Kb</em></div>
                  <input
                    type="file"
                    v-bind:key="Date.now()"
                    class="btn-file"
                    id="pursuitAudio"
                    accept=".mp3"
                    @change="audioUpload($event,configData.source,'bgm',50)"
                  />
                  <div class="audio-preview" v-show="configData.source.bgm">
                    <div class="audio-tools">
                      <p v-show="configData.source.bgm">
                        {{ configData.source.bgm }}
                      </p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="configData.source.bgm"></audio>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <button class="send-btn" v-on:click="onSend">提交</button>
        <div class="edit-show">
          <div class="show-fixed">
            <div class="show-img">
              <img
                src="form/img/preview.jpg?v=<%= new Date().getTime() %>"
                alt=""
              />
            </div>
            <ul class="show-txt">
              <li><em>图片格式：</em>JPG/PNG/GIF</li>
              <li><em>声音格式：</em>MP3/WAV</li>
              <li><em>视频格式：</em>MP4</li>
              <li>带有" * "号为必填项</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="form/js/form.js?v=<%= new Date().getTime() %>"></script>
</html>
