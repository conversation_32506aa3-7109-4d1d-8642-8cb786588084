var configData = {
  bg: "",
  desc: '',
  title: '',
  tImg: '',
  tImgX: 1340,
  tImgY: 15,
  tg: [{
    content: "eegeg appy family. My father i appy family. My father i",
    title: "weqwf appy family. My father i"
  }],
  level: {
    high: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    //追击json动画
    chaseJson: './assets/images/Doll_turn1.json',
    //被追击json动画
    beChaseJson: './assets/images/bear_walk.json',
    //被追击元素Y轴位置
    beChaseY: 550,
    //后退元素数组
    retreatArr: [
      {
        img: './assets/images/chair.png',
        x: '1400',
        y: '300',
        retreatSpeed: 1 //后退元素速度
      },
      {
        img: './assets/images/rewardIcon.png',
        x: '600',
        y: '500',
        retreatSpeed: 1 //后退元素速度
      },
      {
        img: './assets/images/duihua1.png',
        x: '150',
        y: '500',
        retreatSpeed: 2 //后退元素速度
      }
    ],
    //轮次设置
      roundArr: [
      {
        contentImg: './assets/images/chair.png',
        rewardIcon: './assets/images/rewardIcon.png',
        rewardIconMask: './assets/images/rewardIconMask.png',
        micLocation: ''
      },
      {
        contentImg: './assets/images/duihua1.png',
        rewardIcon: './assets/images/rewardIcon.png',
        rewardIconMask: './assets/images/rewardIconMask.png',
        micLocation: 'Right'
      },
    ],
    bgm: './assets/audios/water.mp3'

  },
  feedbackLists:[
    {
      positiveFeedback: '2',
      feedbackList:[
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./image/prefect.json', mp3:'./audio/prefect.mp3' },
        { id:'1', json:'./image/goldCoin.json', mp3:'./audio/goldCoin.mp3' },
        { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
        { id:'9', json: './image/feedback.json', mp3: './audio/feedback01.mp3' },
      ],
      feedbackObj: { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
      feedback:'',
      feedbackAudio:'',
      feedbackName:'整体反馈',
      key:'feedKey1',
    }
  ]
};
