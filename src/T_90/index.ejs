<!DOCTYPE html>
<html lang="en">

<head>
    <% var title = "ZJYX0001FT_追击游戏FT"; %>
    <% include ./src/common/template/index_head %>
</head>

<body>
<div class="container" id="container" data-syncresult='show-result-1'>
    <!-- 遮罩层 -->
    <% include ./src/common/template/feedbackAnimation/index.ejs %>
    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>
    <audio id="bgm" src="" preload="preload" loop="loop" data-syncaudio="soundwater"></audio>
    <audio src="audio/succeed.mp3" id="succeed" data-syncaudio="succeed"></audio>
    <section class="main">
<!--        顶部奖励图标盒子-->
        <div class="reward-icon-box"></div>
<!--        中间图片区域-->
        <div class="pics">
            <!--        话筒-->
            <div class="microphone"></div>
            <div class="lrbtn-bg leftBtn disable-left" data-syncactions="btn-left"></div>
            <div class="pic"></div>
            <div class="lrbtn-bg rightBtn" data-syncactions="btn-right"></div>
            <span id="picpage" class="page">1/6</span>
        </div>
        <!-- 倒计时 -->
        <div class="timeChangeBox hide">
            <div class="timeBg">
                <div class="numberList"></div>
                <audio
                        src="./audio/timeLow.mp3"
                        class="timeLowAudio_1"
                        data-syncaudio="timeLowAudio_1"
                ></audio>
                <audio
                        src="./audio/timeLow.mp3"
                        class="timeLowAudio_2"
                        data-syncaudio="timeLowAudio_2"
                ></audio>
                <audio
                        src="./audio/timeLow.mp3"
                        class="timeLowAudio_3"
                        data-syncaudio="timeLowAudio_3"
                ></audio>
                <audio
                        src="./audio/timehigh.mp3"
                        class="timeLowAudio_4"
                        data-syncaudio="timeLowAudio_4"
                ></audio>
            </div>
        </div>
        <!--        追击元素-->
        <div class="chaseJson"></div>
        <!-- 箭头元素 -->
         <div class="arrow"></div>
        <!--        被追击元素-->
        <div class="beChaseJson"></div>
<!--        彩带容器-->
        <div class="coloredRibbon"></div>
        <div class="modal">
            <div class="arrowMask"></div>
            <img src="./image/microphone.png">
            <div id="btnstart" class="btn-item start" data-syncactions="btn-start"></div>
        </div>
        <!--        gogo按钮-->
        <div id="btngogo" class="btn-item gogo" data-syncactions="btn-gogo"></div>
    </section>

</div>
<% include ./src/common/template/index_bottom %>
<% include ./src/common/template/lottie %>

</body>

</html>
