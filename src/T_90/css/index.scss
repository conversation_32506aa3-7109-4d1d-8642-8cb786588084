@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.hide {
  display: none;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.modal {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999999999;

  .arrowMask {
    position: absolute;
    left: 8.7rem;
    top: 3.92rem;
    width: 2rem;
    height: 2rem;
  }
  
  img {
    position: absolute;
    left: 8.6rem;
    top: 7rem;
    width: 2rem;
  }
}

.reward-icon-box {
  margin-left: 6.5rem;
  margin-top: 0.2rem;
  width: 6.2rem;
  background-image: url("../image/awardbox.png");
  background-size: 100% 100%;
  height: 0.9rem;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  z-index: 10;
}

.container {
  background-size: auto 100%;
  position: relative;

  // font-family:"ARLRDBD";
}

.pics {
  position: absolute;
  top: 1.3rem;
  left: 2.02rem;
  vertical-align: middle;
  z-index: 10;

  .pic {
    border-radius: 0.1rem;
    width: 15.16rem;
    height: 4.72rem;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .page {
    position: absolute;
    width: 1rem;
    height: 0.5rem;
    right: 0;
    bottom: -0.6rem;
    font-size: 0.3rem;
    line-height: 0.5rem;
    background: #eeeeee;
    text-align: center;
    border-radius: 0.4rem;
  }

  .lrbtn-bg {
    width: 1.04rem;
    height: 1.3rem;
    position: absolute;
    top: 1.71rem;
    justify-content: center;
    align-items: center;
    text-align: center;
    cursor: pointer;
    display: none;
  }

  .leftBtn {
    left: -0.56rem;
    z-index: 300;
    background: url(../image/leftBtn.png) no-repeat;
    background-size: auto 100%;
  }

  .disable-left {
    z-index: 300;
    background: url(../image/left-d.png) no-repeat;
    background-size: auto 100%;
    cursor: not-allowed !important;
  }

  .rightBtn {
    z-index: 300;
    right: -0.56rem;
    background: url(../image/right.png) no-repeat;
    background-size: auto 100%;
  }

  .disable-right {
    z-index: 300;
    background: url(../image/right-d.png) no-repeat;
    background-size: auto 100%;
    cursor: not-allowed !important;
  }
}

.chaseJson,
.beChaseJson {
  display: none;
  position: absolute;
  left: 0.4rem;
  bottom: 1.46rem;
  width: 3.84rem;
  height: 3.84rem;
  z-index: 100;
}

.beChaseJson {
  left: 7.68rem;
  z-index: 90;
}

.arrow {
  display: block;
  position: absolute;
  display: none;
  z-index: 100;
  left: 5rem;
  bottom: 2.38rem;
  width: 2rem;
  height: 2rem;
}

#btngogo {
  display: none;
}

.btn {
  position: absolute;
  bottom: 1.2rem;
  color: #fff;
  background: #f1a91e;
  border: #ffffff 3px solid;
  display: block;
  font-size: 0.26rem;
  width: 1.3rem;
  height: 0.6rem;
  line-height: 0.5rem;
  border-radius: 0.4rem;
  text-align: center;
  margin-left: 9rem;
  cursor: pointer;
  z-index: 500;
  display: none;
}

.btn-item {
  position: absolute;
  bottom: 0.34rem;
  width: 4.36rem;
  height: 1.9rem;
  margin-left: 7.42rem;
  cursor: pointer;
  z-index: 500;
  display: none;
}

.start {
  bottom: 0.8rem;
  background: url("../image/gameStart.png") no-repeat;
  background-size: 100%;
}

.gogo {
  background: url("../image/gogo.png") no-repeat;
  background-size: 100%;
}

.coloredRibbon {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 19.2rem;
  height: 10.8rem;
  z-index: 250;
  pointer-events: none;
}

.microphone {
  position: absolute;
  width: 2.5rem;
  left: 6.53rem;
  bottom: -1rem;
  display: none;
}

.microphoneLeft {
  left: 2.7rem;
}

.microphoneRight {
  left: 10.36rem;
}

.timeChangeBox {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  height: 4.8rem;
  width: 7.2rem;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 0.5rem;
  z-index: 400;

  .timeBg {
    width: 3.79rem;
    height: 3.84rem;
    position: absolute;
    top: 1rem;
    background: url(../image/timeBg.png) no-repeat;
    background-size: 100% 100%;
    left: 50%;
    margin-left: -1.9rem;
    top: 50%;
    margin-top: -1.92rem;

    .numberList {
      width: 1.5rem;
      height: 1.5rem;
      position: absolute;
      left: 0;
      bottom: 0;
      right: 0;
      top: 0;
      margin: auto;
      background: url(../image/number1.png) no-repeat;
      background-size: 6rem 100%;
      background-position-x: 0.1rem;
    }
  }
}
