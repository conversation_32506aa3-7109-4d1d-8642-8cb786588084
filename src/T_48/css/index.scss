@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.hide{
    display: none;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content:center;
    align-items:center;
    text-align: center;
}
.container{ 
    background-size: auto 100%;
	position: relative;
    // font-family:"ARLRDBD";
} 

.teacher{ 
    height: 100%;
    width: 4rem;  
    display: flex;
    justify-content:center;
    align-items:center; 
    .teacher-folder{ 
        margin-top: 2rem;
        height: 4.7rem;
        width: 100%;
        display: flex;
        justify-content:center;
        align-items:center; 
        .score{  
            bottom: -1rem;  
            width:2.2rem;
            height: 1.5rem; 
            position: relative; 
            .score_1{
                background: url(../image/nums.png) no-repeat;
                background-size: auto 100%;
                background-position: -9.9rem 0;
                width: 1.1rem;
                height: 1.5rem;
                display: block;
                float: left;
            }
            .score_2{
                background: url(../image/nums.png) no-repeat;
                background-size: auto 100%;
                background-position: -9.9rem 0;
                width: 1.1rem;
                height: 1.5rem;
                display: block;
                float: right;
            }
        }
        
    }
    .teacher-replace-1{
        background: url(../image/tmp_teacher_bg.png) no-repeat;
        background-size: auto 100%;
        background-position: 100% 0;
    }

    .teacher-replace-2{
        background: url(../image/tmp_teacher_bg.png) no-repeat;
        background-size: auto 100%;
        background-position: 0 0;
    }
}

// 倒计时动画类
.shade-run{ 
    animation: run 2.4s steps(3) 1; 
    -webkit-animation: run 2.4s steps(3) 1;
}

@keyframes run {
    0% { 
        background-position-x: 0%;
    }
    100% {
        background-position-x: 150%;
    } 
}

//胜利光圈动画
.light-run{
    animation: light 1s linear infinite; 
    -webkit-animation: light 1s linear infinite;
}

@keyframes light{
    0% { 
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(30deg);
    } 
}

.board{
    height: 100%;
    width: 8.42rem; 
    background: url(../image/board.png) no-repeat;
    background-size: 100% auto;
    display: flex;
    justify-content:center;
    align-items:center; 
    text-align: center;
    flex-direction:column;
    position: relative;
    .shade{
        position: absolute;
        width:4rem;
        height:4rem; 
        top: 4.09rem;
        left: 2.16rem;
        z-index: 500; 
        background: url(../image/readygo.png) no-repeat;
        background-size: auto 100% ;
        background-position: 0 0 ; 
        display: none;
    }
    .pic{
        margin-top: 1.3rem; 
        width:6.6rem;
        height:4.94rem; 
        position: relative;
        img{ 
            position: absolute;
            top:0rem;
            left:0rem;
            width:6.6rem;
            height:4.94rem;
        }
        .hide{
            display: none;
        }
        .pic-end{
            width: 100%;
            height: 100%;
            background: #0FAAF6;
            position: relative;
            overflow: hidden;
            .light{
                position: absolute;
                top: -0.83rem;
                left: 0rem;
                width: 6.6rem;
                height: 6.6rem;
                background: url(../image/light.png) no-repeat;
                background-size: 100% 100%;
            }
            .wincup{
                position: absolute;
                top: 0rem;
                left: 0rem;
                width: 100%;
                height: 100%;
                background: url(../image/cup.png) no-repeat;
                background-size: auto 100%;
            }
        }
    }
    .text{
        position: absolute;
        background: #eeeeee;
        border-radius: .2rem; 
        bottom: 1.55rem;
        left:  3.71rem;
        width:1.2rem;
        height: .4rem;
        font-size: .35rem;
        line-height: .45rem;
    }
    .btn{
        position: absolute; 
        height: .6rem;
        width: 1.6rem;
        color:#fff;
        text-align: center;
        line-height: .6rem;
        font-size: .4rem;
        border-radius: .3rem;
        cursor: pointer;
    }
    .start-btn { 
        border: solid 2px #ffffff;
        bottom: 2.85rem; 
        left: 3.41rem;
        background: #f1a91e; 
    }
}

.tobig{
    animation: tobig 0.5s;
    -webkit-animation: tobig 0.5s;
}

@keyframes tobig{
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}

.student{
    height: 100%;
    width: 4rem; 
    display: flex;
    justify-content:center;
    align-items:center; 
    .student-folder{ 
        margin-top: 2rem;
        height: 4.7rem;
        width: 100%;
        display: flex;
        justify-content:center;
        align-items:center; 
        .score{   
            bottom: -1rem;  
            width:2.2rem;
            height: 1.5rem; 
            position: relative; 
            .score_1{
                background: url(../image/nums.png) no-repeat;
                background-size: auto 100%;
                background-position: -9.9rem 0;
                width: 1.1rem;
                height: 1.5rem;
                display: block;
                float: left;
            }
            .score_2{
                background: url(../image/nums.png) no-repeat;
                background-size: auto 100%;
                background-position: -9.9rem 0;
                width: 1.1rem;
                height: 1.5rem;
                display: block;
                float: right;
            }
        }
    }
    .student-replace-1{
        background: url(../image/tmp_student_bg.png) no-repeat; 
        background-size: auto 100%;
        background-position: 100% 0;
    }

    .student-replace-2{
        background: url(../image/tmp_student_bg.png) no-repeat; 
        background-size: auto 100%;
        background-position: 0 0;
    }
}

.alertBox {
    height: 1rem;
    width: 14.2rem;
    background: #fff;
    border-radius: .2rem .2rem .2rem .2rem;
    position: absolute;
    left: 50%;
    margin-left: -6.6rem;
    bottom: 0rem;
    z-index: 230; 
    display: none;
    p{
        height: 100%;
        width: 100%;
        line-height: 1rem;
        text-align: center;
        font-size: .3rem;
        font-weight: 700; 
        #student_chose{
            cursor: pointer;
        }
        #teacher_chose {
            cursor: pointer;
        }
        img{
            width:.4rem;
            margin-left: .3rem;
            vertical-align: middle; 
        }
        .keystr{
            cursor: default;
            float: left;
            margin-left: .45rem;
            margin-right: .45rem;
        }
        .chosespan{
            float: right;
            margin-right: .8rem;
        }
    } 
}


