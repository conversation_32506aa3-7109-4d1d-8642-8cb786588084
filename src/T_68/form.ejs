<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>PRS0002_呈现帧动画</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">PRS0002_呈现帧动画</h3>

			<% include ./src/common/template/common_head %>

      <!-- 每轮有戏设计 -->
      <div class="c-group">
        <div class="c-title">组合设置</div>
        <div class="c-area upload img-upload">
          <!-- 组合数量 -->
          <div class="field-wrap">
            <div class="c-well">
              <div class="title">动画组合数量</div>
              <div class="sub-title">动画组合类型：每个组合=最多1动画+最多1音频</div>
              <div class="details">
                <span>组合数量</span>
                <label class="inline-label" for="imgtype">
                  <input type="radio" name="imgtype" value="1" v-model="configData.source.imgType">
                  1个
                </label>
                <label class="inline-label" for="imgtype">
                  <input type="radio" name="imgtype" value="2" v-model="configData.source.imgType">
                  2个
                </label>
              </div>
            </div>
          </div>
          <!-- 触发 -->
          <div class="field-wrap">
            <div class="c-well">
              <div class="title">触发<span>（一个组合最多1次触发，提交时判断）</span></div>
              <div class="details">
                <span>触发机制</span>
                <label class="inline-label" for="triggerType">
                  <input type="radio" name="triggerType" value="1" v-model="configData.source.triggerType">
                  1次触发
                </label>
                <label v-if="configData.source.imgType == 2" class="inline-label" for="triggerType">
                  <input type="radio" name="triggerType" value="2" v-model="configData.source.triggerType">
                  2次触发
                </label>
              </div>
            </div>
          </div>
          <!-- 老师面板 -->
          <div class="c-well" v-if="configData.source.triggerType == 2">
            <div class="title">老师面板</div>
            <div class="details">
              <span>序号显示</span>
              <label class="inline-label" for="number">
                <input type="radio" name="number" value="1" v-model="configData.source.number">
                显示序号
              </label>
              <label class="inline-label" for="number">
                <input type="radio" name="number" value="2" v-model="configData.source.number">
                不显示序号
              </label>
            </div>
          </div>

          <!-- 组合关系 -->
          <div v-if="configData.source.imgType == 2 && configData.source.triggerType == 1" class="field-wrap">
            <div class="c-well">
              <div class="title">组合关系</div>
              <div class="audio-list">
                <div class="sub-title">1次触发2个组合时，需要确定组合关系</div>
                <div class="relation">
                  <span class="between">组合关系</span>
                  <div class="relation-label">
                    <label for="relationType" style="display: block;">
                      <input type="radio" name="relationType" value="1" v-model="configData.source.relationType">
                      组合1结束后，播放组合2
                    </label>
                    <label for="relationType" style="display: block;">
                      <input type="radio" name="relationType" value="2" v-model="configData.source.relationType">
                      组合2结束后，播放组合1
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

       <!-- 触发按钮设置 -->
       <div class="c-group">
        <div class="c-title">触发按钮设置</div>
        <div class="c-area upload img-upload">
          <div class="field-wrap" v-for="(item,index) in configData.source.optionsTrigger">
            <div class="c-well">
              <div class="title">触发按钮{{index+1}}</div>
              <div class="details">
                <label class="title-label">
                  <span class="title-t">触发按钮<em>*</em><br>文案</span>
                  <input type="text" class="tiggerCls" placeholder="请在此输入描述" v-model="item.title">
                  <span class="title-s"><=12个字符。<br>
                    参考文案：show 。</span>
                </label>
              </div>
              <div class="details">
                <label class="title-label">
                  <span class="title-t">触发按钮<br>说明文案</span>
                  <input type="text" class="tiggerCls" placeholder="请在此输入描述" v-model="item.subTitle">
                  <span class="title-s"></span>
                </label>
              </div>
              <div class="details">
                <label class="title-label">
                  <span class="title-t">触发按钮<em>*</em><br>位置</span>&nbsp;&nbsp;
                  X:<input type="number" class="tiggerCls distance" v-model="item.moveX" oninput="if(value>1920)value=1920;if(value<0)value=0">
                  Y:<input type="number" class="tiggerCls distance" v-model="item.moveY" oninput="if(value>1920)value=1920;if(value<0)value=0">
                  &nbsp;&nbsp;&nbsp;&nbsp;
                  <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                </label>
              </div>
              <div class="details" v-if="configData.source.triggerType == 2">
                <label class="title-label">
                  <span class="title-t">触发按钮<em>*</em><br>序号</span>&nbsp;&nbsp;
                  <select class="select-class"  v-model="item.number">
                    <option v-for="conPos in updata" name="optive" :value="conPos"
                    >{{conPos}}</option>
                  </select>
                </label>
              </div>
              <div class="details" v-if="configData.source.triggerType == 2">
                <label class="title-label">
                  <span class="title-t">关联组合<em>*</em></span>&nbsp;&nbsp;
                  <select class="select-class"  v-model="item.group">
                    <option v-for="conPos in updata" name="optive" :value="conPos"
                    >组合{{conPos}}</option>
                  </select>
                </label>
              </div>
            </div>
          </div>
        </div>
       </div>

       <!-- 动画组合 -->
       <div class="c-group">
        <div class="c-title">动画组合</div>
        <div class="c-area upload img-upload details-filed-wrap">
          <div class="field-wrap animation-upload" v-for="(item,index) in configData.source.options">
            <div class="field-title">动画组合{{index+1}}</div>
            <div class="c-well">
              <label v-if="!item.isImgShow" class="title-label isAnimation">
                <span class="title-t">动画</span>
                <span class="details-btn" v-on:click="addAnimationFn(index)">添加动画</span>
              </label>
              <!-- 上传动画 -->
              <div v-if="item.isImgShow" class="label-img">
                <label>动画
                  <span class="dele-tg-btn" v-on:click="delOption('img', index)"></span>
                </label>
                <div class="details">
                  <span class='txt-info'>雪碧图<em>*</em>
                    <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
                    <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤50KB </em></span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1" :id="'content-pic-'+index" @change="imageUpload($event,item,'img',50)">
                  </span>
                </div>
                <div class="img-preview" v-if="item.img">
                  <img v-bind:src="item.img" alt=""/>
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                  </div>
                </div>
                <div class="details">
                  <label><span>帧数<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4帧</span></label>
                </div>
                <div class="details">
                  <label><span>循环次数<em>*</em>&nbsp;&nbsp;1次</span></label>
                </div>
                <div class="details">
                  <label>
                    <span>播放时长</span>
                    <em>*</em>
                    <input type="number"  v-model="item.imgPlayTime" class="c-input-txt input70"  oninput="if(value>6000)value=6000;if(value<0)value=0">
                    数字，1000～6000 毫秒
                  </label>
                </div>
                <div class="details">
                  <label class="title-label">
                    <span class="title-t">位置<em>*</em></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    X:<input type="number" v-model="item.positionX" class="tiggerCls distance" oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" v-model="item.positionY" class="tiggerCls distance" oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="c-well">
              <label v-if="!item.isAudioShow" class="title-label isAudio">
                <span class="title-t">音频</span>
                <span class="details-btn" v-on:click="addAudioFn(index)">添加音频</span>
              </label>
              <div v-if="item.isAudioShow" class="label-audio">
                <div class="audio-title">
                  <span class="title">音频</span>
                  <span class="dele-tg-btn" v-on:click="delOption('audio', index)"></span>
                </div>

                <div class="audio-list">
                  <label class="field-label"  for="">音频<em>*</em>&nbsp;&nbsp;&nbsp;</label>
                  <input type="file" accept=".mp3" :id="'content-audio-'+index" volume="30" v-bind:key="Date.now()" class="btn-file" v-on:change="audioUpload($event,item,'audio')">
                  <label :for="'content-audio-'+index"  class="btn btn-show upload" v-if="!item.audio">上传</label>
                  <div class="audio-preview" v-show="item.audio">
                    <div class="audio-tools">
                      <p v-show="item.audio">{{item.audio}}</p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="item.audio"></audio>
                    </span>
                  </div>
                  <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.audio" @click="item.audio=''">删除</label>
                  <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
                  <label><em>大小：≤30KB</em></label>
                </div>
                <div class="details">
                  <label>
                    <span>播放时长</span>
                    <em>*</em>
                    <input type="number" class="c-input-txt input70"  oninput="if(value>6000)value=6000;if(value<0)value=0" v-model="item.audioPlayTime">
                    数字，1000～6000 毫秒
                  </label>
                </div>
                <div class="details">
                  <label><span>播放时机<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;触发组合后立即播放</span></label>
                </div>
                <div class="details">
                  <label><span>教学音频图标<em>*</em></span></label>
                  <label class="inline-label" :for="'imgtype-'+index">
                    <input type="radio" :name="'imgtype' + index"  value="1" v-model="item.isType">
                    不显示
                  </label>
                  <label class="inline-label" :for="'imgtype-'+index">
                    <input type="radio" :name="'imgtype' + index"  value="2" v-model="item.isType">
                    显示(填位置)
                  </label>
                </div>
                <div class="details" v-if="item.isType == 2">
                  <label class="title-label">
                    <span class="title-t">位置<em>*</em></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    X:<input type="number" v-model="item.iconPositionX" class="tiggerCls distance" oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" v-model="item.iconPositionY" class="tiggerCls distance" oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
       </div>


			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
