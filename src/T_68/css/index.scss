@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';



@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
.hide{
  display: none;
}
.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 2.2rem;
	position: absolute;
    right: 0px;
	.desc {
		top: 0.6rem;
	}
	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
}

.content-main{
  .buttons{
    position: relative;
    cursor: pointer;
    ul{
      li{
        position: absolute;
        z-index: 30;
        .content{
          display: flex;
          max-width: 4.5rem;
          border-radius: .2rem;
          font-size: .3rem;
          padding: .16rem .3rem .16rem 0;
          background: rgba($color: #fff, $alpha: 0.6);
          overflow: hidden;
          span{
            display: inline-block;
          }
          .content-tit{
            // float: right;
          }
          .number{
            width: .56rem;
            height: .54rem;
            line-height: .54rem;
            text-align: center;
            color: #fea500;
            background: url('../image/lt_bj.png') no-repeat;
            background-size: 100% 100%;
          }
          .tit{
            background: #fea500;
            padding: .12rem .22rem;
            color: #fff;
            border-radius: .5rem;
            margin-left: .12rem;
          }
          .sub-title{
            display: block;
            width: 3.8rem;
            margin:.1rem 0 0 .1rem;
            height: .44rem;
            line-height: .44rem;
            overflow: hidden;
          }
        }
      }
    }
  }
  .animation{
    position: relative;
    ul{
      li{
        // display: none;
        .option-img{
          position: absolute;
        }
        // 音频播放
        .example-audio{
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          width: 1.45rem;
          height: 1.34rem;
          background: url(../image/sound_bg.png) no-repeat center;
          background-size: cover;
          cursor: pointer;
          z-index: 20;
          display: none;
          .small {
            position: absolute;
            top: .3rem;
            left: .3rem;
            width: .83rem;
            height: .8rem;
          }
          img {
            width: 1.45rem;
          }
          .gif {
            display: none;
          }
        }
      }
    }
  }
}


@keyframes handClick {
	0%{
		background-position: 0 0;
  }
	100%{
    background-position: -300% 0;
  }
}
