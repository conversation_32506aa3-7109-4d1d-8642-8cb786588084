"use strict"
import '../../common/js/common_1v1.js'
// import '../../common/js/commonFunctions.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: '0' // 是否有授权按钮 1：是 0：否
  }

  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/bj.jpg)'
    })
  }
  let imgType = configData.source.imgType, //1：组合数  2 组合数
      triggerType = configData.source.triggerType, //1 1：次触发   2：多次触发
      relationType = configData.source.relationType, //1：组合1结束后，播放组合2   2：组合2结束后，播放组合1
      optionsTrigger = configData.source.optionsTrigger, //触发按钮
      options = configData.source.options, //上次素材
      isCheck = true, //是否可点击
      isPlaySound = true, //是否播放声音
      isKeys = []; //选中后的key集合（用于断线重连）


  /**
   * 初始化触发按钮列表
  */
  triggerList()
  function triggerList() {
    optionsTrigger.forEach(function(item, index){
      let str = '';
      str = $(`<li style="top:${item.moveY/100  + "rem"};`
              + `left:${item.moveX/100 + "rem"}"`
              + `data-syncactions="onList-${index}"`
              + `data-group="${item.group}">`
              + '<div class="content">'
              +  `<span class="number">${item.number}</span>`
              +  `<div class="content-tit"><span class="tit">${item.title}</span>`
              +  `<span class="sub-title">${item.subTitle}</span></div>`
              + '</div>'
              + '</li>');

      $(".buttons ul").append(str);
      // 学生不显示
      if (isSync && window.frameElement.getAttribute('user_type') == 'stu') {
        $(".buttons").hide();
      }
      // 是否显示序号
      if(!item.number || configData.source.triggerType == 1 || configData.source.number == 2)  $(".buttons ul li").find(".number").hide();
      // 是否显示副标题
      if(item.subTitle.length == 0) $(".buttons ul li").eq(Number(index)).find(".sub-title").hide();
    })
  }

  /**
    *初始化动画组合列表
  */
  optionsList()
  function optionsList() {
    options.forEach(function(item, index){
      let liEle = '';
      // 音频和图片的位置
      let imgLeft = item.positionX / 100  + 'rem',
          imgTop = item.positionY / 100  + 'rem',
          audioLeft = item.iconPositionX / 100  + 'rem',
          audioTop = item.iconPositionY / 100  + 'rem';
      liEle = $(`<li class="optionLi optionLiPic" data-isType="${item.isType}" data-audio="${item.audio.length}" data-audio-time="${item.audioPlayTime}">`
              +'<div class="option-img"></div>'
              +`<div class="example-audio sound" data-syncactions="audio-${index}">`
              +'<img src="./image/sound.gif" alt="" class="gif small">'
              +'<img src="./image/sound.png" alt="" class="png small">'
              +`<audio src="${item.audio}" data-syncaudio="audioExample"></audio></div>`
              + '</li>');

      // 图片初始化样式
      liEle.find(".option-img").css({
        left: imgLeft,
        top: imgTop,
        'background-image': 'url(' + item.img + ')',
      });
      // 音频初始化样式
      liEle.find(".example-audio").css({
        left: audioLeft,
        top: audioTop,
      })
      // 加载图片 获取宽高
      var imgList = [
        {
          image: item.img
        }
      ]
      var preImgs = [];
      $.when(preloadImg(imgList, preImgs)).done(function(){//图片加载完成后 才能获得宽高
        liEle.find(".option-img").css({
          backgroundSize: '400% 100%',
          width: preImgs[0].width / 4 / 100 + 'rem',
          height: preImgs[0].height / 100 + 'rem'
        });
      });


      $(".animation ul").append(liEle);
      // 显示第一个动画
      // $(".animation ul li").eq(0).show();
       // 是否显示音频
       if(item.isType == 1) $(".optionUl li").eq(index).find(".example-audio").hide();

    })
  }

  /**
   * 触发按钮列表事件
  */
  $(".buttons ul").on("click touchstart", "li", function(e){
    if(!$(this).hasClass("isAddCheck")) {
      isKeys.push(optionsTrigger[$(this).index()].group-1)
    }


    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("synResultClick");
      return;
    }

    if (window.frameElement.getAttribute('user_type') == 'tea') {
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data("syncactions"),
        eventType: 'click',
        method: 'event',
        syncName: 'synTriggerClick',
        otherInfor: {
          index: isKeys,
          imgType: imgType,
          triggerType: triggerType,
          relationType: relationType
        },
        recoveryMode: '1'
      });
    }
  })

  $(".buttons ul").on("click synTriggerClick", "li", function(e,message){
    let animationKey = 0,//当前动画的key值
        group = $(this).attr('data-group'); //关联组合
    // 断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor,
          exampleAudio = $(".animation ul li"),
          animationShow = '';
      isKeys = obj.index
      if(obj.index.length > 0){
        if(obj.imgType == 1) { //一个组合
          animationShow = exampleAudio.eq(0)
        } else if(imgType == 2 && triggerType == 1) { //2个组合一次触发
          animationShow = exampleAudio.find(".example-audio")
        }  else { //2个组合2次触发
          for(let i = 0; i < obj.index.length; i++) {
            animationShow = exampleAudio.eq(obj.index[i])
            $(".buttons ul li").eq(obj.index[i]).addClass("isAddCheck");
          }
        }
        // 添加事件
        animationShow.find(".example-audio").show();
        animationShow.find(".option-img").css({
          'animation': '',
          'backgroundPosition': '-300% 0',
        })
        $(this).addClass("isAddCheck");
      }
      SDK.setEventLock();
    }

    buttonsFn($(this), group);

    SDK.setEventLock();

  });

  /**
   * 触发事件方法
   */
  function buttonsFn($this, group) {
    let nowKey = 1, //两个组合一次触发时另外一个的key值
        animationKey = 0, //当前动画的key值
        animationLi = $(".animation ul li");
    $this.addClass("isAddCheck");
    if(relationType == 1) nowKey = 2
    if(isCheck) {
      isCheck = false;
      // 是否是一个组合
      if(imgType == 2 && triggerType == 1) { //2个组合一次触发
        console.log("2个组合一次触发")
        animationKey = relationType-1;
        // 第一个组合完毕后播放第二个组合
        setTimeout(function(){
          animationKey = nowKey-1;
          // 列表事件
          groupListFn(animationKey);
          // 动画效果
          positionFn(animationLi.eq(animationKey).find(".option-img"));
        }, animationLi.eq(animationKey).attr("data-audio-time"))
      } else { //2个组合2次触发
        console.log("2个组合2次触发")
        animationKey = group-1;
      }
      // 隐藏素材
      animationLi.eq(animationKey).find(".example-audio").hide()
      // setTimeout(function(){

        // 列表事件
        groupListFn(animationKey);
        // 动画效果
        positionFn(animationLi.eq(animationKey).find(".option-img"));
      // }, 200)
    }
  }


  /**
   * 动画效果animation
   * $this:当前元素
  */
  function positionFn($this) {
    $this.css({
      'animation': `handClick 1s steps(3, start) forwards`,
      'animation-fill-mode': 'forwards'
    })
    // 动画结束
    $this.on("webkitAnimationEnd", function(){
      isCheck = true
      $this.css({
        'animation': '',
        'backgroundPosition': '-300% 0',
      })
    })
  }

  /**
   *
   * 音频列表事件
  */
  $(".optionUl").on("click touchstart", "li .example-audio", function(e){
    if (e.type == "touchstart") {
      e.preventDefault()
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("synAudioClick");
      return;
    }

    if (window.frameElement.getAttribute('user_type') == 'tea') {
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data("syncactions"),
        eventType: 'click',
        method: 'event',
        syncName: 'synAudioClick',
        recoveryMode: '1'
      });
    }

  })
  $(".optionUl").on("click synAudioClick", "li .example-audio", function(e){
    if(isPlaySound) {
      audioFn($(this)).palyRudio();
    }
  })


  /**
   * 组合列表事件
  */
  function groupListFn(animationType){
    let groupType = $(".animation ul li").eq(animationType),
        isShow = groupType.attr("data-isType"),
        isAudio = groupType.attr("data-audio");
    // $(".optionUl").show();
    // 教学音频是否显示
    if(isAudio > 0){
      // 显示素材
      if(isShow == 2) {
        groupType.find(".example-audio").show()
      }
      // 音频播放事件
      audioFn(groupType.find(".example-audio")).palyRudio();
    }
  }

  /**
   *音频方法
  */
  function audioFn($this) {
    return {
      "palyRudio": function(){
        let gif = $this.find('.gif'),
            png = $this.find('.png'),
            audio = $this.find('audio')[0];
            gif.show();
            png.hide();
        // 开始播放
        if(isPlaySound) {
          console.log("开始播放")
          isPlaySound = false;
          isCheck = false;
          SDK.playRudio({
            index: audio,
            syncName: $this.find('audio').attr("data-syncaudio")
          })
          gif.show();
          png.hide();
        }
        // 播放结束
        audio.onended = function () {
          console.log("播放结束")
          gif.hide();
          png.show();
          isPlaySound = true;
          isCheck = true;
        }.bind(this);
        SDK.setEventLock();
      }
    }
  }

})

/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
  var def = $.Deferred(),
      len = list.length;
  $(list).each(function(i, e) {
      var img = new Image();
      img.src = e.image;
      if (img.complete) {
          imgs[i] = img;
          len--;
          if (len == 0) {
              def.resolve();
          }
      } else {
          img.onload = (function(j) {
              return function() {
                  imgs[j] = img
                  len--;
                  if (len == 0) {
                      def.resolve();
                  }
              };
          })(i);
          img.onerror = function() {
              len--;
          };
      }
  });
  return def.promise();
};
