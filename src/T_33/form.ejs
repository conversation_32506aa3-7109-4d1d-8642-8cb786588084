<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TDR0004_全屏拖拽 </title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<div class="h-title">TDR0004_全屏拖拽</div>
			<% include ./src/common/template/common_head_noBg %>
			<div class="c-group">
				<div class="c-title">编辑问题</div>
				<div class="c-area upload img-upload">
					<label>拖入区域 </label>
					<div class="c-well">
						<div class="well-title">
							<p>图片（显示位置：3）尺寸：参考设计稿1640X1080，大小：≤100KB<em>*</em></p>
						</div>
						<div class="well-con">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label for="img-1" class="btn btn-show upload" v-if="configData.source.img==''?true:false">上传</label><label  for="img-1" class="btn upload re-upload" v-if="configData.source.img!=''?true:false">重新上传</label><span class='txt-info'></span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" id="img-1" size="1640*1080" accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,configData.source,'img',100)">
							</div>
							<div class="img-preview" v-if="configData.source.img!=''?true:false">
								<img v-bind:src="configData.source.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="configData.source.img=''">删除</span>
								</div>
							</div>
						</div>
					</div>
					<label>选项（显示位置：4）<em>（文字与图片二选一）</em></label>


					<div class="c-well" v-for="(item,index) in configData.source.seleList">
						<div class="well-title" style="margin-bottom:10px;">
							<p>选项{{index+1}}<em>*</em></p><br>
							<p style="margin-top:10px;">
								对应区域<em>(#为干扰项)</em>：
								<select v-model="configData.source.seleList[index].pos">
									<option v-for="conPos in updata" name="optive" :value="conPos">{{conPos}}</option>
								</select>
							</p>
							<!-- 删除的x -->
							<span class="dele-tg-btn" v-on:click="delSele(item)" v-show='configData.source.seleList.length>1?true:false'></span>
						</div>
						<!-- 选项图片 -->
						<div class="well-con" v-if="item.font==''?true:false">
							<div class="field-wrap">
								<label class="field-label"  for="">上传图片</label><label v-bind:for="'img-upload'+index" class="btn btn-show upload" v-if="item.img==''?true:false">上传</label><label  v-bind:for="'img-upload'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label><span class='txt-info'>（大小：≤50KB)</span>
								<input type="file"  v-bind:key="Date.now()" class="btn-file" v-bind:id="'img-upload'+index" size=""  accept=".gif,.jpg,.jpeg,.png" v-on:change="imageUpload($event,item,'img',50)">
							</div>
							<div class="img-preview" v-if="item.img!=''?true:false">
								<img v-bind:src="item.img" alt=""/>
								<div class="img-tools">
									<span class="btn btn-delete" v-on:click="item.img=''">删除</span>
								</div>
							</div>
						</div>
						<!-- 选项文字 -->
						<div style="margin-bottom:10px;" v-if="item.img==''?true:false">
							<p style="margin-top:10px; margin-bottom:10px;">上传文字 字符：≤20</p>
							<input type="text" class="c-input-txt" placeholder="请输入选项字符" maxlength='20' v-model="item.font" />
						</div>
						<!-- 声音 -->
						<div class="field-wrap">
							<label class="field-label"  for="">上传声音<em>（暂不支持）</em></label>
							<div class="audio-preview" v-show="item.audio!=''?true:false">
								<div class="audio-tools">
									<p v-show="item.audio!=''?true:false">{{item.audio}}</p>
									<img src="" alt="" v-show="item.audio==''?true:false">
								</div>
								<span class="play-btn" v-on:click="play($event)">
									<audio v-bind:src="item.audio"></audio>
								</span>
							</div>
							<!-- <span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio=''">删除</span> -->
							<label class="btn btn-show upload" id="noUpdata" v-if="item.audio==''?true:false">上传</label>
							<!-- <label  v-bind:for="'audio-upload'+index" class="btn upload re-upload mar" v-if="item.audio!=''?true:false">重新上传</label> -->
							<span class='txt-info'>（大小：≤50KB)</span>
							<input type="file" v-bind:id="'audio-upload'+index" class="btn-file upload" size="" volume="50" accept=".mp3" v-on:change="audioUpload($event,item,'audio')" v-bind:key="Date.now()">
						</div>
					</div>
					<button type="button" class="add-tg-btn" v-on:click="addSele()" v-show='configData.source.seleList.length<20?true:false'>+</button>
				</div>
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li><em>图片格式：</em>JPG/PNG/GIF</li>
					<li><em>声音格式：</em>MP3/WAV</li>
					<li><em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>
</html>
