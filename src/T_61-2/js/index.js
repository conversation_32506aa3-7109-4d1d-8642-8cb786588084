"use strict"
import '../../common/js/common_1v1.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;
import {
    resultWin,
    resultHide
} from '../../common/template/ribbonWinCn/index.js';
$(function () {
    //是否在控制器显示功能按钮
    window.h5Template = {
        hasDemo: '1', //0 默认值，无提示功能  1 有提示功能
        hasPractice: '2' //0 无授权功能  1  默认值，普通授权模式  2 start授权模式
    }
    let staticData = configData.source;
    let audioList = staticData.audioList; //上传语音
    let imgList = staticData.imgList; //上传图片
    let frequencyList = staticData.frequencyList; //每轮数据

    // 多语言
    if (configData.lang == 2) {//简体中文
        // $(".startMsg").attr('src', 'https://cdn.51talk.com/apollo/images/240270121463efa9fca1bd204f165c06.png')
        $(".startMsg").attr('src', './image/240270121463efa9fca1bd204f165c06.png')
        // 按钮2个
        // $(".demo-btnStu").attr('src','//cdn.51talk.com/apollo/images/6632e5ad4090fbb1a4e30722a133b3b1.png')
        // $(".startBtn").attr('src','//cdn.51talk.com/apollo/images/519210d78b059247ad1e36b41d94d2ca.png')
        $(".demo-btnStu").attr('src','./image/6632e5ad4090fbb1a4e30722a133b3b1.png')
        $(".startBtn").attr('src','./image/519210d78b059247ad1e36b41d94d2ca.png')
        // 321GO
        $('.numberList').css({
            // 'background-image': `url('//cdn.51talk.com/apollo/images/c7fa150c82b59380842de2dc6b898b31.png')`,
            'background-image': `url('./image/c7fa150c82b59380842de2dc6b898b31.png')`,
        })
        $("#langTips").text('现在是学生点击时间，老师请勿操作。');
    }



    let timeInt = null; // 定时器
    let timeIndex = 0; //取第几轮的数据
    let sum_time = 0; //游戏剩余总时长
    let time = 20; //每轮时间  默认20
    let userType = window.frameElement && window.frameElement.getAttribute('user_type'); //用户身份学生还是老师
    let classStatus = 1; //教室状态
    let score = 0; //分数
    let isAnyOneOut = false; //是否断线  true 已断线
    let rightArr = {
        allRightArr: [], //存储全部正确选项
        currentRightNum: 0 //已答对正确选项个数（如果全部答对停止计时切换下一轮）
    };
    //判断是否使用默认背景图片
    let isFirst = true;
    if (configData.bg == '') {
        $(".container").css({
            'background-image': 'url(./image/defaultBg.jpg)',
        })
    }
    //轮数初始化(选项、声音、时间)
    let timePlay = null;
    //点击气泡事件
    let optionClickStus = true;
    let hideOptionArr = []; //已点击的选项
    let demoTimer; //demo模式出现小手定时器
    let demoOptionNum = 4;

    /**
     * 初始化
     */

    //生成像素网格
    renderPx();
    //小手位置初始化
    initHand();
    //左下角大拇指图片、正确音效配置
    sorceRightAudio();
    //判断用户角色，显示不同功能
    isShowBtn();
    //预加载所有选项图片
    preloadImg(imgList, [])
    //生成像素网格
    function renderPx() {
        let liHtml = '';
        for (let i = 1; i < 801; i++) {
            liHtml += `
                <li class="pos_${i}"></li>	`
        }
        $('.boxUl').html(liHtml);
    }
    //小手位置初始化
    function initHand() {

        let true_options = frequencyList[0].options.filter(function (item) {
            return (item.result == '1')
        })
        let firtstPosition = true_options[0].position;
        if (firtstPosition) {
            let left = ($('.pos_' + firtstPosition).offset().left - $('.container').offset().left) / window.base + 2 + 'rem';
            let top = ($('.pos_' + firtstPosition).offset().top - $('.container').offset().top) / window.base + 2 + 'rem';
            $('.hand').css({
                left: left,
                top: top
            });
        }
    }
    //左下角大拇指图片、正确音效配置
    function sorceRightAudio() {
        $(".countImg").css({
            'background-image': 'url(' + staticData.countImg + ')'
        });
        $(".rightAudio").attr("src", staticData.rightAudio ? staticData.rightAudio : './audio/right.mp3');
    }
    //判断用户角色，显示不同功能(如老师的底部文字提示，学生的预习模式等)
    function isShowBtn() {
        if (isSync) { //同步模式
            classStatus = SDK.getClassConf().h5Course.classStatus;
            if (classStatus == 0 && userType == "stu") {
                $(".funcMask").show(); //预习模式和倒计时
            }
        } else { //非同步模式
            var hrefParam = parseURL("http://www.example.com");
            if (top.frames[0] && top.frames[0].frameElement) {
                hrefParam = parseURL(top.frames[0].frameElement.src)
            }
            var role_num = hrefParam.params['role']

            function parseURL(url) {
                var a = document.createElement('a')
                a.href = url
                return {
                    source: url,
                    protocol: a.protocol.replace(':', ''),
                    host: a.hostname,
                    port: a.port,
                    query: a.search,
                    params: (function () {
                        var ret = {},
                            seg = a.search.replace(/^\?/, '').split('&'),
                            len = seg.length,
                            i = 0,
                            s
                        for (; i < len; i++) {
                            if (!seg[i]) {
                                continue;
                            }
                            s = seg[i].split('=')
                            ret[s[0]] = s[1]
                        }
                        return ret
                    })(),
                    file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
                    hash: a.hash.replace('#', ''),
                    path: a.pathname.replace(/^([^\/])/, '/$1'),
                    relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
                    segments: a.pathname.replace(/^\//, '').split('/')
                }
            }
            if (role_num == '1' || role_num == undefined) {
                $(".funcMask").show();
            } else if (role_num == '2') {
                $(".funcMask").show();
            }
        }
    }


    /**
     *  开始游戏
     *  2种途径可开启游戏，1.老师通过控制器点击开始 2.非课中模式，如预习，学生点击开启
     */
    //1.老师通过控制器点击开始
    window.SDK.actAuthorize = function(message) {
        if (isSync) {
            if (userType == 'tea' && SDK.getClassConf().h5Course.classStatus == 5) {
                //老师显示下方提示条
                $(".doneTip").removeClass('hide');
            }

            if (message && message.operate == 5) {
                isFirst = false;
            }
            if (message && message.type == 'practiceStart') {
                //第一次授权才显示倒计时
                if (isFirst) {
                    isFirst = false;
                    $(".funcMask").show();
                    $(".optionUl").html("");
                    threeTwoOne(); //321倒计时
                    gameProcessBidSync(); //上报游戏数据
                }
            }
        }
    }
    //2.非课中模式，如预习，学生点击开启
    let startBtnStatus = true; //开始按钮是否可点击(预习模式)
    $(".startBtn").on("click touchstart", function(e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (startBtnStatus) {
            startBtnStatus = false;
            threeTwoOne(); //321倒计时
        }
    });

    /**
     *  demo模式
     *  2种途径可开启demo模式，1.老师通过控制器点击提示 2.非课中模式，如预习，学生点击开启
     */
    //1.老师通过控制器点击提示
    window.SDK.actDemo = function (message) {
        demoFun(false); //demo模式
        //老师上传一份空白cldata,来覆盖以前的数据。
        if (isSync) {
            SDK.bindSyncEvt({
                index: 'clear',
                eventType: 'click',
                method: 'event',
                syncName: 'clear', //SDK.js中对其特殊处理
                recoveryMode: '1'
            });
        }
        SDK.setEventLock();
    }
    //2.非课中模式，如预习，学生点击开启
    $(".demo-btnStu").on("click touchstart", function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        demoFun(true); //demo模式
    });


    // 3 2 1倒计时
    function threeTwoOne() {
        //不显示倒计时
        if(configData.isShowCount == 2){
            SDK.setEventLock();
            $('.startBox').hide();
            optionsInit(timeIndex); //本轮游戏选项初始化
            addEvent(); //泡泡选项添加事件
            sorceTimeInit(); //进度条初始化
            timeStartFn(); //计时器倒计时
            gameProcessBidSync(); //上报游戏数据
            $('.funcMask').hide();//解除遮罩
            return;
        }

        let q = 1;
        $('.startBox').hide().siblings('.timeChangeBox').show().find('.numberList');
        SDK.playRudio({
            index: $('.timeLowAudio_' + q).get(0),
            syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
        })
        let audioPlay = setInterval(function () {
            q++;
            if (q > 4) {
                clearInterval(audioPlay);
                //3210倒计时结束
                SDK.setEventLock();
                $('.funcMask').hide();
                $('.timeChangeBox').hide();
                optionsInit(timeIndex); //本轮游戏选项初始化
                addEvent(); //泡泡选项添加事件
                sorceTimeInit(); //进度条初始化
                timeStartFn(); //计时器倒计时
                gameProcessBidSync(); //上报游戏数据
            } else {
                // 播放倒计时声音
                SDK.playRudio({
                    index: $('.timeLowAudio_' + q).get(0),
                    syncName: $('.timeLowAudio_' + q).attr("data-syncaudio")
                })
                $('.numberList').css({
                    'background-position-x': -(1.5 * (q - 1)) + 'rem'
                })
            }
        }, 1000)
    }

    /**
     * 本轮游戏选项初始化
     * @param {*} index  游戏是第几轮
     * @param {*} tip   'tip'代表"demo模式"
     */
    function optionsInit(index, tip) {
        if (!tip) {
            //展示喇叭分数进度条
            $(".intSatge").removeClass("hide")
            $(".proprogressBarBox").removeClass("hide")
            $(".horn").removeClass("hide")
        }

        if (index > frequencyList.length - 1) {
            return;
        }
        hideOptionArr = [];
        let options = frequencyList[index].options; //本轮所有选项
        if (tip == 'tip') { //记录下demo模式有几个选项
            demoOptionNum = frequencyList[index].options.length;
        }
        let audio = frequencyList[index].audio;
        time = frequencyList[index].time; //每轮时间
        //如果是最后一轮，时间为进度条剩余时间，清空进度条为止，不以本轮设置时间为准。
        if (index == frequencyList.length - 1 && frequencyList.length != 1) { //最后一轮且并非只有一轮
            time = sum_time;
        }
        let preList = [],
            preImgs = [];
        $(".optionUl").html("");
        options.forEach(function (img, index) {
            if (img.result == 1) { //result 1 代表正确选项
                rightArr.allRightArr.push({
                    srcNum: img.src,
                    image: imgList[img.src - 1].image,
                    audio: audioList[audio - 1].audio
                });
            }
            let liEle = "";
            let currentPosition = img.position;
            let left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base + 'rem';
            let top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
            liEle = $('<li  class="optionLi"></li>');
            liEle.attr({
                result: img.result,
                'data-syncactions': 'freq' + index + 'bubble' + index + ''
            });
            liEle.css({
                left: left,
                top: top,
                'background-image': 'url(' + imgList[img.src - 1].image + ')',
            });
            $(".optionUl").append(liEle)
            if (staticData.imgTypeEffect == 1) {
                $(".optionLi").addClass("optionUDAni"); //增加上线浮动的效果
            }

            preList.push({
                image: imgList[img.src - 1].image
            });
        });
        //选项间隔一定时间，逐个显示
        let optionIndex = 0;
        var intervalTimer = setInterval(() => {
            $(".optionLi").eq(optionIndex).fadeIn(300); //动画时间
            optionIndex = optionIndex + 1
            if (optionIndex > options.length - 1) {
                clearInterval(intervalTimer)
            }
        }, 300); //间隔时间。为保持连贯性，建议间隔时间与动画时间一致。
        $.when(preloadImg(preList, preImgs)).done(
            function () {
                preImgs.forEach(function (item, index) {
                    initImg($(".optionLi").eq(index), item); //选项图片按位置显示
                })
            }
        );
        $(".frequencyAudio").attr("src", audioList[audio - 1].audio)
        if (tip == 'tip') { //demo模式下音频只播放一次
            $(".frequencyAudio").removeAttr('loop');
        } else { //正常模式 音频是循环播放
            $(".frequencyAudio").attr('loop', 'loop');
        }

        soundControl().pause(); //先停止一次
        soundControl().play(tip); //调用播放
        optionClickStus = true;
        rightArr.currentRightNum = 0; //本轮正确作答数目
        $('.scoreNum').html(score); //右下角当前分数
    }
    //泡泡添加事件
    function addEvent() {
        //泡泡选项
        $(".optionLi").on("click touchstart", function (e) {
            if (e.type == "touchstart") {
                e.preventDefault()
            }
            e.stopPropagation();
            //禁止老师点击
            if (isSync) {
                classStatus = SDK.getClassConf().h5Course.classStatus;
                if (classStatus != 0 && userType == 'tea') {
                    SDK.setEventLock();
                    return false;
                }
            }
            if (optionClickStus) {
                optionClickStus = false;
                if (!isSync) {
                    $(this).trigger("syncItemClick");
                    return;
                }
                SDK.bindSyncEvt({
                    sendUser: '',
                    receiveUser: '',
                    index: $(e.currentTarget).data("syncactions"),
                    eventType: 'click',
                    method: 'event',
                    syncName: 'syncItemClick',
                    otherInfor: {
                        timeIndex: timeIndex,
                        score: score,
                        currentRightNum: rightArr.currentRightNum,
                        allRightArr: rightArr.allRightArr
                    },
                    recoveryMode: '1'
                });
            }
        });
        //点击选项
        $('.optionLi').on('syncItemClick', function (e, message) {
            let self = $(this);
            let result = self.attr("result");
            //播放音效
            if (result == 1) {
                rightWrong(self).right();
            } else {
                rightWrong(self).wrong();
            }
            self.on('animationend webkitAnimationEnd', function () {
                self.removeClass('shake');
                SDK.setEventLock();
                optionClickStus = true;
            });
            //继续播放
            SDK.setEventLock();
            gameProcessBidSync(); //上报游戏数据
        });
    }

    //进度条初始化
    let proprogressBarW = $('.proprogressBar').width();
    let moveItemW, everLen;
    //进度条初始化
    function sorceTimeInit() {
        for (const item of frequencyList) {
            sum_time = sum_time + item.time
        }
        moveItemW = proprogressBarW / window.base; // 获取进度条长度
        everLen = moveItemW / sum_time // 每秒移动距离  time为每轮时间
        $('.proprogressBar').css('width', '84.2%').attr("data_width", moveItemW);
    }

    //声音控制
    function soundControl() {
        return {
            'play': function (tip) {
                setTimeout(() => {
                    if (timeInt || tip) { //tip代表demo模式
                        $(".frequencyAudio")[0].currentTime = 0; //从头播放
                        SDK.playRudio({
                            index: $(".frequencyAudio")[0],
                            syncName: $(".frequencyAudio").attr("data-syncaudio")
                        })
                        $(".horn").addClass("hornAnimation");
                    }
                }, 200);

            },
            'pause': function () {
                clearInterval(timePlay);
                SDK.pauseRudio({
                    index: $(".frequencyAudio")[0],
                    syncName: $(".frequencyAudio").attr("data-syncaudio")
                })
                timePlay = null;
                $(".horn").removeClass("hornAnimation");
            }
        }
    }

    //上报游戏数据方便重连（切换下一轮、点击选项）
    function gameProcessBidSync() {
        if (isSync) {
            SDK.bindSyncEvt({
                index: 'runing',
                eventType: 'click',
                method: 'event',
                syncName: 'syncRuning',
                otherInfor: {
                    timeIndex: timeIndex, //第几轮
                    score: score, //分数
                    currentRightNum: rightArr.currentRightNum, //当前正确作答数目
                    allRightArr: rightArr.allRightArr, //全部正确选项
                    hideOptionArr: hideOptionArr, //已点击的选项
                    sum_time: sum_time, //游戏剩余总时长
                    time: time //每轮剩余时长
                },
                recoveryMode: '1'
            });
        }
    };


    //游戏中恢复数据
    $(".runing").on("syncRuning", function (e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                //直接恢复页面
                rebackPage().whenClick(obj);
            }
        }
        SDK.setEventLock();
    });

    //计时器倒计时
    function timeStartFn() {
        window.localStorage.setItem('countDownTime', time); //将本轮倒计时存储在缓存中
        if (timeInt) {
            clearInterval(timeInt);
            timeInt = null;
        }
        timeInt = setInterval(function () {
            if (isAnyOneOut) { //如断线则停止游戏
                clearInterval(timeInt);
                soundControl().pause(); //停止声音
            }
            time--; //本轮游戏时间
            sum_time--; //总游戏时间
            isEndGameFun(time); //本轮游戏是否结束
            window.localStorage.setItem('countDownTime', time); //每秒更新一次时间
            moveItemW = moveItemW - everLen; //每秒更新一次进度条长度    新进度条长度 = 原进度条长度 - 每秒移动距离
            $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);
        }, 1000);
    };

    //本轮游戏是否结束
    function isEndGameFun(time) {
        if (rightArr.currentRightNum >= $(".optionLi[result='1']").length || time <= -1) {
            optionClickStus = false;
            if (timeInt) {
                clearInterval(timeInt);
                timeInt = null;
            }
            soundControl().pause();
            //本轮元素退场
            var timer = 500
            if ($(".optionLi[result='1']").not('.already-hide').length != 0) { //如果正确选项,正确答案先消失，然后其余选项消失
                $(".optionLi[result='1']").not('.already-hide').addClass("optionRight");
                timer = 1300
            }
            timeIndex++;
            setTimeout(() => {
                $(".optionLi").addClass("optionOther");
                // 准备开启下一轮
                setTimeout(function () {
                    if (timeIndex > frequencyList.length - 1) {
                        //最后一轮已结束  结束游戏
                        // endGameappendImg('over');
                        console.log("00000")
                        if (userType == 'stu' || !isSync) {
                            console.log("000111")
                            sendGameOver();
                        }
                    } else {
                        startNext(); //开始下一轮
                    }
                }, 500);
            }, timer + 300); //目前正确效果时长是1.3s


        }
    }

    //结束之后切换下一轮
    function startNext() {
        // setTimeout(function() {
        optionsInit(timeIndex); //本轮游戏选项初始化
        addEvent(); //泡泡添加事件
        // sorceTimeInit(); //进度条初始化
        timeStartFn(); //计时器倒计时
        gameProcessBidSync(); //上报游戏数据
        // }, 1500)
    };

    //结束游戏 发送数据(由学生判断游戏结束，老师只接受学生的结果)
    function sendGameOver() {
        console.log("222")
        if (!isSync) {
            $(".overBtn").trigger("gameOver");
        } else {
            SDK.bindSyncEvt({
                index: "overBtn",
                eventType: "mygameevent",
                method: 'event',
                syncName: "gameOver",
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    time: time,
                    timeIndex: timeIndex,
                },
                recoveryMode: '1'
            });
        }
    };

    //结束反馈事件
    $(".overBtn").on("gameOver", function (e, message) {

        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == '5') {
                endGameappendImg('whenend');
                rebackPage().whenEnd(obj);
                return;
            } else {
                endGameappendImg('over');
            }
            //如果是老师 告知控制器，将授权状态改为老师控制，classstatus为6
            if (userType == 'tea' && (SDK.getClassConf().h5Course.classStatus == '5' || SDK.getClassConf().h5Course.classStatus == '1')) {
                SDK.bindSyncCtrl({
                    'type': 'gameOverToCtrl',
                    'data': {
                        CID: SDK.getClassConf().course.id + '', //教室id 字符串
                        operate: '1',
                        data: [{
                            key: 'classStatus',
                            value: '6',
                            ownerUID: SDK.getClassConf().user.id
                        }]
                    }
                });
            }
        } else {
            console.log("333")
            endGameappendImg('over');
        }
        SDK.setEventLock();
    });

    /**
     *   终局页面去重动态插入图片  所有正确选项去重
     *   type  'over'正常结束   'whenend'断线重连结束
     */
    function endGameappendImg(type) {
        //隐藏下方提示条
        $(".doneTip").addClass('hide');
        window.localStorage.removeItem('countDownTime');
        var new_allRightArr = []; //存放正确结果
        var new_allRightArr_index = []; //去重使用 存放srcNum
        //根据正确选项中的图片地址进行去重
        rightArr.allRightArr.forEach(function (item, index) {
            if (!new_allRightArr_index.includes(item.srcNum)) {
                new_allRightArr_index.push(item.srcNum)
                new_allRightArr.push(item)
            }
        });
        rightArr.allRightArr = new_allRightArr
        var time = 1;
        //终局特效
        if (score > 0 && type == 'over') { //有回答正确的 且不是断线重连
            //播放终局特效
            resultWin({
                'WinIsLoop': false, // 答对声音是否重复播放 true/false
                'Mask_Z_Index': "500" // 遮罩层z_index
            });

            var prefect = $("<div class='perfectBox'><img src='./image/light.png' class='light'><img src='./image/prefect.png' class='perfect'></div>");
            var prefect_jt = $("<div class='perfectBox'><img src='./image/light.png' class='light'><img src='./image/prefect.png' class='perfect'></div>");
            var prefect_num = $(`<div class='numBox'> <img src="${staticData.countImg}" alt="">  <label></label>  <span class='score'>x<span class='scoreNum'>${score}</span></span> </div>`);
            if (configData.lang == 2) {//简体中文
                $(".resultWin").append(prefect_jt);
            }else{
                $(".resultWin").append(prefect); //默认繁体
            }
            $(".resultWin").append(prefect);
            $(".perfectBox").append(prefect_num);
            time = 5000; //5s后 隐藏终局特效
        }
        setTimeout(function () {
            resultHide();
            let preImgs = [];
            $.when(preloadImg(rightArr.allRightArr, preImgs)).done(
                function () {
                    preImgs.forEach(function (item, index) {
                        //将正确元素添加到终局卡片里
                        var scale = item.naturalWidth / item.naturalHeight;
                        let liEle = "";
                        liEle = $(`
                        <div class="example-audio sound" data-syncactions="audio-${index}">
                        <img src="./image/sound.gif" alt="" class="gif small soundInCard">
                        <img src="./image/sound.png" alt="" class="png small soundInCard">
                        <audio src="${rightArr.allRightArr[index].audio}" data-syncaudio="audio${index}"></audio>
                      </div>
                        `);
                        var dom;
                        if (index < 3) {
                            dom = $(".endGame .scroll-1 li").eq(index); //第一行
                        } else {
                            dom = $(".endGame .scroll-2 li").eq(index - 3); //第二行
                        }
                        dom.css({
                            'background-image': 'url(' + item.src + ')',
                            'opacity': 1,
                        });
                        dom.append(liEle)
                        endGameImgStyle($(".endGame .scrollUl li").eq(index), scale, item.naturalWidth, item.naturalHeight);
                    })
                    if (rightArr.allRightArr.length <= 3) {
                        $(".scrollBtn").hide();
                        if (rightArr.allRightArr.length == 1) {
                            //挨个移除第二个和第三个
                            $(".endGame .scroll-1 li").eq(1).remove();
                            $(".endGame .scroll-1 li").eq(1).remove();
                        } else if (rightArr.allRightArr.length == 2) {
                            $(".endGame .scroll-1 li").eq(2).remove();
                        }
                    }
                }
            );
            soundControl().pause();
            $(".endGame").show();
            if (isSync && type == 'over') {
                startFun(); // 发星
            }
        }, time)

    }


    //终局之后触发发星
    function startFun() {
        if (!isSync) {
            return false;
        }
        var support1v1h5star = SDK.getClassConf().serverData.objCourseInfo.support1v1h5star;
        classStatus = SDK.getClassConf().h5Course.classStatus;
        var device = SDK.getClassConf().h5Course.device;
        if (userType == 'stu' && (classStatus == 5 || classStatus == 6)) {
            if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
                SDK.bindSyncStart({
                    type: "newH5StarData",
                    num: score
                });
            }
        }
    }


    //终局图片样式
    function endGameImgStyle(ele, scale, width, height) {
        if (staticData.imgType == 1) {
            if (scale < 1) { //瘦高型
                if (height > ele.height()) {
                    ele.css({
                        backgroundSize: 'auto 100%',
                        backgroundPosition: '50% 50%'
                    })

                } else {
                    ele.css({
                        backgroundSize: 'auto auto',
                        backgroundPosition: '50% 50%'
                    })
                }

            } else { //胖矮型
                if (width > ele.width()) {
                    ele.css({
                        backgroundSize: '100% auto',
                        backgroundPosition: '50% 50%'
                    })

                } else {
                    ele.css({
                        backgroundSize: 'auto auto',
                        backgroundPosition: '50% 50%'
                    })
                }
            }
        }
        if (staticData.imgType == 2) {
            ele.css({
                backgroundSize: '200% ' + 200 / scale * 0.69 + '%',
                backgroundPosition: '0% 50%'
            })
        }
        if (staticData.imgType == 3) {
            ele.css({
                backgroundSize: '300% ' + 300 / scale * 0.69 + '%',
                backgroundPosition: '0% 50%'
            });
        }
        soundControl().pause();
    }

    //选项图片按位置显示   静态图（1） 1帧（2） 2帧（3） 样式
    function initImg(ele, img) {
        let naturalWidth = img.naturalWidth / 100;
        let naturalHeight = img.naturalHeight / 100;
        if (staticData.imgType == 1) {
            ele.css({
                backgroundSize: '100% 100%',
                width: naturalWidth + 'rem',
                height: naturalHeight + 'rem'
            });
        }
        if (staticData.imgType == 2) {
            ele.css({
                width: naturalWidth / 2 + 'rem',
                height: naturalHeight + 'rem',
                backgroundSize: '200% 100%'
            })
        }
        if (staticData.imgType == 3) {
            ele.css({
                width: naturalWidth / 3 + 'rem',
                height: naturalHeight + 'rem',
                backgroundSize: '300% 100%'
            });
        }
    }


    //正确反馈   静态图（1） 1帧（2） 2帧（3） 动画
    function diffImgAni(self) {
        if (staticData.imgType == 1) {
            self.addClass('optionRight');
        }
        if (staticData.imgType == 2) {
            self.addClass('imgType2Right');
        }
        if (staticData.imgType == 3) {
            self.addClass('imgType3Right');
        }
    }




    //掉线页面恢复（点击选项、结束）
    function rebackPage(data) {
        return {
            'whenClick': function (data) {
                isFirst = false;
                if (userType == 'tea' && SDK.getClassConf().h5Course.classStatus == 5) {
                    //下方提示条
                    $(".doneTip").removeClass('hide');
                }
                rightArr.currentRightNum = data.currentRightNum; //当前正确作答数目
                rightArr.allRightArr = data.allRightArr; //全部正确选项

                score = data.score; //分数
                timeIndex = data.timeIndex; //第几轮
                hideOptionArr = data.hideOptionArr; //已点击的选项

                optionsInit(timeIndex); //本轮游戏选项初始化
                addEvent(); //泡泡添加事件
                //如果是最后一轮，时间为进度条剩余时间，清空进度条为止，不以本轮设置时间为准。
                if (timeIndex == frequencyList.length - 1) { //最后一轮
                    time = data.sum_time;
                } else {
                    time = data.time;
                }

                sum_time = data.sum_time; //当前剩余总时长
                var sum_time_init = 0; //初始化时总时长
                for (const item of frequencyList) {
                    sum_time_init = sum_time_init + item.time
                }
                moveItemW = proprogressBarW / window.base; // 获取进度条长度
                everLen = moveItemW / sum_time_init // 每秒移动距离  time为每轮时间
                moveItemW = sum_time * everLen;
                $('.proprogressBar').css('width', moveItemW + 'rem').attr('data_width', moveItemW);

                if (hideOptionArr && hideOptionArr.length > 0)
                    hideOptionArr.forEach(function (item) {
                        $(".optionLi[data-syncactions='" + item + "']").hide();
                    })
                timeStartFn(); //计时器倒计时
                gameProcessBidSync(); //上报游戏数据
                SDK.setEventLock();
            },
            'whenEnd': function (data) {
                isFirst = false;
                $(".doneTip").hide();
                rightArr.currentRightNum = data.currentRightNum;
                rightArr.allRightArr = data.allRightArr;
                time = data.time;
                score = data.score;
                timeIndex = data.timeIndex;
                endGameappendImg('whenEnd'); //结束游戏
                SDK.setEventLock();
            },
        }
    }


    //正确与错误逻辑(停止选项音、播放反馈音，改变分数、自增答对个数)
    function rightWrong(self) {
        soundControl().pause(); //停止
        return {
            'right': function () {
                score++;
                self.addClass("already-hide");
                diffImgAni(self); //正确反馈
                rightArr.currentRightNum++;
                hideOptionArr.push(self.data("syncactions"))
                $('.scoreNum').html(score);
                SDK.playRudio({
                    index: $(".rightAudio")[0],
                    syncName: $(".rightAudio").attr("data-syncaudio")
                })
                $(".rightAudio")[0].onended = function () {
                    if (timeInt) {
                        soundControl().play();
                    }

                }
            },
            'wrong': function () {
                self.addClass('shake');
                // $(".wrongAudio")[0].play();
                SDK.playRudio({
                    index: $(".wrongAudio")[0],
                    syncName: $(".wrongAudio").attr("data-syncaudio")
                })
                $(".wrongAudio")[0].onended = function () {
                    if (timeInt) {
                        soundControl().play();
                    }
                }
            }
        }
    }

    //终局页面，点击按钮左右卡片
    let scrollBtnStatus = true;
    $(".leftScroll,.rightScroll").on("click touchstart", function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        e.stopPropagation();
        if (scrollBtnStatus) {
            scrollBtnStatus = false;
            if (!isSync) {
                $(this).trigger("btnScroll");
                return;
            }
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data("syncactions"),
                eventType: 'click',
                method: 'event',
                syncName: 'btnScroll',
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    time: time,
                    timeIndex: timeIndex,
                },
                recoveryMode: '1'

            });
        }
    })
    //终局页面，点击按钮左右卡片
    $(".leftScroll,.rightScroll").on("btnScroll", function (e, message) {
        let self = $(this);
        if (self.hasClass("leftScroll")) { //左侧按钮
            $(".scroll-2").css({
                'display': 'none'
            })
            $(".scroll-1").css({
                'display': 'flex'
            })
        } else {
            $(".scroll-1").css({
                'display': 'none'
            })
            $(".scroll-2").css({
                'display': 'flex'
            })
        }
        self.css({
            opacity: 0.5
        })
        self.siblings().css({
            opacity: 1
        })
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                rebackPage().whenEnd(obj)
            }
        }
        scrollBtnStatus = true;
        SDK.setEventLock();
    })


    /**
     * demo模式
     * @param showfuncMask  是否显示预习模式遮罩
     * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
     */
    function demoFun(showfuncMask) {
        clearTimeout(demoTimer) //清除以前的定时器
        optionsInit(0, 'tip'); //本轮游戏选项初始化
        $(".horn").removeClass("hide") //显示喇叭
        if (showfuncMask) { //是否显示学生用的预习模式弹窗
            $(".funcMask").hide();
        }
        $('.right-top-demo').show();
        //手
        demoTimer = setTimeout(function () {
            $('.hand').addClass("handAnimation");
        }, demoOptionNum * 300 + 100); //选项个数*选项出现的时间间隔
        //小手触发正确效果
        setTimeout(function () {
            diffImgAni($(".optionLi[result=1]").eq(0)); //正确反馈 1为正确选项
            SDK.playRudio({
                index: $(".rightAudio")[0],
                syncName: $(".rightAudio").attr("data-syncaudio")
            })
            $(".optionLi").on('animationend webkitAnimationEnd', function () {
                demoOut(showfuncMask); //退出demo模式,恢复正常模式
            })

        }, demoOptionNum * 300 + 2000 - 260)
    }
    /**
     * 退出demo模式,恢复正常模式
     * @param showfuncMask  是否显示预习模式遮罩
     * @todo 1.如果师生任一断线（收到成员变更协议，out者为师/生），则取消demo模式  2. demo模式下  若再次收到demo模式指令，则重新播放
     */
    function demoOut(showfuncMask) {
        soundControl().pause();
        clearTimeout(demoTimer) //清除以前的定时器
        $(".horn").addClass("hide") //隐藏喇叭
        $(".optionUl").html(''); //清空选项区
        $('.hand').removeClass("handAnimation");
        $('.right-top-demo').hide();
        if (showfuncMask) {
            $(".funcMask").show();
        }
        //告知控制器取消demo模式
        if (userType == 'tea') {
            SDK.bindSyncCtrl({
                'type': 'tplDemoOut',
                'data': {
                    CID: SDK.getClassConf().course.id + '', //教室id 字符串
                    operate: '1',
                    data: []
                }
            });
        }
    }



    /**
     * 断线后对端暂停处理
     */
    window.SDK.memberChange = function (message) {
        if (isSync) {
            // 进入教室
            if (message.state == 'enter') {
                $(".tea-stu-not-in").hide();
                if (timeInt) {
                    if (message.role == 'tea') {
                        $(".funcMask").hide();
                    }
                    isAnyOneOut = false;
                    soundControl().play();
                    timeStartFn(); //计时器倒计时
                }

            } else if (message.state == 'out') { // 退出教室
                $(".tea-stu-not-in").show(); //限制任何一方退出  都不能操作
                isAnyOneOut = true;
                if (timeInt) {
                    //再上报一次游戏数据
                    gameProcessBidSync(); //上报游戏数据
                    clearInterval(timeInt);
                    soundControl().pause();
                }
                //如果是demo模式
                if (demoTimer) {
                    demoOut(false); //模板取消demo模式
                }
            }
        }
    }


    // 点击卡片，播放音频和卡片放大
    let soundClick = true; //按钮是否可点击
    let isPlayList = ''; //当前正在播放的声音所在list，如：  "list0"  "list1" "list2"
    let isCardList = ''; //当前正在放大卡片所在list，如：  "list0"  "list1" "list2"
    $('.scrollUl li').on('click touchend', function (e) {
        if (e.type == "touchstart") {
            e.preventDefault()
        }
        //需求：已放大卡片只有点击喇叭才播放音频，否则不响应。因此，当前是正在放大的卡片，且点击的不是喇叭，则阻止
        if (isCardList == $(e.currentTarget).data('syncactions') && $(e.target).is('li')) {
            return;
        }
        let audios = $(this).find('audio');

        if (audios.length == 0) {
            return
        }

        if (soundClick) {
            soundClick = false;
            if (!isSync) {
                $(this).trigger('syncSoundClick');
                return;
            }
            SDK.bindSyncEvt({
                sendUser: '',
                receiveUser: '',
                index: $(e.currentTarget).data('syncactions'),
                eventType: 'click',
                method: 'event',
                syncName: 'syncSoundClick',
                funcType: 'audio',
                otherInfor: {
                    score: score,
                    currentRightNum: rightArr.currentRightNum,
                    allRightArr: rightArr.allRightArr,
                    time: time,
                    timeIndex: timeIndex,
                },
                recoveryMode: '1'
            });

        }
    })

    //播放音频事件响应
    $('.scrollUl li').on('syncSoundClick', function (e, message) {
        if (isSync) {
            let obj = message.data[0].value.syncAction.otherInfor;
            if (message.operate == 5) {
                rebackPage().whenEnd(obj)
            }
        }
        let gif = $(this).find('.gif');
        let png = $(this).find('.png');
        let audio = $(this).find('audio')[0];
        //停止正在播放的声音
        if (isPlayList) {
            let isPlayListDom = $(`[data-syncactions=${isPlayList}]`)
            SDK.pauseRudio({
                index: isPlayListDom.find('audio')[0],
                syncName: isPlayListDom.find('audio').attr("data-syncaudio")
            })
            isPlayListDom.find('audio')[0].currentTime = 0
            isPlayListDom.find('.gif').hide();
            isPlayListDom.find('.png').show();

        }
        if (isCardList) {
            let isCardListDom = $(`[data-syncactions=${isCardList}]`)
            //卡片复原
            isCardListDom.css({
                'transform': `scale(1)`,
                'border': '#ffc71f 0 solid'
            });
        }

        //卡片放大
        $(e.currentTarget).css({
            'transform': `scale(1.16)`,
            'border': '#ffc71f 0.17rem solid'
        });
        isCardList = $(e.currentTarget).data('syncactions');
        //播放声音
        SDK.playRudio({
            index: audio,
            syncName: $(this).find('audio').attr("data-syncaudio")
        })
        isPlayList = $(e.currentTarget).data('syncactions'); //当前播放声音所在list
        gif.show();
        png.hide();
        audio.onended = function () {
            isPlayList = ""; //清空当前播放声音所在list
            gif.hide();
            png.show();
            audio.currentTime = 0
        }.bind(this);

        SDK.setEventLock();
        soundClick = true;

    });

});
/**
 * 预加载图片的方法
 * @param {*} list
 * list示例：  [{
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }, {
            image: "assets/images/5ef1e222783f07ea41000030.png",
        }],
 * @param {*} imgs
 */
function preloadImg(list, imgs) {
    var def = $.Deferred(),
        len = list.length;
    $(list).each(function (i, e) {
        var img = new Image();
        img.src = e.image;
        if (img.complete) {
            imgs[i] = img;
            len--;
            if (len == 0) {
                def.resolve();
            }
        } else {
            img.onload = (function (j) {
                return function () {
                    imgs[j] = img
                    len--;
                    if (len == 0) {
                        def.resolve();
                    }
                };
            })(i);
            img.onerror = function () {
                len--;
            };
        }
    });
    return def.promise();
};
