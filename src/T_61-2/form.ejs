<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>LCH0002_计时听音点选_中文版</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
  <script src='./form/js/jquery-2.1.1.min.js'></script>
  <script src='./form/js/vue.min.js'></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">LCH0002_计时听音点选_中文版</h3>

      <% include ./src/common/template/common_head_nontitle_cn %>

      <div class="edit-show">
        <div class="show-fixed">
          <div class="show-img">
            <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
          </div>
          <ul class="show-txt">
            <li>图片格式：</em>JPG/PNG/GIF</li>
            <li>声音格式：</em>MP3/WAV</li>
            <li>视频格式：</em>MP4</li>
            <li>带有“ * ”号为必填项</li>
          </ul>
        </div>
      </div>
      <!-- 上传资源-声音 -->
      <div class="c-group">
        <!-- 上传声音 -->
        <div class="c-title">上传资源-声音</div>
        <div class="c-desc"><label class="only-show-uplaoded"><input type="checkbox" v-model="configData.showAudUped">
            仅显示已上传的资源</label></div>
        <div class="c-area upload img-upload">
          <div class="c-well" v-for="(item,index) in configData.source.audioList"
            v-show="configData.showAudUped && item.audio != '' || !configData.showAudUped">
            <div class="field-wrap">
              <label class="field-label" for="">声音{{index+1}}&nbsp;&nbsp;
                <label :for="'audio-upload-'+index" class="btn btn-show upload"
                  v-if="item.audio ==''?true:false">上传</label>
                <label :for="'audio-upload-'+index" class="btn upload re-upload mar"
                  v-if="item.audio!=''?true:false">重新上传</label>
                <em>文件大小≤30KB</em></label>
              <div class="audio-preview" v-show="item.audio!=''?true:false">
                <div class="audio-tools">
                  <p v-show="item.audio!=''?true:false">{{item.audio}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="item.audio"></audio>
                </span>
              </div>
              <span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio=''">删除</span>
              <input type="file" :id="'audio-upload-'+index" class="btn-file upload" size="" accept=".mp3"
                v-on:change="audioUpload($event,item,'audio',30)" v-bind:key="Date.now()">
            </div>
            <div>
              声音备注：<input type="text" maxlength="20" class="c-input-txt audio-mark" v-model="item.mark">
            </div>
          </div>
        </div>
      </div>
      <!-- 上传资源-图片 -->
      <div class="c-group">
        <div class="c-title">上传资源-图片</div>
        <div class="c-desc">
          <label class="only-show-uplaoded"><input type="checkbox" v-model="configData.showImgUped">仅显示已上传的资源</label>
          <br>
          <label class="text"> - 上传图片数量：1~10项</label>
          <label class="text"> - 多帧图片为点击后消失的效果，如泡泡破裂。</label>
        </div>
        <div class="c-area upload img-upload">
          <div class="c-well">
            <span>选项图片呈现效果</span>
            <label class="inline-label" for="imgTypeEffect"><input type="radio" name="imgTypeEffect" value="1"
                v-model="configData.source.imgTypeEffect"> 上下浮动</label>
            <label class="inline-label" for="imgTypeEffect"><input type="radio" name="imgTypeEffect" value="2"
                v-model="configData.source.imgTypeEffect"> 静止</label>
          </div>
          <div class="c-well">
            <span>图片类型</span>
            <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="1"
                v-model="configData.source.imgType"> 静态图片</label>
            <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="2"
                v-model="configData.source.imgType"> 2帧雪碧图</label>
            <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="3"
                v-model="configData.source.imgType"> 3帧雪碧图</label>
          </div>
          <ul>
            <li v-for="(item, index) in configData.source.imgList"
              v-show="configData.showImgUped && item.image != '' || !configData.showImgUped">
              <div class="c-well">
                <div class="field-wrap">
                  <label>图片{{index+1}}</label>
                  <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.image">上传</label>
                  <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.image">重新上传</label>
                  <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤50KB </em></span>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="" :id="'content-pic-'+index"
                    @change="imageUpload($event,item,'image',50)">
                </div>
                <div class="img-preview" v-if="item.image">
                  <img :src="item.image" alt="" />
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.image=''">删除</span>
                  </div>
                </div>
              </div>
            </li>
          </ul>
          <!-- <button v-if="configData.source.options.length<6" type="button" class="add-tg-btn" @click="addSele" >+</button>	 -->
        </div>
      </div>
      <!-- 每屏题目设计 -->
      <div class="c-group">
        <div class="c-title">每屏题目设计</div>
        <div class="c-desc">
          <label class="text"> - 至少一轮题目；每屏题目中，至少1个选项图片为正确选项。</label>
          <label class="text"> - 每轮题目的选项图片，图片不能互相重叠（主观审图校验、技术不做校验）。</label>
          <label class="text"> - 每轮题目的选项图片，最多10个。 </label>
          <label class="text"> - 最多20轮题目。</label>
        </div>
        <div class="c-area">
          <div class="c-well" v-for="(item,index) in configData.source.frequencyList">
            <span class="dele-tg-btn" v-on:click="delOption(configData.source.frequencyList,item)"
              v-show="configData.source.frequencyList.length>1"></span><br>
            <h4>第{{index+1}}轮题目</h4>
            <label>本轮作答时间<em>*</em> <input type="number" class="c-input-txt input60" v-model="item.time">(4~30秒)</label>
            <label>题干声音<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <select v-model="item.audio">
                <option v-for="conPos in configData.source.audioList" name="optive" :value="conPos.code"
                  v-show="conPos.audio">声音{{conPos.code}}</option>
              </select>
              &nbsp;&nbsp;<span
                v-if="configData.source.frequencyList[index].audio">{{configData.source.audioList[configData.source.frequencyList[index].audio-1].mark}}</span>
            </label>
            <div class="" v-for="(img,index2) in item.options">
              <span class="dele-tg-btn" v-on:click="delOption(item.options,img)"
                v-show="item.options.length>1"></span><br>
              <label>选项图片<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <select v-model="img.src">
                  <option v-for="conPos in configData.source.imgList" name="optive" :value="conPos.code"
                    v-show="conPos.image">图片{{conPos.code}}</option>
                </select></label>
              <label>位置<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <input type="number" class="c-input-txt input60" v-model="img.position"></label>
              <label>选项属性<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;
                <input type="radio" :name="'result'+index+'-'+index2" value="1" v-model="img.result"> 正确选项
                <input type="radio" :name="'result'+index+'-'+index2" value="2" v-model="img.result"> 错误选项
              </label>
            </div>
            <!-- 图片最多为10个 -->
            <button type="button" class="add-tg-btn text-add-btn add-btn-61" v-show="item.options.length<10"
              v-on:click="addOptions(item.options, {src: '', position: '', result: '1'})">添加图片</button>
          </div>
          <button type="button" class="add-tg-btn text-add-btn add-btn-61"
            v-show="configData.source.frequencyList.length<20"
            v-on:click="addOptions(configData.source.frequencyList, {time: '', audio: '', options: [{src: '', position: '', result: '1'}]})">添加一轮</button>
        </div>
      </div>
      <!-- 计数图片 -->
      <div class="c-group">
        <div class="c-title">计数图片</div>
        <div class="c-area upload img-upload radio-group">
          <div class="field-wrap">
            <input type="file" v-bind:key="Date.now()" class="btn-file" id="leftImg" size="161*169"
              v-on:change="imageUpload($event,configData.source,'countImg',30)">
          </div>
          <div class="field-wrap">
            <label for="leftImg" class="btn btn-show upload" v-if="configData.source.countImg==''?true:false">上传</label>
            <label for="leftImg" class="btn upload re-upload"
              v-if="configData.source.countImg!=''?true:false">重新上传</label><em class="red">*尺寸:161*169 文件大小≤30KB</em>
          </div>
          <div class="img-preview" v-if="configData.source.countImg!=''?true:false">
            <img v-bind:src="configData.source.countImg" alt="" />
            <div class="img-tools">
              <span class="btn btn-delete" v-on:click="configData.source.countImg=''">删除</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 反馈语音 -->
      <div class="c-group">
        <div class="c-title">点击正确后反馈音效</div>
        <div class="c-area upload img-upload radio-group">
          <div class="field-wrap">
            <input type="file" accept=".mp3" v-bind:key="Date.now()" class="btn-file" id="content-audio" time="1"
              v-on:change="audioUpload($event,configData.source,'rightAudio',30)">
            <label for="content-audio" class="btn btn-show upload" v-if="!configData.source.rightAudio">上传</label><em
              class="red">使用短促的音效，时长≤1秒，文件大小≤30KB</em>
          </div>
          <div class="audio-preview" v-show="configData.source.rightAudio">
            <div class="audio-tools">
              <p v-show="configData.source.rightAudio">{{configData.source.rightAudio}}</p>
            </div>
            <span class="play-btn" v-on:click="play($event)">
              <audio v-bind:src="configData.source.rightAudio"></audio>
            </span>
          </div>
          <div class="field-wrap" style="margin-top:20px;">
            <label for="content-audio" class="btn upload btn-audio-dele" v-if="configData.source.rightAudio"
              @click="configData.source.rightAudio=''">删除</label>
            <label for="content-audio" class="btn upload re-upload" v-if="configData.source.rightAudio">重新上传</label>
          </div>
        </div>
      </div>
      <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
        </div>
        <ul class="show-txt">
          <li>图片格式：</em>JPG/PNG/GIF</li>
          <li>声音格式：</em>MP3/WAV</li>
          <li>视频格式：</em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>

</html>
