var configData = {
  bg: "assets/images/1.jpg",
  desc: "qw23213234",
  title: "fdfg fgfh gthrt",
  tImg: 'assets/images/b164806a1b826b1b386f5496478573fe.png',
	tImgX: 1340,
	tImgY: 15,
  tg: [
    {
      content:
        "AHI0002_句子霓虹灯eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i",
    },
  ],
  level: {
    high: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
    low: [
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i",
      },
    ],
  },
  source: {
    options: [
      // {
      //   audio: "",
      //   pos: "",
      //   audioSize: 0,
      // },
      {
        audio: "assets/audios/01.mp3",
        // pos: "3",
        x:100,
        y:100,
      },
      {
        audio: "assets/audios/99k.mp3",
        // pos: "10",
        x:200,
        y:200,
      },
      {
        audio: "assets/audios/01.mp3",
        // pos: "26",
        x:300,
        y:300,
      },
      {
        audio: "assets/audios/02.mp3",
        // pos: "11",
        x:400,
        y:400,
      },
      {
        audio: "assets/audios/99k.mp3",
        // pos: "8",
        x:500,
        y:500,
      },
    ],
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "assets/images/e41570807174c4a7617e2a75455838d1.png",
          audio: "assets/audios/03d7e77310bba5b8ad38811964ce9c9a.mp3"
        },
        {
          text:"assets/images/4dfeec15c2cd5d502bf53095ec38b9ea.png",
          audio: "assets/audios/4a5ec638c60f9afcc25e797e006919ab.mp3"
        }
      ],
      messageLocationX: '550', // 消息内容位置x
      messageLocationY: '100', // 消息内容位置y
      roleLocationX: '100', // 角色位置x
      roleLocationY: '100', // 角色位置y
      roleImg: "assets/images/Doll_turn1.json", // 角色图片
      prevBtn:'assets/images/cd28b2d464fd49af8fba357129e92873.png',
      nextBtn:'assets/images/9aba8b13d42b1e7c51f03ffc923e6e05.png',
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框 1不自动播放  2自动播放
      hiddenStatus: "1", // 播放完是否应藏的状态 1不隐藏  2气泡隐藏  3IP和气泡都隐藏
    },
    multyAnimation: [
      {
        roleLocationX: '200', // 角色位置x
        roleLocationY: '200', // 角色位置y
        roleImg: "assets/images/Doll_turn1.json", // 角色图片,
        scale: 100, //缩放比例  1-500
      },
      {
        roleLocationX: '500', // 角色位置x
        roleLocationY: '500', // 角色位置y
        roleImg: "assets/images/Doll_turn1.json", // 角色图片,
        scale: 100, //缩放比例  1-500
      }
    ]
  },
};
