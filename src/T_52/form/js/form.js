//var domain = 'http://172.16.0.107:9011/pages/159/';
import {
  avatarUpWallData,
  avatarUpWallSend,
  teacherChange,
  studentChange,
  initializeAvatarUpWall
} from "../../../common/template/avatarUpWall/form.js";
var domain = "";
// 对话框初始化数据
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: ""
     }
  ],
  messageLocationX: '', // 消息内容位置x
  messageLocationY: '', // 消息内容位置y
  roleLocationX: '100', // 角色位置x
  roleLocationY: '600', // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2" ,// 播放完之后状态
  scale: 100, //缩放比例  1-500
  autoNext: "1", // 是否自动播放下一条对话框
  hiddenStatus: "1", // 播放完是否应藏的状态
}

const multyAnimationInitData = [
  {
    roleLocationX: '', // 角色位置x
    roleLocationY: '', // 角色位置y
    roleImg: "", // 角色图片,
    scale: 100, //缩放比例  1-500
  },
]

var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    tImgX: "",
    tImgY: "",
    tg: [
      {
        title: "",
        content: "",
      },
    ],
    level: {
      high: [
        {
          title: "",
          content: "",
        },
      ],
      low: [
        {
          title: "",
          content: "",
        },
      ],
    },
    source: {
      options: [
        {
          audio: "",
          // pos: "",
          x:"",
          y:"",
          audioSize: 0,
        },
      ],
      dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
      multyAnimation:JSON.parse(JSON.stringify(multyAnimationInitData))
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
    },
    avatarUpWallData: avatarUpWallData(),
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
};

$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if(!Data.configData.tImg){
          Data.configData.tImg = '';
      }
      if(!Data.configData.tImgX){
          Data.configData.tImgX = 1340
      }
      if(!Data.configData.tImgY){
          Data.configData.tImgY = 15
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "-1", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }

      // todo 多Json动画组件初始数据
      if (!Data.configData.source.multyAnimation || !Array.isArray(Data.configData.source.multyAnimation)) {
        Data.configData.source.multyAnimation = JSON.parse(JSON.stringify(multyAnimationInitData));
      }
      // todo IP组件初始化数据
      if (!Data.configData.source.dialogs) {
        Data.configData.source.dialogs = JSON.parse(JSON.stringify(dialogsInitData));
      }
      if (!Data.configData.source.dialogs.scale) {
        Data.configData.source.dialogs.scale = 100
      }
      if (!Data.configData.source.dialogs.autoNext) {
        Data.configData.source.dialogs.autoNext = "2"
      }
      if (!Data.configData.source.dialogs.hiddenStatus) {
        Data.configData.source.dialogs.hiddenStatus = "1"
      }
      if (Data.configData.source.dialogs.roleLocationX === "" || Data.configData.source.dialogs.roleLocationX === undefined) {
        Data.configData.source.dialogs.roleLocationX = "100"
      }
      if (Data.configData.source.dialogs.roleLocationY === "" || Data.configData.source.dialogs.roleLocationY === undefined) {
          Data.configData.source.dialogs.roleLocationY = "600"
      }
      // todo 网格切换坐标后，点位的初始值
      if (Data.configData.source.options.length > 0) {
        const options = Data.configData.source.options
        for (let i = 0; i < options.length; i++) {
          if (options[i].x === undefined) {
            options[i].x = 0
          }
          if (options[i].y === undefined) {
            options[i].y = 0
          }
        }
      }
      initializeAvatarUpWall(Data);
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
  el: "#container",
  data: Data,
  methods: {
    //辅助提示图片上传
    tImageUpload: function(e, attr, fileSize) {
      console.log("tImageUpload",e)
      var file = e.target.files[0],
          size = file.size,
          naturalWidth = -1,
          naturalHeight = -1,
          that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
          alert("您上传的图片大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
          return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function() {
          naturalWidth = img.naturalWidth;
          naturalHeight = img.naturalHeight;
          var check = that.tImgCheck(e.target, {
              height: naturalHeight,
              width: naturalWidth
          }, item, attr);
          if (check) {
              that.postData(file, item, attr);
              img = null;
          } else {
              img = null;
          }
      }
      var reader = new FileReader();
      reader.onload = function(evt) {
          img.src = evt.target.result;
      }
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function(input, data, item, attr) {
        let dom = $(input),
            size = dom.attr("size").split(",");
        if (size == "") return true;
        let checkSize = size.some(function(item, idx) {
            let _size = item.split("*"),
                width = _size[0],
                height = _size[1];
            if (width == data.width && (height+1) > data.height) {
                return true;
            }
            return false;
        });
        if (!checkSize) {
            item[attr] = "";
            alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width+"*"+data.height);
        }
        return checkSize;
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height == data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
        e.target.value = "";
      }
      return checkSize;
    },
    validate: function () {
      // 验证 IP组件，如果传了json文件，就需要设置对话
      const dialogs = this.configData.source.dialogs;
      if (dialogs.roleImg) {
        for (let index = 0; index < dialogs.messages.length; index++) {
          const item = dialogs.messages[index]
          const { text, audio } = item
          if(!text || !audio) {
            return alert("请上传对话内容")
          }
        }
      }
      var options = this.configData.source.options;
      var num = 0;
      var reg = /^[1-9]\d*$/;
      var outSpec = 0;
      var totalAudioSize = 0;
      // if(this.configData.bg==''){
      //     alert('背景图片不能为空')
      //     return
      // }
      for (var i = 0; i < options.length; i++) {
        var isNumber = reg.test(options[i].pos);
        // if (!isNumber || options[i].pos > 50 || options[i].pos == '') {
        //     alert('请选择1~50范围不为空的正整数')
        //     return
        // }
        // if (options[i].audio == '') {
        //     alert('每个选项得音频不能为空')
        //     return
        // }
        if(!options[i].audio) {
          alert('每个选项得音频不能为空')
          return
        }
        if (options[i].audioSize > 120) {
          outSpec++;
        }
        totalAudioSize += options[i].audioSize;
      }
      if (outSpec >= 2) {
        alert("最多只能有一个音频大于120KB");
        return;
      }
      // if (totalAudioSize > 750) {
      //   alert("所有音频资源的大小总和应≤750KB");
      //   return;
      // }
      return true;
    },
    onSend: function () {
      //

      var data = this.configData;
      avatarUpWallSend(data);
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == "-2") {
        //其他
        data.log.teachTime =
          data.log_editor.TeachTimeOtherM * 60 +
          data.log_editor.TeachTimeOtherS +
          "";
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长");
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther;
      }
      var val = this.validate();

      if (val === true) {
        var _data = JSON.stringify(data);
        $.ajax({
          url: domain + "content?_method=put",
          type: "POST",
          data: {
            content: _data,
          },
          success: function (res) {
            window.parent.postMessage("close", "*");
          },
          error: function (err) {
            console.log(err);
          },
        });
      } else {
        // alert("带有“*”号为必填项！");
      }
    },
    postData: function (file, item, attr) {
      var FILE = "file";
      var oldImg = item[attr];
      var data = new FormData();
      var _this = this;
      data.append("file", file);
      if (oldImg != "") {
        data.append("key", oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: "post",
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          console.log(res.data.key);
          var options = _this.configData.source.options;

          item[attr] = domain + res.data.key;
          console.log(Data.configData);
        },
        error: function (err) {
          console.log(err);
        },
      });
    },
    studyAudioUpload: function (e, item, index, attr, fileSize) {
      //校验规则
      //var _type = this.rules.audio.sources.type;

      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      // if (!_type.test(type)) {
      //     alert("您上传的文件类型错误，请检查后再上传！");
      //     return;
      // }

      this.checkTotalAudio(index, (size / 1024).toFixed(2)); //存储所有音频的文件大小

      if ((size / 1024).toFixed(2) > 500) {
        console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      } else {
        console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      }
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error(
          "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
        );
        alert(
          "您上传的声音大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      this.postData(file, item, attr);
    },
    checkTotalAudio: function (index, size) {
      var options = this.configData.source.options;
      for (var i = 0; i < options.length; i++) {
        if (i == index) {
          options[i].audioSize = size;
        }
      }
    },
    addSele: function () {
      this.configData.source.options.push({
        audio: "",
        // pos: "",
        x:0,
        y:0,
        audioSize: 0,
      });
    },
    delSele: function (item) {
      this.configData.source.cardList.remove(item);
    },
    addTg: function (item) {
      this.configData.tg.push({ title: "", content: "" });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    delOption: function (item) {
      this.configData.source.options.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({ title: "", content: "" });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: "", content: "" });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    delPrew: function (item) {
      item.image = "";
    },
    // 添加对话框
    addDialog: function() {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: ""
      });
    },
    // 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },
    // 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },
    // 添加动效
    addAnimation: function() {
      this.configData.source.multyAnimation.push({
        roleLocationX: '', // 角色位置x
        roleLocationY: '', // 角色位置y
        roleImg: "", // 角色图片,
        scale: 100, //缩放比例  1-500
      });
    },
    // 删除动效
    delAnimation: function (item) {
      this.configData.source.multyAnimation.remove(item);
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      reader.readAsText(file);
    },

    //ip组件音频上传
    audioUpload: function(e, item, attr, fileSize) {
      console.log("audioUpload",item)
        //获取到的内容数据
        var file = e.target.files[0],
            type = file.type,
            size = file.size,
            name = file.name,
            path = e.target.value,
            that = this;
        if ((size / 1024).toFixed(2) > fileSize) {
            console.error("您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB");
            alert("您上传的音频大小为：" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "KB上限，请检查后上传！");
            return;
        }
        var url = URL.createObjectURL(file); //获取录音时长
        var audioElement = new Audio(url);
        var duration;
        audioElement.addEventListener("loadedmetadata", function(_event) {
            duration = audioElement.duration ? audioElement.duration : '';
            var check = that.sourceAudioCheck(e.target, {
                duration: duration,
            });
            if (check) {
                item[attr] = "./form/img/loading.jpg";
                that.postData(file, item, attr);
                audioElement = null;
            } else {
                audioElement = null;
            }
        });
    },
    //音频长度校检
    sourceAudioCheck: function(input, data) {
        let dom = $(input),
            time = dom.attr("time");
        if (time == "" || time == undefined || data.duration == '') return true;
        let checkSize = false;
        if (data.duration <= time) {
            checkSize = true;
        } else {
            alert(`您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`);
        }
        return checkSize;
    },
    teacherChange: function(data) {
      teacherChange(data);
    },
    studentChange: function(data) {
      studentChange(data);
    },
  },
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
