<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>SYMS0001FT_声音魔术FT</title>
  <link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>" />
  <script src="./form/js/jquery-2.1.1.min.js"></script>
  <script src="./form/js/vue.min.js"></script>
</head>

<body>
  <div id="container">
    <div class="edit-form">
      <h3 class="module-title">SYMS0001FT_声音魔术FT</h3>

      <% include ./src/common/template/common_head %>
        <!-- IP对话组件 -->
        <% include ./src/common/template/multyDialog/form %>
          <!-- 交互提示标签 -->
          <% include ./src/common/template/dynamicInstruction/form.ejs %>

            <!-- 内容设置 -->
            <div class="c-group syms">
              <div class="c-title">内容设置</div>
              <div class="c-area upload img-upload">
                <ul>
                  <li v-for="(item,index) in configData.source.options">
                    <div class="c-well">
                      <!-- 内容图片 -->
                      <div class="field-wrap">
                        <div class="add-field-content">
                          <label class="field-label" for="">第{{ index + 1 }}轮</label>
                          <span class="dele-tg-btn" v-on:click="delOption(item)"
                            v-show="configData.source.options.length>=2"></span>
                        </div>
                        <!-- 初始图片 -->
                        <div class="field-wrap">
                          <label class="field-label" style="width: 100px;">初始图片{{ index + 1 }}<em>*</em></label>
                          <select class="select-c" id="teachTime" v-model="item.initialImgOption"
                            @change="initialImgChange(item)" v-if="index >= 1">
                            <option name="optive" value="1">使用上一轮结束图片</option>
                            <option name="optive" value="2">新图片</option>
                          </select>
                          <label :for="'content-pic-one-'+index"
                            v-if="(!item.initialImg && item.initialImgOption == 2 && index != 0) || (!item.initialImg && index == 0)">
                            <label :for="'content-pic-one-'+index" class="btn btn-show upload">上传图片</label>
                          </label>

                          <label :for="'content-pic-one-'+index" class="btn upload re-upload"
                            v-if="item.initialImg!=''?true:false">重新上传</label>
                          <div class="audio-tips">
                            <label>
                              <span><em>JPG、PNG、json格式，大小小于等于240KB</em></span>
                            </label>
                          </div>
                          <input type="file" v-bind:key="Date.now()" class="btn-file" size=""
                            accept=".jpg,.png,.jpeg,.json" isKey="1" :id="'content-pic-one-'+index"
                            @change="imageUpload($event,item,'initialImg',240)" />
                          <div class="img-preview" v-if="item.initialImg">
                            <img v-bind:src="item.initialImg" alt="" />
                            <div class="img-tools">
                              <span class="btn btn-delete" v-on:click="item.initialImg=''">删除</span>
                            </div>
                          </div>
                        </div>
                        <div class="rules-content" style="padding-left: 0px;">
                          <!-- <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label> -->
                          <label class="rules-field-label">X：</label>
                          <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                            v-model="item.initialImgPositionX" placeholder="请输入" />
                          <label class="rules-field-label">Y：</label>
                          <input type="number" class="rules-input-txt" style="margin: 0 5px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                            v-model="item.initialImgPositionY" placeholder="请输入" />
                          <label class="rules-field-label"><em>数字，0<=x<=1920，0<=y<=1080</em></label>
                        </div>
                        <br>
                        <div class="field-wrap">
                          <label class="field-label" style="width: 100px;">循环次数<em>*</em></label>
                          <select class="select-c" id="teachTime" v-model="item.initialImgCycle">
                            <option name="optive" value="1">循环</option>
                            <option name="optive" value="2">循环一次</option>
                          </select>
                        </div>
                        <br>
                        <!-- 内容图片 -->
                        <div class="field-wrap">
                          <label class="field-label" style="width: 100px;">内容图片{{ index + 1 }}</label>
                          <select class="select-c" id="teachTime" v-model="item.contentImgOption"
                            @change="contentImgChange(item)" v-if="index >= 1">
                            <option name="optive" value="1">使用上一轮内容图片</option>
                            <option name="optive" value="2">新图片</option>
                          </select>

                          <label :for="'content-pic-two-'+index"
                            v-if="(!item.contentImg && item.contentImgOption == 2 && index != 0) || (!item.contentImg && index == 0)">
                            <label :for="'content-pic-two-'+index" class="btn btn-show upload">上传图片</label>
                          </label>
                          <label :for="'content-pic-two-'+index" class="btn upload re-upload"
                            v-if="item.contentImg!=''?true:false">重新上传</label>
                          <div class="audio-tips">
                            <label>
                              <span><em>JPG、PNG格式，大小小于等于100KB</em></span>
                            </label>
                          </div>
                          <input type="file" v-bind:key="Date.now()" class="btn-file" size="" accept=".jpg,.png,.jpeg"
                            isKey="1" :id="'content-pic-two-'+index"
                            @change="imageUpload($event,item,'contentImg',100)" />
                          <div class="img-preview" v-if="item.contentImg">
                            <img v-bind:src="item.contentImg" alt="" />
                            <div class="img-tools">
                              <span class="btn btn-delete" v-on:click="item.contentImg=''">删除</span>
                            </div>
                          </div>
                        </div>
                        <div class="rules-content" style="padding-left: 0px;">
                          <!-- <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label> -->
                          <label class="rules-field-label">X：</label>
                          <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                            v-model="item.contentImgPositionX" placeholder="请输入" />
                          <label class="rules-field-label">Y：</label>
                          <input type="number" class="rules-input-txt" style="margin: 0 5px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                            v-model="item.contentImgPositionY" placeholder="请输入" />
                          <label class="rules-field-label"><em>数字，0<=x<=1920，0<=y<=1080</em></label>
                        </div>
                        <br>
                        <div class="field-wrap">
                          <label class="field-label" style="width: 100px">内容音频{{ index + 1 }}</label>
                          <select class="select-c" id="teachTime" v-model="item.audioOption" @change="audioChange(item)"
                            v-if="index >= 1">
                            <option name="optive" value="1">使用上一轮内容音频</option>
                            <option name="optive" value="2">新音频</option>
                          </select>
                          <input type="file" accept=".mp3,.wav" :id="'content-audio-'+index" volume=""
                            v-bind:key="Date.now()" class="btn-file" v-on:change="audioUpload($event,item,'audio',50)">

                          <label :for="'content-audio-'+index"
                            v-if="(!item.audio && item.audioOption == 2 && index != 0) || (!item.audio && index == 0)">
                            <label :for="'content-audio-'+index" class="btn btn-show upload">上传音频</label>
                          </label>
                          <div class="audio-preview" v-show="item.audio">
                            <div class="audio-tools">
                              <p v-show="item.audio">{{item.audio}}</p>
                            </div>
                            <span class="play-btn" v-on:click="play($event)">
                              <audio v-bind:src="item.audio"></audio>
                            </span>
                          </div>
                          <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.audio"
                            @click="item.audio=''">删除</label>
                          <label :for="'content-audio-'+index" class="btn upload re-upload"
                            v-if="item.audio">重新上传</label>
                          <div class="audio-tips">
                            <label>
                              <span><em>支持mp3，wav格式，小于等于50Kb</em></span>
                            </label>
                          </div>
                        </div>

                        <!-- 收音后的图片 -->
                        <div class="field-wrap">
                          <label class="field-label" style="width: 100px;">收音后的图片{{ index + 1 }}<em>*</em></label>
                          <label :for="'content-pic-three-'+index" class="btn btn-show upload"
                            v-if="!item.recordingAudioImg" style="margin-left: 0px;">上传图片</label>
                          <label :for="'content-pic-three-'+index" class="btn upload re-upload"
                            v-if="item.recordingAudioImg!=''?true:false">重新上传</label>
                          <div class="audio-tips">
                            <label>
                              <span><em>JPG、PNG、json格式，大小小于等于240KB</em></span>
                            </label>
                          </div>
                          <input type="file" v-bind:key="Date.now()" class="btn-file" size=""
                            accept=".jpg,.png,.jpeg,.json" isKey="1" :id="'content-pic-three-'+index"
                            @change="imageUpload($event,item,'recordingAudioImg',240)" />
                          <div class="img-preview" v-if="item.recordingAudioImg">
                            <img v-bind:src="item.recordingAudioImg" alt="" />
                            <div class="img-tools">
                              <span class="btn btn-delete" v-on:click="item.recordingAudioImg=''">删除</span>
                            </div>
                          </div>
                        </div>
                        <div class="rules-content" style="padding-left: 0px;">
                          <!-- <label class="rules-field-label" style="margin-right: 10px;">显示位置<em>*</em></label> -->
                          <label class="rules-field-label">X：</label>
                          <input type="number" class="rules-input-txt" style="margin: 0 10px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1920)value=1920;if(value<0)value=0"
                            v-model="item.recordingAudioImgPositionX" placeholder="请输入" />
                          <label class="rules-field-label">Y：</label>
                          <input type="number" class="rules-input-txt" style="margin: 0 5px 0 0px;
                          width: 60px !important;
                          display: inline-block;" oninput="if(value>1080)value=1080;if(value<0)value=0"
                            v-model="item.recordingAudioImgPositionY" placeholder="请输入" />
                          <label class="rules-field-label"><em>数字，0<=x<=1920，0<=y<=1080</em></label>
                        </div>
                        <br>
                        <div class="field-wrap">
                          <label class="field-label" style="width: 100px;">知识点</label>
                          <input type="text" class='c-input-txt' maxlength="100" placeholder="请在此输入知识点"
                            v-model="item.knowledgePoints" style="width: 200px;display: inline-block;">
                        </div>
                      </div>
                      <br>
                    </div>
                  </li>
                </ul>
                <button type="button" class="add-btn"
                  v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            initialImgOption: '1',
        initialImg: '',
        initialImgCycle: '1',
        initialImgPositionX: '',
        initialImgPositionY: '',

        contentImgOption: '1',
        contentImg: '',
        contentImgPositionX: '',
        contentImgPositionY: '',

        audioOption: '1',
        audio: '',

        recordingAudioImg: '',
        recordingAudioImgPositionX: '',
        recordingAudioImgPositionY: '',

        knowledgePoints:''
          })">
                  添加轮次（最多12轮）
                </button>
              </div>
            </div>

            <!-- 头像上墙 -->
            <% include ./src/common/template/avatarUpWall/form %>
              <!-- 正反馈 -->
              <% include ./src/common/template/feedbackAnimation/form %>

                <button class="send-btn" v-on:click="onSend">提交</button>
    </div>
    <div class="edit-show">
      <div class="show-fixed">
        <div class="show-img">
          <img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="" />
        </div>
        <ul class="show-txt">
          <li>图片格式：<em></em>JPG/PNG</li>
          <li>声音格式：<em></em>MP3</li>
          <li>视频格式：<em></em>MP4</li>
          <li>带有“ * ”号为必填项</li>
        </ul>
      </div>
    </div>
  </div>
</body>
<script src="./form/js/form.js?_=<%= Date.now() %>"></script>

</html>
