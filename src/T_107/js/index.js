"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import "../../common/template/multyDialog/index.js";
import "../../common/template/avatarUpWall/index.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量

const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  var knowledgePointsList = configData.source.options.map(function (item) {
    return item.knowledgePoints;
  });
  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      item: configData.source.options.length || 0,
      knowledge: knowledgePointsList,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.SPEAK,
    },
  });
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasDemo: "0", //0 默认值，无提示功能  1 有提示功能
    hasPractice: "0", //0 无授权功能  1  默认值，普通授权模式  2 start授权模式 3 新授权模式
  };
  let classStatus = 0; //未开始上课 0未上课 1开始上课 2开始练习
  if (isSync) {
    classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
  }

  let options = configData.source.options, //卡牌内容
    fruitMoveSpeed = Number(configData.source.fruitMoveSpeed), //卡牌速度
    userType =
      window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师

  let audioJson = null; //音频动画
  let currentIndex = 0;
  let microphoneJson = null;
  let explosionJson = null;
  let recordingAudioImgWidth = 0;
  let recordingAudioImgHeight = 0;

  // 处理options数据，根据规则继承上一个对象的数据
  processOptionsData();
  function processOptionsData() {
    for (let i = 1; i < options.length; i++) {
      const prevOption = options[i - 1];
      const currentOption = options[i];

      // 初始图片继承规则
      if (currentOption.initialImgOption == "1" && prevOption) {
        currentOption.initialImg = prevOption.recordingAudioImg;
      }

      // 内容图片继承规则
      if (currentOption.contentImgOption == "1" && prevOption) {
        currentOption.contentImg = prevOption.contentImg;
      }

      // 音频继承规则
      if (currentOption.audioOption == "1" && prevOption) {
        currentOption.audio = prevOption.audio;
      }
    }
    console.log("数据处理完成:", options);
  }
  // 检查是否为JSON动画
  function isJsonAnimation(path) {
    return typeof path == "string" && path.toLowerCase().endsWith(".json");
  }
  //麦克风动画
  microphoneFun();
  async function microphoneFun() {
    microphoneJson = await lottieAnimations.init(
      microphoneJson,
      "./image/microphone.json",
      "#microphone",
      true
    );
    lottieAnimations.play(microphoneJson);
    microphoneJson.addEventListener("complete", function microphoneAnimation() {
      console.log("麦克风动画结束");
      lottieAnimations.stop(microphoneJson);
      microphone.hide();
    });
  }
  //爆炸动画
  async function explosionFun() {
    explosionJson = await lottieAnimations.init(
      explosionJson,
      "./image/fruit-ribbon.json",
      "#explosion",
      false
    );
    lottieAnimations.play(explosionJson);
    explosionJson.addEventListener("complete", function microphoneAnimation() {
      console.log("爆炸动画结束");
      lottieAnimations.stop(explosionJson);
      lottieAnimations.destroy(explosionJson); //销毁动画
    });
  }

  // 显示当前内容
  function showCurrentContent() {
    const currentOption = options[currentIndex];
    if (!isSync) {
      $(".record-btn").show();
    } else {
      if (window.frameElement.getAttribute("user_type") == USER_TYPE.TEA) {
        $(".record-btn").show();
      }
    }
    // 清空图片容器
    $(".image-container").empty();
    $(".speaker-dn").hide();

    // 设置初始图片
    if (currentOption.initialImg) {
      if (isJsonAnimation(currentOption.initialImg)) {
        // 为JSON动画创建容器
        const initialImg = $('<div class="initial-img"></div>');
        initialImg.attr("id", "initial-" + currentIndex);
        // 获取动画尺寸并应用到容器
        $.getJSON(currentOption.initialImg, function (data) {
          initialImg
            .css({
              width: data.w / 100 + "rem",
              height: data.h / 100 + "rem",
              left: currentOption.initialImgPositionX / 100 + "rem",
              top: currentOption.initialImgPositionY / 100 + "rem",
            })
            .show();
        });
        $(".image-container").append(initialImg);
        let initialImgCycleStatus = true;
        if (currentOption.initialImgCycle == "2") {
          initialImgCycleStatus = false;
        } else {
          initialImgCycleStatus = true;
        }
        // 异步加载动画
        setTimeout(async () => {
          const animInitialImg = await lottieAnimations.init(
            animInitialImg,
            currentOption.initialImg,
            "#initial-" + currentIndex,
            initialImgCycleStatus
          );
          if (initialImgCycleStatus == false) {
            animInitialImg.addEventListener(
              "data_ready",
              function microphoneAnimation() {
                let frameRate = animInitialImg.frameRate; // 帧率
                let totalFrames = animInitialImg.totalFrames; // 总帧数
                setTimeout(() => {
                  lottieAnimations.stop(animInitialImg);
                }, (totalFrames / frameRate).toFixed(2) * 1000);
              }
            );
          }
          // 播放一次动画
          lottieAnimations.play(animInitialImg);
        }, 100);
      } else {
        // 创建初始图片元素并添加index属性
        const initialImg = $("<img>", {
          class: "initial-img",
          src: currentOption.initialImg,
          alt: "初始图片",
          "data-index": currentIndex,
        });
        initialImg.hide();
        initialImg.on("load", function () {
          initialImg
            .css({
              width: this.naturalWidth / 100 + "rem",
              height: this.naturalHeight / 100 + "rem",
              left: currentOption.initialImgPositionX / 100 + "rem",
              top: currentOption.initialImgPositionY / 100 + "rem",
            })
            .show();
        });
        // 添加到容器
        $(".image-container").append(initialImg);
      }
    }

    // 设置内容图片
    if (currentOption.contentImg) {
      if (isJsonAnimation(currentOption.contentImg)) {
        // 为JSON动画创建容器
        const contentImg = $('<div class="content-img"></div>');
        contentImg.attr("id", "content-" + currentIndex);
        // 获取动画尺寸并应用到容器
        $.getJSON(currentOption.contentImg, function (data) {
          contentImg
            .css({
              width: data.w / 100 + "rem",
              height: data.h / 100 + "rem",
              left: currentOption.contentImgPositionX / 100 + "rem",
              top: currentOption.contentImgPositionY / 100 + "rem",
            })
            .show();
        });
        $(".image-container").append(contentImg);

        // 异步加载动画
        setTimeout(async () => {
          const animContentImg = await lottieAnimations.init(
            animContentImg,
            currentOption.contentImg,
            "#content-" + currentIndex,
            true
          );
          animContentImg.addEventListener(
            "data_ready",
            function microphoneAnimation() {
              let frameRate = animContentImg.frameRate; // 帧率
              let totalFrames = animContentImg.totalFrames; // 总帧数
              setTimeout(() => {
                lottieAnimations.stop(animContentImg);
              }, (totalFrames / frameRate).toFixed(2) * 1000);
            }
          );
          // 播放一次动画
          lottieAnimations.play(animContentImg);
        }, 100);
      } else {
        // 创建内容图片元素并添加index属性
        const contentImg = $("<img>", {
          class: "content-img",
          src: currentOption.contentImg,
          alt: "内容图片",
          "data-index": currentIndex,
        });
        contentImg.hide();
        contentImg.on("load", function () {
          contentImg
            .css({
              width: this.naturalWidth / 100 + "rem",
              height: this.naturalHeight / 100 + "rem",
              left: currentOption.contentImgPositionX / 100 + "rem",
              top: currentOption.contentImgPositionY / 100 + "rem",
            })
            .show();
        });
        // 添加到容器
        $(".image-container").append(contentImg);
      }
    }
    // 设置音频按钮
    if (currentOption.audio) {
      $(".speaker-dn")
        .css({
          left: (currentOption.contentImgPositionX - 120) / 100 + "rem",
          top: currentOption.contentImgPositionY / 100 + "rem",
        })
        .show();
    } else {
      $(".speaker-dn").hide();
    }
    // 更新音频源
    $(".speaker-audio").attr("src", currentOption.audio);

    // 显示收音部分，隐藏麦克风部分
    $(".record-img").show();
    if (!isSync) {
      $(".record-btn").show();
    } else {
      if (window.frameElement.getAttribute("user_type") == USER_TYPE.TEA) {
        $(".record-btn").show();
      }
    }
    $(".microphone").hide();
    $(".confirm-btn").hide();
  }

  // 播放音频
  $(".speaker").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    const currentOption = options[currentIndex];
    if (currentOption.audio) {
      if (!isSync) {
        $(this).trigger("speakerBtnClick");
        return;
      }
      SDK.bindSyncEvt({
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "speakerBtnClick",
        recoveryMode: "1",
      });
    }
  });

  $(".speaker").on("speakerBtnClick", function () {
    console.log("speakerBtnClick");
    $(".speaker-json").css("display", "block");
    $(".speaker").css({
      "background-image": "url('./image/laba.png')",
      display: "none",
    });
    SDK.playRudio({
      index: $(".speaker-audio")[0],
      syncName: $(".speaker-audio").attr("data-syncaudio"),
    });
    $(".speaker-audio").one("ended", () => {
      console.log("音频结束");
      $(".speaker").css({
        "background-image": "url('./image/laba.png')",
        display: "block",
      });
      $(".speaker-json").css("display", "none");
    });
    SDK.setEventLock();
  });

  // 播放音频事件已在创建按钮时绑定
  // 点击收音按钮
  $(".record-btn").on("click touchstart", function (e) {
    console.log("recordBtnClick");
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("sycnRecordBtnClick");
      return;
    }
    SDK.reportTrackData({
      action: "CK_FT_INTERACTION_STARTBUTTON",
      teaData: {},
    });
    SDK.bindSyncEvt({
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "sycnRecordBtnClick",
      recoveryMode: "1",
    });
  });
  $(".record-btn").on("sycnRecordBtnClick", function () {
    console.log("sycnRecordBtnClick");
    // const currentOption = options[currentIndex];

    // 隐藏收音部分，显示麦克风部分
    $(".record-img").hide();
    $(".record-btn").hide();
    $(".microphone").show();
    if (!isSync) {
      $(".confirm-btn").show();
    } else {
      if (window.frameElement.getAttribute("user_type") == USER_TYPE.TEA) {
        $(".confirm-btn").show();
      }
    }
    SDK.setEventLock();
  });

  // 点击确认按钮
  $(".confirm-btn").on("click touchstart", function (e) {
    console.log("confirmBtnclick");
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("sycnConfirmBtnClick");
      return;
    }
    SDK.reportTrackData(
      {
        action: "CK_FT_INTERACTION_SPOKEBUTTON",
        teaData: {
          roundid: currentIndex + 1,
          knowledge: options[currentIndex].knowledgePoints,
        },
      },
      USER_TYPE.TEA
    );
    SDK.bindSyncEvt({
      index: $(e.currentTarget).data("syncactions"),
      eventType: "click",
      method: "event",
      syncName: "sycnConfirmBtnClick",
      recoveryMode: "1",
    });
  });

  $(".confirm-btn").on("sycnConfirmBtnClick", function () {
    console.log("sycnConfirmBtnClick");
    const currentOption = options[currentIndex];

    $(".speaker-dn").hide();
    // 隐藏麦克风部分
    $(".microphone").hide();
    $(".confirm-btn").hide();

    // 创建并显示收音后图片
    if (isJsonAnimation(currentOption.recordingAudioImg)) {
      // 为JSON动画创建容器
      const recordingImg = $('<div class="recording-audio-img"></div>');
      recordingImg.attr("id", "recording-" + currentIndex);
      // 获取动画尺寸并应用到容器
      $.getJSON(currentOption.recordingAudioImg, function (data) {
        recordingAudioImgWidth = data.w;
        recordingAudioImgHeight = data.h;
        recordingImg
          .css({
            width: data.w / 100 + "rem",
            height: data.h / 100 + "rem",
            left: currentOption.recordingAudioImgPositionX / 100 + "rem",
            top: currentOption.recordingAudioImgPositionY / 100 + "rem",
          })
          .show();
      });
      $(".image-container").append(recordingImg);
      // 异步加载动画
      setTimeout(async () => {
        const animRecordingAudioImg = await lottieAnimations.init(
          animRecordingAudioImg,
          currentOption.recordingAudioImg,
          "#recording-" + currentIndex,
          false
        );
        // 清除初始图片和内容图片
        setTimeout(() => {
          $(".image-container").find(".initial-img").remove();
        }, 200);
        // 播放一次动画
        lottieAnimations.play(animRecordingAudioImg);
        $(".explosion").css({
          left:
            (currentOption.recordingAudioImgPositionX -
              recordingAudioImgWidth / 2) /
              100 +
            "rem",
          top:
            (currentOption.recordingAudioImgPositionY -
              recordingAudioImgHeight / 2) /
              100 +
            "rem",
          width: (recordingAudioImgWidth * 2) / 100 + "rem",
          height: (recordingAudioImgHeight * 2) / 100 + "rem",
        });

        // 触发彩带效果
        explosionFun();

        animRecordingAudioImg.addEventListener(
          "complete",
          function microphoneAnimation() {
            console.log("点击后动画结束");
            completeCallback();
            lottieAnimations.stop(animRecordingAudioImg);
            lottieAnimations.destroy(animRecordingAudioImg); //销毁动画
            SDK.setEventLock();
          }
        );
      }, 100);
    } else {
      const recordingImg = $("<img>", {
        class: "recording-audio-img",
        src: currentOption.recordingAudioImg,
        alt: "收音后图片",
        "data-index": currentIndex,
      });
      recordingImg.hide();
      recordingImg.on("load", function () {
        // 清除初始图片和内容图片
        setTimeout(() => {
          $(".image-container").find(".initial-img").remove();
        }, 200);
        recordingAudioImgWidth = this.naturalWidth;
        recordingAudioImgHeight = this.naturalHeight;
        recordingImg
          .css({
            width: this.naturalWidth / 100 + "rem",
            height: this.naturalHeight / 100 + "rem",
            left: currentOption.recordingAudioImgPositionX / 100 + "rem",
            top: currentOption.recordingAudioImgPositionY / 100 + "rem",
          })
          .show();
        $(".explosion").css({
          left:
            (currentOption.recordingAudioImgPositionX -
              recordingAudioImgWidth / 2) /
              100 +
            "rem",
          top:
            (currentOption.recordingAudioImgPositionY -
              recordingAudioImgHeight / 2) /
              100 +
            "rem",
          width: (recordingAudioImgWidth * 2) / 100 + "rem",
          height: (recordingAudioImgHeight * 2) / 100 + "rem",
        });

        // 触发彩带效果
        explosionFun();
      });

      // 添加到容器
      $(".image-container").append(recordingImg);

      setTimeout(() => {
        completeCallback();
        SDK.setEventLock();
      }, 2000);
    }
  });

  function completeCallback() {
    // 清空图片容器
    $(".image-container").empty();
    // 切换到下一组数据
    currentIndex++;

    if (currentIndex < options.length) {
      showCurrentContent();
    } else {
      SDK.reportTrackData({
        action: "CK_FT_INTERACTION_COMPLETE",
        data: {
          result: "success",
        },
      });
      feedback();
    }
  }

  // 初始显示第一组内容
  showCurrentContent();

  //音频动画
  audioFun();
  async function audioFun() {
    audioJson = await lottieAnimations.init(
      audioJson,
      "./image/laba.json",
      "#speaker-json",
      true
    );
    lottieAnimations.play(audioJson);
    audioJson.addEventListener("complete", function microphoneAnimation() {
      console.log("音频动画结束");
      lottieAnimations.stop(audioJson);
    });
  }

  //正反馈动画
  async function feedback() {
    console.log("全部正确结束，执行反馈动画");
    await feedbackAnimation("feedKey1");
  }
});
