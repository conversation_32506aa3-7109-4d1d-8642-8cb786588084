@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import '../../common/template/multyDialog/style.scss';
@import "../../common/template/disconnectRecover/style.scss";
@import "../../common/template/avatarUpWall/style.scss";

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;

  .desc {
    top: 0.6rem;
  }

  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.container {
  background-size: auto 100%;
  position: relative;

  .content-main {
    overflow: hidden;
    height: 100%;
    width: 100%;
    position: relative;

    .image-container {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .initial-img,
    .content-img,
    .recording-audio-img {
      position: absolute;
      transition: all 0.3s ease;
      /* 移除固定尺寸，使用图片原始尺寸 */
    }

    .record-img {
      position: absolute;
      width: 2.1rem;
      height: 1.46rem;
      bottom: 2.09rem;
      left: 8.55rem;
      background-image: url('../image/microphone.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
    }

    .record-btn {
      position: absolute;
      width: 5.8rem;
      height: 1rem;
      top: 8.82rem;
      left: 6.69rem;
      background-image: url('../image/startOralTest.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      cursor: pointer;
      display: none;
    }

    .microphone {
      position: absolute;
      width: 2.8rem;
      height: 1.8rem;
      bottom: 1.9rem;
      left: 8.16rem;
      display: none;
    }

    .confirm-btn {
      position: absolute;
      width: 1.2rem;
      height: 1.2rem;
      bottom: 2rem;
      right: 6.6rem;
      background-image: url('../image/correct.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      cursor: pointer;
      display: none;
    }

    .explosion {
      position: absolute;
      // width: 100%;
      // height: 100%;
      pointer-events: none;
      z-index: 100;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.1);
      }

      100% {
        transform: scale(1);
      }
    }
    .speaker-dn{
      position: absolute;
      // top: 0.77rem;
      // left: 5.52rem;
      display: none;
    }
    .speaker {
      // position: absolute;
      width: 0.83rem;
      height: 0.83rem;
      // top: 0.77rem;
      // left: 5.52rem;
      background-image: url('../image/laba.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      cursor: pointer;
    }

    .speaker-json {
      // position: absolute;
      width: 0.9rem;
      height: 0.9rem;
      // top: 0.7rem;
      // left: 5.48rem;
      display: none;
    }
  }
}
