// var domain = 'http://172.16.0.107:9011/pages/1152/';
import {
  addInstruction,
  validateInstructions,
  removeInstruction,
  getDynamicInstructions,
} from "../../../common/template/dynamicInstruction/form.js";
import {
  feedbackAnimationSend,
  feedbackData,
  initializeFeedback,
  feedBackChange,
} from "../../../common/template/feedbackAnimation/form.js";
import {
  avatarUpWallData,
  avatarUpWallSend,
  teacherChange,
  studentChange,
  initializeAvatarUpWall
} from "../../../common/template/avatarUpWall/form.js";
var domain = "";
var feedbackObjData1 = feedbackData({
  key: "feedKey1",
  name: "整体反馈",
});
// 对话框初始化数据
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: "",
    },
  ],
  messageLocationX: "", // 消息内容位置x
  messageLocationY: "", // 消息内容位置y
  roleLocationX: "100", // 角色位置x
  roleLocationY: "600", // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2", // 播放完之后状态
  scale: 100, //缩放比例  1-500
  autoNext: "1", // 是否自动播放下一条对话框
  hiddenStatus: "1", // 播放完是否应藏的状态
};
var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    tImgX: "",
    tImgY: "",
    instructions: [
      {
        commandId: "-1",
      },
    ],
    tg: [
      {
        title: "",
        content: "",
      },
    ],
    level: {
      high: [
        {
          title: "",
          content: "",
        },
      ],
      low: [
        {
          title: "",
          content: "",
        },
      ],
    },
    source: {
      dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
      optionLength: 12, //选项上限
      options: [
        {
          initialImgOption: "2", //初始图片状态
          initialImg: "", //初始图片
          initialImgCycle: "1", //循环次数
          initialImgPositionX: "", //初始图片x坐标
          initialImgPositionY: "", //初始图片y坐标

          contentImgOption: "2", //内容图片状态
          contentImg: "", //内容图片
          contentImgPositionX: "", //内容图片x坐标
          contentImgPositionY: "", //内容图片y坐标

          audioOption: "2", //内容音频状态
          audio: "", //内容音频

          recordingAudioImg: "", //收音后图片
          recordingAudioImgPositionX: "", //收音后图片x坐标
          recordingAudioImgPositionY: "", //收音后图片y坐标
          knowledgePoints: "", //知识点
        },
      ],
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: "2", //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
    },
    feedbackLists: [...feedbackObjData1],
    avatarUpWallData: avatarUpWallData(),
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [],
};
Data.configData.feedbackLists.push(feedbackObjData1);

$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      // todo IP组件初始化数据
      if (!Data.configData.source.dialogs) {
        Data.configData.source.dialogs = JSON.parse(
          JSON.stringify(dialogsInitData)
        );
      }
      if (!Data.configData.source.dialogs.scale) {
        Data.configData.source.dialogs.scale = 100;
      }
      if (!Data.configData.source.dialogs.autoNext) {
        Data.configData.source.dialogs.autoNext = "2";
      }
      if (!Data.configData.source.dialogs.hiddenStatus) {
        Data.configData.source.dialogs.hiddenStatus = "1";
      }
      if (
        Data.configData.source.dialogs.roleLocationX === "" ||
        Data.configData.source.dialogs.roleLocationX === undefined
      ) {
        Data.configData.source.dialogs.roleLocationX = "100";
      }
      if (
        Data.configData.source.dialogs.roleLocationY === "" ||
        Data.configData.source.dialogs.roleLocationY === undefined
      ) {
        Data.configData.source.dialogs.roleLocationY = "600";
      }
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [
            {
              title: "",
              content: "",
            },
          ],
          low: [
            {
              title: "",
              content: "",
            },
          ],
        };
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: "2", //-1 请选择  0无题目  1主观判断  2客观判断
        };
        Data.configData.log_editor = {
          isTeachTimeOther: "-1", //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: "", //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: "", //当建议教学时长选择了其他时  用户填写的秒
        };
      }
      if (!Data.configData.instructions) {
        Data.configData.instructions = [
          {
            commandId: "-1",
          },
        ];
      }
      initializeFeedback(Data);
      initializeAvatarUpWall(Data);
    }
  },
  error: function (res) {
    console.log(res);
  },
});

new Vue({
  el: "#container",
  data: Data,
  mounted: function () {
    this.getDynamicInstructions();
    $(".c-group ul li:first-child .upload").css("margin-left", "0px");
  },
  watch: {},
  methods: {
    getDynamicInstructions: function () {
      var that = this;
      getDynamicInstructions(function (res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function () {
      addInstruction(this.configData);
    },
    removeInstruction: function (index) {
      removeInstruction(index, this.configData);
    },
    validateInstructions: function () {
      return validateInstructions(this.configData);
    },
    feedBackChange(item) {
      feedBackChange(item);
    },
    // feedback 上传
    feedbackUpload: function (e, item, attr, fileSize) {
      console.log(e, item, attr, fileSize);
      const file = e.target.files[0];
      if (file.type === "image/png") {
        this.imageUpload(e, item, attr, fileSize);
      } else {
        this.lottieUpload(e, item, attr, fileSize);
      }
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
        } else {
        }
      };
      reader.readAsText(file);
    },
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e);
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height + 1 > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
            size.join("或") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
      }
      return checkSize;
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      // if ($(e.target).attr("accept")) {
      //   var acceptList = $(e.target).attr("accept").split(",");
      //   var fileAccept = "." + file.type.split("/")[1];
      //   var index = acceptList.indexOf(fileAccept);
      //   if (index === -1) {
      //     alert(
      //       "您上传的图片格式为" +
      //         fileAccept +
      //         ", 不符合上传图片格式要求，请检查后上传！"
      //     );
      //     return;
      //   }
      // }

      if (file.type === "application/json") {
        this.lottieUpload(e, item, attr, fileSize);
      }
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(","),
        isKey = dom.attr("isKey");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (isKey == "1") {
          if (data.width <= width && data.height <= height) {
            return true;
          } else {
            return false;
          }
        } else {
          if (data.width == width && data.height == height) {
            return true;
          } else {
            return false;
          }
        }
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：小于等于" +
            size.join("*") +
            ", 但上传图片尺寸为：" +
            data.width +
            "*" +
            data.height
        );
      }
      return checkSize;
    },
    validate: function () {
      var data = this.configData.source;
      var check = true;
      const optionsData = new Map(
        data.options.map((item, index) => [index, item])
      );
      if (!this.configData.bg) {
        alert("请上传背景图片");
        return;
      }
      for (let [key, item] of optionsData) {
        key++;
        console.log(key);
        if (key == 1) {
          if (!item.initialImg) {
            alert("请上传选项" + key + "初始图片");
            return;
          }
        } else {
          if (item.initialImgOption == "2") {
            if (!item.initialImg) {
              alert("请上传选项" + key + "初始图片");
              return;
            }
          }
        }

        if (item.initialImgPositionX === "") {
          alert("请上传选项" + key + "初始图片位置X");
          return;
        }
        if (item.initialImgPositionY === "") {
          alert("请上传选项" + key + "初始图片位置Y");
          return;
        }
        if (!item.initialImgCycle) {
          alert("请选择选项" + key + "初始图片循环次数");
          return;
        }
        if (item.contentImg || item.contentImgOption == "1") {
          console.log(item.contentImgPositionX,'499');
          if (item.contentImgPositionX === "") {
            alert("请上传选项" + key + "内容图片位置X");
            return;
          }
          if (item.contentImgPositionY === "") {
            alert("请上传选项" + key + "内容图片位置Y");
            return;
          }
        }

        if (!item.recordingAudioImg) {
          alert("请上传选项" + key + "收音后的图片");
          return;
        }
        if (item.recordingAudioImgPositionX === "") {
          alert("请上传选项" + key + "收音后的图片位置X");
          return;
        }
        if (item.recordingAudioImgPositionY === "") {
          alert("请上传选项" + key + "收音后的图片位置Y");
          return;
        }
        if (!item.knowledgePoints) {
          alert("请填写" + key + "知识点内容");
          return;
        }
        // if (item.knowledgePoints != "") {
        //   if (
        //     !/^[\u4e00-\u9fa5\u3400-\u4dbf\uf900-\ufaff]+$/.test(
        //       item.knowledgePoints
        //     )
        //   ) {
        //     alert("请检查" + key + "知识点内容是否符合只能输入文本的规范");
        //     return;
        //   }
        // }
      }

      if (data.optionLength > 12) {
        alert("上传轮次不超过12轮");
        return;
      }
      return check;
    },
    onSend: function () {
      var data = this.configData;
      let feedbackStatus = feedbackAnimationSend(data);
      if(!feedbackStatus){
        return;
      }
      avatarUpWallSend(data);
      //计算“建议教学时长”
      if (data.log_editor.isTeachTimeOther == "-2") {
        //其他
        data.log.teachTime =
          data.log_editor.TeachTimeOtherM * 60 +
          data.log_editor.TeachTimeOtherS +
          "";
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长");
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther;
      }
      var _data = JSON.stringify(data);
      var val = this.validate();
      console.log(data);
      if (val === true && this.validateInstructions()) {
        $.ajax({
          url: domain + "content?_method=put",
          type: "POST",
          data: {
            content: _data,
          },
          success: function (res) {
            console.log(res);
            window.parent.postMessage("close", "*");
          },
          error: function (err) {
            console.log(err);
          },
        });
      } else {
        // alert("带“*”为必填项");
      }
    },
    postData: function (file, item, attr) {
      var FILE = "file";
      var bg = arguments.length > 2 ? arguments[2] : null;
      var oldImg = item[attr];
      var data = new FormData();
      data.append("file", file);
      if (oldImg != "") {
        data.append("key", oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: "post",
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          item[attr] = domain + res.data.key;
        },
        error: function (err) {
          console.log(err);
        },
      });
    },
    delPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.pic = "";
      }
    },
    //ip组件音频上传
    audioUpload: function (e, item, attr, fileSize) {
      if (!fileSize && $(e.target).attr("volume")) {
        fileSize = $(e.target).attr("volume");
      }
      console.log("audioUpload", item);
      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        console.error(
          "您上传的音频存储大小为：" + (size / 1024).toFixed(2) + "KB"
        );
        alert(
          "您上传的音频大小为：" +
            (size / 1024).toFixed(2) +
            "KB, 超过" +
            fileSize +
            "KB上限，请检查后上传！"
        );
        return;
      }
      var url = URL.createObjectURL(file); //获取录音时长
      var audioElement = new Audio(url);
      var duration;
      audioElement.addEventListener("loadedmetadata", function (_event) {
        duration = audioElement.duration ? audioElement.duration : "";
        var check = that.sourceAudioCheck(e.target, {
          duration: duration,
        });
        if (check) {
          item[attr] = "./form/img/loading.jpg";
          that.postData(file, item, attr);
          audioElement = null;
        } else {
          audioElement = null;
        }
      });
    },
    //音频长度校检
    sourceAudioCheck: function (input, data) {
      let dom = $(input),
        time = dom.attr("time");
      if (time == "" || time == undefined || data.duration == "") return true;
      let checkSize = false;
      if (data.duration <= time) {
        checkSize = true;
      } else {
        alert(
          `您上传的音频时长为${data.duration}秒，超过${time}秒上限，请检查后上传！`
        );
      }
      return checkSize;
    },
    // 添加对话框
    addDialog: function () {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: "",
      });
    },
    // 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },
    // 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },
    addTg: function (item) {
      this.configData.tg.push({
        title: "",
        content: "",
      });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    addH: function () {
      this.configData.level.high.push({ title: "", content: "" });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: "", content: "" });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    addOption: function (item) {
      if (item) {
        this.configData.source.options.push(item);
      } else {
        this.configData.source.options.push("");
      }
    },
    delOption: function (item) {
      this.configData.source.options.remove(item);
    },
    initialImgChange: function (item) {
      item.initialImg = "";
    },
    contentImgChange: function (item) {
      item.contentImg = "";
    },
    audioChange: function (item) {
      item.audio = "";
    },
    teacherChange: function(data) {
      teacherChange(data);
    },
    studentChange: function(data) {
      studentChange(data);
    },
  },
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
