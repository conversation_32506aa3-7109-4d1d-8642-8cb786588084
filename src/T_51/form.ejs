<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>TRP0001_角色扮演</title>
	<link rel="stylesheet" href="form/css/style.css">
	<script src='form/js/jquery-2.1.1.min.js'></script>
	<script src='form/js/vue.min.js'></script>
</head>

<body>
	<div id="container">
		<div class="edit-form">
			<div class="module-title">TRP0001_角色扮演</div>

			<% include ./src/common/template/common_head %>

			<div class="c-group">
				<div class="c-title">上传角色图</div>
				<div class="c-areAtation img-upload">
					<font>注:图片数量2～4个</font>
				</div>
				<div class="c-area upload img-upload" >
					<div class="c-well" v-for="(item, index) in configData.source.pics">
						<span class="dele-tg-btn" @click="delCards(item)" v-show="configData.source.pics.length>2"></span>
						角色名称：<input type="name" style="height:25px; width:200px" v-model="item.role" maxlength="20" /> <em style="margin-left:30px;">字符：≤20</em>
						<div class="field-wrap">
							<label class="field-label"  for="">图片</label>
							<span class='txt-info'><em>文件大小≤50KB * </em></span> 
							<input type="file"  v-bind:key="Date.now()" class="btn-file" :id="'content-pic-'+index" size="" accept=".gif,.jpg,.jpeg,.png" @change="imageUpload($event,item,'pic',50)">
						</div>

						<div class="field-wrap">
							<label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.pic">上传</label>
							<label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.pic">重新上传</label>
						</div>
						<div class="img-preview" v-if="item.pic">
							<img :src="item.pic" alt=""/>
							<div class="img-tools">
								<span class="btn btn-delete" @click="delPrew(item)">删除</span>
							</div>
						</div>

						<!-- 上传声音 -->
						<div class="field-wrap">
							<label class="field-label"  for="">音效&nbsp;&nbsp;<em>文件大小≤30KB</em></label>
							<div>
								<label :for="'audio-upload-'+index" class="btn btn-show upload" v-if="item.audio==''?true:false">上传</label>
								<label  :for="'audio-upload-'+index" class="btn upload re-upload mar" v-if="item.audio!=''?true:false">重新上传</label>
							</div>
							<div class="audio-preview" v-show="item.audio!=''?true:false">
								<div class="audio-tools">
									<p v-show="item.audio!=''?true:false">{{item.audio}}</p>
								</div>
								<span class="play-btn" v-on:click="play($event)">
									<audio v-bind:src="item.audio"></audio>
								</span>
							</div>
							<span class="btn btn-audio-dele" v-show="item.audio!=''?true:false" v-on:click="item.audio=''">删除</span>
							<input type="file" :id="'audio-upload-'+index" class="btn-file upload" volume="30" accept=".mp3" v-on:change="audioUpload($event,item,'audio')" v-bind:key="Date.now()">
						</div> 
					</div>
					<button v-if="configData.source.pics.length<4" type="button" class="add-tg-btn" @click="addCards" >+</button>	
				</div> 
			</div>
			<button class="send-btn" v-on:click="onSend">提交</button>
		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="form/img/preview.jpg?v=<%=new Date().getTime()%>" alt="">
				</div>
				<ul class="show-txt">
					<li>
						<em>图片格式：</em>JPG/PNG/GIF</li>
					<li>
						<em>声音格式：</em>MP3/WAV</li>
					<li>
						<em>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='form/js/form.js?v=<%=new Date().getTime()%>'></script>

</html>