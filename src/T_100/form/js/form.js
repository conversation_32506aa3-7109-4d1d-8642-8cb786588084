//var domain = 'http://172.16.0.107:9011/pages/159/';
import { addInstruction, validateInstructions, removeInstruction, getDynamicInstructions } from "../../../common/template/dynamicInstruction/form.js";
import {
  feedbackAnimationSend,
  feedbackData,
  initializeFeedback,
  feedBackChange
} from "../../../common/template/feedbackAnimation/form.js";
import {getMemoryForm, setMemoryForm} from "../../../common/template/memory/form.js";
var domain = '';
var feedbackObjData1 = feedbackData({
  key: 'feedKey1',
  name: '整体反馈',
});
const dialogsInitData = {
  // 对话框信息列表
  messages: [
    {
      text: "",
      audio: "",
    },
  ],
  messageLocationX: "", // 消息内容位置x
  messageLocationY: "", // 消息内容位置y
  roleLocationX: "100", // 角色位置x
  roleLocationY: "600", // 角色位置y
  roleImg: "", // 角色图片
  playAfterStauts: "2", // 播放完之后状态
  scale: 100, //缩放比例  1-500
  autoNext: "1", // 是否自动播放下一条对话框
  hiddenStatus: "1", // 播放完是否应藏的状态
};
var Data = {
  configData: {
    bg: "",
    desc: "",
    title: "",
    tImg: "",
    tImgX: "",
    tImgY: "",
    instructions: [{
      commandId: '-1'
    }],
    tg: [{
      title: "",
      content: "",
    }],
    level: {
      high: [{
        title: "",
        content: ""
      }],
      low: [{
        title: "",
        content: "",
      }]
    },
    source: {
      memoryForm: [],
      dialogs: JSON.parse(JSON.stringify(dialogsInitData)),
      //拖拽元素数组
      dragArr: [
        {
          memoryId: '',
          id: '1',//拖拽元素唯一标识
          dragItem: '',//拖拽元素url
          x: 0,//拖拽元素x坐标
          y: 0,//拖拽元素y坐标，
          initialSize: 100,//拖拽元素初始大小
          audio: '',//拖拽元素音频url
          dragNum: '0',//拖拽元素次数 一次0 多次1 无限拖拽2
        }
      ],
      //热区数组
      hotAreaArr: [
        {
          hotAreaItem: '',//热区元素url
          x: 0,//热区元素x坐标
          y: 0,//热区元素y坐标
          audio: '',//热区元素音频url
          correctArr: [],//正确选项数组 存储拖拽元素id
        }
      ]
    },
    // 需上报的埋点
    log: {
      teachPart: -1, //教学环节 -1未选择
      teachTime: -1, // 整理好的教学时长
      tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
    },
    // 供编辑器使用的埋点填写信息
    log_editor: {
      isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
      TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
      TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
    },
    feedbackLists: [
      ...feedbackObjData1,
    ],
  },
  teachInfo: window.teachInfo, //接口获取的教学环节数据
  dynamicInstructions: [], //交互提示标签
  oldDragNum:{
    1: '0'
  }//用于记录拖拽次数变化前的值
};
Data.configData.feedbackLists.push(feedbackObjData1)
$.ajax({
  type: "get",
  url: domain + "content?_method=put",
  async: false,
  success: function (res) {
    if (res.data != "") {
      Data.configData = JSON.parse(res.data);
      if (!Data.configData.tImg) {
        Data.configData.tImg = "";
      }
      if (!Data.configData.tImgX) {
        Data.configData.tImgX = 1340;
      }
      if (!Data.configData.tImgY) {
        Data.configData.tImgY = 15;
      }
      if (Data.configData.source && Data.configData.source.dragArr) {
        Data.configData.source.dragArr.forEach(item => {
          Data.oldDragNum[item.id] = item.dragNum
        })
      }

      // todo IP组件初始化数据
      if (!Data.configData.source.dialogs) {
        Data.configData.source.dialogs = JSON.parse(
          JSON.stringify(dialogsInitData)
        );
      }
      if (!Data.configData.source.dialogs.scale) {
        Data.configData.source.dialogs.scale = 100;
      }
      if (!Data.configData.source.dialogs.autoNext) {
        Data.configData.source.dialogs.autoNext = "2";
      }
      if (!Data.configData.source.dialogs.hiddenStatus) {
        Data.configData.source.dialogs.hiddenStatus = "1";
      }
      if (
        Data.configData.source.dialogs.roleLocationX === "" ||
        Data.configData.source.dialogs.roleLocationX === undefined
      ) {
        Data.configData.source.dialogs.roleLocationX = "100";
      }
      if (
        Data.configData.source.dialogs.roleLocationY === "" ||
        Data.configData.source.dialogs.roleLocationY === undefined
      ) {
        Data.configData.source.dialogs.roleLocationY = "600";
      }
      if (!Data.configData.level) {
        Data.configData.level = {
          high: [{
            title: "",
            content: ""
          }],
          low: [{
            title: "",
            content: "",
          }]
        }
      }
      //老模板未保存log信息，放入默认log
      if (!Data.configData.log) {
        Data.configData.log = {
          teachPart: -1, //教学环节 -1未选择
          teachTime: -1, // 整理好的教学时长
          tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        }
        Data.configData.log_editor = {
          isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
          TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
          TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }
      }
      if (!Data.configData.instructions) {
        Data.configData.instructions = [{
          commandId: '-1'
        }]
      }
      initializeFeedback(Data)
    }
    getMemoryForm(Data)
  },
  error: function (res) {
    console.log(res)
  }
});

new Vue({
  el: '#container',
  data: Data,
  mounted: function () {
    this.getDynamicInstructions();
    this.initMultipleSelect();
  },
  methods: {
    // 获取可用选项（排除已选ID）
    getAvailableOptions: function(currentItem) {
      // 获取所有已被选择的ID列表（排除当前项的ID）
      const selectedIds = this.configData.source.dragArr
        .filter(item => item !== currentItem && item.memoryId !== '')
        .map(item => item.memoryId);

      // 返回未被选中的选项
      if (!this.configData.source.memoryForm) {
        return [];
      }
      return this.configData.source.memoryForm.filter(item =>
        !selectedIds.includes(item.id)
      );
    },
    // 清空记忆ID
    clearMemoryId: function(item) {
      item.memoryId = '';
    },
    // 初始化 multiple-select 插件
    initMultipleSelect: function() {
      const that = this;
      this.$nextTick(() => {
        $('.multiple-select').each(function(idx) {
          const $this = $(this);
          const index = $this.attr('class').match(/multiple-select-(\d+)/)[1];
          const hotArea = that.configData.source.hotAreaArr[index];

          $this.multipleSelect({
            selectAll: true,
            width: 250,
            filter: true,
            filterPlaceholder: '搜索',
            formatAllSelected: function() {
              return $this.multipleSelect('getSelects', 'text').join(', ');
            },
            onClose: function() {
              // 获取所选值并更新 Vue 数据
              const selectedValues = $this.multipleSelect('getSelects');

              // 获取之前的值
              const previousValues = hotArea.correctArr || [];

              // 找出被取消选择的项
              const deselectedItems = previousValues.filter(id => !selectedValues.includes(id));

              // 检查是否存在已在其他热区中使用过的"拖拽一次"的项
              const invalidSelections = [];

              // 验证"拖拽一次"的选项是否已在其他热区中使用
              selectedValues.forEach(dragId => {
                // 获取对应的拖拽项
                const dragItem = that.configData.source.dragArr.find(item => item.id === dragId);
                if (dragItem && dragItem.dragNum === '0') {
                  // 检查该"拖拽一次"的拖拽项是否已在其他热区中使用
                  const isUsedInOtherHotArea = that.configData.source.hotAreaArr.some(area => {
                    // 排除当前正在编辑的热区
                    if (area === hotArea) return false;
                    // 检查是否在其他热区的答案中
                    return area.correctArr && area.correctArr.includes(dragId);
                  });

                  if (isUsedInOtherHotArea) {
                    invalidSelections.push(dragId);
                  }
                }
              });

              // 如果有无效选择，则移除这些选择并提示用户
              if (invalidSelections.length > 0) {
                // 获取拖拽项的索引，用于显示名称
                const invalidNames = invalidSelections.map(id => {
                  const dragIndex = that.configData.source.dragArr.findIndex(item => item.id === id);
                  return `拖拽元素${dragIndex + 1}`;
                }).join('、');

                // 从选择中移除无效项
                const validSelections = selectedValues.filter(id => !invalidSelections.includes(id));

                // 更新热区答案
                that.$set(hotArea, 'correctArr', validSelections);

                // 重新设置选中状态
                $this.multipleSelect('setSelects', validSelections);

                // 提示用户
                alert(`${invalidNames} 已在其他热区中设为答案，设置为"拖拽一次"的拖拽项只能在一个热区中使用。`);
              } else {
                // 没有无效选择，正常更新
                that.$set(hotArea, 'correctArr', selectedValues);
              }

              // 强制刷新 multiple-select 以更新选项状态
              that.$nextTick(() => {
                that.refreshMultipleSelect();
              });
            }
          });

          // 确保选中状态与数据一致
          if (hotArea.correctArr && hotArea.correctArr.length) {
            $this.multipleSelect('setSelects', hotArea.correctArr);
          } else {
            // 如果数据为空，确保清空选中状态
            $this.multipleSelect('setSelects', []);
          }
        });
      });
    },
    getDynamicInstructions: function() {
      var that = this;
      getDynamicInstructions(function(res, newIstructions) {
        that.dynamicInstructions = res;
        that.configData.instructions = newIstructions;
      }, that.configData.instructions);
    },
    addInstruction: function () {
      addInstruction(this.configData);
    },
    removeInstruction: function (index) {
      removeInstruction(index, this.configData);
    },
    validateInstructions: function () {
      return validateInstructions(this.configData);
    },
    // lottie 图片上传
    lottieUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        that = this;
      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      const reader = new FileReader();
      reader.onload = async function (processEvent) {
        const jsonData = JSON.parse(processEvent.target.result);
        // 假设 Lottie 动画 JSON 包含宽度和高度 (常见的是 `w` 和 `h`)
        const naturalWidth = jsonData.w || jsonData.width;
        const naturalHeight = jsonData.h || jsonData.height;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr, 'json');
        } else {
        }
      };
      reader.readAsText(file);
    },
    // 删除对话框音频
    delDialogPrew: function (item, key) {
      if (key) {
        item[key] = "";
      } else {
        item.dialog = "";
      }
    },
    // 添加对话框
    addDialog: function () {
      this.configData.source.dialogs.messages.push({
        text: "",
        audio: "",
      });
    },
    // 删除对话框
    delDialog: function (item) {
      this.configData.source.dialogs.messages.remove(item);
    },
    imageUpload: function (e, item, attr, fileSize) {
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "K上限，请检查后上传！"
        );
        e.target.value = "";
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.sourceImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //ip组件音频上传
    //辅助提示图片上传
    tImageUpload: function (e, attr, fileSize) {
      console.log("tImageUpload", e);
      var file = e.target.files[0],
        size = file.size,
        naturalWidth = -1,
        naturalHeight = -1,
        that = this;
      var item = this.configData;

      if ((size / 1024).toFixed(2) > fileSize) {
        alert(
          "您上传的图片大小为：" +
          (size / 1024).toFixed(2) +
          "KB, 超过" +
          fileSize +
          "KB上限，请检查后上传！"
        );
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      var img = new Image();
      img.onload = function () {
        naturalWidth = img.naturalWidth;
        naturalHeight = img.naturalHeight;
        var check = that.tImgCheck(
          e.target,
          {
            height: naturalHeight,
            width: naturalWidth,
          },
          item,
          attr
        );
        if (check) {
          that.postData(file, item, attr);
          img = null;
        } else {
          img = null;
        }
      };
      var reader = new FileReader();
      reader.onload = function (evt) {
        img.src = evt.target.result;
      };
      reader.readAsDataURL(file, "UTF-8"); //读取文件
    },
    //辅助提示图片大小校检
    tImgCheck: function (input, data, item, attr) {
      let dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      let checkSize = size.some(function (item, idx) {
        let _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width == data.width && height + 1 > data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert(
          "应上传图片大小为：" +
          size.join("或") +
          ", 但上传图片尺寸为：" +
          data.width +
          "*" +
          data.height
        );
      }
      return checkSize;
    },
    feedBackChange(item) {
      feedBackChange(item)
    },
    feedbackUpload: function (e, item, attr, fileSize) {
      console.log(e, item, attr, fileSize);
      const file = e.target.files[0];
      if (file.type === "image/png" || file.type === "image/jpeg") {
        this.imageUpload(e, item, attr, fileSize);
      } else {
        this.lottieUpload(e, item, attr, fileSize);
      }
    },
    sourceImgCheck: function (input, data, item, attr) {
      var dom = $(input),
        size = dom.attr("size").split(",");
      if (size == "") return true;
      var checkSize = size.some(function (item, idx) {
        var _size = item.split("*"),
          width = _size[0],
          height = _size[1];
        if (width >= data.width && height >= data.height) {
          return true;
        }
        return false;
      });
      if (!checkSize) {
        item[attr] = "";
        alert("文件尺寸有误, 上传文件尺寸为：" + data.width + "*" + data.height);
        e.target.value = '';
      }
      return checkSize;
    },
    onSend: function () {
      var data = this.configData;
      let feedbackStatus = feedbackAnimationSend(data);
      if(!feedbackStatus){
        return;
      }
      //计算"建议教学时长"
      if (data.log_editor.isTeachTimeOther == '-2') { //其他
        data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
        if (data.log.teachTime == 0) {
          alert("请填写正确的建议教学时长")
          return;
        }
      } else {
        data.log.teachTime = data.log_editor.isTeachTimeOther
      }
      if (this.validate() && this.validateInstructions()) {
        setMemoryForm(data);
        var _data = JSON.stringify(data);
        $.ajax({
          url: domain + 'content?_method=put',
          type: 'POST',
          data: {
            content: _data
          },
          success: function (res) {
            window.parent.postMessage('close', '*');
          },
          error: function (err) {
            console.log(err)
          }
        });
      } else {
        // alert("带有"*"号为必填项！");
      }
    },
    postData: function (file, item, attr, filetype) {
      var FILE = 'file';
      let bg = arguments.length > 2 ? arguments[2] : null;
      var oldImg = item[attr];
      var data = new FormData();
      data.append('file', file);
      if (oldImg != "") {
        data.append('key', oldImg);
      }
      $.ajax({
        url: domain + FILE,
        type: 'post',
        data: data,
        async: false,
        processData: false,
        contentType: false,
        success: function (res) {
          item[attr] = domain + res.data.key;
        },
        error: function (err) {
          item[attr] = '';
        }
      })
    },
    audioUpload: function (e, item, attr) {
      //校验规则
      //var _type = this.rules.audio.sources.type;

      //获取到的内容数据
      var file = e.target.files[0],
        type = file.type,
        size = file.size,
        name = file.name,
        path = e.target.value;
      if ((size / 1024).toFixed(2) > 500) {
        console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      } else {
        console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
      }
      if ((!isNaN(parseInt($(e.target).attr('volume')))) && (size / 1024).toFixed(2) > parseInt($(e.target).attr('volume'))) {
        alert("您上传的音频大小为" + (size / 1024).toFixed(2) + "KB, 超过" + $(e.target).attr('volume') + "K上限，请检查后上传！");
        e.target.value = '';
        return;
      }
      item[attr] = "./form/img/loading.jpg";
      this.postData(file, item, attr);
    },
    addTg: function (item) {
      this.configData.tg.push({ title: '', content: '' });
    },
    deleTg: function (item) {
      this.configData.tg.remove(item);
    },
    play: function (e) {
      e.target.children[0].play();
    },
    addH: function () {
      this.configData.level.high.push({ title: '', content: '' });
    },
    addL: function (item) {
      this.configData.level.low.push({ title: '', content: '' });
    },
    deleH: function (item) {
      this.configData.level.high.remove(item);
    },
    deleL: function (item) {
      this.configData.level.low.remove(item);
    },
    delPrew: function (item, prop) {
      item[prop] = ''
    },
    //下面开始是模板需要的方法
    validate: function () {
      const dialogs = this.configData.source.dialogs;
      if (dialogs.roleImg) {
        for (let index = 0; index < dialogs.messages.length; index++) {
          const item = dialogs.messages[index];
          const { text, audio } = item;
          if (!text || !audio) {
            return alert("请上传对话内容");
          }
        }
      }

      // 检查热区答案设置
      const hotAreas = this.configData.source.hotAreaArr;
      for (let i = 0; i < hotAreas.length; i++) {
        const hotArea = hotAreas[i];
        if (!hotArea.hotAreaItem) {
          alert(`热区${i + 1}缺少热区图片`);
          return false;
        }
        if (hotArea.x === '' || hotArea.y === '') {
          alert(`热区${i + 1}缺少坐标设置`);
          return false;
        }
        if (!hotArea.correctArr || hotArea.correctArr.length === 0) {
          alert(`热区${i + 1}未设置答案`);
          return false;
        }
      }

      // 原有的options验证逻辑
      let dragArr = this.configData.source.dragArr
      for (let i = 0; i < dragArr.length; i++) {
        const drag = dragArr[i];
        if (!drag.dragItem) {
          alert(`拖拽元素${i + 1}未上传图片`);
          return false;
        }
        if (drag.x === '' || drag.y === '') {
          alert(`拖拽元素${i + 1}缺少坐标设置`);
          return false;
        }
      }
      return true;
    },
    // 获取可用的拖拽项
    getAvailableDragItems: function(currentHotArea) {
      // 获取所有拖拽项
      const dragItems = this.configData.source.dragArr;
      // 获取所有热区
      const hotAreas = this.configData.source.hotAreaArr;

      // 过滤可用的拖拽项
      return dragItems.filter(dragItem => {
        // 如果是可多次拖拽的项，始终可选
        if (dragItem.dragNum === '1' ||  dragItem.dragNum=== '2') {
          return true;
        }
        // 如果是仅可拖拽一次的项
        // 检查是否已被其他热区选择
        const isUsed = hotAreas.some(hotArea => {
          // 跳过当前正在设置的热区
          if (hotArea === currentHotArea) {
            return false;
          }
          // 检查是否已被其他热区选择
          return hotArea.correctArr && hotArea.correctArr.includes(dragItem.id);
        });

        // 如果未被使用，则可选
        return !isUsed;
      });
    },
    // 当拖拽项的拖拽次数变化时，清空包含该拖拽项ID的热区答案
    clearHotAreaAnswers: function(dragItem) {
      console.log('clearHotAreaAnswers', dragItem);
      //从oldDragNum中获取拖拽次数变化前的值
      const oldval = this.oldDragNum[dragItem.id];

      // 当从多次拖拽或无限拖拽变为一次拖拽时，需要清空热区答案
      if ((oldval === '1' || oldval === '2') && dragItem.dragNum === '0') {
        // 获取所有热区
        const hotAreas = this.configData.source.hotAreaArr;

        // 遍历所有热区，清除包含该拖拽项ID的答案
        hotAreas.forEach(hotArea => {
          if (hotArea.correctArr && hotArea.correctArr.includes(dragItem.id)) {
            // 从热区答案数组中移除该拖拽项ID
            hotArea.correctArr = hotArea.correctArr.filter(id => id !== dragItem.id);
          }
        });
      }

      this.oldDragNum[dragItem.id] = dragItem.dragNum
      // 清空关联答案后刷新 multiple-select
      this.refreshMultipleSelect();
    },
    deleteDrag: function (item) {
      // 显示确认对话框，提醒用户删除拖拽项会导致关联的热区答案被清空
      if (confirm("确定要删除该拖拽项吗？\n\n注意：删除后，所有包含该拖拽项的热区答案选择框将被清空。")) {
        // 查找所有包含当前拖拽项ID的热区，清除其答案
        const hotAreas = this.configData.source.hotAreaArr;
        hotAreas.forEach(hotArea => {
          if (hotArea.correctArr && hotArea.correctArr.includes(item.id)) {
            // 从热区答案数组中移除该拖拽项ID
            hotArea.correctArr = hotArea.correctArr.filter(id => id !== item.id);
          }
        });

        // 执行删除操作
        this.configData.source.dragArr.remove(item);
        delete this.oldDragNum[item.id]

        // 删除拖拽项后刷新 multiple-select
        this.refreshMultipleSelect();
      }
    },
    deleteHotArea: function (item) {
      // 显示确认对话框
      if (confirm("确定要删除该热区吗？")) {
        this.configData.source.hotAreaArr.remove(item);

        // 删除热区后刷新 multiple-select
        this.refreshMultipleSelect();
      }
    },
    addDrag: function () {
      // 获取当前最大id编号
      const currentIds = this.configData.source.dragArr.map(item => {
        // 如果id是数字字符串，则转为数字；
        if (!isNaN(parseInt(item.id))) {
          return parseInt(item.id);
        }
      });
      const maxId = Math.max(...currentIds, 0);
      const nextId = (maxId + 1).toString(); // 直接使用数字作为ID

      this.configData.source.dragArr.push({
        memoryId: '',
        id: nextId, //拖拽元素唯一标识，直接使用数字
        dragItem: '', //拖拽元素url
        x: 0, //拖拽元素x坐标
        y: 0, //拖拽元素y坐标，
        initialSize: 100, //拖拽元素初始大小
        audio: '', //拖拽元素音频url
        dragNum: '0', //拖拽元素次数 一次0 多次1
      });
      this.oldDragNum[nextId] = '0'
      // 新增拖拽项后刷新 multiple-select
      this.refreshMultipleSelect();
    },
    addHotArea: function () {
      this.configData.source.hotAreaArr.push({
        hotAreaItem: '',//热区元素url
        x: 0,//热区元素x坐标
        y: 0,//热区元素y坐标
        audio: '',//热区元素音频url
        correctArr: [],//正确选项数组 存储拖拽元素id
      });

      // 新增热区后刷新 multiple-select
      this.refreshMultipleSelect();
    },
    // 新增方法：刷新multiple-select
    refreshMultipleSelect: function() {
      this.$nextTick(() => {
        // 销毁现有实例
        $('.multiple-select').multipleSelect('destroy');
        // 重新初始化
        this.initMultipleSelect();
      });
    },
  }
});
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
