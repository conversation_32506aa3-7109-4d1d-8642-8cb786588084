/**
  * multiple-select - Multiple select is a jQuery plugin to select multiple elements with checkboxes :).
  *
  * @version v2.1.1
  * @homepage http://multiple-select.wenzhixin.net.cn
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";const e={name:"",placeholder:"",classes:"",classPrefix:"",classInput:"",data:void 0,locale:void 0,selectAll:!0,single:void 0,singleRadio:!1,multiple:!1,hideOptgroupCheckboxes:!1,multipleWidth:80,width:void 0,size:void 0,dropWidth:void 0,maxHeight:250,maxHeightUnit:"px",position:"bottom",displayValues:!1,displayTitle:!1,displayDelimiter:", ",minimumCountSelected:3,ellipsis:!1,isOpen:!1,keepOpen:!1,openOnHover:!1,container:null,filter:!1,filterGroup:!1,filterPlaceholder:"",filterAcceptOnEnter:!1,filterByDataLength:void 0,filterSelectAll:!0,customFilter:({text:t,label:e,search:s})=>(e||t).includes(s),showClear:!1,animate:void 0,styler:()=>!1,textTemplate:t=>t[0].innerHTML.trim(),labelTemplate:t=>t[0].getAttribute("label"),onOpen:()=>!1,onClose:()=>!1,onCheckAll:()=>!1,onUncheckAll:()=>!1,onFocus:()=>!1,onBlur:()=>!1,onOptgroupClick:()=>!1,onBeforeClick:()=>!0,onClick:()=>!1,onFilter:()=>!1,onClear:()=>!1,onAfterCreate:()=>!1},s={formatSelectAll:()=>"全选",formatAllSelected:()=>"全部选中",formatCountSelected:(t,e)=>`已选择 ${t} 项，共 ${e} 项`,formatNoMatchesFound:()=>"没有找到匹配项"};Object.assign(e,s);const i={BLOCK_ROWS:500,CLUSTER_BLOCKS:4,DEFAULTS:e,METHODS:["getOptions","refreshOptions","getData","getSelects","setSelects","enable","disable","open","close","check","uncheck","checkAll","uncheckAll","checkInvert","focus","blur","refresh","resetFilter","destroy"],LOCALES:{en:s,"en-US":s}};class u{constructor(t){this.rows=t.rows,this.scrollEl=t.scrollEl,this.contentEl=t.contentEl,this.callback=t.callback,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;const e=()=>{this.lastCluster!==(this.lastCluster=this.getNum())&&(this.initDOM(this.rows),this.callback())};this.scrollEl.addEventListener("scroll",e,!1),this.destroy=()=>{this.contentEl.innerHtml="",this.scrollEl.removeEventListener("scroll",e,!1)}}initDOM(t){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));const e=this.initData(t,this.getNum()),s=e.rows.join(""),i=this.checkChanges("data",s),u=this.checkChanges("top",e.topOffset),l=this.checkChanges("bottom",e.bottomOffset),o=[];i&&u?(e.topOffset&&o.push(this.getExtra("top",e.topOffset)),o.push(s),e.bottomOffset&&o.push(this.getExtra("bottom",e.bottomOffset)),this.contentEl.innerHTML=o.join("")):l&&(this.contentEl.lastChild.style.height=`${e.bottomOffset}px`)}getRowsHeight(){if(void 0===this.itemHeight){const t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=this.itemHeight*i.BLOCK_ROWS,this.clusterRows=i.BLOCK_ROWS*i.CLUSTER_BLOCKS,this.clusterHeight=this.blockHeight*i.CLUSTER_BLOCKS}getNum(){return this.scrollTop=this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}initData(t,e){if(t.length<i.BLOCK_ROWS)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};const s=Math.max((this.clusterRows-i.BLOCK_ROWS)*e,0),u=s+this.clusterRows,l=Math.max(s*this.itemHeight,0),o=Math.max((t.length-u)*this.itemHeight,0),n=[];let a=s;l<1&&a++;for(let e=s;e<u;e++)t[e]&&n.push(t[e]);return this.dataStart=s,this.dataEnd=u,{topOffset:l,bottomOffset:o,rowsAbove:a,rows:n}}checkChanges(t,e){const s=e!==this.cache[t];return this.cache[t]=e,s}getExtra(t,e){const s=document.createElement("li");return s.className=`virtual-scroll-${t}`,e&&(s.style.height=`${e}px`),s.outerHTML}}const l=(t,e,s)=>{for(const i of t){if(i[e]===s||i[e]===""+ +i[e]&&+i[e]===s)return i;if("optgroup"===i.type)for(const t of i.children)if(t[e]===s||t[e]===""+ +t[e]&&+t[e]===s)return t}},o=t=>{if(t.normalize)return t.normalize("NFD").replace(/[\u0300-\u036F]/g,"");return[{base:"A",letters:/[\u0041\u24B6\uFF21\u00C0\u00C1\u00C2\u1EA6\u1EA4\u1EAA\u1EA8\u00C3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\u00C4\u01DE\u1EA2\u00C5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F]/g},{base:"AA",letters:/[\uA732]/g},{base:"AE",letters:/[\u00C6\u01FC\u01E2]/g},{base:"AO",letters:/[\uA734]/g},{base:"AU",letters:/[\uA736]/g},{base:"AV",letters:/[\uA738\uA73A]/g},{base:"AY",letters:/[\uA73C]/g},{base:"B",letters:/[\u0042\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0182\u0181]/g},{base:"C",letters:/[\u0043\u24B8\uFF23\u0106\u0108\u010A\u010C\u00C7\u1E08\u0187\u023B\uA73E]/g},{base:"D",letters:/[\u0044\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018B\u018A\u0189\uA779]/g},{base:"DZ",letters:/[\u01F1\u01C4]/g},{base:"Dz",letters:/[\u01F2\u01C5]/g},{base:"E",letters:/[\u0045\u24BA\uFF25\u00C8\u00C9\u00CA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\u00CB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E]/g},{base:"F",letters:/[\u0046\u24BB\uFF26\u1E1E\u0191\uA77B]/g},{base:"G",letters:/[\u0047\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E]/g},{base:"H",letters:/[\u0048\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D]/g},{base:"I",letters:/[\u0049\u24BE\uFF29\u00CC\u00CD\u00CE\u0128\u012A\u012C\u0130\u00CF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197]/g},{base:"J",letters:/[\u004A\u24BF\uFF2A\u0134\u0248]/g},{base:"K",letters:/[\u004B\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2]/g},{base:"L",letters:/[\u004C\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780]/g},{base:"LJ",letters:/[\u01C7]/g},{base:"Lj",letters:/[\u01C8]/g},{base:"M",letters:/[\u004D\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C]/g},{base:"N",letters:/[\u004E\u24C3\uFF2E\u01F8\u0143\u00D1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u0220\u019D\uA790\uA7A4]/g},{base:"NJ",letters:/[\u01CA]/g},{base:"Nj",letters:/[\u01CB]/g},{base:"O",letters:/[\u004F\u24C4\uFF2F\u00D2\u00D3\u00D4\u1ED2\u1ED0\u1ED6\u1ED4\u00D5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\u00D6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\u00D8\u01FE\u0186\u019F\uA74A\uA74C]/g},{base:"OI",letters:/[\u01A2]/g},{base:"OO",letters:/[\uA74E]/g},{base:"OU",letters:/[\u0222]/g},{base:"P",letters:/[\u0050\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754]/g},{base:"Q",letters:/[\u0051\u24C6\uFF31\uA756\uA758\u024A]/g},{base:"R",letters:/[\u0052\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782]/g},{base:"S",letters:/[\u0053\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784]/g},{base:"T",letters:/[\u0054\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786]/g},{base:"TZ",letters:/[\uA728]/g},{base:"U",letters:/[\u0055\u24CA\uFF35\u00D9\u00DA\u00DB\u0168\u1E78\u016A\u1E7A\u016C\u00DC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244]/g},{base:"V",letters:/[\u0056\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245]/g},{base:"VY",letters:/[\uA760]/g},{base:"W",letters:/[\u0057\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72]/g},{base:"X",letters:/[\u0058\u24CD\uFF38\u1E8A\u1E8C]/g},{base:"Y",letters:/[\u0059\u24CE\uFF39\u1EF2\u00DD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE]/g},{base:"Z",letters:/[\u005A\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762]/g}].reduce(((t,{letters:e,base:s})=>t.replace(e,s)),t)},n=t=>(Object.keys(t).forEach((e=>void 0===t[e]?delete t[e]:"")),t),a=t=>t&&"object"==typeof t&&t.__v_raw?t.__v_raw:t;class h{constructor(e,s){this.$el=e,this.options=t.extend({},i.DEFAULTS,s)}init(){this.initLocale(),this.initContainer(),this.initData(),this.initSelected(!0),this.initFilter(),this.initDrop(),this.initView(),this.options.onAfterCreate()}initLocale(){if(this.options.locale){const{locales:e}=t.fn.multipleSelect,s=this.options.locale.split(/-|_/);s[0]=s[0].toLowerCase(),s[1]&&(s[1]=s[1].toUpperCase()),e[this.options.locale]?t.extend(this.options,e[this.options.locale]):e[s.join("-")]?t.extend(this.options,e[s.join("-")]):e[s[0]]&&t.extend(this.options,e[s[0]])}}initContainer(){const e=this.$el[0],s=e.getAttribute("name")||this.options.name||"";this.options.classes&&this.$el.addClass(this.options.classes),this.options.classPrefix&&(this.$el.addClass(this.options.classPrefix),this.options.size&&this.$el.addClass(`${this.options.classPrefix}-${this.options.size}`)),this.$el.hide(),this.$label=this.$el.closest("label"),!this.$label.length&&this.$el.attr("id")&&(this.$label=t(`label[for="${this.$el.attr("id")}"]`)),this.$label.find(">input").length&&(this.$label=null),void 0===this.options.single&&(this.options.single=null===e.getAttribute("multiple")),this.$parent=t(`\n      <div class="ms-parent ${e.getAttribute("class")||""} ${this.options.classes}"\n      title="${e.getAttribute("title")||""}" />\n    `),this.options.placeholder=this.options.placeholder||e.getAttribute("placeholder")||"",this.tabIndex=e.getAttribute("tabindex");let i="";if(null!==this.tabIndex&&(i=this.tabIndex&&`tabindex="${this.tabIndex}"`),this.$el.attr("tabindex",-1),this.$choice=t(`\n      <button type="button" class="ms-choice"${i}>\n      <span class="ms-placeholder">${this.options.placeholder}</span>\n      ${this.options.showClear?'<div class="icon-close"></div>':""}\n      <div class="icon-caret"></div>\n      </button>\n    `),this.$drop=t(`<div class="ms-drop ${this.options.position}" />`),this.$close=this.$choice.find(".icon-close"),this.options.dropWidth&&this.$drop.css("width",this.options.dropWidth),this.$el.after(this.$parent),this.$parent.append(this.$choice),this.$parent.append(this.$drop),e.disabled&&this.$choice.addClass("disabled"),this.selectAllName=`data-name="selectAll${s}"`,this.selectGroupName=`data-name="selectGroup${s}"`,this.selectItemName=`data-name="selectItem${s}"`,!this.options.keepOpen){const s=((t="")=>`click.multiple-select-${t=t||`${+new Date}${~~(1e6*Math.random())}`}`)(this.$el.attr("id"));t(document).off(s).on(s,(s=>{t(s.target)[0]!==this.$choice[0]&&t(s.target).parents(".ms-choice")[0]!==this.$choice[0]&&(t(s.target)[0]===this.$drop[0]||t(s.target).parents(".ms-drop")[0]!==this.$drop[0]&&s.target!==e)&&this.options.isOpen&&this.close()}))}}initData(){const e=[];if(this.options.data){if(Array.isArray(this.options.data))this.data=this.options.data.map((t=>"string"==typeof t||"number"==typeof t?{text:t,value:t}:t.children?.length?{...t,children:t.children.map((t=>({...t})))}:{...t}));else if("object"==typeof this.options.data){for(const[t,s]of Object.entries(this.options.data))e.push({value:t,text:s});this.data=e}}else t.each(this.$el.children(),((t,s)=>{this.initRow(t,s)&&e.push(this.initRow(t,s))})),this.options.data=e,this.data=e,this.fromHtml=!0;this.dataTotal=(t=>{let e=0;return t.forEach(((t,s)=>{"optgroup"===t.type?(t._key=`group_${s}`,t.visible=void 0===t.visible||t.visible,t.children.forEach(((t,i)=>{t.visible=void 0===t.visible||t.visible,t.divider||(t._key=`option_${s}_${i}`,e+=1)}))):(t.visible=void 0===t.visible||t.visible,t.divider||(t._key=`option_${s}`,e+=1))})),e})(this.data)}initRow(e,s,i){const u={},l=t(s);return l.is("option")?(u.type="option",u.text=this.options.textTemplate(l),u.value=s.value,u.visible=!0,u.selected=!!s.selected,u.disabled=i||s.disabled,u.classes=s.getAttribute("class")||"",u.title=s.getAttribute("title")||"",(s._value||l.data("value"))&&(u._value=s._value||l.data("value")),Object.keys(l.data()).length&&(u._data=l.data(),u._data.divider&&(u.divider=u._data.divider)),u):l.is("optgroup")?(u.type="optgroup",u.label=this.options.labelTemplate(l),u.visible=!0,u.selected=!!s.selected,u.disabled=s.disabled,u.children=[],Object.keys(l.data()).length&&(u._data=l.data()),t.each(l.children(),((t,e)=>{u.children.push(this.initRow(t,e,u.disabled))})),u):null}initSelected(t){let e=0;for(const t of this.data)if("optgroup"===t.type){const s=t.children.filter((t=>t.selected&&!t.disabled&&t.visible)).length;t.children.length&&(t.selected=!this.options.single&&s&&s===t.children.filter((t=>!t.disabled&&t.visible&&!t.divider)).length),e+=s}else e+=t.selected&&!t.disabled&&t.visible?1:0;this.allSelected=this.data.filter((t=>t.selected&&!t.disabled&&t.visible)).length===this.data.filter((t=>!t.disabled&&t.visible&&!t.divider)).length,t||(this.allSelected?this.options.onCheckAll():0===e&&this.options.onUncheckAll())}initFilter(){if(this.filterText="",this.options.filter||!this.options.filterByDataLength)return;let t=0;for(const e of this.data)"optgroup"===e.type?t+=e.children.length:t+=1;this.options.filter=t>this.options.filterByDataLength}initDrop(){this.initList(),this.update(!0),this.options.isOpen&&setTimeout((()=>{this.open()}),50),this.options.openOnHover&&this.$parent.hover((()=>{this.open()}),(()=>{this.close()}))}initList(){const t=[];this.options.filter&&t.push(`\n        <div class="ms-search">\n          <input type="text" autocomplete="off" autocorrect="off"\
            autocapitalize="off" spellcheck="false"\
            placeholder="${this.options.filterPlaceholder}">\n        </div>\n      `),t.push("<ul></ul>"),this.$drop.html(t.join("")),this.$ul=this.$drop.find(">ul"),this.initListItems()}initListItems(){const t=this.getListRows();let e=0;if(this.options.selectAll&&!this.options.single&&(e=-1),t.length>i.BLOCK_ROWS*i.CLUSTER_BLOCKS){this.virtualScroll&&this.virtualScroll.destroy();const s=this.$drop.is(":visible");s||this.$drop.css("left",-1e4).show();const i=()=>{this.updateDataStart=this.virtualScroll.dataStart+e,this.updateDataEnd=this.virtualScroll.dataEnd+e,this.updateDataStart<0&&(this.updateDataStart=0),this.updateDataEnd>this.data.length&&(this.updateDataEnd=this.data.length)};this.virtualScroll=new u({rows:t,scrollEl:this.$ul[0],contentEl:this.$ul[0],callback:()=>{i(),this.events()}}),i(),s||this.$drop.css("left",0).hide()}else this.$ul.html(t.join("")),this.updateDataStart=0,this.updateDataEnd=this.updateData.length,this.virtualScroll=null;this.events()}getListRows(){const t=[];return!this.options.selectAll||this.options.single||!this.options.filterSelectAll&&this.filterText||t.push(`\n        <li class="ms-select-all" tabindex="0">\n        <label>\n        <input\
          type="checkbox" ${this.selectAllName}\
          ${this.allSelected?' checked="checked"':""}\
          ${this.options.classInput?`class="${this.options.classInput}"`:""}\
          tabindex="-1"\
        />\n        <span>${this.options.formatSelectAll()}</span>\n        </label>\n        </li>\n      `),this.updateData=[],this.data.forEach((e=>{t.push(...this.initListItem(e))})),t.push(`<li class="ms-no-results">${this.options.formatNoMatchesFound()}</li>`),t}initListItem(t,e=0){const s=t.title?`title="${t.title}"`:"",i=this.options.multiple?"multiple":"",u=this.options.single?"radio":"checkbox";let l="";if(!t.visible)return[];if(this.updateData.push(t),this.options.single&&!this.options.singleRadio&&(l="hide-radio "),t.selected&&(l+="selected "),"optgroup"===t.type){const e=this.options.styler(t),s=e?`style="${e}"`:"",i=[],u=this.options.hideOptgroupCheckboxes||this.options.single?`<span ${this.selectGroupName} data-key="${t._key}"></span>`:`<input type="checkbox"\
          ${this.selectGroupName}\
          data-key="${t._key}"\
          ${t.selected?' checked="checked"':""}\
          ${t.disabled?' disabled="disabled"':""}\
          ${this.options.classInput?`class="${this.options.classInput}"`:""}\
          tabindex="-1"\
        >`;return l.includes("hide-radio")||!this.options.hideOptgroupCheckboxes&&!this.options.single||(l+="hide-radio "),i.push(`\n        <li class="group ${l}" ${s} tabindex="${l.includes("hide-radio")||t.disabled?-1:0}">\n        <label class="optgroup${this.options.single||t.disabled?" disabled":""}">\n        ${u}${t.label}\n        </label>\n        </li>\n      `),t.children.forEach((t=>{i.push(...this.initListItem(t,1))})),i}const o=this.options.styler(t),n=o?`style="${o}"`:"";return l+=`${t.classes||""} option-level-${e} `,t.divider?'<li class="option-divider"/>':[`\n      <li class="${i} ${l}" ${s} ${n} tabindex="${t.disabled?-1:0}">\n      <label class="${t.disabled?"disabled":""}">\n      <input type="${u}"\
        value="${t.value}"\
        data-key="${t._key}"\
        ${this.selectItemName}\
        ${t.selected?' checked="checked"':""}\
        ${t.disabled?' disabled="disabled"':""}\
        ${this.options.classInput?`class="${this.options.classInput}"`:""}\
        tabindex="-1"\
      >\n      <span>${t.text}</span>\n      </label>\n      </li>\n    `]}events(){this.$searchInput=this.$drop.find(".ms-search input"),this.$selectAll=this.$drop.find(`input[${this.selectAllName}]`),this.$selectGroups=this.$drop.find(`input[${this.selectGroupName}],span[${this.selectGroupName}]`),this.$selectItems=this.$drop.find(`input[${this.selectItemName}]:enabled`),this.$disableItems=this.$drop.find(`input[${this.selectItemName}]:disabled`),this.$noResults=this.$drop.find(".ms-no-results");const e=e=>{e.preventDefault(),t(e.target).hasClass("icon-close")||this[this.options.isOpen?"close":"open"]()};this.$label&&this.$label.length&&this.$label.off("click").on("click",(t=>{"label"===t.target.nodeName.toLowerCase()&&(e(t),this.options.filter&&this.options.isOpen||this.focus(),t.stopPropagation())})),this.$choice.off("click").on("click",e).off("focus").on("focus",this.options.onFocus).off("blur").on("blur",this.options.onBlur),this.$parent.off("keydown").on("keydown",(t=>{27!==t.which||this.options.keepOpen||(this.close(),this.$choice.focus())})),this.$close.off("click").on("click",(t=>{t.preventDefault(),this._checkAll(!1,!0),this.initSelected(!1),this.updateSelected(),this.update(),this.options.onClear()})),this.$searchInput.off("keydown").on("keydown",(t=>{9===t.keyCode&&t.shiftKey&&this.close()})).off("keyup").on("keyup",(t=>{if(this.options.filterAcceptOnEnter&&[13,32].includes(t.which)&&this.$searchInput.val()){if(this.options.single){const t=this.$selectItems.closest("li").filter(":visible");t.length&&this.setSelects([t.first().find(`input[${this.selectItemName}]`).val()])}else this.$selectAll.click();return this.close(),void this.focus()}this.filter()})),this.$selectAll.off("click").on("click",(e=>{this._checkAll(t(e.currentTarget).prop("checked"))})),this.$selectGroups.off("click").on("click",(e=>{const s=t(e.currentTarget),i=s.prop("checked"),u=l(this.data,"_key",s.data("key"));this._checkGroup(u,i),this.options.onOptgroupClick(n({label:u.label,selected:u.selected,data:u._data,children:u.children.map((t=>n({text:t.text,value:t.value,selected:t.selected,disabled:t.disabled,data:t._data})))}))})),this.$selectItems.off("click").on("click",(e=>{const s=t(e.currentTarget),i=s.prop("checked"),u=l(this.data,"_key",s.data("key")),o=()=>{this.options.single&&this.options.isOpen&&!this.options.keepOpen&&this.close()};!1!==this.options.onBeforeClick(u)?(this._check(u,i),this.options.onClick(n({text:u.text,value:u.value,selected:u.selected,data:u._data})),o()):o()})),this.$ul.find("li").off("keydown").on("keydown",(e=>{const s=t(e.currentTarget);let i,u;switch(e.key){case"ArrowUp":e.preventDefault(),i=s.prev("li.option-divider"),u=i.length?i:s,u.prev().trigger("focus");break;case"ArrowDown":e.preventDefault(),i=s.next("li.option-divider"),u=i.length?i:s,u.next().trigger("focus");break;case"Enter":e.preventDefault(),s.find("input").trigger("click"),this.options.single&&this.$choice.trigger("focus")}}))}initView(){let t;window.getComputedStyle?(t=window.getComputedStyle(this.$el[0]).width,"auto"===t&&(t=this.$drop.outerWidth()+20)):t=this.$el.outerWidth()+20,this.$parent.css("width",this.options.width||t),this.$el.show().addClass("ms-offscreen")}open(){if(this.$choice.hasClass("disabled"))return;if(this.options.isOpen=!0,this.$parent.addClass("ms-parent-open"),this.$choice.find(">div").addClass("open"),this.$drop[this.animateMethod("show")](),this.$selectAll.parent().show(),this.$noResults.hide(),this.data.length||(this.$selectAll.parent().hide(),this.$noResults.show()),this.options.container){const e=this.$drop.offset();this.$drop.appendTo(t(this.options.container)),this.$drop.offset({top:e.top,left:e.left}).css("min-width","auto").outerWidth(this.$parent.outerWidth())}let e=this.options.maxHeight;"row"===this.options.maxHeightUnit&&(e=this.$drop.find(">ul>li").first().outerHeight()*this.options.maxHeight),this.$drop.find(">ul").css("max-height",`${e}px`),this.$drop.find(".multiple").css("width",`${this.options.multipleWidth}px`),this.data.length&&this.options.filter&&(this.$searchInput.val(""),this.$searchInput.focus(),this.filter(!0)),this.options.onOpen()}close(){this.options.isOpen=!1,this.$parent.removeClass("ms-parent-open"),this.$choice.find(">div").removeClass("open"),this.$drop[this.animateMethod("hide")](),this.options.container&&(this.$parent.append(this.$drop),this.$drop.css({top:"auto",left:"auto"})),this.options.onClose()}animateMethod(t){return{show:{fade:"fadeIn",slide:"slideDown"},hide:{fade:"fadeOut",slide:"slideUp"}}[t][this.options.animate]||t}update(t){const e=this.getSelects();let s=this.getSelects("text");this.options.displayValues&&(s=e);const i=this.$choice.find(">span"),u=e.length;let l="";0===u?i.addClass("ms-placeholder").html(this.options.placeholder):l=u<this.options.minimumCountSelected?s.join(this.options.displayDelimiter):this.options.formatAllSelected()&&u===this.dataTotal?this.options.formatAllSelected():this.options.ellipsis&&u>this.options.minimumCountSelected?`${s.slice(0,this.options.minimumCountSelected).join(this.options.displayDelimiter)}...`:this.options.formatCountSelected()&&u>this.options.minimumCountSelected?this.options.formatCountSelected(u,this.dataTotal):s.join(this.options.displayDelimiter),l&&i.removeClass("ms-placeholder").html(l),this.options.displayTitle&&i.prop("title",this.getSelects("text")),this.$el.val(this.getSelects()),t||this.$el.trigger("change")}updateSelected(){for(let t=this.updateDataStart;t<this.updateDataEnd;t++){const e=this.updateData[t];this.$drop.find(`input[data-key=${e._key}]`).prop("checked",e.selected).closest("li").toggleClass("selected",e.selected)}const t=0===this.data.filter((t=>t.visible)).length;this.$selectAll.length&&this.$selectAll.prop("checked",this.allSelected).closest("li").toggleClass("selected",this.allSelected).toggle(!t),this.$noResults.toggle(t),this.virtualScroll&&(this.virtualScroll.rows=this.getListRows())}getData(){return this.options.data}getOptions(){const e=t.extend({},this.options);return delete e.data,t.extend(!0,{},e)}refreshOptions(e){((t,e)=>{const s=Object.keys(t),i=Object.keys(e);if(s.length!==i.length)return!1;for(const u of s)if(i.includes(u)&&t[u]!==e[u])return!1;return!0})(this.options,e)||(this.options=t.extend(this.options,e),this.destroy(),this.init())}getSelects(t="value"){const e=[];for(const s of this.data)if("optgroup"===s.type){const i=s.children.filter((t=>t.selected));if(!i.length)continue;if("value"===t||this.options.single)e.push(...i.map((e=>"value"===t&&e._value||e[t])));else{const u=[];u.push("["),u.push(s.label),u.push(`: ${i.map((e=>e[t])).join(", ")}`),u.push("]"),e.push(u.join(""))}}else s.selected&&e.push("value"===t&&s._value||s[t]);return e}setSelects(e,s="value",i=!1){let u=!1;const l=i=>{for(const l of i){let i=!1;if("text"===s)i=e.includes(t("<div>").html(l.text).text().trim());else{const t=a(l._value||l.value);i=e.some((e=>a(e)===t)),i||l.value!==""+ +l.value||(i=e.includes(+l.value))}l.selected!==i&&(u=!0),l.selected=i}};for(const t of this.data)"optgroup"===t.type?l(t.children):l([t]);u&&(this.initSelected(i),this.updateSelected(),this.update(i))}enable(){this.$choice.removeClass("disabled")}disable(){this.$choice.addClass("disabled")}check(t){const e=l(this.data,"value",t);e&&this._check(e,!0)}uncheck(t){const e=l(this.data,"value",t);e&&this._check(e,!1)}_check(t,e){this.options.single&&this._checkAll(!1,!0),t.selected=e,this.initSelected(),this.updateSelected(),this.update()}checkAll(){this._checkAll(!0)}uncheckAll(){this._checkAll(!1)}_checkAll(t,e){for(const s of this.data)"optgroup"===s.type?this._checkGroup(s,t,!0):s.disabled||s.divider||!e&&!s.visible||(s.selected=t);e||(this.initSelected(),this.updateSelected(),this.update())}_checkGroup(t,e,s){t.selected=e,t.children.forEach((t=>{t.disabled||t.divider||!s&&!t.visible||(t.selected=e)})),s||(this.initSelected(),this.updateSelected(),this.update())}checkInvert(){if(!this.options.single){for(const t of this.data)if("optgroup"===t.type)for(const e of t.children)e.divider||(e.selected=!e.selected);else t.divider||(t.selected=!t.selected);this.initSelected(),this.updateSelected(),this.update()}}focus(){this.$choice.focus(),this.options.onFocus()}blur(){this.$choice.blur(),this.options.onBlur()}refresh(){this.destroy(),this.init()}resetFilter(){this.options.filter&&(this.$searchInput.val(""),this.filter(!0))}filter(t){const e=this.$searchInput.val().trim(),s=e.toLowerCase();if(this.filterText!==s){this.filterText=s;for(const t of this.data)if("optgroup"===t.type)if(this.options.filterGroup){const i=this.options.customFilter({label:o(t.label.toString().toLowerCase()),search:o(s),originalLabel:t.label,originalSearch:e,row:t});t.visible=i;for(const e of t.children)e.visible=i}else{for(const i of t.children)i.visible=this.options.customFilter({text:o(i.text.toString().toLowerCase()),search:o(s),originalText:i.text,originalSearch:e,row:i,parent:t});t.visible=t.children.filter((t=>t.visible)).length>0}else t.visible=this.options.customFilter({text:o(t.text.toString().toLowerCase()),search:o(s),originalText:t.text,originalSearch:e,row:t});this.initListItems(),this.initSelected(t),this.updateSelected(),t||this.options.onFilter(e)}}destroy(){this.$parent&&(this.$el.before(this.$parent).removeClass("ms-offscreen"),null!==this.tabIndex&&this.$el.attr("tabindex",this.tabIndex),this.$parent.remove(),this.fromHtml&&(delete this.options.data,this.fromHtml=!1))}}t.fn.multipleSelect=function(e,...s){let u;return this.each(((l,o)=>{const n=t(o);let a=n.data("multipleSelect");const r=t.extend({},n.data(),"object"==typeof e&&e);if(a||(a=new h(n,r),n.data("multipleSelect",a)),"string"==typeof e){if(t.inArray(e,i.METHODS)<0)throw new Error(`Unknown method: ${e}`);u=a[e](...s),"destroy"===e&&n.removeData("multipleSelect")}else a.init()})),void 0!==u?u:this},t.fn.multipleSelect.Constructor=h,t.fn.multipleSelect.defaults=i.DEFAULTS,t.fn.multipleSelect.locales=i.LOCALES,t.fn.multipleSelect.methods=i.METHODS}));
