@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@import '../../common/template/multyDialog/style.scss';

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}

* {
  box-sizing: border-box;
}

.desc-visi {
  visibility: hidden;
}

.container {
  position: relative;
}

audio {
  width: 0;
  height: 0;
  opacity: 0;
}

.main {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

.hide {
  opacity: 0;

}

.mainArea {
  position: relative;
  height: 10.8rem;

  .done {
    width: 2.2rem;
    height: 0.9rem;
    position: absolute;
    left: 16.03rem;
    bottom: 1.03rem;
    display: none;
  }

  .image-container {
    display: inline-block;

    .images {
      display: block;
    }
  }


}

//错误反馈动画
.shake {
  animation: shakeUp .7s both ease-in;
}

@keyframes shakeUp {
  0% {
    margin-left: 8px;
  }

  20% {
    margin-left: -8px;
  }

  40% {
    margin-left: 8px;
  }

  60% {
    margin-left: -8px;
  }

  80% {
    margin-left: 8px;
  }

  100% {
    margin-left: 0px;
  }
}
