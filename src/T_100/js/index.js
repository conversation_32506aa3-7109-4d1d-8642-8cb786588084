"use strict"
import '../../common/js/common_1v1.js'
import "../../common/template/multyDialog/index.js";
import "../../common/js/teleprompter.js"
import '../../common/js/drag.js'
import {feedbackAnimation} from "../../common/template/feedbackAnimation/index.js";
import {getMemory} from "../../common/template/memory";

$(function () {
  /**
   * 全局配置和状态
   */
  window.h5Template = {hasPractice: '1'}
  let memoryData = getMemory();
  const h5SyncActions = parent.window.h5SyncActions;
  const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

  // 存储状态和实例
  const lottieInstances = {}; // 存储所有Lottie动画实例
  let hasPlacedCorrectly = []; // 存储已正确放置的拖拽元素ID
  let maxPlacedZIndex = 100; // 记录已放置元素的最大z-index值
  let currentAudioInfo = null; // 存储当前播放的音频信息 {element, syncName}
  let isDoneButtonShown = false; // 标记done按钮是否已经显示，防止重复显示


  // 设置背景
  if (configData.bg == '') {
    $(".container").css({'background-image': 'url(./image/bg_53.jpg)'})
  }


  /**
   * 预加载图片资源
   * @param {Array} urls - 需要预加载的图片URL数组
   * @returns {Promise} 所有图片加载完成的Promise
   */
  function preloadImages(urls) {
    const promises = urls.map(url => {
      if (!url || isJsonFile(url)) return Promise.resolve();
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = resolve;
        img.onerror = reject;
        img.src = url;
      });
    });
    return Promise.all(promises);
  }

  /**
   * 主初始化函数
   * 负责初始化拖拽元素、热区、动画和事件绑定
   */
  async function init() {
    // 收集所有需要预加载的图片URL
    const imageUrls = [];
    configData.source.dragArr.forEach(item => {
      if (item.dragItem && !isJsonFile(item.dragItem)) {
        imageUrls.push(item.dragItem);
      }
    });
    configData.source.hotAreaArr.forEach(item => {
      if (item.hotAreaItem && !isJsonFile(item.hotAreaItem)) {
        imageUrls.push(item.hotAreaItem);
      }
    });

    // 预加载所有图片
    try {
      await preloadImages(imageUrls);
    } catch (error) {
      console.error('图片预加载失败:', error);
    }

    // 初始化各组件
    initDragElements();
    initHotAreas();

    setTimeout(() => {
      initStarAnimation();
    }, 1000);
    bindEvents();

    $(document).on('posDrag', '.drag-item', async function (e, dragId, hotArea) {
      if (isSync) {
        dragId = SDK.syncData.dragId
        hotArea = SDK.syncData.hotArea
        // 在这里 获取当前托转元素位置并且设置上
        $(this).css({
          left: SDK.syncData.dragElementLeft,
          top: SDK.syncData.dragElementTop,
          zIndex: 10,
          transition: '0.2s'
        })
      }

      // 禁止再次拖动，无论dragNum设置如何
      disableDragging(this);
      const $dragElement = $(this);
      const dragConfig = getDragConfigById(dragId);
      // 检查dragNum设置，确定是否是多次拖拽项或无限拖拽项
      const isDragMultiple = dragConfig && (dragConfig.dragNum === '1' || dragConfig.dragNum === '2');
      if (isDragMultiple) {
        handleMultipleDragItem($dragElement, dragId);
      }
      // 处理正确放置后的动画和音频（异步执行，不阻塞拖拽）
      handleCorrectPlacement($dragElement[0], dragId, hotArea);
      // 立即恢复所有拖拽元素的交互能力，包括克隆的元素
      $('.drag-item').not('.fixed-position').css('pointer-events', 'auto');
      SDK.setEventLock();
    });
  }

  /**
   * 创建元素并设置基本属性
   * @param {Object} config - 元素配置
   * @param {string} type - 元素类型('drag'或'hot-area')
   * @param {number} index - 元素索引
   * @returns {Object} jQuery元素对象
   */
  function createElementFromConfig(config, type, index) {
    // 创建元素
    const element = $('<div>');
    const isDrag = type === 'drag';

    // 设置元素类和属性
    if (isDrag) {
      // 不在此处设置 data-syncactions，而在后续流程设置唯一标识
      element.addClass('drag-item')
        .attr('data-id', config.id)
        .attr('data-dragnum', config.dragNum)
        .attr('data-memory-id', config.memoryId || ''); // 添加记忆ID属性
    } else {
      element.addClass('hot-area')
        .attr('data-index', index)
        .attr('data-correct', JSON.stringify(config.correctArr));
    }

    // 设置基本样式
    const styleObject = {
      position: 'absolute',
      left: config.x / 100 + 'rem',
      top: config.y / 100 + 'rem',
      zIndex: isDrag ? 10 : 5
    };

    // 添加拖拽元素特定样式
    if (isDrag) {
      styleObject.cursor = 'move';

      // 判断是否可以拖拽：memoryId为空或者在selectedArr中存在
      const memoryId = config.memoryId;
      const canDrag = !memoryId || memoryData.selectedArr.includes(memoryId);

      if (!canDrag) {
        // 不可以拖拽：添加禁用样式
        styleObject.filter = 'brightness(0.5)';
        styleObject['pointer-events'] = 'none';
      }
    }

    // 处理图片资源
    const imgUrl = isDrag ? config.dragItem : config.hotAreaItem;

    // 如果不是JSON动画或者是热区，设置为背景图
    if (!isJsonFile(imgUrl) || !isDrag) {
      Object.assign(styleObject, {
        backgroundImage: `url(${imgUrl})`,
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center'
      });
    }

    // 应用样式
    element.css(styleObject);

    // 获取并设置尺寸
    getImageDimensions(imgUrl)
      .then(dimensions => {
        const scale = isDrag && config.initialSize ? config.initialSize / 100 : 1;
        const originalWidth = dimensions.width;
        const originalHeight = dimensions.height;

        // 存储原始尺寸为元素的自定义属性
        if (isDrag) {
          element.attr({
            'data-original-width': originalWidth,
            'data-original-height': originalHeight
          });
        }

        element.css({
          width: originalWidth * scale / 100 + 'rem',
          height: originalHeight * scale / 100 + 'rem'
        });
      })
      .catch(error => console.error(`获取${isDrag ? '拖拽元素' : '热区'}尺寸失败:`, error));

    return element;
  }

  /**
   * 初始化拖拽元素
   */
  function initDragElements() {
    // 从configData中获取拖拽元素数组
    const dragArr = configData.source.dragArr || [];
    const fragment = document.createDocumentFragment();

    // 遍历所有拖拽元素
    dragArr.forEach((item, index) => {
      // 创建拖拽元素
      const dragEl = createElementFromConfig(item, 'drag', index);

      // 将拖拽元素添加到文档片段
      fragment.appendChild(dragEl[0]);

      // 设置唯一的 data-syncactions 属性
      const uniqueSyncAction = 'syncactions' + item.id + '_original';

      // 设置初始位置和其他属性
      dragEl.attr({
        'init-left': item.x / 100 + 'rem',
        'init-top': item.y / 100 + 'rem',
        'data-syncactions': uniqueSyncAction
      });

      // 初始化Lottie动画（如果是JSON文件）
      if (isJsonFile(item.dragItem)) {
        initLottieAnimation(item, dragEl);
      }
    });

    // 一次性将所有元素添加到DOM
    $('.mainArea').append(fragment);
  }

  /**
   * 初始化Lottie动画
   * @param {Object} item - 元素配置
   * @param {jQuery} $element - 元素jQuery对象
   */
  function initLottieAnimation(item, $element) {
    const selector = `.drag-item[data-id="${item.id}"]`;
    lottieAnimations.init(null, item.dragItem, selector, true)
      .then(instance => {
        lottieInstances[`drag_${item.id}`] = instance;
        // 初始化完成后立即播放动画
        lottieAnimations.play(instance);
      });
  }

  /**
   * 初始化热区
   */
  function initHotAreas() {
    // 从configData中获取热区数组
    const hotAreaArr = configData.source.hotAreaArr || [];
    const fragment = document.createDocumentFragment();

    // 遍历所有热区
    hotAreaArr.forEach((hotArea, index) => {
      // 创建热区元素
      const hotAreaEl = createElementFromConfig(hotArea, 'hot-area', index);

      // 将热区添加到文档片段
      fragment.appendChild(hotAreaEl[0]);
    });

    // 一次性将所有热区添加到DOM
    $('.mainArea').append(fragment);
  }

  /**
   * 判断是否为JSON文件
   * @param {string} url - 文件URL
   * @returns {boolean} 是否为JSON文件
   */
  function isJsonFile(url) {
    if (!url) return false;
    return url.split('.').pop().toLowerCase() === 'json';
  }

  /**
   * 获取图片尺寸
   * @param {string} url - 图片URL
   * @returns {Promise} 包含宽高信息的Promise对象
   */
  function getImageDimensions(url) {
    return new Promise((resolve, reject) => {
      if (!url) {
        reject(new Error('无效的图片URL'));
        return;
      }

      if (isJsonFile(url)) {
        // 处理JSON文件
        $.getJSON(url)
          .done(data => {
            const width = data.width || data.w;
            const height = data.height || data.h;
            resolve({width, height});
          })
          .fail(() => reject(new Error('JSON文件加载失败')));
      } else {
        // 处理图片文件
        const img = new Image();
        img.onload = () => resolve({width: img.width, height: img.height});
        img.onerror = () => reject(new Error('图片加载失败'));
        img.src = url;
      }
    });
  }

  /**
   * 判断两个矩形是否重叠或包含
   * @param {DOMRect} rect1 - 第一个矩形（拖拽元素）
   * @param {DOMRect} rect2 - 第二个矩形（热区元素）
   * @param {boolean} requireFullyContained - 是否要求第一个矩形完全被第二个矩形包含
   * @returns {boolean} 是否满足判断条件
   */
  function isOverlapping(rect1, rect2, requireFullyContained = false) {
    // 如果要求完全包含，检查拖拽元素是否完全在热区内
    if (requireFullyContained) {
      return (
        rect1.left >= rect2.left &&
        rect1.right <= rect2.right &&
        rect1.top >= rect2.top &&
        rect1.bottom <= rect2.bottom
      );
    }

    // 否则只检查是否有重叠
    return !(
      rect1.right < rect2.left ||
      rect1.left > rect2.right ||
      rect1.bottom < rect2.top ||
      rect1.top > rect2.bottom
    );
  }

  async function end() {
    await feedbackAnimation('feedKey1');
    SDK.setEventLock();
  }

  /**
   * 检查拖拽元素是否在正确的热区内
   * @param {Element} dragElement - 拖拽元素
   * @returns {boolean} 是否放置正确
   */
  function checkDragInHotArea(dragElement) {
    const $dragElement = $(dragElement);
    const dragId = $dragElement.data('id');
    const dragIdStr = String(dragId);
    const dragRect = dragElement.getBoundingClientRect();

    let isCorrect = false;
    let correctHotArea = null;

    // 遍历所有热区找到匹配的
    $('.hot-area').each(function () {
      const $hotArea = $(this);
      const hotAreaRect = this.getBoundingClientRect();
      const correctArr = JSON.parse($hotArea.attr('data-correct') || '[]');

      // 判断该拖拽项是否是热区的正确答案之一
      if (correctArr.includes(dragIdStr)) {
        // 根据热区正确答案数量决定判断方式
        const requireFullyContained = correctArr.length > 1;

        // 检查拖拽元素是否满足热区判断条件
        if (isOverlapping(dragRect, hotAreaRect, requireFullyContained)) {
          isCorrect = true;
          correctHotArea = $hotArea;
          return false; // 跳出each循环
        }
      }
    });
    // 处理拖拽结果
    if (isCorrect && correctHotArea) {
      // 异步处理成功放置，不等待完成
      processDragSuccess($dragElement, dragId, correctHotArea);
      return true;
    } else {
      // 处理拖拽失败，返回原位置
      returnToOriginalPosition(dragElement);
      return false;
    }
  }

  /**
   * 处理成功拖放到热区的元素
   * @param {jQuery} $dragElement - 拖拽元素jQuery对象
   * @param {string|number} dragId - 拖拽元素ID
   * @param {jQuery} $correctHotArea - 正确热区jQuery对象
   */
  function processDragSuccess($dragElement, dragId, $correctHotArea) {
    // 获取热区元素
    const hotAreaIndex = $correctHotArea.data('index');
    const hotArea = configData.source.hotAreaArr[hotAreaIndex];
    const dragIdStr = String(dragId);
    const correctArr = JSON.parse($correctHotArea.attr('data-correct') || '[]');

    // 获取拖拽元素配置
    const dragConfig = getDragConfigById(dragId);

    // 检查该热区内是否已经有相同ID的拖拽项
    if (checkHotAreaHasDragId($correctHotArea, dragIdStr)) {
      returnToOriginalPosition($dragElement[0]);
      return;
    }

    // 判断是否只有一个正确答案，若是则居中放置（异步执行，不阻塞）
    if (correctArr.length === 1) {
      // 将元素居中放置到热区
      centerElementInHotArea($dragElement, $correctHotArea);
    }

    // 获取当前拖拽元素位置转为rem
    const dragElementStyle = $dragElement.attr('style');
    // 使用正则表达式从style中提取left和top值
    const leftMatch = dragElementStyle.match(/left:\s*([\d.]+)rem/);
    const topMatch = dragElementStyle.match(/top:\s*([\d.]+)rem/);
    const dragElementLeft = leftMatch ? leftMatch[1] : 0;
    const dragElementTop = topMatch ? topMatch[1] : 0;
    SDK.syncData.dragId = dragId
    SDK.syncData.hotArea = hotArea
    SDK.syncData.dragElementLeft = dragElementLeft + 'rem'
    SDK.syncData.dragElementTop = dragElementTop + 'rem'
    if (isSync) {
      SDK.bindSyncEvt({
        index: $dragElement.data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'posDrag'
      });
    } else {
      $dragElement.trigger('posDrag', [dragId, hotArea]);
    }
  }

  /**
   * 处理可多次拖拽的元素
   */
  function handleMultipleDragItem($dragElement, dragId) {
    // 将dragId转为字符串形式
    const dragIdStr = String(dragId);

    // 获取拖拽元素配置
    const dragConfig = getDragConfigById(dragId);
    if (dragConfig.dragNum === '2') {
      // 无限拖拽，直接复制
      cloneDragElement($dragElement[0], null, dragConfig);
      return;
    }

    // 获取正确的热区
    const $correctHotArea = $('.hot-area').filter(function () {
      const correctArr = JSON.parse($(this).attr('data-correct') || '[]');
      return correctArr.includes(dragIdStr);
    });

    // 统计此ID在所有热区中出现的次数
    const totalOccurrences = countTotalOccurrencesInAllHotAreas(dragIdStr);

    // 统计当前已有的此ID元素数量（包括原始和克隆的）
    const existingElementsCount = $('.drag-item[data-id="' + dragId + '"]').length;

    // 如果现有元素少于需要的总数，就复制
    if (existingElementsCount < totalOccurrences) {
      cloneDragElement($dragElement[0], $correctHotArea, dragConfig);
    }
  }

  /**
   * 将元素居中放置在热区内
   */
  function centerElementInHotArea($dragElement, $hotArea) {
    // 计算热区中心位置
    const hotAreaRect = $hotArea[0].getBoundingClientRect();
    const containerOffset = $('.container').offset();
    const centerX = (hotAreaRect.left + hotAreaRect.width / 2 - containerOffset.left) / window.base;
    const centerY = (hotAreaRect.top + hotAreaRect.height / 2 - containerOffset.top) / window.base;

    // 优先使用元素上存储的原始尺寸
    const originalWidth = $dragElement.attr('data-original-width');
    const originalHeight = $dragElement.attr('data-original-height');

    // 先添加过渡效果，确保动画流畅
    $dragElement.css({
      'transition': 'left 0.2s ease, top 0.2s ease'
    });

    if (originalWidth && originalHeight) {
      // 使用存储的原始尺寸计算，立即设置位置
      $dragElement.css({
        left: `${centerX - originalWidth / 200}rem`, // 除以200 = 除以100再除以2（取中点）
        top: `${centerY - originalHeight / 200}rem`
      });
    } else {
      // 回退方案：从配置中获取尺寸
      const dragId = $dragElement.data('id');
      const dragConfig = getDragConfigById(dragId);

      if (dragConfig && dragConfig.dragItem) {
        // 异步获取尺寸，但不阻塞主流程
        getImageDimensions(dragConfig.dragItem).then(dimensions => {
          // 使用原始尺寸计算
          const originalWidth = dimensions.width / 100;
          const originalHeight = dimensions.height / 100;

          // 将拖拽元素定位到热区中央
          $dragElement.css({
            left: `${centerX - originalWidth / 2}rem`,
            top: `${centerY - originalHeight / 2}rem`
          });
        });
      } else {
        // 最后的回退方案：使用当前尺寸，立即设置
        const dragWidth = $dragElement.width() / window.base;
        const dragHeight = $dragElement.height() / window.base;

        // 将拖拽元素定位到热区中央
        $dragElement.css({
          left: `${centerX - dragWidth / 2}rem`,
          top: `${centerY - dragHeight / 2}rem`
        });
      }
    }
  }

  /**
   * 克隆拖拽元素
   * @param {Element} originalElement - 原始拖拽元素
   * @param {jQuery} hotArea - 热区元素
   * @param {Object} dragConfig - 拖拽元素配置
   * @returns {jQuery} 克隆的元素
   */
  function cloneDragElement(originalElement, hotArea, dragConfig) {
    // 获取原始元素信息
    const $original = $(originalElement);
    const dragId = $original.data('id');
    const originalWidth = $original.attr('data-original-width');
    const originalHeight = $original.attr('data-original-height');

    // 计算克隆元素的唯一标识
    const cloneCount = $('.drag-item.cloned[data-id="' + dragId + '"]').length;
    const uniqueSyncAction = 'syncactions' + dragId + '_clone_' + cloneCount;

    // 创建一个干净的副本，不包含任何SVG元素
    const $cleanOriginal = $('<div>')
      .addClass('drag-item')
      .attr('data-id', dragId)
      .attr('data-dragnum', $original.attr('data-dragnum'))
      .attr('data-memory-id', $original.attr('data-memory-id')) // 复制记忆ID属性
      .css({
        position: 'absolute',
        left: $original.attr('init-left'),
        top: $original.attr('init-top'),
        width: $original.css('width'),
        height: $original.css('height'),
        backgroundImage: $original.css('backgroundImage'),
        backgroundSize: 'contain',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        transform: 'scale(1)',
        transition: '0s',
        cursor: 'move',
        zIndex: '10',
        'pointer-events': 'none'
      })
      .addClass('cloned')
      .attr({
        'data-syncactions': uniqueSyncAction,
        'init-left': $original.attr('init-left'),
        'init-top': $original.attr('init-top'),
        'data-original-width': originalWidth,
        'data-original-height': originalHeight
      });

    // 应用initialSize设置宽高
    if (dragConfig && dragConfig.initialSize && originalWidth && originalHeight) {
      const scale = dragConfig.initialSize / 100;
      $cleanOriginal.css({
        width: originalWidth * scale / 100 + 'rem',
        height: originalHeight * scale / 100 + 'rem'
      });
    }

    // 将克隆元素添加到容器
    $('.mainArea').append($cleanOriginal);

    // 如果原始元素使用Lottie动画，也为克隆元素创建动画
    if (dragConfig && isJsonFile(dragConfig.dragItem)) {
      const instanceKey = `drag_${dragId}_clone_${cloneCount}`;

      // 确保动画容器元素已准备好
      setTimeout(() => {
        lottieAnimations.init(
          null,
          dragConfig.dragItem,
          `[data-syncactions="${uniqueSyncAction}"]`,
          true
        ).then(instance => {
          lottieInstances[instanceKey] = instance;
          lottieAnimations.play(instance);
        });
      }, 50);
    }
    bindDragEvents($cleanOriginal);

    return $cleanOriginal;
  }

  /**
   * 为元素绑定拖拽事件
   * @param {jQuery} $element - 需要绑定事件的元素
   */
  function bindDragEvents($element) {
    // 检查元素是否可以拖拽
    const memoryId = $element.attr('data-memory-id');
    const canDrag = !memoryId || memoryData.selectedArr.includes(memoryId);

    if (!canDrag) {
      // 不可拖拽的元素，直接返回，不绑定任何事件
      return;
    }
    // 悬停效果
    $element.on('mouseover', function () {
      if (!$(this).hasClass('fixed-position')) {
        $(this).css('transform', 'scale(1.2)');
      }
    }).on('mouseout', function () {
      if (!$(this).hasClass('fixed-position')) {
        $(this).css('transform', 'scale(1)');
      }
    });

    // 绑定拖拽事件
    $element.drag({
      before: function (e) {
        $(this).css({
          'transition': '0s',
          'transform': 'scale(1)',
          'z-index': '1000'
        }).off('mouseover');
      },
      process: function (e) {
        $(this).css('z-index', '1000');
      },
      end: function (e) {
        $(this).css({
          'transition': '0.3s',
          'z-index': '100'
        }).on('mouseover', function () {
          if (!$(this).hasClass('fixed-position')) {
            $(this).css('transform', 'scale(1.2)');
          }
        });
        checkDragInHotArea(this);
      }
    });
  }

  /**
   * 处理正确放置的拖拽元素
   * @param {Element} dragElement - 拖拽元素
   * @param {string|number} dragId - 拖拽元素ID
   * @param {Object} hotArea - 热区配置
   */
  function handleCorrectPlacement(dragElement, dragId, hotArea) {
    hasPlacedCorrectly.push(dragId);
    // 获取热区元素
    const $correctHotArea = $('.hot-area').filter(function () {
      const correctArr = JSON.parse($(this).attr('data-correct') || '[]');
      return correctArr.includes(String(dragId));
    });

    // 如果热区只有一个正确答案，确保元素已经中心对齐后再播放动画
    const correctArr = JSON.parse($correctHotArea.attr('data-correct') || '[]');

    //恢复原大小 - 立即设置，不等待
    const $dragElement = $(dragElement);

    // 设置递增的z-index，确保后放置的元素在上层
    maxPlacedZIndex += 1;
    $dragElement.css('z-index', maxPlacedZIndex);

    const originalWidth = $dragElement.attr('data-original-width');
    const originalHeight = $dragElement.attr('data-original-height');

    // 优先使用元素上存储的原始尺寸，立即设置不等待
    if (originalWidth && originalHeight) {
      $dragElement.css({
        transition: 'width 200ms, height 200ms',
        width: originalWidth / 100 + 'rem',
        height: originalHeight / 100 + 'rem'
      });
    } else {
      // 回退方案：异步获取尺寸，但不阻塞主流程
      const dragItemUrl = getDragConfigById(dragId).dragItem;
      getImageDimensions(dragItemUrl).then(dimensions => {
        $dragElement.css({
          transition: 'width 200ms, height 200ms',
          width: dimensions.width / 100 + 'rem',
          height: dimensions.height / 100 + 'rem'
        });
      }).catch(error => {
        console.error('获取图片尺寸失败:', error);
      });
    }

    // 等待元素尺寸恢复完成后再播放星星动画
    setTimeout(() => {
      // 停止当前播放的音频
      stopCurrentAudio();
      // 异步播放动画和音频，不阻塞拖拽功能
      Promise.all([
        playStarAnimation(dragElement),
        $('#correctAudio').playAudioSync()
      ]).then(() => {
        // 获取拖拽元素配置
        const dragConfig = getDragConfigById(dragId);
        // 播放自定义音频（优先播放拖拽项的音频，如果没有则播放热区的音频）
        if (dragConfig && dragConfig.audio) {
          $("#dragAudio").attr("src", dragConfig.audio);
          // 记录当前播放的音频信息
          currentAudioInfo = {
            element: $('#dragAudio').get(0),
            syncName: $('#dragAudio').attr("data-syncaudio")
          };
          return $('#dragAudio').playAudioSync();
        } else if (hotArea.audio) {
          $("#hotAreaAudio").attr("src", hotArea.audio);
          // 记录当前播放的音频信息
          currentAudioInfo = {
            element: $('#hotAreaAudio').get(0),
            syncName: $('#hotAreaAudio').attr("data-syncaudio")
          };
          return $('#hotAreaAudio').playAudioSync();
        }
      }).then(() => {
        // 播放完成后清除音频信息
        currentAudioInfo = null;
        // 移除移动光标样式
        $(dragElement).css('cursor', 'default');
        // 检查是否所有拖拽任务都已完成
        checkAllTasksCompleted();
      });
    }, 150);
  }

  /**
   * 禁用元素的拖拽功能
   * @param {Element} dragElement - 需要禁用拖拽的元素
   */
  function disableDragging(dragElement) {
    $(dragElement)
      .addClass('fixed-position')
      .css({
        'pointer-events': 'none',
        'cursor': 'default'
      })
      .off('mousedown touchstart mouseover mouseout');
  }

  /**
   * 获取拖拽元素的配置
   * @param {string|number} dragId - 拖拽元素ID
   * @returns {Object} 拖拽元素配置
   */
  function getDragConfigById(dragId) {
    // 将dragId转换为字符串进行比较
    const dragIdStr = String(dragId);
    return configData.source.dragArr.find(item => String(item.id) === dragIdStr);
  }

  /**
   * 返回原位置
   * @param {Element} dragElement - 拖拽元素
   */
  function returnToOriginalPosition(dragElement) {
    // 使用元素上保存的初始位置
    const $element = $(dragElement);
    $element.css({
      'transition': '0.3s',
      'left': $element.attr('init-left'),
      'top': $element.attr('init-top')
    });

    // 立即恢复所有拖拽元素的交互能力，不等待过渡完成
    $('.drag-item').not('.fixed-position').css('pointer-events', 'auto');
  }

  /**
   * 绑定事件
   */
  function bindEvents() {
    // 为每个拖拽元素绑定拖拽事件
    $('.drag-item').each(function () {
      bindDragEvents($(this));
    })

    //绑定done按钮事件 用于结束
    $(document).on('click touchstart', '.done', function (e) {
      if (e.type == 'touchstart') {
        e.preventDefault()
      }
      e.stopPropagation();
      if (!isSync) {
        $(this).trigger('doneClick');
        return;
      } else if (window.frameElement.getAttribute('user_type') == 'stu') {
        return;
      }
      SDK.bindSyncEvt({
        sendUser: '',
        receiveUser: '',
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event',
        syncName: 'doneClick',
        recoveryMode: '1'
      });
    })
    $(document).on('doneClick', '.done', function (e, message) {
        $('.done').hide();
        isDoneButtonShown = true;
        end();
    })

  }

  /**
   * 检查是否所有拖拽任务已完成
   */
  function checkAllTasksCompleted() {
    //如果配置项里有dragNum === '2' 的元素 那么展示是 done
    const hasInfiniteDrag = configData.source.dragArr.some(item => item.dragNum === '2');
    if (hasInfiniteDrag) {
      // 确保done按钮只显示一次
      if (!isDoneButtonShown) {
        $('.done').show();
      }
      return; // 如果有无限拖拽元素，不需要检查完成状态，直接显示done按钮
    }
    // 创建一个平铺的所有热区答案数组
    const allRequiredDragIds = [];

    // 收集所有热区正确答案ID
    configData.source.hotAreaArr.forEach(hotArea => {
      if (hotArea.correctArr) {
        allRequiredDragIds.push(...hotArea.correctArr);
      }
    });

    // 简单方法：排序并字符串化后比较
    const sortedRequired = [...allRequiredDragIds].map(String).sort().join(',');
    const sortedPlaced = [...hasPlacedCorrectly].map(String).sort().join(',');

    console.log('答案', sortedRequired)
    console.log('已选择的', sortedPlaced)
    // 比较结果并在任务完成时调用end方法
    if (sortedRequired === sortedPlaced) {
      end();
    }
  }

  /**
   * 初始化星星动画
   */
  function initStarAnimation() {
    const starJsonPath = './image/star.json';

    // 获取JSON文件的宽高
    getImageDimensions(starJsonPath)
      .then(dimensions => {
        // 初始设置为隐藏
        $('#starEl').css({
          'position': 'absolute',
          'width': dimensions.width / 100 + 'rem',
          'height': dimensions.height / 100 + 'rem',
          'display': 'none',
          'z-index': '200',
        });

        // 初始化星星Lottie动画
        return lottieAnimations.init(null, starJsonPath, '#starEl', false);
      })
      .then(instance => {
        lottieInstances.starAnimation = instance;
      })
      .catch(error => {
        console.error('星星动画初始化失败:', error);
      });
  }

  /**
   * 播放星星动画
   * @param {HTMLElement} dragElement - 拖拽元素
   * @returns {Promise} 播放完成的Promise
   */
  function playStarAnimation(dragElement) {
    return new Promise((resolve, reject) => {
      try {
        // 计算拖拽元素中心位置
        const $dragElement = $(dragElement);
        const dragPosition = $dragElement.position();
        const dragCenterX = dragPosition.left + $dragElement.outerWidth() / 2;
        const dragCenterY = dragPosition.top + $dragElement.outerHeight() / 2;
        // 显示星星元素并定位到拖拽元素中心
        $('#starEl').css({
          'display': 'block',
          'position': 'absolute',
          'left': dragCenterX + 'px',
          'top': dragCenterY + 'px',
          'transform': 'translate(-50%, -50%)',
          'z-index': '200'
        });
        // 播放动画
        const starAnim = lottieInstances.starAnimation;
        lottieAnimations.play(starAnim);
        // 监听动画完成事件
        starAnim.addEventListener("complete", () => {
          stopLottieAnimation('starAnimation');
          $('#starEl').hide();
          resolve();
        });
      } catch (error) {
        console.error('播放星星动画出错:', error);
        reject(error);
      }
    });
  }

  /**
   * 停止指定动画
   * @param {string} animId - 动画ID
   * @returns {boolean} 是否成功停止
   */
  function stopLottieAnimation(animId) {
    const anim = typeof animId === 'string' ? lottieInstances[animId] || lottieInstances.animId : animId;
    if (anim) {
      lottieAnimations.stop(anim);
      return true;
    }
    return false;
  }

  /**
   * 停止当前播放的音频
   */
  function stopCurrentAudio() {
    if (currentAudioInfo) {
      SDK.pauseRudio({
        index: currentAudioInfo.element,
        syncName: currentAudioInfo.syncName
      });
      currentAudioInfo = null;
    }
  }

  /**
   * 统计一个ID在所有热区中出现的总次数
   * @param {string} dragIdStr - 拖拽元素ID（字符串格式）
   * @returns {number} 出现总次数
   */
  function countTotalOccurrencesInAllHotAreas(dragIdStr) {
    return configData.source.hotAreaArr.reduce((total, hotArea) => {
      const correctArr = hotArea.correctArr || [];
      // 计算此ID在当前热区中出现的次数
      const countInThisArea = correctArr.filter(id => id === dragIdStr).length;
      return total + countInThisArea;
    }, 0);
  }

  /**
   * 检查热区是否已经有相同ID的拖拽项
   * @param {jQuery} hotAreaEl - 热区元素
   * @param {string} dragIdStr - 拖拽元素ID
   * @returns {boolean} 是否已有相同ID的拖拽项
   */
  function checkHotAreaHasDragId(hotAreaEl, dragIdStr) {
    console.log('拖拽项', dragIdStr)
    //拿dragIdStr id去配置项里找到对应dragNum 如果等于2 那么直接返回false
    const dragConfig = getDragConfigById(dragIdStr);
    if (dragConfig && dragConfig.dragNum === '2') {
      return false; // 无限拖拽项，不限制放置
    }

    // 获取热区的位置和尺寸
    const hotAreaRect = hotAreaEl[0].getBoundingClientRect();

    // 获取热区允许的同一ID拖拽项的数量
    const correctArr = JSON.parse(hotAreaEl.attr('data-correct') || '[]');
    const allowedCount = correctArr.filter(id => id === dragIdStr).length;

    // 计算当前热区已放置的相同ID拖拽项的数量
    const placedCount = $('.drag-item.fixed-position[data-id="' + dragIdStr + '"]').filter(function () {
      return isOverlapping(this.getBoundingClientRect(), hotAreaRect, true);
    }).length;

    // 如果已放置数量达到或超过允许数量，则返回true表示不允许再放置
    return placedCount >= allowedCount;
  }

  // 初始化应用
  init();
});



