<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>DYTZ0001FT_多样拖拽FT</title>
    <base href="" />
    <link rel="stylesheet" href="form/css/style.css" />
    <link rel="stylesheet" href="form/js/multiple-select.min.css" />
    <script src="form/js/jquery-2.1.1.min.js"></script>
    <script src="form/js/vue.min.js"></script>
    <script src="form/js/multiple-select.min.js"></script>
  </head>
  <body>
    <div id="container">
      <div class="edit-form">
        <div class="h-title">DYTZ0001FT_多样拖拽FT</div>

        <% include ./src/common/template/common_head %>
        <!-- 交互提示标签 -->
        <% include ./src/common/template/dynamicInstruction/form.ejs %> <%
        include ./src/common/template/multyDialog/form.ejs %> <% include
        ./src/common/template/memory/form.ejs %>

        <div class="c-group">
          <div class="c-title">拖拽项（最多18个）</div>
          <div class="c-area upload img-upload">
            <ul>
              <li v-for="(item, index) in configData.source.dragArr">
                <div class="c-well">
                  <span
                    class="dele-tg-btn"
                    style="z-index: 9; position: relative"
                    v-show="configData.source.dragArr.length > 1"
                    v-on:click="deleteDrag(item)"
                  ></span>
                  <!-- 初始状态 -->
                  <div class="field-wrap">
                    <label class="field-label" style="width: 80px">
                      选择记忆ID</label
                    >
                    <select v-model="item.memoryId" style="width: 175px">
                      <option
                        v-for="(memItem,index) in getAvailableOptions(item)"
                        :value="memItem.id"
                      >
                        ID:{{ memItem.id }} &nbsp;&nbsp;名称:{{ memItem.name }}
                      </option>
                    </select>
                    <a
                      href="javascript:void(0)"
                      style="
                        margin-left: 8px;
                        font-size: 12px;
                        color: #666;
                        text-decoration: none;
                      "
                      @click="clearMemoryId(item)"
                      title="清空选择"
                      v-show="item.memoryId"
                      >清空</a
                    >
                    <div style="color: red">
                       选择ID：限制为该记忆ID才可拖拽；不选择ID：可自由拖拽
                    </div>
                  </div>
                  <div class="field-wrap" style="margin-top: 10px">
                    <label class="field-label"
                      >拖拽项{{ index + 1 }}&nbsp;&nbsp;&nbsp;[ID:{{
                        item.id
                      }}]<em>*</em></label
                    >
                    <span class="field-wrap">
                      <label
                        :for="'content-pic-dragItem'+index"
                        class="btn btn-show upload"
                        v-if="!item.dragItem"
                        >上传</label
                      >
                      <label
                        :for="'content-pic-dragItem'+index"
                        class="btn upload re-upload"
                        v-if="item.dragItem"
                        >重新上传</label
                      >
                    </span>
                    <div class="txt-info">
                      <em>JPG、PNG、JSON格式小于等于240Kb</em>
                    </div>
                    <input
                      type="file"
                      v-bind:key="Date.now()+'dragItem'"
                      class="btn-file"
                      :id="'content-pic-dragItem'+index"
                      size=""
                      accept=".jpg,.png,.json"
                      @change="feedbackUpload($event,item,'dragItem',240)"
                    />
                  </div>

                  <div class="img-preview" v-if="item.dragItem">
                    <img
                      :src="item.dragItem.endsWith('.json') ? './image/1f3f6f9a5c2053c323a9819c947347f6.jpeg' : item.dragItem"
                      alt=""
                    />
                    <div class="img-tools">
                      <span
                        class="btn btn-delete"
                        @click="delPrew(item,'dragItem')"
                        >删除</span
                      >
                    </div>
                  </div>
                  <div class="field-wrap-item" style="margin-top: 20px">
                    <span>拖拽项位置: &nbsp;&nbsp;</span>
                    X:<input
                      type="number"
                      class="c-input-txt"
                      style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                      oninput="if(value>1920)value=1920;if(value<0)value=0"
                      v-model="item.x"
                    />
                    Y:<input
                      type="number"
                      class="c-input-txt"
                      style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                      oninput="if(value>1080)value=1080;if(value<0)value=0"
                      v-model="item.y"
                    />
                    <br />
                  </div>

                  <div class="field-wrap" style="margin-top: 20px">
                    <label>初始大小：</label>
                    <input
                      class="c-input-txt"
                      type="number"
                      style="
                        margin: 0 10px;
                        width: 100px !important;
                        display: inline-block;
                      "
                      oninput="if(value>200)value=200;if(value<0)value=0"
                      placeholder=""
                      v-model="item.initialSize"
                    />
                    <span>1%-200%</span>
                  </div>

                  <!-- 上传声音 -->
                  <div class="field-wrap" style="margin-top: 20px">
                    <label class="field-label" for="">拖拽项音频</label>
                    <span>
                      <label
                        :for="'audio-upload-audio'+index"
                        class="btn btn-show upload"
                        v-if="item.audio==''?true:false"
                        >上传</label
                      >
                      <label
                        :for="'audio-upload-audio'+index"
                        class="btn upload re-upload mar"
                        v-if="item.audio!=''?true:false"
                        >重新上传</label
                      >
                    </span>
                    <div style="color: red">mp3格式，小于等于50Kb</div>

                    <div
                      class="audio-preview"
                      v-show="item.audio!=''?true:false"
                    >
                      <div class="audio-tools">
                        <p v-show="item.audio!=''?true:false">
                          {{ item.audio }}
                        </p>
                      </div>
                      <span class="play-btn" v-on:click="play($event)">
                        <audio v-bind:src="item.audio"></audio>
                      </span>
                    </div>
                    <span
                      class="btn btn-audio-dele"
                      v-show="item.audio!=''?true:false"
                      v-on:click="item.audio=''"
                      >删除</span
                    >
                    <input
                      type="file"
                      :id="'audio-upload-audio'+index"
                      class="btn-file upload"
                      size=""
                      accept=".mp3"
                      v-on:change="audioUpload($event,item,'audio',50)"
                      v-bind:key="Date.now()+'audio'"
                    />
                  </div>
                  <div class="field-wrap" style="margin-top: 20px">
                    <label class="field-label" style="width: 80px"
                      >拖拽次数<em>*</em></label
                    >
                    <select
                      v-model="item.dragNum"
                      style="width: 150px"
                      @change="clearHotAreaAnswers(item)"
                    >
                      <option value="0">拖拽一次</option>
                      <option value="1">拖拽多次</option>
                      <option value="2">无限拖拽</option>
                    </select>
                    <div style="color: red">
                      多次切换到一次，会导致关联该选项的热区答案清除该拖拽项
                    </div>
                  </div>

                  <button
                    class="add-tg-btn"
                    style="margin-top: 20px"
                    v-on:click="addDrag"
                    v-if="index < 17 && index === configData.source.dragArr.length -1"
                  >
                    +
                  </button>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="c-group">
          <div class="c-title">热区项（最多18个）</div>
          <div class="c-area upload img-upload">
            <ul>
              <li v-for="(item, index) in configData.source.hotAreaArr">
                <div class="c-well">
                  <span
                    class="dele-tg-btn"
                    style="z-index: 9; position: relative"
                    v-on:click="deleteHotArea(item)"
                    v-show="configData.source.hotAreaArr.length > 1"
                  ></span>
                  <div class="field-wrap">
                    <label class="field-label"
                      >热区图片{{ index + 1 }}<em>*</em></label
                    >
                    <span class="field-wrap">
                      <label
                        :for="'content-pic-hotAreaItem'+index"
                        class="btn btn-show upload"
                        v-if="!item.hotAreaItem"
                        >上传</label
                      >
                      <label
                        :for="'content-pic-hotAreaItem'+index"
                        class="btn upload re-upload"
                        v-if="item.hotAreaItem"
                        >重新上传</label
                      >
                    </span>
                    <div class="txt-info">
                      <em>JPG、PNG格式，小于等于240Kb </em>
                    </div>
                    <input
                      type="file"
                      v-bind:key="Date.now()+'hotAreaItem'"
                      class="btn-file"
                      :id="'content-pic-hotAreaItem'+index"
                      size=""
                      accept=".jpg,.png"
                      @change="imageUpload($event,item,'hotAreaItem',240)"
                    />
                  </div>

                  <div class="img-preview" v-if="item.hotAreaItem">
                    <img :src="item.hotAreaItem" alt="" />
                    <div class="img-tools">
                      <span
                        class="btn btn-delete"
                        @click="delPrew(item,'hotAreaItem')"
                        >删除</span
                      >
                    </div>
                  </div>
                  <div class="field-wrap-item">
                    <span>热区位置: &nbsp;&nbsp;</span>
                    X:<input
                      type="number"
                      class="c-input-txt"
                      style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                      oninput="if(value>1920)value=1920;if(value<0)value=0"
                      v-model="item.x"
                    />
                    Y:<input
                      type="number"
                      class="c-input-txt"
                      style="
                        margin: 0 10px;
                        width: 60px !important;
                        display: inline-block;
                      "
                      oninput="if(value>1080)value=1080;if(value<0)value=0"
                      v-model="item.y"
                    />
                    <br />
                  </div>

                  <div class="field-wrap" style="margin-top: 20px">
                    <label class="field-label" for="">热区音频</label>
                    <span>
                      <label
                        :for="'audio-upload-hotAreaAudio'+index"
                        class="btn btn-show upload"
                        v-if="item.audio==''?true:false"
                        >上传</label
                      >
                      <label
                        :for="'audio-upload-hotAreaAudio'+index"
                        class="btn upload re-upload mar"
                        v-if="item.audio!=''?true:false"
                        >重新上传</label
                      >
                    </span>
                    <div style="color: red">mp3格式，小于等于50Kb</div>

                    <div
                      class="audio-preview"
                      v-show="item.audio!=''?true:false"
                    >
                      <div class="audio-tools">
                        <p v-show="item.audio!=''?true:false">
                          {{ item.audio }}
                        </p>
                      </div>
                      <span class="play-btn" v-on:click="play($event)">
                        <audio v-bind:src="item.audio"></audio>
                      </span>
                    </div>
                    <span
                      class="btn btn-audio-dele"
                      v-show="item.audio!=''?true:false"
                      v-on:click="item.audio=''"
                      >删除</span
                    >
                    <input
                      type="file"
                      :id="'audio-upload-hotAreaAudio'+index"
                      class="btn-file upload"
                      size=""
                      accept=".mp3"
                      v-on:change="audioUpload($event,item,'audio',50)"
                      v-bind:key="Date.now()"
                    />
                  </div>
                  <div class="field-wrap" style="margin-top: 20px">
                    <label class="field-label" style="width: 80px"
                      >答案设置<em>*</em></label
                    >
                    <select
                      :class="['multiple-select', 'multiple-select-'+index]"
                      v-model="item.correctArr"
                      multiple
                    >
                      <option
                        v-for="dragItem in getAvailableDragItems(item)"
                        :key="dragItem.id"
                        :value="dragItem.id"
                      >
                        拖拽元素{{
                          configData.source.dragArr.indexOf(dragItem) + 1
                        }}
                        (ID: {{ dragItem.id }})
                      </option>
                    </select>
                  </div>

                  <button
                    class="add-tg-btn"
                    style="margin-top: 20px"
                    v-if="index < 17 && index === configData.source.hotAreaArr.length -1"
                    v-on:click="addHotArea"
                  >
                    +
                  </button>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <!-- 反馈动画添加 -->
        <% include ./src/common/template/feedbackAnimation/form %>
        <button class="send-btn" v-on:click="onSend">提交</button>
      </div>
      <div class="edit-show">
        <div class="show-fixed">
          <div class="show-img">
            <img
              src="form/img/preview.jpg?v=<%= new Date().getTime() %>"
              alt=""
            />
          </div>
          <ul class="show-txt">
            <li><em>图片格式：</em>JPG/PNG/GIF</li>
            <li><em>声音格式：</em>MP3/WAV</li>
            <li><em>视频格式：</em>MP4</li>
            <li>带有" * "号为必填项</li>
          </ul>
        </div>
      </div>
    </div>
  </body>
  <script src="form/js/form.js?v=<%= new Date().getTime() %>"></script>
</html>
