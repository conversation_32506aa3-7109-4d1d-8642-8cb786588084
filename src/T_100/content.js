var configData = {
  bg: '',
  desc: '',
  title: '',
  tg: [
    {
      content: "c",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
  ],
  level: {
    high: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      }],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    dialogs: {
      // 对话框信息列表
      messages: [
        {
          text: "",
          audio: "",
        },
      ],
      messageLocationX: "", // 消息内容位置x
      messageLocationY: "", // 消息内容位置y
      roleLocationX: "100", // 角色位置x
      roleLocationY: "600", // 角色位置y
      roleImg: "", // 角色图片
      playAfterStauts: "2", // 播放完之后状态
      scale: 100, //缩放比例  1-500
      autoNext: "1", // 是否自动播放下一条对话框
      hiddenStatus: "1", // 播放完是否应藏的状态
    },
    dragArr: [
      {
        memoryId: '1',
        id: '1',//拖拽元素唯一标识
        dragItem: './assets/images/11.png',//拖拽元素url
        x: 100,//拖拽元素x坐标
        y: 100,//拖拽元素y坐标，
        initialSize: 150,//拖拽元素初始大小
        audio: './assets/audios/01.mp3',//拖拽元素音频url
        dragNum: '1',//拖拽元素次数 一次0 多次1
      },
      {
        memoryId: '2',
        id: '2',//拖拽元素唯一标识
        dragItem: './assets/images/3.png',//拖拽元素url
        x: 222,//拖拽元素x坐标
        y: 222,//拖拽元素y坐标，
        initialSize: 100,//拖拽元素初始大小
        audio: './assets/audios/gorgeous.mp3',//拖拽元素音频url
        dragNum: '0',//拖拽元素次数 一次0 多次1
      },
      {
        memoryId: '3',
        id: '3',//拖拽元素唯一标识
        dragItem: './assets/images/Doll_turn1.json',//拖拽元素url
        x: 555,//拖拽元素x坐标
        y: 555,//拖拽元素y坐标，
        initialSize: 50,//拖拽元素初始大小
        audio: './assets/audios/gorgeous.mp3',//拖拽元素音频url
        dragNum: '0',//拖拽元素次数 一次0 多次1
      }
      ,
      {
        memoryId: '4',
        id: '4',//拖拽元素唯一标识
        dragItem: './assets/images/a70a2c30dabf21ea34c76cc400dbed10.png',//拖拽元素url
        x: 888,//拖拽元素x坐标
        y: 555,//拖拽元素y坐标，
        initialSize: 80,//拖拽元素初始大小
        audio: '',//拖拽元素音频url
        dragNum: '0',//拖拽元素次数 一次0 多次1
      }
      ,
      {
        memoryId: '',
        id: '5',//拖拽元素唯一标识
        dragItem: './assets/images/durk.jpg',//拖拽元素url
        x: 999,//拖拽元素x坐标
        y: 555,//拖拽元素y坐标，
        initialSize: 80,//拖拽元素初始大小
        audio: './assets/audios/gorgeous.mp3',//拖拽元素音频url
        dragNum: '1',//拖拽元素次数 一次0 多次1
      },
      {
        memoryId: '',
        id: '6',//拖拽元素唯一标识
        dragItem: './assets/images/11.png',//拖拽元素url
        x: 1200,//拖拽元素x坐标
        y: 300,//拖拽元素y坐标，
        initialSize: 100,//拖拽元素初始大小
        audio: './assets/audios/01.mp3',//拖拽元素音频url
        dragNum: '2',//拖拽元素次数 一次0 多次1 无限拖拽2
      }
    ],
    //热区数组
    hotAreaArr: [
      {
        hotAreaItem: './assets/images/1.png',//热区元素url
        x: '100',//热区元素x坐标
        y: '700',//热区元素y坐标
        audio: './assets/audios/gorgeous.mp3',//热区元素音频url
        correctArr: ['1'],//正确选项数组 存储拖拽元素id
      },
      {
        hotAreaItem: './assets/images/4c0752766ed721e5091db3e7734745ae.png',//热区元素url
        x: '400',//热区元素x坐标
        y: '700',//热区元素y坐标
        audio: './assets/audios/01.mp3',//热区元素音频url
        correctArr: ['1', '2', '3'],//正确选项数组
      },
      {
        hotAreaItem: './assets/images/carota.jpg',//热区元素url
        x: '1200',//热区元素x坐标
        y: '700',//热区元素y坐标
        audio: './assets/audios/01.mp3',//热区元素音频url
        correctArr: ['1', '5','6'],//正确选项数组
      }
    ]
  },
  feedbackLists: [
    {
      positiveFeedback: '2',
      feedbackList: [
        {id: '-1', json: '', mp3: ''},
        {id: '0', json: './image/prefect.json', mp3: './audio/prefect.mp3'},
        {id: '1', json: './image/goldCoin.json', mp3: './audio/goldCoin.mp3'},
        {id: '2', json: './image/FKJB.json', mp3: './audio/resultWin.mp3'},
        {id: '9', json: './image/feedback.json', mp3: './audio/feedback01.mp3'},
      ],
      feedbackObj: {id: '0', json: './image/prefect.json', mp3: './audio/prefect.mp3'},
      feedback: '',
      feedbackAudio: '',
      feedbackName: '整体反馈',
      key: 'feedKey1',
    }
  ]
};
