<!DOCTYPE html>
<html lang="en">
<head>
    <% var title="PRS0002AI_呈现帧动画AI"; %>
    <%include ./src/common/template/index_head %>
</head>
<body>
  <!-- <button id="playButton">Play</button>
  <button id="playMoveButton">PlayMove</button>
  <button id="pauseButton">Pause</button>
  <button id="stopButton">Stop</button> -->

<div class="container" id="container" data-syncresult="1">

  <div id="animationDiv0" class="animationDiv0"></div>
  <div id="animationDiv1" class="animationDiv1"></div>

    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>

    <div class="content-main">
      <!-- 触发按钮 -->
      <div class="buttons hide">
        <div class="play1">play1</div>
        <div class="play2">play2</div>
      </div>
      <!-- 动画组合 -->
      <div class="animation">
        <ul class="optionUl">
        </ul>
      </div>
    </div>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<script inline type="text/javascript" src="js/lottie_svg.min.js"></script>
<%include ./src/common/template/index_bottom %>
<%include ./src/common/template/track_ai %>

</body>
</html>
