// var domain = 'http://172.16.0.107:9011/pages/1152/';
var domain = '';
var updata = [1, 2];
var Data = {
    configData: {
        bg: '',
        desc: '',
        title: '',
        sizeArr: ['', '', '', '1350*600'], //图片尺寸限制
        tg: [{
            title: '',
            content: ''
        }],
        level: {
            high: [{
                title: "",
                content: ""
            }],
            low: [{
                title: "",
                content: "",
            }]
        },
        source: {

            options: [{
              isImgShow: false, //是否显示动画
                isAudioShow: false, //是否显示音频

                img: "", //上传图片
                positionX: "", //上传后图片的横向位置
                positionY: "", //上传后图片的纵向位置
                audio: "", //音频
                audioPlayTime: "", //音频播放时长
                isType: 1, //教学音频是否显示  1：不显示  2:显示
                iconPositionX: "", //教学音频图标的横向位置
                iconPositionY: "", //教学音频图标的纵向位置
            },{
              isImgShow: false, //是否显示动画
                isAudioShow: false, //是否显示音频

              img: "", //上传图片
              positionX: "", //上传后图片的横向位置
              positionY: "", //上传后图片的纵向位置
              audio: "", //音频
              audioPlayTime: "", //音频播放时长
              isType: 1, //教学音频是否显示  1：不显示  2:显示
              iconPositionX: "", //教学音频图标的横向位置
              iconPositionY: "", //教学音频图标的纵向位置
            }
          ]
        },
        // 需上报的埋点
        log: {
            teachPart: -1, //教学环节 -1未选择
            teachTime: -1, // 整理好的教学时长
            tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
        },
        // 供编辑器使用的埋点填写信息
        log_editor: {
            isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
            TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
            TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
        }

    },
    teachInfo: window.teachInfo, //接口获取的教学环节数据
};
$.ajax({
    type: "get",
    url: domain + "content?_method=put",
    async: false,
    success: function(res) {
        if (res.data != "") {
            Data.configData = JSON.parse(res.data);
            if (!Data.configData.level) {
                Data.configData.level = {
                    high: [{
                        title: "",
                        content: "",
                    }],
                    low: [{
                        title: "",
                        content: "",
                    }]
                }
            }
            //老模板未保存log信息，放入默认log
            if (!Data.configData.log) {
                Data.configData.log = {
                    teachPart: -1, //教学环节 -1未选择
                    teachTime: -1, // 整理好的教学时长
                    tplQuestionType: '-1' //-1 请选择  0无题目  1主观判断  2客观判断
                }
                Data.configData.log_editor = {
                    isTeachTimeOther: '-1', //教学时长下拉框选项  -1 未选择 -2其他  30 30S  60 1分钟等
                    TeachTimeOtherM: '', //当建议教学时长选择了其他时  用户填写的分
                    TeachTimeOtherS: '', //当建议教学时长选择了其他时  用户填写的秒
                }
            }
        }
    },
    error: function(res) {
        console.log(res)
    }
});

new Vue({
    el: '#container',
    data: Data,
    watch: {
      'configData.source.imgType': function() {
        console.log(this.configData.source.imgType)
        let isSource = this.configData.source;
        let item = {
            isImgShow: false, //是否显示动画
            isAudioShow: false, //是否显示音频
            img: "", //上传图片
            positionX: "", //上传图片位置
            positionY: "", //上传后的纵向位置
            audio: "", //音频
            audioPlayTime: "", //音频播放时长
            isType: 1, //教学音频是否显示  1：不显示  2:显示
            iconPositionX: "", //教学音频图标的横向位置
            iconPositionY: "", //教学音频图标的纵向位置
        }
        if (isSource.imgType == 1) {
            isSource.triggerType = 1
            isSource.options.splice(1, 1);
        } else {
            isSource.options.push(item)
        }
    },
    'configData.source.triggerType': function() {
        let isSource = this.configData.source;
        let item = {
            title: "",
            subTitle: "",
            moveX: "",
            moveY: "",
            number: '',
            group: '',
        }
        if (isSource.triggerType > 1) {
            if (isSource.optionsTrigger.length < isSource.triggerType) {
                isSource.optionsTrigger.push(item)
            }
        } else {
            isSource.optionsTrigger.splice(1, 1);
        }
    }

    },
    methods: {

        imageUpload: function(e, item, attr, fileSize) {
            var file = e.target.files[0],
                size = file.size,
                naturalWidth = -1,
                naturalHeight = -1,
                that = this;
            if ((size / 1024).toFixed(2) > fileSize) {
                alert("您上传的图片大小为" + (size / 1024).toFixed(2) + "KB, 超过" + fileSize + "K上限，请检查后上传！");
                return;
            }
            console.log("1")
            var img = new Image();
            console.log("2")
            // img.onload = function() {
            //     naturalWidth = img.naturalWidth;
            //     naturalHeight = img.naturalHeight;
            //     console.log("33")
            //     var check = that.sourceImgCheck(e.target, {
            //         height: naturalHeight,
            //         width: naturalWidth
            //     }, item, attr);
            //     console.log("44")
            //     if (check) {
            //         item[attr] = "./form/img/loading.jpg";
                    that.postData(file, item, attr);
            //         img = null;
            //         console.log("441")
            //     } else {
            //       console.log("442")
            //         img = null;
            //     }
            // }
            console.log("55")
            var reader = new FileReader();
            reader.onload = function(evt) {
                img.src = evt.target.result;
            }
            console.log("66")
            reader.readAsDataURL(file, "UTF-8"); //读取文件
        },
        sourceImgCheck: function(input, data, item, attr) {
            let dom = $(input),
                size = dom.attr("size").split(",");
            if (size == "") return true;
            let checkSize = size.some(function(item, idx) {
                let _size = item.split("*"),
                    width = _size[0],
                    height = _size[1];
                if (width == data.width && height == data.height) {
                    return true;
                }
                return false;
            });
            if (!checkSize) {
                //console.error("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
                item[attr] = "";
                alert("应上传图片大小为：" + size.join("或") + ", 但上传图片尺寸为：" + data.width + "*" + data.height);
            }
            return checkSize;
        },
        validate: function() {
            var data = this.configData.source
            var check = true
            const optionsData = new Map(data.options.map((item, index) => [index, item]));
            if (!this.configData.bg) {
                alert("请上传背景图片")
                return
            }


            // 动画组合
            for (let [key, item] of optionsData) {
                key++;
                    if (!item.img) {
                        alert("请上传动画组合" + key + "的图片")
                        return
                    }
                    if (!item.positionX) {
                        alert("请填写动画组合" + key + "的图片X的位置")
                        return
                    }
                    if (!item.positionY) {
                        alert("请填写动画组合" + key + "的图片Y的位置")
                        return
                    }
                    if (!item.audio) {
                        alert("请上传动画组合" + key + "的音频")
                        return
                    }
                    if (!item.audioPlayTime) {
                        alert("请填写动画组合" + key + "的音频时长")
                        return
                    }
                    if (item.audioPlayTime < 1000) {
                        alert("请填写动画组合" + key + "的音频时长为1000~60000毫秒之间")
                        return
                    }
                    if (item.isType == 2) {
                        if (!item.iconPositionX) {
                            alert("请填写动画组合" + key + "的音频X的位置")
                            return
                        }
                        if (!item.iconPositionY) {
                            alert("请填写动画组合" + key + "的音频Y的位置")
                            return
                        }
                    }
            }
            return check
        },
        onSend: function() {
            console.log(Data)
            var data = this.configData;
            //计算“建议教学时长”
            if (data.log_editor.isTeachTimeOther == '-2') { //其他
                data.log.teachTime = data.log_editor.TeachTimeOtherM * 60 + data.log_editor.TeachTimeOtherS + ''
                if (data.log.teachTime == 0) {
                    // alert("请填写正确的建议教学时长")
                    return;
                }
            } else {
                data.log.teachTime = data.log_editor.isTeachTimeOther
            }
            var _data = JSON.stringify(data);
            var val = this.validate();
            if (val === true) {
                $.ajax({
                    url: domain + 'content?_method=put',
                    type: 'POST',
                    data: {
                        content: _data
                    },
                    success: function(res) {
                        console.log(res);
                        window.parent.postMessage('close', '*');
                    },
                    error: function(err) {
                        console.log(err)
                    }
                });
            } else {
                // alert("带“*”为必填项");
            }
        },
        postData: function(file, item, attr) {
            var FILE = 'file';
            bg = arguments.length > 2 ? arguments[2] : null;
            var oldImg = item[attr];
            var data = new FormData();
            data.append('file', file);
            if (oldImg != "") {
                data.append('key', oldImg);
            };
            $.ajax({
                url: domain + FILE,
                type: 'post',
                data: data,
                async: false,
                processData: false,
                contentType: false,
                success: function(res) {
                    console.log(res.data.key);
                    item[attr] = domain + res.data.key;
                },
                error: function(err) {
                    console.log(err)
                }
            })
        },
        audioUpload: function(e, item, attr) {
            //校验规则
            //var _type = this.rules.audio.sources.type;

            //获取到的内容数据
            var file = e.target.files[0],
                type = file.type,
                size = file.size,
                name = file.name,
                path = e.target.value;
            // if (!_type.test(type)) {
            //     alert("您上传的文件类型错误，请检查后再上传！");
            //     return;
            // }
            if ((size / 1024).toFixed(2) > 500) {
                console.warn("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            } else {
                console.log("您上传的音频文件大小：", (size / 1024).toFixed(2) + "K");
            }
            if ((!isNaN(parseInt($(e.target).attr('volume')))) && (size / 1024).toFixed(2) > parseInt($(e.target).attr('volume'))) {
                alert("您上传的音频大小为" + (size / 1024).toFixed(2) + "KB, 超过" + $(e.target).attr('volume') + "K上限，请检查后上传！");
                e.target.value = '';
                return;
            }
            item[attr] = "./form/img/loading.jpg";
            this.postData(file, item, attr);
        },
        addScreen: function(items, obj) {
            // items.push({
            //     "id": Date.now(),
            //     "subTitle": "",
            //     "img": "",
            //     "audio": "",
            //     "text": ""
            // })
        },
        delQue: function(item, array) {
            array.remove(item);
        },
        addTg: function(item) {
            this.configData.tg.push({
                title: '',
                content: ''
            });
        },
        deleTg: function(item) {
            this.configData.tg.remove(item);
        },
        addH: function() {
            this.configData.level.high.push({ title: '', content: '' });
        },
        addL: function(item) {
            this.configData.level.low.push({ title: '', content: '' });
        },
        deleH: function(item) {
            this.configData.level.high.remove(item);
        },
        deleL: function(item) {
            this.configData.level.low.remove(item);
        },
        play: function(e) {
            e.target.children[0].play();
        },
        setAnswer: function(item) {
            this.configData.source.right = item;
        },
        addOption: function(item) {
            if (item) {
                this.configData.source.options.push(item)
            } else {
                this.configData.source.options.push('')
            }
        },
        delOption: function(type, index) {
            let isHideField = this.configData.source.options[index]
            if (type == "img") {
              isHideField.isImgShow = false
          } else {
              isHideField.isAudioShow = false
          }

            isHideField.isType = 1
            isHideField.audio = ''
        },
        // 添加动画
        addAnimationFn: function(index) {
            let isHideField = this.configData.source.options[index]
            isHideField.isImgShow = true
        },
        // 添加音频
        addAudioFn: function(index) {
            let isHideField = this.configData.source.options[index]
            isHideField.isAudioShow = true
        }
    },
});
Array.prototype.remove = function(val) {
    var index = this.indexOf(val);
    if (index > -1) {
        this.splice(index, 1);
    }
};
