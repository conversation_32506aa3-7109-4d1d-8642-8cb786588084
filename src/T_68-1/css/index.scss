@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";

@mixin setEle($l: 0rem, $t: 0rem, $w: 0rem, $h: 0rem) {
  position: absolute;
  left: $l;
  top: $t;
  width: $w;
  height: $h;
}
.hide {
  display: none;
}
.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;
  .desc {
    top: 0.6rem;
  }
  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.content-main {
  .buttons {
    position: absolute;
    cursor: pointer;
    right: 0;
    bottom: 0;
    width: 2rem;
    height: 2rem;
    font-size: 0.6rem;
    div {

    }
  }
  .animation {
    position: relative;
    ul {
      li {
        // display: none;
        .option-img {
          position: absolute;
        }
        .option-img2 {
          position: absolute;
        }
        // 音频播放
        .example-audio {
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          width: 1.45rem;
          height: 1.34rem;
          background: url(../image/sound_bg.png) no-repeat center;
          background-size: cover;
          cursor: pointer;
          z-index: 20;
          display: none;
          .small {
            position: absolute;
            top: 0.3rem;
            left: 0.3rem;
            width: 0.83rem;
            height: 0.8rem;
          }
          img {
            width: 1.45rem;
          }
          .gif {
            display: none;
          }
        }
      }
    }
  }
}

@keyframes handClick {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: -300% 0;
  }
}

#animationDiv0 {
  position: absolute; /* 绝对定位 */
  bottom: 10%; /* 位于父容器的底部 */
  left: 0; /* 位于父容器的左部 */
  width: 100px;
  height: 100px;
  background-color: rgba(0, 0, 0, 0); /* 可选：具有透明效果 */
}

#animationDiv1 {
  position: absolute; /* 绝对定位 */
  bottom: 10%; /* 位于父容器的底部 */
  left: 0; /* 位于父容器的左部 */
  width: 100px;
  height: 100px;
  background-color: rgba(0, 0, 0, 0); /* 可选：具有透明效果 */
}
