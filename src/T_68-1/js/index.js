"use strict";
import "../../common/js/common_1v1.js";
// import '../../common/js/commonFunctions.js'
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: "0", // 是否有授权按钮 1：是 0：否
  };

  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bj.jpg)",
    });
  }
  let options = configData.source.options, //素材
    isCheck = true, //是否可点击
    isPlaySound = true, //是否播放声音
    isKeys = []; //选中后的key集合（用于断线重连）

  //初始化
  var animations = { 0: "", 1: "" };

  /**
   *初始化动画组合列表
   */
  optionsList();
  function optionsList() {
    if (SDK.getUserType() == "tea") {
      $(".buttons").show();
    }
    options.forEach(function (item, index) {
      let liEle = "";
      // 音频和图片的位置
      let audioLeft = item.iconPositionX / 100 + "rem",
        audioTop = item.iconPositionY / 100 + "rem";
      liEle = $(
        `<li class="optionLi optionLiPic" data-isType="${item.isType}" data-audio="${item.audio.length}" data-audio-time="${item.audioPlayTime}">` +
          // '<img  class="option-img">' +
          // '<img  class="option-img2">' +
          `<div class="example-audio sound " data-syncactions="audio-${index}">` +
          '<img src="./image/sound.gif" alt="" class="gif small">' +
          '<img src="./image/sound.png" alt="" class="png small">' +
          `<audio src="${item.audio}" data-syncaudio="audioExample"></audio></div>` +
          "</li>"
      );
      // liEle.find(".option-img").css({
      //   left: imgLeft,
      //   top: imgTop,
      // });
      // liEle.find(".option-img").attr("src", item.img);
      // // 图片初始化样式
      // liEle.find(".option-img2").css({
      //   left: imgLeft,
      //   top: imgTop,
      // });
      // liEle.find(".option-img2").hide();
      // liEle.find(".option-img2").attr("src", item.img2);
      // 音频初始化样式
      liEle.find(".example-audio").css({
        left: audioLeft,
        top: audioTop,
      });

      getdata(index, item); //加载动画并初始化动画

      // // 加载图片 获取宽高
      // var imgList = [
      //   {
      //     image: item.img,
      //   },
      // ];
      // var preImgs = [];
      // //不再分割雪碧图
      // $.when(preloadImg(imgList, preImgs)).done(function () {
      //   //图片加载完成后 才能获得宽高
      //   liEle.find(".option-img").css({
      //     // backgroundSize: '100% 100%',
      //     width: preImgs[0].width / 100 + "rem",
      //     height: preImgs[0].height / 100 + "rem",
      //   });
      //   liEle.find(".option-img2").css({
      //     // backgroundSize: '100% 100%',
      //     width: preImgs[0].width / 100 + "rem",
      //     height: preImgs[0].height / 100 + "rem",
      //   });
      // });
      $(".animation ul").append(liEle);
      // 是否显示音频
      if (item.isType == 2)
        $(".optionUl li").eq(index).find(".example-audio").show();
    });
  }

  //获取动画的宽高，并初始化动画
  function getdata(index, item) {
    console.log("getdatagetdatagetdata", index);
    // let url = `https://cdn.51talk.com/apollo/json/93f0832ba9481ef76140f42e4b20a521.json`;
    $.get(item.img, {}, function (res) {
      if (res.w) {
        console.log("宽高", res.w, res.h);
        $(`#animationDiv${index}`).css({
          width: res.w / 100 + "rem",
          height: res.h / 100 + "rem",
          left: item.positionX / 100 + "rem",
          top: item.positionY / 100 + "rem",
        });
       
        // 初始化动画
        animations[index] = lottie.loadAnimation({
          //初始化
          container: document.getElementById(`animationDiv${index}`), //在哪个dom容器中生效
          renderer: "svg", //渲染方式svg
          loop: false, //循环
          autoplay: false, //自动播放
          path: item.img, //动画数据
        });
        // 监听动画加载完成事件
        //设置动画大小
      }
    });
  }

  /**
   *
   * 音频列表事件
   */
  $(".optionUl").on("click touchstart", "li .example-audio", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    if (!isSync) {
      $(this).trigger("synAudioClick");
      return;
    }

    if (window.frameElement.getAttribute("user_type") == "tea") {
      SDK.bindSyncEvt({
        sendUser: "",
        receiveUser: "",
        index: $(e.currentTarget).data("syncactions"),
        eventType: "click",
        method: "event",
        syncName: "synAudioClick",
        recoveryMode: "1",
      });
    }
  });
  $(".optionUl").on("click synAudioClick", "li .example-audio", function (e) {
    if (isPlaySound) {
      audioFn($(this)).playRudio();
    }
  });

  /**
   * 组合列表事件
   */
  function groupListFn(animationType) {
    let groupType = $(".animation ul li").eq(animationType),
      isShow = groupType.attr("data-isType"),
      isAudio = groupType.attr("data-audio");
    // $(".optionUl").show();
    // 教学音频是否显示
    if (isAudio > 0) {
      // 显示素材
      if (isShow == 2) {
        groupType.find(".example-audio").show();
      }
      // 音频播放事件
      audioFn(groupType.find(".example-audio")).playRudio();
    }
  }

  //为了老师预览 临时代码
  $(".play1").on("click", function () {
    animations[0].stop();
    animations[0].play(); //播放动画
    //播放音频
    var targetEle = $(".example-audio")[0];
    audioFn(targetEle).playRudio();
  });
  $(".play2").on("click", function () {
    animations[1].stop();
    animations[1].play(); //播放动画
     //播放音频
     var targetEle = $(".example-audio")[1];
     audioFn(targetEle).playRudio();
  });

  /**
   *音频方法
   */
  function audioFn($this) {
    $this = $($this);
    return {
      playRudio: function () {
        let gif = $this.find(".gif"),
          png = $this.find(".png"),
          audio = $this.find("audio")[0];
        gif.show();
        png.hide();
        // 开始播放
        if (isPlaySound) {
          
          console.log("开始播放");
          isPlaySound = false;
          isCheck = false;
          SDK.playRudio({
            index: audio,
            syncName: $this.find("audio").attr("data-syncaudio"),
          });
          gif.show();
          png.hide();
        }
        // 播放结束
        audio.onended = function () {
          console.log("播放结束");
          gif.hide();
          png.show();
          isPlaySound = true;
          isCheck = true;
        }.bind(this);
        SDK.setEventLock();
      },
    };
  }

  window.generalTplData = function (message) {
    //根据不同协议调不同方法
    if (message.actionType == "play") {
      //播放动画
      console.log("play", message);
     
      let startTime;
      let endTime;
      startTime = performance.now();
     
      
      // 监听动画结束事件
      animations[message.num].addEventListener("complete", function() {
        // 记录动画结束的时间
        endTime = performance.now();
      
        // 计算播放时长
        const playDuration = (endTime - startTime) / 1000;
        trackify.pind_send({
          num: message.num + 1,
          time:playDuration.toFixed(2),
        });
      });

      
      animations[message.num].stop();
      animations[message.num].play(); //播放动画
     
      //播放音频
      var targetEle = $(".example-audio")[message.num];
      audioFn(targetEle).playRudio();
    }
    SDK.setEventLock();
  };

});
