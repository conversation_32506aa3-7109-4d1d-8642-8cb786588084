!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.trackify=t():e.trackify=t()}(this,(()=>(()=>{"use strict";var e={d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)},t={};function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,n){return(t=function(e){var t=function(e){if("object"!=r(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==r(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,c=[],s=!0,d=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=a.call(r)).done)&&(c.push(n.value),c.length!==t);s=!0);}catch(e){d=!0,o=e}finally{try{if(!s&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(d)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?c(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}e.d(t,{default:()=>j});var s=new URL(location.href),d=new Date,l=s.hostname,u=function(e){if("string"!=typeof e)return!1;try{return JSON.parse(e),!0}catch(e){return console.error("[tracking-sdk error:] isJsonString error:",e),!1}},p=function(e){try{var t="".concat("//mercury.51talkjr.com/t/t.gif?").concat(e,"&nocache=").concat(Date.now()),r=new Image(1,1);r.src=t;var n=setTimeout((function(){console.warn("銆怭indDataTracker error銆� sendPixelTracking request timed out")}),15e3);r.onload=r.onerror=function(){clearTimeout(n)}}catch(e){console.error("[tracking-sdk error:] sendPixelTracking error:",e)}},f=function(e,t){try{h(e,encodeURIComponent(t),"Sun, 18 Jan 2038 00:00:00 GMT")}catch(e){console.error("[tracking-sdk error:] setLongTermCookie error:",e)}},v=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:30;try{var n=new Date;n.setMinutes(n.getMinutes()+r),h(e,encodeURIComponent(t),n.toUTCString())}catch(e){console.error("[tracking-sdk error:] setTimedCookie error:",e)}},h=function(e,t,r){try{var n=l.endsWith(".51talk.com")?".51talk.com":l;document.cookie="".concat(e,"=").concat(t,"; expires=").concat(r,"; path=/; domain=").concat(n)}catch(e){console.error("[tracking-sdk error:] setCookieWithExpiry error:",e)}},m=function(e){try{var t=decodeURIComponent(document.cookie).match(new RegExp("(^|\\s)".concat(e,"=([^;]*)")));return t?t[2]:""}catch(e){return console.error("[tracking-sdk error:] getCookie error:",e),""}},g=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{var r="g",n="SpMLdaPx"+e,o=m(n);if(!o){var a=d.getUTCMilliseconds();for(o=Math.round(2147483647*Math.random())*a%1e10;!o;)o=Math.round(2247483647*Math.random())*a%1e10;r="s",(t?f:v)(n,o)}return o?r+o:"ra"}catch(e){return console.error("[tracking-sdk error:] generateUniqueID error:",e),"r"+Math.floor(1e10*Math.random())}},y=function(){try{var e={scr:"",scl:"",lang:navigator.language.toLowerCase(),pf:navigator.platform,ct:""};self.screen&&(e.scr="".concat(screen.width,"x").concat(screen.height),e.scl="".concat(screen.colorDepth,"-bit")),navigator.connection&&(e.ct=navigator.connection.effectiveType||navigator.connection.type||"");var t=Object.entries(e).map((function(e){var t=i(e,2),r=t[0],n=t[1];return"".concat(r,"=").concat(encodeURIComponent(n))})).join("&");return t?"&".concat(t):""}catch(e){return console.error("[tracking-sdk error:] getUserDeviceInfo error:",e),""}};function b(e,t){var r,n,o=this;return function(){for(var a=arguments.length,i=new Array(a),c=0;c<a;c++)i[c]=arguments[c];var s=o;n?(clearTimeout(r),r=setTimeout((function(){Date.now()-n>=t&&(e.apply(s,i),n=Date.now())}),t-(Date.now()-n))):(e.apply(s,i),n=Date.now())}}var w=function(e,t,r){try{var n=document.getElementById(e),a=document.getElementById(t),c=m("SpMLdaPx_poid"),s=function(e){var t=parseInt(e)>1e4?1:parseInt(e)+1;return f("SpMLdaPx_poid",t),t}(c),d=i((h=location.href.match(/[?&#]((referurl|m_referer)=[^&#]+)(&|#|$)/))?[encodeURIComponent(h[1])]:[""],1)[0],u="(".concat(n.value,")");if("pv"===r){var p=JSON.parse(window.__m_static_jsv),v=JSON.parse(JSON.stringify(p));v.hasOwnProperty("options")&&delete v.options,v.v=v.p?o(o({},v.p),v.v):v.v,delete v.p,u="(".concat(JSON.stringify(v),")")}return["t=".concat(r),"m=".concat(l),"rm=".concat(d),"cul=".concat(encodeURIComponent(location.href)),"ref=".concat(encodeURIComponent(document.referrer)),"v=".concat(u),"uuid=".concat(g("_uuid",!0)),"sid=".concat(g("_sid",!1)),"auid=".concat(m("talk_user_id")),"visit=".concat(m("visitid")),"pvid=".concat(a.value),"_title=".concat(encodeURIComponent(document.title)).concat(y()),"ver=".concat("2.3.2"),"rand=".concat(Math.floor(1e5*Math.random())),"poid=".concat(s),"lapoid=".concat(c)]}catch(e){return console.error("[tracking-sdk error:] commonParams error:",e),[]}var h};function k(e){return k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},k(e)}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach((function(t){O(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function O(e,t,r){return(t=P(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,P(n.key),n)}}function P(e){var t=function(e){if("object"!=k(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=k(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==k(t)?t:t+""}var I=function(){return e=function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);try{if(window.trackify)return window.trackify;this.timer=null,this.dataQueue=[],this.sentDataCache=new Set,this.cacheExpiryTime=300,this.pageLoadTime=null,this.isPageLoaded=!1,this.lastManualReportTime=0,this.isFirstLoad=!0,this.initPingData(),this.setupEventHandlers(),this.setupErrorHandlers(),window.__sdonclick=this.__sdonclick.bind(this),window.add_params=this.add_params.bind(this),this.handlePageChange=this.handlePageChange.bind(this),this.currentPageUrl=window.location.href,this.setupMutationObserver(),this.setupHistoryProxy(),window.addEventListener("popstate",this.handlePageChange),function(){try{window.onload=(e=window.onload,function(){try{e&&e(),t.isPageLoaded=!0,t.setupDom(),t.processQueuedData(),setTimeout((function(){t.trackPerformance()}),0),t.isFirstLoad=!1}catch(e){console.error("[tracking-sdk error:] addLoadEvent callback error:",e)}})}catch(e){console.error("[tracking-sdk error:] addLoadEvent error:",e)}var e}()}catch(e){console.error("[tracking-sdk error:] Initialization error:",e)}},t=[{key:"setupMutationObserver",value:function(){this.observer=new MutationObserver(this.checkUrlChange.bind(this)),this.observer.observe(document,{subtree:!0,childList:!0})}},{key:"setupHistoryProxy",value:function(){var e=this,t=history.pushState,r=history.replaceState;history.pushState=function(){for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];t.apply(history,n),e.handlePageChange()},history.replaceState=function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];r.apply(history,n),e.handlePageChange()}}},{key:"checkUrlChange",value:function(){window.location.href!==this.currentPageUrl&&this.handlePageChange()}},{key:"handlePageChange",value:function(){var e=this,t=window.location.href;t!==this.currentPageUrl&&(this.currentPageUrl=t,this.isFirstLoad||(this.isPageLoaded=!1,this.setupDom(),this.processQueuedData(),setTimeout((function(){e.isPageLoaded=!0,e.trackPerformance()}),0)))}},{key:"setupDom",value:function(){var e=this;try{this.inputButton||(this.inputButton=this.createInput("button","added_object_for_bi","-",(function(){return p(function(){try{return w("added_object_for_bi","page_view_id_for_bi","oc").join("&")}catch(e){return console.error("[tracking-sdk error:] trackOC error:",e),""}}(e.inputButton.value))}))),this.pageViewIdInput||(this.pageViewIdInput=this.createInput("hidden","page_view_id_for_bi")),this.trackPageView()}catch(e){console.error("[tracking-sdk error:] setupDom error:",e)}}},{key:"trackPageView",value:function(){p(function(){try{return document.getElementById("page_view_id_for_bi").value=(e=d.getTime(),f("SpMLdaPx_pvid",e),e),w("page_view_id_for_bi","page_view_id_for_bi","pv").join("&")}catch(e){return console.error("[tracking-sdk error:] trackPV error:",e),""}var e}())}},{key:"createInput",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",n=arguments.length>3?arguments[3]:void 0;try{var o=document.createElement("input");return o.type=e,o.style.display="none",o.id=t,o.value=r,n&&o.addEventListener("click",n),document.body.appendChild(o),o}catch(e){console.error("[tracking-sdk error:] createInput error:",e)}}},{key:"__sdonclick",value:function(e){try{var t=document.getElementById("added_object_for_bi");t?(t.value=e,t.click()):setTimeout(this.__sdonclick.bind(this,e),1e3)}catch(e){console.error("[tracking-sdk error:] __sdonclick error:",e)}}},{key:"initPingData",value:function(){try{var e=window.__m_static_jsv||{},t=u(e)?JSON.parse(e):e,r=t.t,n=void 0===r?"":r,o=t.p,a=void 0===o?null:o,i=t.a,c=void 0!==i&&i,s=t.b,d=void 0===s?"":s,l=t.c,p=void 0===l?null:l,f=t.options,v=void 0===f?[]:f;window.global_ping_data={t:n,p:a,a:c,b:d,c:p,options:v}}catch(e){console.error("[tracking-sdk error:] initPingData error:",e)}}},{key:"add_params",value:function(e){try{"object"===function(e){try{return Object.prototype.toString.call(e).slice(8,-1).toLowerCase()}catch(e){console.error("[tracking-sdk error:] getType error:",e)}}(e)?window.global_ping_data=e:console.warn("鍏叡鍙傛暟蹇呴』涓簅bject绫诲瀷")}catch(e){console.error("[tracking-sdk error:] add_params error:",e)}}},{key:"handleBodyClick",value:function(){var e=this;try{document.body.addEventListener("click",(function(t){if(!(Date.now()-e.lastManualReportTime<50)){if(t.target.hasAttribute("data-analytics")){var r=t.target.getAttribute("data-analytics");return e.pind_send(r)}if(window.global_ping_data.options&&Array.isArray(window.global_ping_data.options)&&window.global_ping_data.options.length>0){var n=window.global_ping_data.options.find((function(e){return e.id===t.target.id}));if(n)return e.pind_send(n)}window.global_ping_data&&window.global_ping_data.a&&"added_object_for_bi"!==t.target.id&&setTimeout((function(){e.pind_send({t:"all_tracking_points",v:{actions:"tracking_points",tag:t.target.tagName,id:t.target.id,classes:t.target.className,text:t.target.innerText.trim()}})}),window.global_ping_data.b?Number(window.global_ping_data.b):300)}}))}catch(e){console.error("[tracking-sdk error:] handleBodyClick error:",e)}}},{key:"pind_send",value:function(e){var t=this;try{this.lastManualReportTime=Date.now();var r={t:(e=u(e)?JSON.parse(e):e).t||window.global_ping_data.t,v:E(E({},window.global_ping_data.p),e.v?e.v:e)};window.global_ping_data.c&&(r.v=E(E({},r.v),window.global_ping_data.c));var n=JSON.stringify(r);if(this.sentDataCache.has(n))return void console.log("Duplicate data detected, skipping:",r);this.sentDataCache.add(n),setTimeout((function(){return t.sentDataCache.delete(n)}),this.cacheExpiryTime),this.isPageLoaded?this.__sdonclick(n):this.dataQueue.push(n)}catch(e){console.error("[tracking-sdk error:] pind_send error:",e)}}},{key:"processQueuedData",value:function(){try{var e,t=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return _(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,c=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){c=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(c)throw a}}}}(this.dataQueue);try{for(t.s();!(e=t.n()).done;){var r=e.value;this.__sdonclick(r)}}catch(e){t.e(e)}finally{t.f()}this.dataQueue=[]}catch(e){console.error("[tracking-sdk error:] processQueuedData error:",e)}}},{key:"setupEventHandlers",value:function(){try{this.handleBodyClick(),this.handleFormInteractions(),this.handleScrollEvents(),this.handleUnloadEvent(),this.handleVisibilityEvents(),this.handleVideoEvents()}catch(e){console.error("[tracking-sdk error:] setupEventHandlers error:",e)}}},{key:"handleFormInteractions",value:function(){try{document.addEventListener("input",function(e){var t,r=this;return function(){for(var n=arguments.length,o=new Array(n),a=0;a<n;a++)o[a]=arguments[a];var i=r;clearTimeout(t),t=setTimeout((function(){return e.apply(i,o)}),500)}}(function(e){if("INPUT"===e.target.tagName||"TEXTAREA"===e.target.tagName||"SELECT"===e.target.tagName){var t={eventType:"input",elementTag:e.target.tagName,elementId:e.target.id,elementClasses:e.target.className,value:e.target.value,timestamp:(new Date).toISOString()};this.pind_send(t)}}.bind(this))),document.addEventListener("submit",function(e){var t=new FormData(e.target),r={};t.forEach((function(e,t){r[t]=e}));var n={eventType:"submit",formId:e.target.id,formClasses:e.target.className,formData:r,timestamp:(new Date).toISOString()};this.pind_send(n)}.bind(this))}catch(e){console.error("[tracking-sdk error:] handleFormInteractions error:",e)}}},{key:"handleMouseEvents",value:function(){try{document.addEventListener("mousemove",b(function(e){var t={eventType:"mousemove",x:e.clientX,y:e.clientY,timestamp:(new Date).toISOString()};this.pind_send(t)}.bind(this),1e3)),document.addEventListener("mouseenter",function(e){var t={eventType:"mouseenter",elementTag:e.target.tagName,elementId:e.target.id,elementClasses:e.target.className,timestamp:(new Date).toISOString()};this.pind_send(t)}.bind(this))}catch(e){console.error("[tracking-sdk error:] handleMouseEvents error:",e)}}},{key:"handleTouchEvents",value:function(){try{document.addEventListener("touchstart",function(e){var t=e.touches[0],r={eventType:"touchstart",x:t.clientX,y:t.clientY,timestamp:(new Date).toISOString()};this.pind_send(r)}.bind(this)),document.addEventListener("touchmove",b(function(e){var t=e.touches[0],r={eventType:"touchmove",x:t.clientX,y:t.clientY,timestamp:(new Date).toISOString()};this.pind_send(r)}.bind(this),1e3))}catch(e){console.error("[tracking-sdk error:] handleTouchEvents error:",e)}}},{key:"handleScrollEvents",value:function(){try{window.addEventListener("scroll",b(function(){var e={eventType:"scroll",scrollX:window.scrollX,scrollY:window.scrollY,timestamp:(new Date).toISOString()};this.pind_send(e)}.bind(this),1e3))}catch(e){console.error("[tracking-sdk error:] handleScrollEvents error:",e)}}},{key:"handleUnloadEvent",value:function(){try{this.pageLoadTime=Date.now(),window.addEventListener("beforeunload",function(){var e={eventType:"unload",timeSpent:Date.now()-this.pageLoadTime,timestamp:(new Date).toISOString()};this.pind_send(e)}.bind(this))}catch(e){console.error("[tracking-sdk error:] handleUnloadEvent error:",e)}}},{key:"handleVisibilityEvents",value:function(){try{var e=new IntersectionObserver(function(e){var t=this;e.forEach((function(e){if(e.isIntersecting){var r={eventType:"visibility",elementTag:e.target.tagName,elementId:e.target.id,elementClasses:e.target.className,timestamp:(new Date).toISOString()};t.pind_send(r)}}))}.bind(this),{threshold:.1});document.querySelectorAll("[data-vis]").forEach((function(t){e.observe(t)}))}catch(e){console.error("[tracking-sdk error:] handleVisibilityEvents error:",e)}}},{key:"handleVideoEvents",value:function(){var e=this;try{document.querySelectorAll("video").forEach((function(t){t.addEventListener("play",function(){var e={eventType:"videoPlay",videoId:t.id,videoClasses:t.className,timestamp:(new Date).toISOString()};this.pind_send(e)}.bind(e)),t.addEventListener("pause",function(){var e={eventType:"videoPause",videoId:t.id,videoClasses:t.className,timestamp:(new Date).toISOString()};this.pind_send(e)}.bind(e)),t.addEventListener("timeupdate",b(function(){var e={eventType:"videoTimeUpdate",videoId:t.id,videoClasses:t.className,currentTime:t.currentTime,timestamp:(new Date).toISOString()};this.pind_send(e)}.bind(e),1e3))}))}catch(e){console.error("[tracking-sdk error:] handleVideoEvents error:",e)}}},{key:"trackPerformance",value:function(){try{var e={eventType:"performance",timestamp:(new Date).toISOString()};if(window.performance){if(performance.timing){var t=performance.timing;e.pageLoadTime=t.loadEventEnd-t.navigationStart,e.timeToInteractive=t.domInteractive-t.navigationStart,e.domContentLoaded=t.domContentLoadedEventEnd-t.navigationStart,e.domComplete=t.domComplete-t.navigationStart}else if(performance.getEntriesByType){var r=performance.getEntriesByType("navigation")[0];r&&(e.pageLoadTime=r.loadEventEnd,e.timeToInteractive=r.domInteractive,e.domContentLoaded=r.domContentLoadedEventEnd,e.domComplete=r.domComplete)}void 0===e.pageLoadTime&&(e.pageLoadTime=Date.now()-performance.timing.navigationStart),performance.getEntriesByType("paint").forEach((function(t){"first-paint"===t.name&&(e.firstPaint=t.startTime),"first-contentful-paint"===t.name&&(e.firstContentfulPaint=t.startTime)}))}this.pind_send(e)}catch(e){console.error("[tracking-sdk error:] trackPerformance error:",e)}}},{key:"setupErrorHandlers",value:function(){var e=this;try{window.addEventListener("error",(function(t){return e.trackError("uncaughtException",{message:t.message,source:t.filename,lineno:t.lineno,colno:t.colno,error:t.error?t.error.stack:null}),!0}),!0),window.onerror=function(t,r,n,o,a){return e.trackError("uncaughtException",{message:t,source:r,lineno:n,colno:o,error:a?a.stack:null}),!0},window.addEventListener("unhandledrejection",(function(t){e.trackError("unhandledRejection",{reason:t.reason?t.reason.message||t.reason.toString():"Unknown reason"})}))}catch(e){console.error("[tracking-sdk error:] setupErrorHandlers error:",e)}}},{key:"trackError",value:function(e,t){try{var r={eventType:"error",errorType:e,details:t,timestamp:(new Date).toISOString()};this.pind_send(r)}catch(e){console.error("[tracking-sdk error:] trackError error:",e)}}}],t&&T(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();const j="undefined"!=typeof window&&window.trackify||new I;return t.default})()));