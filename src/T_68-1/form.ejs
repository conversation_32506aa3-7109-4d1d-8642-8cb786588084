<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>PRS0002AI_呈现帧动画AI</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">PRS0002AI_呈现帧动画AI</h3>

			<% include ./src/common/template/common_head %>

       <!-- 动画组合 -->
       <div class="c-group">
        <div class="c-title">动画组合</div>
        <div class="c-area upload img-upload details-filed-wrap">
          <div class="field-wrap animation-upload" v-for="(item,index) in configData.source.options">
            <div class="field-title">动画组合{{index+1}}</div>
            <div class="c-well">
              <label v-if="!item.isImgShow" class="title-label isAnimation">
                <span class="title-t">动画</span>
                <span class="details-btn" v-on:click="addAnimationFn(index)">添加动画</span>
              </label>
              <!-- 上传动画 -->
              <div v-if="item.isImgShow" class="label-img">
                <label>动画
                  <span class="dele-tg-btn" v-on:click="delOption('img', index)"></span>
                </label>
                <div class="details">
                  <span class='txt-info'>json动画<em>*</em>
                    <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
                    <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                    <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤5000KB </em></span>
                    <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1" :id="'content-pic-'+index" @change="imageUpload($event,item,'img',5000)">
                  </span>
                </div>
                <div class="img-preview" v-if="item.img">
                  <!-- <img src="https://cdn.51talk.com/apollo/images/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/> -->
                  <img src="./image/1f3f6f9a5c2053c323a9819c947347f6.jpeg" alt=""/>
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                  </div>
                </div>
                <div class="details">
                  <label class="title-label">
                    <span class="title-t">位置<em>*</em></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    X:<input type="number" v-model="item.positionX" class="tiggerCls distance" oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" v-model="item.positionY" class="tiggerCls distance" oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>
              </div>
            </div>

            <div class="c-well">
              <label v-if="!item.isAudioShow" class="title-label isAudio">
                <span class="title-t">音频</span>
                <span class="details-btn" v-on:click="addAudioFn(index)">添加音频</span>
              </label>
              <div v-if="item.isAudioShow" class="label-audio">
                <div class="audio-title">
                  <span class="title">音频</span>
                  <span class="dele-tg-btn" v-on:click="delOption('audio', index)"></span>
                </div>

                <div class="audio-list">
                  <label class="field-label"  for="">音频<em>*</em>&nbsp;&nbsp;&nbsp;</label>
                  <input type="file" accept=".mp3" :id="'content-audio-'+index" volume="3000" v-bind:key="Date.now()" class="btn-file" v-on:change="audioUpload($event,item,'audio')">
                  <label :for="'content-audio-'+index"  class="btn btn-show upload" v-if="!item.audio">上传</label>
                  <div class="audio-preview" v-show="item.audio">
                    <div class="audio-tools">
                      <p v-show="item.audio">{{item.audio}}</p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="item.audio"></audio>
                    </span>
                  </div>
                  <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.audio" @click="item.audio=''">删除</label>
                  <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
                  <label><em>大小：≤3000KB</em></label>
                </div>
                <div class="details">
                  <label>
                    <span>播放时长</span>
                    <em>*</em>
                    <input type="number" class="c-input-txt input70"  oninput="if(value>60000)value=60000;if(value<0)value=0" v-model="item.audioPlayTime">
                    数字，1000～60000 毫秒
                  </label>
                </div>
                <div class="details">
                  <label><span>教学音频图标<em>*</em></span></label>
                  <label class="inline-label" :for="'imgtype-'+index">
                    <input type="radio" :name="'imgtype' + index"  value="1" v-model="item.isType">
                    不显示
                  </label>
                  <label class="inline-label" :for="'imgtype-'+index">
                    <input type="radio" :name="'imgtype' + index"  value="2" v-model="item.isType">
                    显示(填位置)
                  </label>
                </div>
                <div class="details" v-if="item.isType == 2">
                  <label class="title-label">
                    <span class="title-t">位置<em>*</em></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    X:<input type="number" v-model="item.iconPositionX" class="tiggerCls distance" oninput="if(value>1920)value=1920;if(value<0)value=0">
                    Y:<input type="number" v-model="item.iconPositionY" class="tiggerCls distance" oninput="if(value>1080)value=1080;if(value<0)value=0">
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="title-s">数字, 0<=x<=1920, <br> 0<=y<=1080</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
       </div>


			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
