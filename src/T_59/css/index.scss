@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.desc-visi{
	visibility: hidden;
}
.hide{
    display: none;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}
.container{
    background-size: auto 100%;
    position: relative;
    .main {
        position: absolute;
        left: 0;
        left: 0;
        width: 100%;
        height: 100%;
        .boxList {
            position: absolute;
            left: 1.08rem;
            top: .3rem;
            width: 17rem;
            height: 9.8rem;
            .boxUl {
                position: absolute;
                left: .5rem;
                top: .7rem;
                width: 16rem;
                height: 8.4rem;
                display: flex;
                flex-wrap: wrap;
                li {
                    // background: red;
                    margin-left: .01rem;
                    width: .39rem;
                    height: .39rem;
                }
            }
        }
        .moveImg {
            position: absolute;
            left: 0;
            top: 0;
            width: .39rem;
            height: .39rem;
            // background: red;
            img {
                position: absolute;
            }
            .light {
                position: absolute;
                width:3rem;
                height: 3rem;
                left: -1.4rem;
                top: -1rem;
                display: none;
            }
        }
        .toMin {
            transform-origin: left top;
            animation: toMin .8s forwards;
        }
        @keyframes toMin {
            0%{
                transform: scale(1);
                opacity: 1;
            }
            100%{
                transform: scale(.1);
                opacity: 0;
            }
        }
        .toMax {
            transform-origin: left top;
            animation: toMax .8s forwards;
        }
        @keyframes toMax {
            0%{
                transform: scale(.1);
                opacity: 0;
            }
            100%{
                transform: scale(1);
                opacity: 1;
            }
        }
        .hand{
            width:1.8rem;
            height:1.8rem;
            background: url('../image/hands.png');
            background-size: 7.2rem 1.8rem;
            position: absolute;
            bottom: 1.4rem;
            right: 1.2rem;
            animation: handClick 1s steps(4) infinite;
            opacity: 1;
            cursor: pointer;
            z-index: 20;
        }
       
        @keyframes handClick {
            0%{
                background-position: 0 0;
            }
            100%{
                background-position:133% 0;
            }
        }
        @-webkit-keyframes handClick {
            0%{
                background-position: 0 0;
            }
            100%{
                background-position:133% 0;
            }
        }
    }
} 

