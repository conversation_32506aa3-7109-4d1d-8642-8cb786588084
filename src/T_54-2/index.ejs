<!DOCTYPE html>
<html lang="en">
  <head>
    <% var title="TME0001FT_泡泡爱消除FT"; %> <%include
    ./src/common/template/index_head %>
  </head>
  <body>
    <div class="container" id="container" data-syncresult="1">
      <!-- 学生重连老师弹窗 -->
      <%include ./src/common/template/disconnectRecover/index.ejs %>

      <!-- 反馈动效 -->
      <%include ./src/common/template/feedbackAnimation/index.ejs %>
      <section class="commom">
        <div class="desc"></div>
        <div class="title">
          <h3></h3>
        </div>
      </section>
      <div class="content-main">
        <div class="game-container" id="game-container">
          <!-- 开屏效果 -->
          <div class="start-screen">
            <ul class="start-fruit"></ul>
            <div class="lightning"></div>
            <div class="electric-ball"></div>
            <div class="electric-ball-two"></div>
            <div class="microphone-static"></div>
            <div class="start-button" data-syncactions="startSyncactions"></div>
          </div>

          <!-- 闪电球动画 -->
          <div class="lightning-container" id="lightning-container"></div>
          <audio
            class="current-attack"
            id="current-attack"
            data-syncaudio="current-attack"
            src="./audio/currentAttack.mp3"
          ></audio>
          <div class="explosion" id="explosion"></div>
          <div class="explosion-ceshi"></div>
          <!-- 轨道 -->
          <div id="track1" class="track"></div>
          <div id="track2" class="track"></div>
          <div id="track3" class="track"></div>

          <!-- 进度条 -->
          <div class="progress-container">
            <div class="progress-img"></div>
            <div class="progress-content">
              <div class="progress-bar"></div>
            </div>
          </div>

          <!-- 提词器 -->
          <div class="controls-prompter">
            <img class="controls-prompter-img" src="" alt="" />
          </div>

          <!-- 麦克风 -->
          <div class="controls-mic" id="controls-mic"></div>
          <div class="controls-mic-img"></div>

          <!-- 按钮 -->
          <div
            class="controls-button"
            data-syncactions="controlsSyncactions"
          ></div>
          <audio
            class="controls-button-sound"
            data-syncaudio="controlsButtonSound"
            src="./audio/controlsButton.mp3"
          ></audio>
          <span class="controls-button-text">Tick it if S is right</span>

          <!-- 倒计时 -->
          <div class="timeChangeBox hide">
            <div class="timeBg">
              <div class="numberList"></div>
              <audio
                src="./audio/timeLow.mp3"
                class="timeLowAudio_1"
                data-syncaudio="timeLowAudio_1"
              ></audio>
              <audio
                src="./audio/timeLow.mp3"
                class="timeLowAudio_2"
                data-syncaudio="timeLowAudio_2"
              ></audio>
              <audio
                src="./audio/timeLow.mp3"
                class="timeLowAudio_3"
                data-syncaudio="timeLowAudio_3"
              ></audio>
              <audio
                src="./audio/timehigh.mp3"
                class="timeLowAudio_4"
                data-syncaudio="timeLowAudio_4"
              ></audio>
            </div>
          </div>
        </div>
      </div>

      <script type="text/javascript">
        document.documentElement.addEventListener(
          "touchstart",
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault();
            }
          },
          false
        );
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener(
          "touchend",
          function (event) {
            var now = Date.now();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
      </script>
    </div>
    <%include ./src/common/template/index_bottom %> <%include
    ./src/common/template/lottie %>
  </body>
</html>
