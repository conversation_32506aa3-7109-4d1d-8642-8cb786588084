"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量
import { createTeaToast } from "../../common/template/disconnectRecover/index.js";
// import '../../common/js/commonFunctions.js'
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      roundcount: configData.source.rulesNumber || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.SPEAK,
    },
  });
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: "0", // 是否有授权按钮 1：是 0：否
  };

  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bj.jpg)",
    });
  }
  let options = configData.source.options,
    rulesNumber = configData.source.rulesNumber,
    fallingSpeed = configData.source.fallingSpeed,
    startBackground = configData.source.startBackground,
    microphoneJson = null,
    customerJson = null,
    explosionJson = null,
    clickAble = false,
    currentFruitIndex = 0,
    userType =
      window.frameElement && window.frameElement.getAttribute("user_type"); //用户身份学生还是老师
  let fallingSpeedList = [
    { id: "1", generate: 8000, generation: 4000, name: "快" },
    { id: "2", generate: 12000, generation: 5000, name: "慢" },
    { id: "3", generate: 16000, generation: 8000, name: "更慢" },
  ];

  // 初始化开始界面
  startFun();
  function startFun() {
    if (startBackground) {
      if (startBackground == "white") {
        $(".start-screen").css({
          "background-color": "rgba(255, 255, 255, 0.8)",
        });
      } else {
        $(".start-screen").css({
          "background-color": "rgba(0, 0, 0, 0.8)",
        });
      }
    } else {
      $(".start-screen").css({
        "background-color": "rgba(0, 0, 0, 0.8)",
      });
    }
    let startList = options.slice(0, 3);
    let str = "";
    for (let i = 0; i < startList.length; i++) {
      str += `<li class="start-fruit-li key${i}" data-key="${i}">
                  <div class="start-fruit-li-bg" style="background-image: url(${startList[i].fruitImg})"></div>
                </li>`;
    }
    $(".start-fruit").html(str);
    clickAble = true;
  }

  //麦克风动画
  microphoneFun();
  async function microphoneFun() {
    microphoneJson = await lottieAnimations.init(
      microphoneJson,
      // "./image/ppxc_idle_small.json",
      "./image/ppxc_idle2.json",
      "#controls-mic",
      true
    );
    lottieAnimations.play(microphoneJson);
    microphoneJson.addEventListener(
      "microphoneComplete",
      function microphoneAnimation() {
        console.log("麦克风动画结束");
      }
    );
  }
  // 闪电球动画
  electricBallFun();
  async function electricBallFun() {
    customerJson = await lottieAnimations.init(
      customerJson,
      "./image/ppxc_xiaoshi.json",
      "#explosion",
      true
    );
    lottieAnimations.play(customerJson);
  }
  // 爆炸动画
  explosionFun();
  async function explosionFun() {
    explosionJson = await lottieAnimations.init(
      explosionJson,
      "./image/ppxc_dianqiu.json",
      "#lightning-container",
      true
    );
    lottieAnimations.play(explosionJson);
  }

  // 游戏状态
  let gameStarted = false;
  let activeBubbles = [];
  let missedBubbles = [];
  let progress = 0;
  let bubbleInterval;
  let currentTrackIndex = 0;
  const tracks = [30, 50, 70]; // 轨道位置百分比
  let startClick = true;
  let controlsClick = true;
  if (!isSync) {
    $(".start-button").css({
      "pointer-events": "auto",
    });
  } else {
    const classStatus = SDK.getClassConf().h5Course.classStatus;
    if (
      classStatus == CLASS_STATUS.NOT &&
      window.frameElement.getAttribute("user_type") == USER_TYPE.STU
    ) {
      $(".start-button").css({
        "pointer-events": "auto",
      });
    }
    if (window.frameElement.getAttribute("user_type") == USER_TYPE.TEA) {
      $(".start-button").css({
        "pointer-events": "auto",
      });
    }
  }
  // start开始游戏
  $(".start-button").on("click touchstart", function (e) {
    if (clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation();

      if (startClick) {
        startClick = false;
        if (!isSync) {
          $(this).trigger("syncStartClick");
          return;
        }
          SDK.bindSyncEvt({
            sendUser: "",
            receiveUser: "",
            index: $(e.currentTarget).data("syncactions"),
            eventType: "click",
            method: "event",
            syncName: "syncStartClick",
            funcType: "audio",
          });
      }
    }
  });

  $(".start-button").on("syncStartClick", function (e, message) {
    $(".start-screen").hide();
    initGame(); //因为开始下落时间大于3000，所以直接开始执行
    threeTwoOne(); //倒计时
  });

  //倒计时
  function threeTwoOne() {
    let q = 1;
    $(".timeChangeBox").show().find(".numberList");
    SDK.playRudio({
      index: $(".timeLowAudio_" + q).get(0),
      syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
    });
    let audioPlay = setInterval(function () {
      q++;
      if (q > 4) {
        console.log("开始游戏");
        $(".controls-mic").show();
        // 正确按钮只有老师显示
        if (!isSync) {
          $(".controls-button").show();
          $(".controls-button-text").show();
        }else{
          const classStatus = SDK.getClassConf().h5Course.classStatus;
          if(classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU){
            $(".controls-button").show();
          $(".controls-button-text").show();
          }
          if(window.frameElement.getAttribute('user_type') == USER_TYPE.TEA){
            $(".controls-button").show();
          $(".controls-button-text").show();
          }
        }
        clearInterval(audioPlay);
        // SDK.setEventLock();
        $(".timeChangeBox").hide();
        // initGame();
      } else {
        SDK.playRudio({
          index: $(".timeLowAudio_" + q).get(0),
          syncName: $(".timeLowAudio_" + q).attr("data-syncaudio"),
        });
        $(".numberList").css({
          "background-position-x": -(1.5 * (q - 1)) + "rem",
        });
      }
    }, 1000);
  }

  // 结束游戏
  function endGame() {
    gameStarted = false;
    clearInterval(bubbleInterval);
    // $(".start-screen").show();
    $(".controls-mic").hide();
    $(".controls-mic-img").hide();
    $(".controls-button").hide();
    $(".controls-button-text").hide();
    activeBubbles = [];
    missedBubbles = [];
    currentTrackIndex = 0;
    progress = 0;
    startClick = true;
    controlsClick = true;
    $(".controls-prompter-img").attr("src", "");
    feedback();
  }
  // 初始化游戏
  function initGame() {
    activeBubbles = [];
    missedBubbles = [];
    progress = 0;
    currentTrackIndex = 0;
    currentFruitIndex = 0;
    updateProgress();
    $(".bubble").remove();
    gameStarted = true;

    let fallingSpeedObj = fallingSpeedList.find((item) => {
      return item.id == fallingSpeed;
    });
    console.log(fallingSpeed, "196");
    // 开始生成泡泡
    bubbleInterval = setInterval(createBubble, fallingSpeedObj.generation);
    SDK.setEventLock();
  }

  // 创建泡泡
  function createBubble() {
    // if (!gameStarted || activeBubbles.length > 2) return;
    if (!gameStarted) return;

    // 选择随机水果
    // const randomFruit = options[Math.floor(Math.random() * options.length)];
    // 顺序选择水果
    const randomFruit = options[currentFruitIndex];
    currentFruitIndex = (currentFruitIndex + 1) % options.length;

    // 确定轨道 (从左到右循环)
    const trackPercentage = tracks[currentTrackIndex];
    currentTrackIndex = (currentTrackIndex + 1) % tracks.length;

    // 创建泡泡元素
    const bubble = $("<img>")
      .addClass("bubble")
      .css({
        // left: `calc(${trackPercentage}% - 1.5rem)`,
        // top: "-1.5rem",
        // "background-image": `url(${randomFruit.fruitImg})`,
      })
      .data("fruitImg", randomFruit.fruitImg)
      .attr("src", `${randomFruit.fruitImg}`);
    // // 创建泡泡元素
    // const bubble = $("<div>")
    //   .addClass("bubble")
    //   .css({
    //     left: `calc(${trackPercentage}% - 1.6rem)`,
    //     top: "-1.6rem",
    //     "background-image": `url(${randomFruit.fruitImg})`,
    //   })
    //   // .text(randomFruit.word)
    //   .data("fruitImg", randomFruit.fruitImg);

    $("#game-container").append(bubble);
    // 添加到活动泡泡列表
    const bubbleId = "bubble-" + Date.now();
    const bubbleWidth = $(".bubble").width();
    bubble.attr("id", bubbleId);
    bubble.css({
      left: `calc(${trackPercentage}% - ${bubbleWidth / 100}rem)`,
      top: `-${bubbleWidth / 100}rem`,
    });
    activeBubbles.push({
      id: bubbleId,
      element: bubble,
      fruitImg: randomFruit.fruitImg,
      tipImg: randomFruit.tipImg,
      position: -bubbleWidth,
      isHit: false,
    });

    // 开始下落动画
    animateBubble(bubbleId);

    // 更新提词器
    updatePrompter();
  }

  // 泡泡下落动画
  function animateBubble(bubbleId) {
    const bubbleObj = activeBubbles.find((b) => b.id === bubbleId);
    if (!bubbleObj) return;
    const bubbleWidth = $(".bubble").width();
    let fallingSpeedObj = fallingSpeedList.find((item) => {
      return item.id == fallingSpeed;
    });
    const animationDuration = fallingSpeedObj.generate; // 8秒下落到底部
    const startTime = Date.now();
    const startPosition = -bubbleWidth;
    const endPosition = $(".game-container").height() - bubbleWidth;
    const distance = endPosition - startPosition;

    function updatePosition() {
      if (!gameStarted || bubbleObj.isHit) return;

      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / animationDuration, 1);
      const currentPosition = startPosition + distance * progress;

      bubbleObj.position = currentPosition;
      bubbleObj.element.css("top", currentPosition + "px");
      if (progress < 1) {
        requestAnimationFrame(updatePosition);
      } else {
        // 泡泡到达底部
        if (!bubbleObj.element.hasClass("missed")) {
          bubbleObj.element.addClass("missed");

          // 移除脉动效果
          bubbleObj.element.css("transform", "scale(1)");

          missedBubbles.push(bubbleObj.fruitImg);
          activeBubbles = activeBubbles.filter((b) => b.id !== bubbleId);
          updatePrompter();
        }
        setTimeout(() => {
          bubbleObj.element.remove();
        }, 500);
      }
    }

    requestAnimationFrame(updatePosition);
  }

  // 更新提词器
  function updatePrompter() {
    if (activeBubbles.length === 0) {
      return;
    }

    // 找到最下面的泡泡
    let lowestBubble = null;
    let maxPosition = -Infinity;

    activeBubbles.forEach((bubble) => {
      if (bubble.position > maxPosition) {
        maxPosition = bubble.position;
        lowestBubble = bubble;
      }
    });

    if (lowestBubble) {
      console.log(lowestBubble);
      $(".controls-prompter-img").attr("src", `${lowestBubble.tipImg}`);

      // 移除所有泡泡的脉动类
      $(".bubble").removeClass("pulse");

      // 为最下面的泡泡添加脉动效果
      lowestBubble.element.addClass("pulse");
    }
  }
  var containerOffset = $(".game-container").offset();

  // 更新进度条
  function updateProgress() {
    const percentage = (progress / rulesNumber) * 100;
    $(".progress-bar").css("width", percentage + "%");

    if (progress >= rulesNumber) {
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_COMPLETE",
          data: {
            result: "success",
          },
        },
        USER_TYPE.TEA
      );
      endGame();
    }
  }

  // 创建闪电动画效果
  async function createLightningAnimation(
    fromX,
    fromY,
    toX,
    toY,
    bubbleOffset
  ) {
    console.log(fromX, fromY, toX, toY);
    const bubbleWidth = $(".bubble").width();
    const dx = toX - bubbleWidth / 2;
    const dy = toY - bubbleWidth / 2;
    const length = Math.sqrt(dx * dx + dy * dy);
    const angle = (Math.atan2(dy, dx) * 180) / Math.PI;

    let explosionWidth = $(".explosion").width();
    let withdrawPx = (explosionWidth - bubbleWidth) / 2;
    let explosionX = bubbleOffset.left - containerOffset.left;
    let explosionY = bubbleOffset.top - containerOffset.top;

    $(".lightning-container").show();
    $(".lightning-container").animate(
      {
        left: dx + "px", // 目标 X 坐标
        top: dy + "px", // 目标 Y 坐标
      },
      600,
      function () {
        let audio = $(".current-attack")[0];
        SDK.playRudio({
          index: audio,
          syncName: $(".current-attack").attr("data-syncaudio"),
        });

        $(".explosion").css({
          display: "block",
          left: explosionX - withdrawPx + "px",
          top: explosionY - withdrawPx + "px",
        });

        $(".lightning-container").hide();
        $(".lightning-container").css({
          left: `calc(50% - 1rem)`,
          // top: `calc(100% - 2.1rem)`,
          top: "100%",
        });
      }
    );

    return container;
  }

  //点击按钮发射
  $(".controls-button").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();
    console.log(controlsClick, "374");
    if (controlsClick) {
      console.log(controlsClick, "376");
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_SPOKEBUTTON",
          teaData: {
            roundid: progress + 1,
          },
        },
        USER_TYPE.TEA
      );
      if (!isSync) {
        $(this).trigger("syncControlsClick");
        return;
      }
      // if (window.frameElement.getAttribute("user_type") == "tea") {
        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "syncControlsClick",
          funcType: "audio",
        });
      // }
    }
  });
  $(".controls-button").on("syncControlsClick", async function (e, message) {
    console.log(gameStarted, "394");
    if (gameStarted) {
      popBubble();
    }
  });
  // 消除泡泡
  function popBubble() {
    console.log(activeBubbles.length, "400");
    if (activeBubbles.length === 0) {
      SDK.setEventLock();
      return;
    }

    // 找到最下面的泡泡
    let lowestBubble = null;
    let maxPosition = -Infinity;

    activeBubbles.forEach((bubble) => {
      if (bubble.position > maxPosition) {
        maxPosition = bubble.position;
        lowestBubble = bubble;
        bubble.isHit = true;
      }
    });

    if (lowestBubble) {
      let audio = $(".controls-button-sound")[0];
      SDK.playRudio({
        index: audio,
        syncName: $(".controls-button-sound").attr("data-syncaudio"),
      });

      controlsClick = false;
      // 获取麦克风按钮和泡泡的位置
      const micButton = $(".controls-mic");
      const micOffset = micButton.offset();
      const micCenterX = micOffset.left + micButton.width() / 2;
      const micCenterY = micOffset.top + micButton.height() / 2;

      const bubbleOffset = lowestBubble.element.offset();
      const bubbleCenterX =
        bubbleOffset.left -
        containerOffset.left +
        lowestBubble.element.width() / 2;
      const bubbleCenterY =
        bubbleOffset.top -
        containerOffset.top +
        lowestBubble.element.height() / 2;
      console.log(bubbleOffset, "283");
      // 创建闪电动画
      createLightningAnimation(
        micCenterX,
        micCenterY,
        bubbleCenterX,
        bubbleCenterY,
        bubbleOffset
      );

      setTimeout(() => {
        // 泡泡爆炸效果
        lowestBubble.element.animate(
          {
            opacity: 0,
            // transform: "scale(1.5)",
          },
          500,
          function () {
            $(".explosion").hide();

            lowestBubble.element.remove();
            activeBubbles = activeBubbles.filter(
              (b) => b.id !== lowestBubble.id
            );

            // 更新进度
            progress++;
            updateProgress();
            updatePrompter();

            controlsClick = true;
            SDK.setEventLock();
          }
        );
      }, 800);
    }
  }

  SDK.memberChange = function (message) {
    if (message.role === USER_TYPE.STU && message.state === 'enter') {
      let disconnectText = "The student  has been disconnected on this page.<br/> You need to click the 'Refresh' button to start over."
      // 学生重连
      createTeaToast(disconnectText)
      SDK.reportTrackData({
        action: 'PG_FT_INTERACTION_RECONNECTION',
        data: {},
      },USER_TYPE.TEA)
    }
  }

  async function feedback() {
    console.log("全部正确结束，执行反馈动画");
    await feedbackAnimation("feedKey1", false);
    SDK.setEventLock();
    startClick = true;
  }
});
