<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TME0001FT_泡泡爱消除FT</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">TME0001FT_泡泡爱消除FT</h3>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>

      <!-- 上传泡泡 -->
      <div class="c-group">
        <div class="c-title">上传泡泡（最少3组，最多12组）</div>
        <div class="c-area upload img-upload">
          <ul>
            <li  v-for="(item,index) in configData.source.options">
              <div class="c-well">
                <!-- 泡泡图片 -->
                <div class="field-wrap">
                  <div class="add-field-content">
                    <label class="field-label"  for="">第{{index+1}}组</label>
                    <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>3"></span>
                  </div>

                  <label class='field-label'>泡泡图片<em>*</em></label>
                  <label :for="'content-pic-read-'+index" class="btn btn-show upload" v-if="!item.fruitImg">上传图片</label>
                  <label :for="'content-pic-read-'+index" class="btn upload re-upload" v-if="item.fruitImg!=''?true:false">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>JPG、PNG格式，最大尺寸不能超过360x360，小于等于50KB</em></span>
                    </label>
                  </div>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="360*360" accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-read-'+index" @change="imageUpload($event,item,'fruitImg',50)">

                  <div class="img-preview" v-if="item.fruitImg">
                    <img v-bind:src="item.fruitImg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.fruitImg=''">删除</span>
                    </div>
                  </div>
                </div>

                <!-- 提词图片 -->
                <div class="field-wrap">
                  <label class='field-label'>提词图片<em>*</em></label>
                  <label :for="'content-pic-back-'+index" class="btn btn-show upload" v-if="!item.tipImg">上传图片</label>
                  <label :for="'content-pic-back-'+index" class="btn upload re-upload" v-if="item.tipImg!=''?true:false">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>JPG、PNG格式，最大尺寸不能超过800x100，小于等于30KB</em></span>
                    </label>
                  </div>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="800*100" accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-back-'+index" @change="imageUpload($event,item,'tipImg',30)">

                  <div class="img-preview" v-if="item.tipImg">
                    <img v-bind:src="item.tipImg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.tipImg=''">删除</span>
                    </div>
                  </div>
                </div>

              </div>
            </li>
          </ul>
          <button type="button" class="add-btn" v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            fruitImg: '',
            tipImg: '',
          })">添加</button>
        </div>
      </div>

      <!-- 规则设置 -->
      <div class="c-group">
        <div class="c-title">规则设置</div>
        <div class="c-area upload img-upload">
          <div class="field-wrap">
            <div class="rules-content">
              <label class="rules-field-label">答对</label>
              <input type="number" class="rules-input-txt"
                             style="margin: 0 10px;width: 60px!important;display: inline-block;"
                             oninput="if(value>12)value=12;if(value<3)value=3"
                             v-model="configData.source.rulesNumber">
              <label class="rules-field-label">次结束游戏（范围3--12）</label>
            </div>
          </div>
          <div class="field-wrap">
            <label class="field-label" style="margin-right: 10px;">选择泡泡下落速度</label>
            <select id="feedBack" v-model="configData.source.fallingSpeed" style="width: 100px">
              <option name="optive" value="1">8/4（快）</option>
              <option name="optive" value="2">12/5（慢）</option>
              <option name="optive" value="3">16/8（更慢）</option>
            </select>
          </div>
          <div class="field-wrap">
            <label class="field-label" style="width: 150px">初始页面透明蒙版颜色</label>
            <label for="twomodule" class="inline-label">
              <input type="radio" name="startBackground" value="black" v-model="configData.source.startBackground">黑色
            </label>
            <label for="twomodule" class="inline-label">
              <input type="radio" name="startBackground" value="white" v-model="configData.source.startBackground">白色
            </label>
          </div>
        </div>
      </div>

      <!-- 正反馈 -->
      <% include ./src/common/template/feedbackAnimation/form %>


			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG</li>
					<li>声音格式：</em>MP3</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
