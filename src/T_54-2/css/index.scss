@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";
@import "../../common/template/disconnectRecover/style.scss";

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;

  .desc {
    top: 0.6rem;
  }

  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.container {
  background-size: auto 100%;
  position: relative;

  .content-main {
    width: 100%;
    height: 100%;
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    overflow: hidden;

    .game-container {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .start-screen {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      // background-color: rgba(0, 0, 0, 0.8);
      // background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 100;
      // display: none;

      .start-fruit {
        li {
          position: absolute;
          width: 3rem;
          height: 3rem;

          .start-fruit-li-bg {
            width: 100%;
            height: 100%;
            background-size: 100% 100%;
            background-position: center;
            background-repeat: no-repeat;
          }
        }

        .key0 {
          left: 1.88rem;
          top: 3.09rem;
        }

        .key1 {
          left: 7.7rem;
          top: 1.19rem;
        }

        .key2 {
          left: 13.99rem;
          top: 2.78rem;
        }
      }

      .lightning {
        position: absolute;
        width: 5.5rem;
        height: 2.92rem;
        left: 6.4rem;
        top: 1.5rem;
        background-image: url('../image/lightning.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }

      .electric-ball {
        position: absolute;
        width: 2.1rem;
        height: 2.1rem;
        left: 9.1rem;
        top: 3.23rem;
        background-image: url('../image/electric-ball.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
      }
      .electric-ball-two{
        position: absolute;
        width: 1.43rem;
        height: 1.43rem;
        left: 10.36rem;
        bottom: 0.12rem;
        background-image: url('../image/electric-ball.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 8;
      }
      .microphone-static{
        position: absolute;
        width: 5.42rem;
        height: 2.71rem;
        left: 6.89rem;
        bottom: 0rem;
        background-image: url('../image/microphoneA.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 1;
      }

      .start-button {
        position: absolute;
        width: 3.2rem;
        height: 1.2rem;
        left: 8rem;
        top: 8.1rem;
        background-image: url('../image/start.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        cursor: pointer;
        z-index: 9;
        pointer-events: none;
        // display: none;
      }
    }


    .progress-container {
      margin: 0 auto;
      width: 8rem;
      height: 0.87rem;
      position: absolute;
      top: 0rem;
      left: 5.6rem;
      background-image: url('../image/progressa.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 6;

      .progress-img {
        position: absolute;
        width: 1.07rem;
        height: 1rem;
        top: 0.07rem;
        right: 0.49rem;
        background-image: url('../image/progressb.png');
        background-size: 100% 100%;
        background-position: center;
        background-repeat: no-repeat;
        z-index: 6;
      }

      .progress-content {
        position: absolute;
        width: 6.8rem;
        height: 0.3rem;
        left: 0.6rem;
        top: 0.4rem;
        border-radius: 0.12rem;
        overflow: hidden;

        .progress-bar {
          height: 100%;
          width: 0%;
          background-color: #FFD02F;
          transition: width 0.3s;
          border-radius: 0.12rem;
        }
      }
    }

    .bubble {
      position: absolute;
      max-width: 3rem;
      max-height: 3rem;
      // background-size: 100% 100%;
      // background-position: center;
      // background-repeat: no-repeat;
      // background-image: url('../assets/images/fruit1.png');
      // border-radius: 50%;
      // display: flex;
      // justify-content: center;
      // align-items: center;
      // color: white;
      // font-weight: bold;
      // text-shadow: 0.01rem 0.01rem 0.02rem rgba(0, 0, 0, 0.5);
      // box-shadow: 0rem 0.04rem 0.08rem rgba(0, 0, 0, 0.2);
      transition: transform 0.3s ease;
      transform: scale(1);
      // animation: breathe 3s ease-in-out infinite;
      // will-change: top;
      // font-size: 12px;
    }

    // .bubble:hover {
    //   transform: scale(1.05);
    // }

    .bubble.missed {
      opacity: 0.5;
      transform: scale(1) !important;
      animation: none !important;
      text-decoration: line-through;
    }

    .bubble.pulse {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.1);
      }

      100% {
        transform: scale(1);
      }
    }

    .controls-prompter {
      position: absolute;
      width: 100%;
      height: 1rem;
      bottom: 0.13rem;

      .controls-prompter-img {
        position: relative;
        display: block;
        height: 100%;
        margin: 0 auto;
        z-index: 6;
        // background-image: url('../assets/images/prompt2.png');
        // background-size: 100% 100%;
        // background-position: center;
        // background-repeat: no-repeat;
      }
    }

    .controls-mic {
      position: absolute;
      width: 13.4em;
      height: 13.4rem;
      left: 2.9rem;
      bottom: -6.7rem;
      // width: 5em;
      // height: 5rem;
      // left: 7.1rem;
      // bottom: -0.7rem;
      display: none;
    }

    .controls-mic-img {
      position: absolute;
      width: 0.66rem;
      height: 0.92rem;
      left: 9.27rem;
      bottom: 1.21rem;
      background-image: url('../image/microphone.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      display: none;

    }

    .controls-button {
      position: absolute;
      width: 1.04rem;
      height: 1.04rem;
      // right: 6.5rem;
      // bottom: 1.4rem;
      right: 4.5rem;
      bottom: 1rem;
      background-image: url('../image/correct-button.png');
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      z-index: 6;
      display: none;
      cursor: pointer;
    }

    .controls-button-text {
      position: absolute;
      display: inline-block;
      // width: 2.48rem;
      height: 0.8rem;
      // right: 3.7rem;
      // bottom: 1.4rem;
      right: 1.6rem;
      bottom: 1rem;
      font-family: Century Gothic Bold;
      font-weight: Bold;
      font-size: 0.31rem;
      display: none;
      z-index: 6;
      color: #FFFFFF;
    }

    // #mic-button:active {
    //   transform: scale(0.95);
    // }



    .lightning-container {
      position: absolute;
      pointer-events: none;
      z-index: 10;
      left: 50%;
      top: calc(100% - 2.1rem);
      width: 2.1rem;
      height: 2.1rem;
      display: none;
    }

    .explosion {
      position: absolute;
      width: 6.3rem;
      height: 6.3rem;
      left: 0rem;
      top: 0rem;
      display: none;
    }

    .lightning-svg {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .track {
      position: absolute;
      top: 0;
      width: 0.02rem;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.1);
    }

    #track1 {
      left: 25%;
      display: none;
    }

    #track2 {
      left: 50%;
      display: none;
    }

    #track3 {
      left: 75%;
      display: none;
    }
    .timeChangeBox {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      margin: auto;
      height: 4.8rem;
      width: 7.2rem;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 0.5rem;

      .timeBg {
          width: 3.79rem;
          height: 3.84rem;
          position: absolute;
          top: 1rem;
          background: url(../image/timeBg.png) no-repeat;
          background-size: 100% 100%;
          left: 50%;
          margin-left: -1.9rem;
          top: 50%;
          margin-top: -1.92rem;

          .numberList {
              width: 1.5rem;
              height: 1.5rem;
              position: absolute;
              left: 0;
              bottom: 0;
              right: 0;
              top: 0;
              margin: auto;
              background: url(../image/number1.png) no-repeat;
              background-size: 6rem 100%;
              background-position-x: 0.1rem;
          }
      }
  }
  }
}
