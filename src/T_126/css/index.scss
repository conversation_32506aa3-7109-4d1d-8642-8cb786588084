@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.commom {
    position: relative;
    z-index: 100;
}
.desc-visi{
	visibility: hidden;
}
.container{
    background: url(../image/defaultBg.png) no-repeat;
    background-size: auto 100%;
    position: relative;
    overflow: hidden;
}
.main {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    transition: 1.2s;
    transform-origin: 6.5rem 6.5rem;

    .texts-box {
        display: flex;
        justify-content: space-around;
        position: absolute;
        text-align: center;
        width: calc(100% - 1rem);
        height: 2.15rem;
        top: 2.3rem;
        left: .5rem;

        .texts-box-text {
            width: 3.5rem;
            height: 2.15rem;
            transition: .2s;
            background: url(../image/sugar_01.png) no-repeat center;
            background-size: cover;

            p {
                width: 2.8rem;
                height: 100%;
                margin: 0 auto;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: .48rem;
                color: #fff;
            }
        }
        .texts-box-text.active {
            transform: scale(1.2);
        }
    }

    .imgs-box {
        display: flex;
        justify-content: space-around;
        position: absolute;
        width: 100%;
        padding: 0 .8rem;
        box-sizing: border-box;
        height: 4.2rem;
        top: 6rem;

        .imgs-box-img {
            width: 3.44rem;
            height: 4.2rem;
            position: relative;

            .monster {
                width: 100%;
                height: 100%;
                position: relative;
                top: -.3rem;

                div {
                    position: absolute;
                }

                .monster-sugar {
                    width: 1.4rem;
                    height: 1.1rem;
                    position: relative;
                    left: 1rem;
                    top: .5rem;
                    transform: rotateZ(-30deg);
                    z-index: -1;
                    // z-index: 100;
                }
                .monster-sugar-pos {
                    top: -2.5rem;
                }
                .monster-sugar.ans {
                    transition: 1s;
                }

                .monster-head {
                    width: 100%;
                    height: 1.86rem;
                    background: url(../image/initial_01.png) no-repeat center;
                    background-size: contain;
                }

                .monster-head.sad {
                    background: url(../image/crying.png) no-repeat center;
                    background-size: contain;
                }
                .monster-sweat {
                    position: relative;
                    left: 3rem;
                    top: -1rem;
                    display: none;
                }
                .sweat {
                    animation: sweatting 1.5s 1 linear;
                }
                @keyframes sweatting {
                    0% {
                        transform: translateY(0);
                    }
                    100% {
                        transform: translateY(1rem);
                    }
                }

                .monster-head.happy {
                    background: url(../image/smile.png) no-repeat center;
                    background-size: contain;
                }

                .monster-head.eat {
                    animation: eating .5s infinite ease;
                }
                @keyframes eating {
                    0% {
                        background: url(../image/openBig.png) no-repeat center;
                        background-size: contain;
                    }
                    49% {
                        background: url(../image/openBig.png) no-repeat center;
                        background-size: contain;
                    }
                    50% {
                        background: url(../image/openSmall.png) no-repeat center;
                        background-size: contain;
                    }
                    100% {
                        background: url(../image/openSmall.png) no-repeat center;
                        background-size: contain;
                    }
                }

                .monster-body {
                    width: 100%;
                    height: 2.5rem;
                    top: 1.85rem;
                    background: url(../image/guai_1.png) no-repeat center;
                    background-size: contain;
                }

                .monster-hand-left {
                    width: .76rem;
                    height: 1.67rem;
                    top: 2rem;
                    background: url(../image/handLeft.png) no-repeat center;
                    background-size: contain;
                }

                .monster-hand-right {
                    width: .76rem;
                    height: 1.67rem;
                    top: 2rem;
                    right: 0;
                    background: url(../image/handLeft.png) no-repeat center;
                    background-size: contain;
                    transform: rotateY(180deg);
                }
            }

            .img {
                position: absolute;
                width: 4.4rem;
                height: 3rem;
                background: url(../image/palette.png) no-repeat center;
                background-size: contain;
                border-radius: .2rem;
                bottom: 0;
                left: -.48rem;

                img {
                    width: 3.6rem;
                    height: 2.3rem;
                    position: absolute;
                    left: .4rem;
                    bottom: .2rem;
                    border-radius: .1rem;
                }
            }
        }
    }

}