@import "../../common/css/reset-title.css";
@import "../../common/css/animation.css";

.commom {
  display: -webkit-flex;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2.2rem;
  position: absolute;
  right: 0px;

  .desc {
    top: 0.6rem;
  }

  .title-first {
    width: 100%;
    height: 0.8rem;
    padding: 0 1.4rem;
    box-sizing: border-box;
    text-align: center;
    margin: 0.45rem auto 0.2rem;
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
  }
}

.container {
  background-size: auto 100%;
  position: relative;

  .content-main {
    .waiter {
      position: absolute;
      width: 3.36rem;
      height: 5.82rem;
      left: 0.51rem;
      bottom: 0.16rem;
    }
    .waiter-mistake {
      position: absolute;
      width: 4.36rem;
      height: 7.65rem;
      // left: 0.48rem;
      // top: 3.52rem;
      left: 0rem;
      bottom: -0.7rem;
      // background-size: 100% 100%;
      // display: none;
    }
    .optionUl{
      position: absolute;
      left: 5.67rem;
      top: 2.32rem;
      width: 10.33rem;
      height: 4.3rem;
      .option-li{
        position: absolute;
        width: 100%;
        height: 100%;
        .read-img-bg{
          position: absolute;
          width: 7.3rem;
          height: 4.3rem;
          left: 0rem;
          top: 0rem;
          background-size: 100% 100%;
          background-position: center;
          background-repeat: no-repeat;
          // display: none;
        }
        .ice-cream{
          position: absolute;
          width: 2.6rem;
          height: 2.6rem;
          right: 0rem;
          top: 1.45rem;
          background-size: 100% 100%;
          background-position: center;
          background-repeat: no-repeat;
          // display: none;
          z-index: 1;
        }
      }

    }

    .microphone{
      position: absolute;
      width: 3.2rem;
      height: 2.23rem;
      left: 7.72rem;
      top: 6.87rem;
      // display: none;
    }
    .start{
      position: absolute;
      width: 2.49rem;
      height: 1.04rem;
      left: 8.08rem;
      top: 7.47rem;
      background: url("../image/start.png") no-repeat center;
      background-size: 100% 100%;
      cursor: pointer;
      display: none;
    }
    .correct{
      position: absolute;
      width: 1.04rem;
      height: 1.04rem;
      left: 11.37rem;
      top: 7.47rem;
      background: url("../image/correct.png") no-repeat center;
      background-size: 100% 100%;
      display: none;
      cursor: pointer;
    }
    .mistake{
      position: absolute;
      width: 1.04rem;
      height: 1.04rem;
      left: 6.24rem;
      top: 7.47rem;
      background: url("../image/mistake.png") no-repeat center;
      background-size: 100% 100%;
      display: none;
      cursor: pointer;
    }

    .customer{
      position: absolute;
      width: 3.76rem;
      height: 5.6rem;
      right: 0.43rem;
      bottom: 0.16rem;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      // transition: all 0.5s;
    }
    .customer-excitement{
      position: absolute;
      width: 3.76rem;
      height: 5.6rem;
      right: 0.43rem;
      bottom: 0.16rem;
      background-size: 100% 100%;
      background-position: center;
      background-repeat: no-repeat;
      // display: none;
    }
    .smoke{
      position: absolute;
      width: 4rem;
      height: 4rem;
      left: 12.7rem;
      top: 2.8rem;
      // display: none;
      z-index: 9;
    }
  }
}
