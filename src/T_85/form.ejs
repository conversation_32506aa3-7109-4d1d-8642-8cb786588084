<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>BQL0001_冰激凌机</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">BQL0001_冰激凌机</h3>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>

      <!-- 内容设置 -->
      <div class="c-group">
        <div class="c-title">内容设置（最多六组）</div>
        <div class="c-area upload img-upload">
          <ul>
            <li  v-for="(item,index) in configData.source.options">
              <div class="c-well">
                <!-- 读图图片 -->
                <div class="field-wrap">
                  <div class="add-field-content">
                    <label class="field-label"  for="">选项{{index+1}}</label>
                    <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>=2"></span>
                  </div>

                  <label class='field-label'>读图图片<em>*</em></label>
                  <label :for="'content-pic-read-'+index" class="btn btn-show upload" v-if="!item.img">上传图片</label>
                  <label :for="'content-pic-read-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>JPG、PNG格式，图片尺寸730x430，小于等于50KB</em></span>
                    </label>
                  </div>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="730*430" accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-read-'+index" @change="imageUpload($event,item,'img',50)">

                  <div class="img-preview" v-if="item.img">
                    <img v-bind:src="item.img" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                    </div>
                  </div>
                </div>

                <!-- 读图音频 -->
                <!-- <div class="field-wrap">
                  <label class="field-label">读图音频</label>
                  <input type="file" accept=".mp3" :id="'content-audio-'+index" volume="50" v-bind:key="Date.now()" class="btn-file"  v-on:change="audioUpload($event,item,'audio')">
                  <label :for="'content-audio-'+index" class="btn btn-show upload" v-if="!item.audio">上传音频</label>
                  <div class="audio-preview" v-show="item.audio">
                    <div class="audio-tools">
                      <p v-show="item.audio">{{item.audio}}</p>
                    </div>
                    <span class="play-btn" v-on:click="play($event)">
                      <audio v-bind:src="item.audio"></audio>
                    </span>
                  </div>
                  <label :for="'content-audio-'+index" class="btn upload btn-audio-dele" v-if="item.audio" @click="item.audio=''">删除</label>
                  <label :for="'content-audio-'+index" class="btn upload re-upload" v-if="item.audio">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>支持mp3格式，小于等于50Kb</em></span>
                    </label>
                  </div>
                </div> -->

                <!-- 反馈图片 -->
                <div class="field-wrap">
                  <label class='field-label'>反馈图片<em>*</em></label>
                  <label :for="'content-pic-back-'+index" class="btn btn-show upload" v-if="!item.backImg">上传图片</label>
                  <label :for="'content-pic-back-'+index" class="btn upload re-upload" v-if="item.backImg!=''?true:false">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>JPG、PNG格式，图片尺寸260x260，小于等于40KB</em></span>
                    </label>
                  </div>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="260*260" accept=".jpg,.png,.jpeg" isKey="1" :id="'content-pic-back-'+index" @change="imageUpload($event,item,'backImg',40)">

                  <div class="img-preview" v-if="item.backImg">
                    <img v-bind:src="item.backImg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.backImg=''">删除</span>
                    </div>
                  </div>
                </div>

              </div>
            </li>
          </ul>
          <button type="button" class="add-btn" v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            img: '',
            backImg: '',
          })">添加</button>
        </div>
      </div>

      <!-- 顾客 -->
      <div class="c-group">
        <div class="c-title">IP设置（不填展示默认形象）</div>
        <div class="c-area upload img-upload">
          <label>顾客设置</label>
          <ul>
            <li  v-for="(item,index) in configData.source.optionsIP">
              <div class="c-well">
                <!-- 顾客1待机 -->
                <div class="field-wrap">

                  <label class='field-label'>{{item.awaitName}}<em>*</em></label>
                  <label :for="'content-pic-await-'+index" class="btn btn-show upload" v-if="!item.awaitImg">上传图片</label>
                  <label :for="'content-pic-await-'+index" class="btn upload re-upload" v-if="item.awaitImg!=''?true:false">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>json、PNG格式，图片尺寸410x680，小于等于200KB</em></span>
                    </label>
                  </div>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="410*680" isKey="1" accept=".json,.png" :id="'content-pic-await-'+index" @change="imageUpload($event,item,'awaitImg',200)">

                  <div class="img-preview" v-if="item.awaitImg">
                    <img v-bind:src="item.awaitImg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.awaitImg=''">删除</span>
                    </div>
                  </div>
                </div>

                <!-- 顾客1兴奋 -->
                <div class="field-wrap">
                  <label class='field-label'>{{item.excitementName}}<em>*</em></label>
                  <label :for="'content-pic-excitement-'+index" class="btn btn-show upload" v-if="!item.excitementImg">上传图片</label>
                  <label :for="'content-pic-excitement-'+index" class="btn upload re-upload" v-if="item.excitementImg!=''?true:false">重新上传</label>
                  <div class="audio-tips">
                    <label>
                      <span><em>json、PNG格式，图片尺寸410x680，小于等于200KB</em></span>
                    </label>
                  </div>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="410*680" isKey="1" accept=".json,.png" :id="'content-pic-excitement-'+index" @change="imageUpload($event,item,'excitementImg',200)">

                  <div class="img-preview" v-if="item.excitementImg">
                    <img v-bind:src="item.excitementImg" alt=""/>
                    <div class="img-tools">
                      <span class="btn btn-delete" v-on:click="item.excitementImg=''">删除</span>
                    </div>
                  </div>
                </div>

              </div>
            </li>
          </ul>
        </div>
      </div>

      <!-- 正反馈 -->
      <% include ./src/common/template/feedbackAnimation/form %>


			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG</li>
					<li>声音格式：</em>MP3</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
