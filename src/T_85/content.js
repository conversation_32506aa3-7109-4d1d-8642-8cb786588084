var configData = {
  bg: './assets/images/bg.png',
  desc: '',
  title: '',
  tImg: './image/b164806a1b826b1b386f5496478573fe.png',
  tImgX: 1340,
  tImgY: 15,
  tg: [{
    content: "eegeg appy family. My father i appy family. My father i",
    title: "1111111111111111111"
  },
  {
    content: "eegeg appy family. My father i appy family. My father i",
    title: "weqwf appy family. My father i"
  },
  {
    content: "eegeg appy family. My father i appy family. My father i",
    title: "weqwf appy family. My father i"
  }
  ],
  level: {
    high: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
    ],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    optionLength: 4,  //选项上限
    options: [
      {
        img: "./assets/images/read-img-bg.png",
        // audio: "./audio/photo.mp3",
        backImg: "./assets/images/icecream1.png",
      },
      {
        img: "./assets/images/read-img-bg-a.png",
        // audio: "./audio/lemon.mp3",
        backImg: "./assets/images/icecream2.png",
      },
      {
        img: "./assets/images/read-img-bg.png",
        // audio: "./audio/prefect.mp3",
        backImg: "./assets/images/icecream1.png",
      },
      {
        img: "./assets/images/read-img-bg-a.png",
        // audio: "./audio/lemon.mp3",
        backImg: "./assets/images/icecream2.png",
      },
    ],
    optionsIP: [
      {
        awaitName:"顾客1待机",
        excitementName:"顾客1兴奋",
        awaitImg: "./assets/images/customer-a.png",
        excitementImg: "./assets/images/customer.png",
      },
      {
        awaitName:"顾客2待机",
        excitementName:"顾客2兴奋",
        awaitImg: "./assets/images/customer-a.png",
        excitementImg: "./assets/images/customer.png",
      },
    ],
    widthImg: {
      width: 1200,
      height: 401,
    },
    isKeyCheck: true
  },
  feedbackLists:[
    {
      positiveFeedback: '-1',
      feedbackList:[
        { id:'-1', json:'', mp3:'' },
        { id:'0', json:'./image/prefect.json', mp3:'./audio/prefect.mp3' },
        { id:'1', json:'./image/goldCoin.json', mp3:'./audio/goldCoin.mp3' },
        { id:'2', json:'./image/FKJB.json', mp3:'./audio/resultWin.mp3' },
        { id:'9', json: './image/guang.json', mp3: '' },
      ],
      feedbackObj:{ id:'9', json:'./image/guang.json', mp3:'' },
      feedback:'./image/prefect.json',
      feedbackAudio:'./audio/prefect.mp3',
      feedbackName:'整体反馈',
      key:'feedKey1',
    }
  ]
};
(function (pageNo) { configData.page = pageNo })(0)
