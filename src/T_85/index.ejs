<!DOCTYPE html>
<html lang="en">
  <head>
    <% var title="BQL0001_冰激凌机"; %> <%include ./src/common/template/index_head %>
  </head>
  <body>
    <div class="container" id="container" data-syncresult="1">
      <!-- 反馈动效 -->
      <%include ./src/common/template/feedbackAnimation/index.ejs %>
      <section class="commom">
        <div class="desc"></div>
        <div class="title">
          <h3></h3>
        </div>
      </section>
      <div class="content-main">
        <!-- 服务员 -->
        <div class="waiter" id="waiter"></div>
        <div class="waiter-mistake" id="waiter-mistake"></div>
        <!-- 读图+冰激凌 -->
        <ul class="optionUl">
          <!-- <li class="option-li"> -->
            <!-- 读图 -->
            <!-- <div class="read-img-bg"></div> -->
            <!-- 冰激凌 -->
            <!-- <div class="ice-cream"></div> -->
          <!-- </li> -->
        </ul>
        <!-- start音频 -->
         <audio class="start-audio" src="./audio/right.mp3" data-syncaudio="correct-audio"></audio>
        <!-- 麦克风 -->
        <div class="microphone" id="microphone"></div>
        <!-- 开始 -->
        <div class="start" data-syncactions="startSyncactions"></div>
        <!-- 正确 -->
        <div class="correct" data-syncactions="correctSyncactions">
          <audio class="correct-audio" src="./audio/correct.mp3" data-syncaudio="correct-audio"></audio>
        </div>
        <!-- 错误 -->
        <div class="mistake" data-syncactions="mistakeSyncactions">
          <audio class="mistake-audio" src="./audio/tryagain.mp3" data-syncaudio="mistake-audio"></audio>
        </div>
        <!-- 顾客 -->
        <div class="customer" id="customer"></div>
        <div class="customer-excitement" id="customer-excitement"></div>
        <!-- 烟雾动效 -->
         <div class="smoke" id="smoke"></div>
      </div>

      <script type="text/javascript">
        document.documentElement.addEventListener(
          "touchstart",
          function (event) {
            if (event.touches.length > 1) {
              event.preventDefault();
            }
          },
          false
        );
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener(
          "touchend",
          function (event) {
            var now = Date.now();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }
            lastTouchEnd = now;
          },
          false
        );
      </script>
    </div>
    <%include ./src/common/template/index_bottom %> <%include
    ./src/common/template/lottie %>
  </body>
</html>
