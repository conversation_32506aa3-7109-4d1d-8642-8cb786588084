"use strict";
import "../../common/js/common_1v1.js";
import "../../common/js/lottie.js";
import "../../common/js/teleprompter.js";
import { feedbackAnimation } from "../../common/template/feedbackAnimation/index.js";
import {
  USER_TYPE,
  CLASS_STATUS,
  TEACHER_TYPE,
  INTERACTION_TYPE,
  USERACTION_TYPE,
} from "../../common/js/constants.js"; // 导入常量
// import '../../common/js/commonFunctions.js'
const isSync =
  parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {
  SDK.reportTrackData({
    action: "PG_FT_INTERACTION_LIST",
    data: {
      roundcount: configData.source.options.length || 0,
    },
    teaData: {
      teacher_type: TEACHER_TYPE.PRACTICE_OUTPUT,
      interaction_type: INTERACTION_TYPE.CLICK,
      useraction_type: USERACTION_TYPE.SPEAK,
    },
  });
  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: "0", // 是否有授权按钮 1：是 0：否
  };

  if (configData.bg == "") {
    $(".container").css({
      "background-image": "url(./image/bj.jpg)",
    });
  }
  let options = configData.source.options, //图片列表
    optionsIP = configData.source.optionsIP, //顾客图片列表
    imgType = configData.source.imgType, // 1  静态图  2 2帧雪碧图
    imgWidth = configData.source.imgWidth, //设定图片的宽
    imgHeight = configData.source.imgHeight, //设定图片的高
    // animationAudio = configData.source.animationAudio, //动画音效
    time = configData.source.time, //动画时长
    // audio = configData.source.audio, //题干音频
    // imgPosition = configData.source.imgPosition,//题干图标位置
    // right = configData.source.right, //是否正确答案
    userType =
      window.frameElement && window.frameElement.getAttribute("user_type"), //用户身份学生还是老师
    imgIndex = 0,
    timeFlag = null,
    timeInit = null, //定时器
    clickAble = false,
    answerArr = [], //选中后的的正确答案
    optionsRightKey = []; //初始化正确答案的个数

  // 初始化读图区域
  optionsFn();
  function optionsFn() {
    let str = "";
    for (let i = 0; i < options.length; i++) {
      str += `<li class="option-li ans-img key${i}" data-key="${i}" data-syncactions="sele-${i}">
                <div class="read-img-bg" style="background-image: url(${options[i].img})"></div>

                <div class="ice-cream" style="background-image: url(${options[i].backImg})"></div>
              </li>`;
    }
    $(".optionUl").html(str);
    clickAble = true;
    // initType();
    // <audio class="audiolist" src="${options[i].audio}" data-syncaudio="audiolist${i}"></audio>
  }

  // 点击start展示读图图片，播放读图对应音频，展示麦克风动图，展示正确和错误按钮
  let soundClick = true;
  // 点击start按钮
  $(".start").on("click touchstart", function (e) {
    if (clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault();
      }
      e.stopPropagation();

      if (soundClick) {
        soundClick = false;
        SDK.reportTrackData(
          {
            action: "CK_FT_INTERACTION_STARTBUTTON",
            data: {
              roundid:currentIndex + 1
            },
          },
        );
        if (!isSync) {
          $(this).trigger("syncSoundClick");
          return;
        }
          SDK.bindSyncEvt({
            sendUser: "",
            receiveUser: "",
            index: $(e.currentTarget).data("syncactions"),
            eventType: "click",
            method: "event",
            syncName: "syncSoundClick",
            funcType: "audio",
          });
      }
    }
  });

  let currentIndex = 0;
  let start = $(".start"); //开始图片
  let microphone = $(".microphone"); //麦克风
  let correct = $(".correct"); //正确图片
  let mistake = $(".mistake"); //错误图片
  let waiter = $(".waiter"); //服务员图片
  let waiterMistake = $(".waiter-mistake"); //服务员错误时图片
  let smoke = $(".smoke"); //烟雾动效
  let customer = $(".customer"); //顾客1
  let customerExcitement = $(".customer-excitement"); //顾客2
  let microphoneJson = null;
  let waiterJson = null;
  let waiterMistakeJson = null;

  var customerJson = null;
  var customerExcitementJson = null;
  //顾客动画
  async function customerFun(json,identification) {
    if(identification == 'one'){

      customerJson = await lottieAnimations.init(
        customerJson,
        json,
        "#customer",
        true
      );
      customer.show()
      customerExcitement.hide()
      lottieAnimations.play(customerJson);
      customerJson.addEventListener(
        "customerComplete",
        function customerAnimation() {
          console.log("顾客动画结束");
          lottieAnimations.stop(customerJson);
        }
      );
    }
    if(identification == 'two'){
      customerExcitementJson = await lottieAnimations.init(
        customerExcitementJson,
        json,
        "#customer-excitement",
        true
      );
      customer.hide()
      customerExcitement.show()
      lottieAnimations.play(customerExcitementJson);
      customerExcitementJson.addEventListener(
        "customerExcitementComplete",
        function customerAnimation() {
          console.log("顾客动画结束");
          lottieAnimations.stop(customerExcitementJson);
        }
      );
    }
  }

  initialization();
  async function initialization() {
    console.log(currentIndex % optionsIP.length, "151");
    let extension = optionsIP[currentIndex % optionsIP.length].awaitImg
      .split(".")
      .pop()
      .toLowerCase();
    console.log(currentIndex, "130");
    if (currentIndex >= options.length) {
    } else {
      $(".customer").css({
        right: "0.3rem",
        transition: "all 0.5s",
      });
      $(".customer-excitement").css({
        right: "0.3rem",
        transition: "all 0.5s",
      });
    }
    customer.show()
    customerExcitement.hide()
    if (extension == "json") {
      // if (customerJson) {
      //   lottieAnimations.destroy(customerJson); //销毁顾客动画
      // }
      customerFun(optionsIP[currentIndex % optionsIP.length].awaitImg,'one');
      $(".customer").css("background-image", "none");
    } else {
      $(".customer").css(
        "background-image",
        `url(${optionsIP[currentIndex % optionsIP.length].awaitImg})`
      );
      // if (customerJson) {
      //   lottieAnimations.destroy(customerJson); //销毁顾客动画
      // }
    }

    $(`.optionUl .key${currentIndex}`).css("display", "block");
    $(`.optionUl .key${currentIndex}`).siblings().css("display", "none");
    if (!isSync) {
      start.show();
    }else{
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      if(classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU){
        start.show();
      }
      if(window.frameElement.getAttribute('user_type') == USER_TYPE.TEA){
        start.show();
      }
    }

    waiter.show();
    waiterMistake.hide();
    microphone.hide();
    correct.hide();
    mistake.hide();
    smoke.hide();
    customerExcitement.hide()
    // $(".read-img-bg").css("display", "none");
    $(".ice-cream").css("display", "none");

    // $(".customer").css({
    //   transition: "all 0.5s",
    // });
    // $(".customer").css({
    //   transform: "none",
    // });
    $(".customer").css({
      transform: "rotateY(0deg)",
      transition: "all 0s",
    });
    $(".customer-excitement").css({
      transform: "rotateY(0deg)",
      transition: "all 0s",
    });
    // options.forEach((option, index) => {
    //   const ipIndex = index % optionsIP.length; // 循环索引
    //   const currentIP = optionsIP[ipIndex];

    //   // 绑定展示数据（如名称、图片路径）
    //   console.log(`当前展示的 IP 数据：`, currentIP.awaitName, currentIP.excitementName);
    // });
    // roteAnimations = await lottieAnimations.init(
    //   roteAnimations,
    //   "./image/smoke.json",
    //   "#smoke",
    //   true
    // );
    // lottieAnimations.play(roteAnimations);
  }
  //麦克风动画
  microphoneFun();
  async function microphoneFun() {
    microphoneJson = await lottieAnimations.init(
      microphoneJson,
      "./image/microphone.json",
      "#microphone",
      true
    );
    lottieAnimations.play(microphoneJson);
    microphoneJson.addEventListener(
      "microphoneComplete",
      function microphoneAnimation() {
        console.log("麦克风动画结束");
        lottieAnimations.stop(microphoneJson);
        microphone.hide();
      }
    );
  }
  //服务员初始动画
  waiterFun();
  async function waiterFun() {
    waiterJson = await lottieAnimations.init(
      waiterJson,
      "./image/Waiter.json",
      "#waiter",
      true
    );
    lottieAnimations.play(waiterJson);
    waiterJson.addEventListener("waiterComplete", function waiterAnimation() {
      console.log("服务员初始动画结束");
      lottieAnimations.stop(waiterJson);
    });
  }
  //服务员听音动画
  waiterMistakeFun();
  async function waiterMistakeFun() {
    waiterMistakeJson = await lottieAnimations.init(
      waiterMistakeJson,
      "./image/Waiter-mistake.json",
      "#waiter-mistake",
      true
    );
    lottieAnimations.play(waiterMistakeJson);
    waiterMistakeJson.addEventListener(
      "waiterMistakeComplete",
      function waiterMistakeAnimation() {
        console.log("服务员听音动画结束");
        lottieAnimations.stop(waiterMistakeJson);
      }
    );
  }

  $(".start").on("syncSoundClick", function (e, message) {
    if (currentIndex < 0 || currentIndex >= options.length) return;
    microphone.show();
    // if (!isSync || window.frameElement.getAttribute('user_type') == 'tea') {
    //   correct.show();
    //   mistake.show();
    // }
    if (!isSync) {
      correct.show();
      mistake.show();
    }else{
      const classStatus = SDK.getClassConf().h5Course.classStatus;
      if(classStatus == CLASS_STATUS.NOT && window.frameElement.getAttribute('user_type') == USER_TYPE.STU){
        correct.show();
        mistake.show();
      }
      if(window.frameElement.getAttribute('user_type') == USER_TYPE.TEA){
        correct.show();
        mistake.show();
      }
    }

    start.hide();
    // $(`.optionUl .key${currentIndex}`)
    //   .find(".read-img-bg")
    //   .css("display", "block");
    // $(`.optionUl .key${currentIndex}`).find('.ice-cream').css('display','block');
    // if ($(`.optionUl .key${currentIndex}`).find("audio").attr("src") != "") {
      // let audio = $(`.optionUl .key${currentIndex}`).find("audio")[0];
      let audio = $('.start-audio')[0];
      SDK.playRudio({
        index: audio,
        syncName: $('.start-audio').attr("data-syncaudio"),
      });

      // audio.onended = function () {}.bind(this);
    // }

    SDK.setEventLock();
    soundClick = true;
  });

  let mistakeClick = true;
  // 点击错误按钮
  $(".mistake").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();

    if (mistakeClick) {
      mistakeClick = false;
      if (!isSync) {
        $(this).trigger("syncMistakeClick");
        return;
      }

        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "syncMistakeClick",
          funcType: "audio",
        });
    }
  });

  $(".mistake").on("syncMistakeClick", function (e, message) {
    waiter.hide();
    waiterMistake.show();
    let mistakeAudio = $(this).find("audio")[0];
    SDK.playRudio({
      index: mistakeAudio,
      syncName: $(this).find("audio").attr("data-syncaudio"),
    });

    mistakeAudio.onended = function () {}.bind(this);

    SDK.setEventLock();
    mistakeClick = true;
  });

  let correctClick = true;
  // 点击正确按钮
  $(".correct").on("click touchstart", function (e) {
    if (e.type == "touchstart") {
      e.preventDefault();
    }
    e.stopPropagation();

    if (correctClick) {
      correctClick = false;
      SDK.reportTrackData(
        {
          action: "CK_FT_INTERACTION_SPOKEBUTTON",
          teaData: {
            roundid: currentIndex + 1,
          },
        },
        USER_TYPE.TEA
      );
      if (!isSync) {
        $(this).trigger("syncCorrectClick");
        return;
      }

        SDK.bindSyncEvt({
          sendUser: "",
          receiveUser: "",
          index: $(e.currentTarget).data("syncactions"),
          eventType: "click",
          method: "event",
          syncName: "syncCorrectClick",
          funcType: "audio",
        });
    }
  });
  let roteAnimations = null;
  $(".correct").on("syncCorrectClick", async function (e, message) {
    microphone.hide();
    correct.hide();
    mistake.hide();
    waiterMistake.hide();
    waiter.show();


    //播放正确音频
    let correctAudio = $(this).find("audio")[0];
    SDK.playRudio({
      index: correctAudio,
      syncName: $(this).find("audio").attr("data-syncaudio"),
    });
    //烟雾动效显示
    smoke.show();
    roteAnimations = await lottieAnimations.init(
      roteAnimations,
      "./image/smoke.json",
      "#smoke",
      false
    );
    //播放动画
    // roteAnimations = roteAnimations
    //   ? roteAnimations
    //   : await lottieAnimations.init(
    //       roteAnimations,
    //       "./image/coinTC.json",
    //       "#smoke",
    //       false
    //     );

    lottieAnimations.play(roteAnimations);
    //先展示烟雾动画，200毫秒后展示冰激凌，防止提前看到冰激凌
    setTimeout(() => {
      //冰激凌显示
      $(`.optionUl .key${currentIndex}`)
        .find(".ice-cream")
        .css("display", "block");
    }, 200);
    roteAnimations.addEventListener(
      "complete",
      function handleAnimationComplete() {
        console.log("烟雾动画结束");
        lottieAnimations.stop(roteAnimations);
        smoke.hide();



        //顾客切换兴奋状态
        let extension = optionsIP[currentIndex % optionsIP.length].excitementImg
          .split(".")
          .pop()
          .toLowerCase();
          customerExcitement.show()
          customer.hide()
        if (extension == "json") {
          // if (customerJson) {
          //   lottieAnimations.destroy(customerJson); //销毁顾客动画
          // }
          customerFun(optionsIP[currentIndex % optionsIP.length].excitementImg,'two');
          $(".customer-excitement").css("background-image", "none");
        } else {
          $(".customer-excitement").css(
            "background-image",
            `url(${optionsIP[currentIndex % optionsIP.length].excitementImg})`
          );
          // if (customerJson) {
          //   lottieAnimations.destroy(customerJson); //销毁顾客动画
          // }
        }

        // $(".customer").css(
        //   "background-image",
        //   // `url(${optionsIP[0].excitementImg})`
        //   `url(${optionsIP[currentIndex % optionsIP.length].excitementImg})`
        // );
        //冰激凌移动到顾客身上
        $(`.optionUl .key${currentIndex}`)
          .find(".ice-cream")
          .animate(
            {
              right: "-2.6rem",
              top: "4.5rem",
            },
            1000,
            function () {
              // $(this).hide(); // 冰激凌移动完成后隐藏

              $(".customer-excitement").css({
                transform: "rotateY(180deg)",
                transition: "all 0s",
              });
              setTimeout(() => {
                $(".customer-excitement").css({
                  right: "-4.36rem",
                  transition: "all 0.5s",
                });
                // 冰激凌移动完成后随着顾客一起移出
                $(this).css({
                  right: "-6rem",
                  transition: "all 0.3s",
                })

                setTimeout(() => {
                  // $(".customer").css({
                  //   transition: "all 0s",
                  // });
                  currentIndex++; //数组顺序加一
                  console.log(currentIndex, "439");
                  //恢复初始状态
                  initialization();

                  correctClick = true;
                  lottieAnimations.destroy(roteAnimations); //销毁上一轮的烟雾动画
                  //轮数长度是否大于数组长度
                  if (currentIndex >= options.length) {
                    start.hide();
                    SDK.reportTrackData({
                      action: "CK_FT_INTERACTION_COMPLETE",
                      data: {
                        result: "success",
                      },
                    });
                    feedback(); //播放正反馈
                  } else {
                    SDK.setEventLock();
                  }
                }, 500);
              }, 500);
            }
          );
      }
    );
    correctAudio.onended = function () {}.bind(this);
  });

  async function feedback() {
    console.log("全部正确结束，执行反馈动画");
    await feedbackAnimation("feedKey1");
    SDK.setEventLock();
  }
});
