<!DOCTYPE html>
<html lang="en">
<head>
        <% var title="TCH0003_四项选图"; %>
        <%include ./src/common/template/index_head %>
</head>
<body>
<div class="container" id="container" data-syncresult="1">

    <section class="commom">
        <div class="desc"></div>
        <div class="title">
            <h3></h3>
        </div>
    </section>
    <div class="content-main">
      <!-- 正确错误音效 -->
      <audio class="wrong" src="./audio/wrong.mp3" data-syncaudio="audiowrong"></audio>
      <audio class="right" src="./audio/right.mp3" data-syncaudio="audioright"></audio>
      <!-- 动画音效 -->
      <audio src="./audio/drag.mp3" class="animationAudio" data-syncaudio="audio2"></audio>
      <!-- 音频播放 -->
      <div class="example-audio sound" data-syncactions="audio-1">
        <img src="./image/sound.gif" alt="" class="gif small">
        <img src="./image/sound.png" alt="" class="png small">
        <audio src="" data-syncaudio="audioExample"></audio>
      </div>
      <!-- 图片列表 -->
      <div class="picList">
        <ul></ul>
      </div>
      <!-- 小手 -->
      <div class="hand"></div>
      <!-- 像素格 -->
      <div class="boxList">
        <ul class="boxUl"></ul>
      </div>
    </div>
    <!-- 老师提示面板 -->
    <div class="right-top-hint"><img src="./image/hint.png" alt=""> Hint</div>
    <div class="doneTip">
      <p>Show a hint to help S</p>
      <span class="btn hint-btn" data-syncactions="hintBtn">Hint for S</span>
    </div>

    <script type="text/javascript">
        document.documentElement.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        }, false);
        // 禁用手指双击缩放：

        var lastTouchEnd = 0;
        document.documentElement.addEventListener('touchend', function (event) {
          var now = Date.now();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);
    </script>
</div>
<%include ./src/common/template/index_bottom %>
</body>
</html>
