<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>TCH0003_四项选图</title>
	<link rel="stylesheet" href="./form/css/style.css?v=<%=new Date().getTime()%>">
	<script src='./form/js/jquery-2.1.1.min.js'></script>
	<script src='./form/js/vue.min.js'></script>
</head>
<body>
	<div id="container">
		<div class="edit-form">
			<h3 class="module-title">TCH0003_四项选图</h3>

			<% include ./src/common/template/common_head %>
      <!-- 交互提示标签 -->
      <% include ./src/common/template/dynamicInstruction/form.ejs %>
      <!-- 选项 -->
      <div class="c-group">
        <div class="c-title">选项</div>
        <div class="c-area upload img-upload">
          <label><em>- 选项个数为2～4个；</em></label>
          <label><em>- 学术团队：选项图片不能互相重叠 (主观校验、技术不做校验)。</em></label>
          <label><em>- 学术团队：设计图片时，注意形态饱满，减少图形的空白区域。</em></label>
          <div class="field-wrap">
            <div class="c-well">
              <span>图片类型</span>
              <label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="1" v-model="configData.source.imgType"> 静态图片</label>
							<label class="inline-label" for="imgtype"><input type="radio" name="imgtype" value="2" v-model="configData.source.imgType"> 3帧雪碧图</label>
              <label>设定图片的宽（横向尺寸）&nbsp;<input type="number" class="c-input-txt input50" v-model="configData.source.imgWidth"><em>像素，数字n~m</em></label>
              <label>设定图片的高（纵向尺寸）&nbsp;<input type="number" class="c-input-txt input50" v-model="configData.source.imgHeight"><em>像素，数字n~m</em></label>
            </div>
          </div>
          <ul>
            <li  v-for="(item,index) in configData.source.options">
              <div class="c-well">
                <div class="field-wrap">
                  <div class="add-field-content">
                    <label class="field-label"  for="">选项{{index+1}}</label>
                    <span class="dele-tg-btn" v-on:click="delOption(item)" v-show="configData.source.options.length>2"></span>
                    <!-- 勾选正确答案 -->
                    <div class="field-radio-wrap fr">
                      <input type="radio"   v-bind:value="index"  :disabled="!item" v-model="configData.source.right">
                      <label for="answer" class="fr">正确答案</label>
                    </div>
                  </div>

                  <span class='txt-info'>上传图片</em>
                  <label :for="'content-pic-'+index" class="btn btn-show upload" v-if="!item.img">上传</label>
                  <label :for="'content-pic-'+index" class="btn upload re-upload" v-if="item.img!=''?true:false">重新上传</label>
                  <span class='txt-info'><em>&nbsp;&nbsp;文件大小:≤50KB </em></span>
                  <input type="file" v-bind:key="Date.now()" class="btn-file" size="" isKey="1" :id="'content-pic-'+index" @change="imageUpload($event,item,'img',50)">

                </div>
                <div class="img-preview" v-if="item.img">
                  <img v-bind:src="item.img" alt=""/>
                  <div class="img-tools">
                    <span class="btn btn-delete" v-on:click="item.img=''">删除</span>
                  </div>
                </div>
                <label>位置<em>*</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="number" class="c-input-txt input60" v-model="item.position">数字，1～800</label>
              </div>
            </li>
          </ul>
          <button type="button" class="add-tg-btn" v-show="configData.source.options.length<configData.source.optionLength" v-on:click="addOption({
            img: '',
            position:'',
            isRight:0
          })">+</button>
        </div>
      </div>

      <!-- 选项的显示规则 -->
      <div class="c-group">
        <div class="c-title">选项的显示规则</div>
        <div class="c-area upload img-upload">
          <div class="c-well">
            <span>每个动态图片选项出现形式：（静态图片无动画，请勿填写）</span>
            <div class="field-wrap">
              <label class="field-label"  for="">动画音效</label>
              <input type="file" accept=".mp3" id="content-audio-1" volume="20" v-bind:key="Date.now()" class="btn-file" v-on:change="audioUpload($event,configData.source,'animationAudio')">
              <label for="content-audio-1" class="btn btn-show upload" v-if="!configData.source.animationAudio">上传</label>
              <div class="audio-preview" v-show="configData.source.animationAudio">
                <div class="audio-tools">
                  <p v-show="configData.source.animationAudio">{{configData.source.animationAudio}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="configData.source.animationAudio"></audio>
                </span>
              </div>
              <label for="content-audio-1" class="btn upload btn-audio-dele" v-if="configData.source.animationAudio" @click="configData.source.animationAudio=''">删除</label>
              <label for="content-audio-1" class="btn upload re-upload" v-if="configData.source.animationAudio">重新上传</label>
              <label><em>大小：≤20KB</em></label>
              <label>动画时长<em>*</em> <input type="number" class="c-input-txt input70" v-model="configData.source.time">数字，100～500 毫秒</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 题干音频 -->
      <div class="c-group">
        <div class="c-title">题干音频</div>
        <div class="c-area upload img-upload">
          <div class="c-well">
            <label><em>- 学术团队：音频图标避免和其他元素重叠。</em></label>
            <div class="field-wrap">
              <label class="field-label"  for="">题干声音</label>
              <input type="file" accept=".mp3" id="content-audio-2" volume="40" v-bind:key="Date.now()" class="btn-file"  v-on:change="audioUpload($event,configData.source,'audio')">
              <label for="content-audio-2" class="btn btn-show upload" v-if="!configData.source.audio">上传</label>
              <div class="audio-preview" v-show="configData.source.audio">
                <div class="audio-tools">
                  <p v-show="configData.source.audio">{{configData.source.audio}}</p>
                </div>
                <span class="play-btn" v-on:click="play($event)">
                  <audio v-bind:src="configData.source.audio"></audio>
                </span>
              </div>
              <label for="content-audio-2" class="btn upload btn-audio-dele" v-if="configData.source.audio" @click="configData.source.audio=''">删除</label>
              <label for="content-audio-2" class="btn upload re-upload" v-if="configData.source.audio">重新上传</label>
              <label><em>大小：≤40KB  非必填</em></label>
              <label>图标位置&nbsp;&nbsp;<input type="number" class="c-input-txt input70"  v-model="configData.source.imgPosition">数字，1～800</label>
            </div>
          </div>
        </div>
      </div>

			<button class="send-btn" v-on:click="onSend">提交</button>

		</div>
		<div class="edit-show">
			<div class="show-fixed">
				<div class="show-img">
					<img src="./form/img/bg.jpg?_=<%= Date.now() %>" alt="">
				</div>
				<ul class="show-txt">
					<li>图片格式：</em>JPG/PNG/GIF</li>
					<li>声音格式：</em>MP3/WAV</li>
					<li>视频格式：</em>MP4</li>
					<li>带有“ * ”号为必填项</li>
				</ul>
			</div>
		</div>
	</div>
</body>
<script src='./form/js/form.js?_=<%= Date.now() %>'></script>
</html>
