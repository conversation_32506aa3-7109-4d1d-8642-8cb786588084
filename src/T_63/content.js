var configData = {
  bg: './assets/images/bg.jpg',
  desc: 'TCH0003_四项选图',
  title: '',
  sizeArr: ['', '', '900*600', '1350*600'], //图片尺寸限制
  tImg: 'assets/images/b164806a1b826b1b386f5496478573fe.png',
  tImgX: 1340,
  tImgY: 15,
  tg: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "1111111111111111111"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    },
    {
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }
  ],
  level: {
    high: [{
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      },
      {
        content: "eegeg appy family. My father i appy family. My father i",
        title: "weqwf appy family. My father i"
      }
    ],
    low: [{
      content: "eegeg appy family. My father i appy family. My father i",
      title: "weqwf appy family. My father i"
    }]
  },
  source: {
    optionLength: 7,  //选项上限
    imgType: 2,       //1  静态图  2 2帧雪碧图
    imgWidth:1200, //设定图片的宽
    imgHeight:458, //设定图片的高
    options:[
      {
        img: "./assets/images/img01.png",
        position: 241,
      },
      {
        img: "./assets/images/img02.png",
        position: 372,
      },
      {
        img: "./assets/images/img03.png",
        position: 223,
      },
      {
        img: "./assets/images/img04.png",
        position: 314,
      }
    ],
    audio: './audio/lemon.mp3', //题干音频
    imgPosition: '48', //题干图标位置
    animationAudio:'./audio/drag.mp3', //动画音效
    time:150, //动画时长
    right: 0,
  }
};
(function(pageNo) { configData.page = pageNo })(0)
