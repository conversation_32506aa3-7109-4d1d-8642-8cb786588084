"use strict"
import '../../common/js/common_1v1.js'
import "../../common/js/teleprompter.js"
// import '../../common/js/commonFunctions.js'
const isSync = parent.window.h5SyncActions && parent.window.h5SyncActions.isSync;

$(function () {

  // 埋点：进入页面
  var h5SyncActions = parent.window.h5SyncActions;
  /**
   * 控制器调用的变量
   */
  window.h5Template = {
    hasPractice: '1' // 是否有授权按钮 1：是 0：否
  }

  if (configData.bg == '') {
    $(".container").css({
      'background-image': 'url(./image/bj.jpg)'
    })
  }
  let options = configData.source.options, //图片列表
      imgType = configData.source.imgType, // 1  静态图  2 2帧雪碧图
      imgWidth = configData.source.imgWidth, //设定图片的宽
      imgHeight = configData.source.imgHeight, //设定图片的高
      animationAudio = configData.source.animationAudio, //动画音效
      time = configData.source.time, //动画时长
      audio = configData.source.audio, //题干音频
      imgPosition = configData.source.imgPosition,//题干图标位置
      right = configData.source.right, //是否正确答案
      userType = window.frameElement && window.frameElement.getAttribute('user_type'),//用户身份学生还是老师
      imgIndex = 0,
      timeFlag = null,
      timeInit = null, //定时器
      clickAble = false;

  //添加音频
  if(audio) {
    $('.example-audio audio').attr('src', audio);
  } else {
    $('.example-audio').hide()
  }
  //生成像素表格
  renderPx();
  function renderPx() {
    let liHtml = '';
    for (let i = 1; i < 801; i++) {
      liHtml += `
				<li class="pos_${i}"></li>	`
    }
    $('.boxUl').html(liHtml);
  }

  //小手位置初始化
  initHand();
  function initHand() {
    let firtstPosition = options[right].position;
    let imgIndex = 3;
    if(imgType == 1) {
      imgIndex = 1
    }
    let marginWidth = imgWidth/imgIndex/2/100 - .6 + 'rem';
    let marginHg = imgHeight/2/100 - .9 + 'rem'
    if (firtstPosition) {
      let left = ($('.pos_' + firtstPosition).offset().left - $('.container').offset().left) / window.base  + 'rem';
      let top = ($('.pos_' + firtstPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
      $('.hand').css({
        left: left,
        top: top,
        marginLeft:marginWidth,
        marginTop:marginHg
      });
    }
  }

  // 初始化列表
  optionsFn();
  function optionsFn() {
    let str = '';
    for (let i = 0; i < options.length; i++) {
      let currentPosition = options[i].position,
          left = ($('.pos_' + currentPosition).offset().left - $('.container').offset().left) / window.base + 'rem',
          top = ($('.pos_' + currentPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
      str += `<li class="ans-img" data-imgType="${imgType}" data-width="${imgWidth}" data-key="${i}" data-syncactions="sele-${i}" data-positon="${options[i].position}" style="
              left:${left}; top:${top}; background-image:url(${options[i].img})">
              <div class="right-answer shade">
                <img src="./image/right.png" alt="">
              </div>
            </li>`
    }
    $(".picList ul").html(str);
    initType()
  }

  //初始化   静态图（1） 2帧（3） 样式
  function initType(){
    let naturalWidth = imgWidth / 100,
        ele = $(".picList ul li"),
        naturalHeight = imgHeight / 100;
    if(imgType == 1) {
      ele.css({
        backgroundSize: '100% 100%',
        width: naturalWidth + 'rem',
        height: naturalHeight + 'rem'
      });
      clickAble = true;
    } else {
      ele.css({
        backgroundSize: '300% 100%',
        width: naturalWidth/3 + 'rem',
        height: naturalHeight + 'rem',
        backgroundPosition: '0% 100%'
      });
      animationFn();
    }
  }

  // 定时器
  function timelyFun() {
    console.log("第二帧动画")
    if(imgIndex != 0) {
      pauseRudioFn();
    }
    clearInterval(timeInit);
    $(".picList ul li").eq(imgIndex).css({
      backgroundPosition: '-200% 100%'
    })
    imgIndex++;
    if(imgIndex == options.length) {
      //清除定时器
      clearInterval(timeFlag);
      clearInterval(timeInit);
      // 添加抖动效果
      $(".picList ul").addClass("shakeLeft");
      clickAble = true;
    } else {
      timeFn()
    }
  }
  // 雪碧图的动画
  function timeFn() {
    timeInit = setInterval(function(){
      console.log("第一帧动画")
      audioFn()
      $(".picList ul li").eq(imgIndex).css({
        backgroundPosition: '-100% 100%'
      })
    }, time)
  }
  function timeAnimatonFn() {
    timeFlag = setInterval(timelyFun, time*2)
  }

  function animationFn() {

    timeAnimatonFn()
    timeFn()
  }

  // // 初始化雪碧图播放音频
  function audioFn() {
    if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
      SDK.playRudio({
        index: $('.animationAudio')[0],
        syncName: $('.animationAudio').attr("data-syncaudio")
      })
    }

  }

  // 暂定播放
  function pauseRudioFn() {
    if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
      SDK.pauseRudio({
        index: $('.animationAudio')[0],
        syncName: $('.animationAudio').attr("data-syncaudio")
      })
    }
  }

  // 音频初始化
  andioShow();
  function andioShow() {
    // 动画音频
    $('.animationAudio').attr('src', animationAudio);
    if (!audio) {
      $('.example-audio').hide();
    }
    if(audio && imgPosition) {
      let left = ($('.pos_' + imgPosition).offset().left - $('.container').offset().left) / window.base + 'rem',
          top = ($('.pos_' + imgPosition).offset().top - $('.container').offset().top) / window.base + 'rem';
      $('.example-audio').css({
        top: top,
        left: left
      })
    }
  }

  //判断是学生还是老师显示底部按钮和mask
  isShowBtn();
  function isShowBtn() {
    if (isSync) {
      if (userType == 'tea') {
        $(".doneTip").show();
      } else {
        $(".doneTip").hide();
      }
    } else {
      var hrefParam = parseURL("http://www.example.com");
      if (top.frames[0] && top.frames[0].frameElement) {
        hrefParam = parseURL(top.frames[0].frameElement.src)
      }
      var role_num = hrefParam.params['role']

      function parseURL(url) {
        var a = document.createElement('a')
        a.href = url
        return {
          source: url,
          protocol: a.protocol.replace(':', ''),
          host: a.hostname,
          port: a.port,
          query: a.search,
          params: (function () {
            var ret = {},
              seg = a.search.replace(/^\?/, '').split('&'),
              len = seg.length,
              i = 0,
              s
            for (; i < len; i++) {
              if (!seg[i]) {
                continue;
              }
              s = seg[i].split('=')
              ret[s[0]] = s[1]
            }
            return ret
          })(),
          file: (a.pathname.match(/\/([^\/?#]+)$/i) || [, ''])[1],
          hash: a.hash.replace('#', ''),
          path: a.pathname.replace(/^([^\/])/, '/$1'),
          relative: (a.href.match(/tps?:\/\/[^\/]+(.+)/) || [, ''])[1],
          segments: a.pathname.replace(/^\//, '').split('/')
        }
      }
      if (role_num == '1' || role_num == undefined) {
        $(".doneTip").show();
      } else if (role_num == '2') {
        $(".doneTip").hide();
      }
    }
  }

  // 点击播放音频
  let soundClick = true,
    isPlaySound = true;
  $('.sound').on('click touchstart', function (e) {
    if(clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();

      if (soundClick) {
        soundClick = false;
        if (!isSync) {
          $(this).trigger('syncSoundClick');
          return;
        }
        if (window.frameElement.getAttribute('user_type') == 'tea') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            syncName: 'syncSoundClick',
            funcType: 'audio'
          });
        } else {
          $(this).trigger('syncSoundClick');
          return;
        }
      }
    }
  })
  $('.sound').on('syncSoundClick', function (e, message) {

    let gif = $(this).find('.gif');
    let png = $(this).find('.png');
    let audio = $(this).find('audio')[0];
    if (isPlaySound) {
      // audio.play();
      SDK.playRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.show();
      png.hide();
    } else {
      // audio.pause();
      SDK.pauseRudio({
        index: audio,
        syncName: $(this).find('audio').attr("data-syncaudio")
      })
      gif.hide();
      png.show();
    }
    audio.onended = function () {
      gif.hide();
      png.show();
      isPlaySound = true;
    }.bind(this);

    isPlaySound = !isPlaySound;

    SDK.setEventLock();
    soundClick = true;

  });

  // 选择答案
  let itemClick = true;
  $(".picList li").on('click touchstart', function(e) {
    if(clickAble) {
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if (itemClick) {
        itemClick = false;
        if (!isSync) {
          $(this).trigger('syncItemClick');
          return
        }
        if (window.frameElement.getAttribute('user_type') == 'stu') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data('syncactions'),
            eventType: 'click',
            method: 'event',
            syncName: 'syncItemClick',
            otherInfor: {
              answer: $(this).index() === right
            },
            recoveryMode: '1'
          });
        }
      }
    }
  });
  $(".picList li").on('syncItemClick', function(e, message) {
    // 答对后不可再选择
    if ($(this).hasClass('isRight')) {
      SDK.setEventLock();
      itemClick = false;
      return;
    }

    //断线重连
    if (isSync && message && message.operate == 5) {
      let obj = message.data[0].value.syncAction.otherInfor;
      let answer = obj.answer;
      if (answer) {
        $('.picList li').addClass('isRight');
        let mask = $('.shade').eq($(this).index());
        mask.addClass('flex');
      }
      SDK.setEventLock();
      return;
    }

    if ($(this).index() === right) {
      console.log('c-page:----------------%s: 答对了！');
      if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
        // $('.right')[0].play();
        SDK.playRudio({
          index: $('.right')[0],
          syncName: $('.right').attr("data-syncaudio")
        })
      }

      $('.picList li').addClass('isRight');
      let mask = $('.shade').eq($(this).index());
      mask.addClass('flex');
      // for(var i=0; i<options.length; i++) {
      //   if(i != right) {
      //     $(".picList li").eq(i).css({opacity:0.8})
      //   }
      // }
      SDK.setEventLock();
      // starFun();
      itemClick = false;
    } else {
      itemClick = false;
      console.log('c-page:----------------%s: 答错了！');
      if ($(window.frameElement).attr('id') === 'h5_course_self_frame' || !isSync) {
        // $('.wrong')[0].play();
        SDK.playRudio({
          index: $('.wrong')[0],
          syncName: $('.wrong').attr("data-syncaudio")
        })
      }
      $(this).addClass('shake');

      $(this).on('animationend webkitAnimationEnd', function () {
        $(this).removeClass('shake');
        SDK.setEventLock();
        setTimeout(function(){
          itemClick = true;
        },100)

      })
    }
  })

  //老师点击 hint 按钮
  let btnStatus = true;
  $(".hint-btn").on("click touchstart", function (e) {
    if(clickAble){
      if (e.type == "touchstart") {
        e.preventDefault()
      }
      e.stopPropagation();
      if (btnStatus) {
        btnStatus = false;
        if (!isSync) {
          $(this).trigger("btnClick");
          return;
        }
        if (window.frameElement.getAttribute('user_type') == 'tea') {
          SDK.bindSyncEvt({
            sendUser: '',
            receiveUser: '',
            index: $(e.currentTarget).data("syncactions"),
            eventType: 'click',
            method: 'event',
            syncName: 'btnClick',
            otherInfor: '',
            recoveryMode: '1'
          });
        }
      }
    }
  })
  $(".hint-btn").on('btnClick', function (e, message) {
    $('.hand').show();
    $('.right-top-hint').show();
    clickAble = false
    setTimeout(function () {
      $('.hand').hide();
      $('.right-top-hint').hide();
      btnStatus = true;
      clickAble = true;
    }, 3000)
    SDK.setEventLock();
  });


  //答题结束触发发送星星
  function starFun() {
    if (!isSync) {
      return false;
    }
    console.log('进入发星')
    var classStatus = parent.window.h5SyncActions.classConf.h5Course.classStatus;
    var support1v1h5star = parent.window.h5SyncActions.classConf.serverData.objCourseInfo.support1v1h5star;
    var device = parent.window.h5SyncActions.classConf.h5Course.device;
    if (window.frameElement.getAttribute('user_type') == 'stu' && classStatus == 2) {
      if ((device == 'pc' && support1v1h5star == 1) || device != 'pc') {
        console.log("学生答题正确后发星星")
        SDK.bindSyncStart({
          type: "newH5StarData",
          num: 1
        });
      }

    }
  }


})
