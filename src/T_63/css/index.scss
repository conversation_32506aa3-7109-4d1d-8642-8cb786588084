@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}

.commom {
	display: -webkit-flex;
	display: flex;
	flex-direction: column;
	width: 100%;
	height: 2.2rem;
	position: absolute;
    right: 0px;
	.desc {
		top: 0.6rem;
	}
	.title-first {
		width: 100%;
		height: .8rem;
		padding: 0 1.4rem;
		box-sizing: border-box;
		text-align: center;
		margin: .45rem auto .2rem;
		font-size: .8rem;
		font-weight: bold;
		color: #333;
	}
}
.container {
	background-size: auto 100%;
	position: relative;
  .content-main{
    // 音频播放
    .example-audio{
      display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			width: 1.45rem;
			height: 1.34rem;
			background: url(../image/sound_bg.png) no-repeat center;
			background-size: cover;
      cursor: pointer;
      .small {
				width: 0.9rem;
				margin-top: 1px;
			}
			img {
				width: 1.45rem;
			}
			.gif {
				display: none;
			}
    }
    // 列表渲染
    .picList{
      position: relative;
      ul{
        li{
          position: absolute;
          background-size: 100%;
          img{
            width: 100%;
          }
          .right-answer{
            display: none;
            justify-content: center;
            align-items: center;
            position: absolute;
            left: 50%;
            bottom: -.26rem;
            width: 1.16rem;
            height: 1.16rem;
            cursor: pointer;
            transform: translate(-50%, 0);
          }
          .flex{
            display:block;
          }
        }
        .shadow{
          opacity: 0.7;
        }
      }
    }
    // 像素格
    .boxList {
      position: absolute;
      left: 1.08rem;
      top: .3rem;
      width: 17rem;
      height: 9.8rem;
      z-index: -1;
      .boxUl {
        position: absolute;
        left: .2rem;
        top: .7rem;
        width: 16rem;
        height: 8.4rem;
        display: flex;
        flex-wrap: wrap;
        li {
          // background: red;
          margin-left: .01rem;
          width: .39rem;
          height: .39rem;
        }
      }
    }
    //小手点击动画
    .hand{
      position: absolute;
      left: 1.4rem;
      top: 1.3rem;
      margin-left: -0.35rem;
      background: url('../image/hands.png');
      background-size: 7.2rem 1.8rem;
      animation: handClick 1s steps(4) infinite;
      cursor: pointer;
      opacity: 1;
      z-index: 99;
      width: 1.8rem;
      height: 1.8rem;
      display: none;
    }
  }
  // 老师提示面板
  .right-top-hint {
		display: none;
		position: absolute;
		right: 0;
		top: 0;
		width:1.95rem;
		height:0.67rem;
		background:rgba(70,70,70,1);
		border-bottom-left-radius: 0.22rem;
		color: #fff;
		line-height:0.67rem;
		font-size: 0.43rem;
		img {
			width: 0.51rem;
			height: 0.53rem;
			margin-left: 0.22rem;
		}
  }
  .doneTip{
    width: 7.91rem;
    height: 1rem;
    border-radius: 0.2rem .2rem 0 0;
    position: absolute;
    margin-left: -3.5rem;
    left:50%;
    bottom: 0;
    background: rgba(255,255,255,.7);
    display: none;
    font-size: .3rem;
    p{
      height: 100%;
      width: 5.14rem;
      line-height: 1rem;
      text-align: center;
      padding-left: 0.53rem
    }
    .btn {
      position: absolute;
      top: .2rem;
      height: .23rem;
      padding: 0.17rem 0.26rem;
      color:#fff;
      text-align: center;

      line-height: 0.23rem;
      border-radius: .3rem;
      cursor: pointer;
    }
    .hint-btn{
      background: #f1a91e;
      left: 5.6rem;
    }
  }
}

.shakeLeft{
  // animation: zoomLarge .1s both ease-in;
  li{
    cursor: pointer;
  }
}
@keyframes zoomLarge {
  0% {
      transform:scale(1)
  }

  50% {
      transform:scale(1.1)
  }

  100% {
      transform:scale(1)
  }
}

@keyframes shakeLeft{
	  0% {
        transform: translateX(-8px);
    }
    60% {
        transform: translateX(8px);
    }
    100% {
        transform: translateX(0px);
    }
}

.shake{
	animation: shakeUp 0.4s both ease-in;
}
@keyframes shakeUp{
	0% {
        transform: translateX(10px);
    }
    20% {
        transform: translateX(-10px);
    }
    40% {
        transform: translateX(10px);
    }
    60% {
        transform: translateX(-10px);
    }
    80% {
        transform: translateX(10px);
    }
    100% {
        transform: translateX(0px);
    }
}

@keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}
@-webkit-keyframes handClick {
	0%{
		background-position: 0 0;
	}
	100%{
		background-position:133% 0;
	}
}
