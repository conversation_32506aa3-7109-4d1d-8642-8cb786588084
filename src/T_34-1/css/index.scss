@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';
@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
*{
    box-sizing: border-box;
}
.commom {
    position: relative;
    z-index: 100;
}
.desc-visi{
	visibility: hidden;
}
.container{
    // background: url(../image/defaultBg.jpg) no-repeat;
    background-size: auto 100%;
	position: relative;
	
	.main {
		position: relative;
		top: 2rem;
	}
}

.content {
	width: 14.8rem;
	height: 7.64rem;
	display: flex;
	// justify-content: space-between;
	justify-content: center;
	align-items: center;
	margin: 0 auto;

	.left-text {
	    width: 6.58rem;
	    height: 7.64rem;
	    margin-right: .43rem;
	    display: flex;
	    flex-direction: column;
	    justify-content: center;

		.text-list {
			width: 6.58rem;
			margin-bottom: .3rem;
			padding: .6rem .5rem;
			box-shadow: 0 0.12rem 0.1rem 0 rgba(0,0,0,.15);
			box-sizing: border-box;
			border-radius: .45rem;
			background-color: #fff;
			font-size: .52rem;
			word-break: break-all;
		}

		.stu-list {
			width: 5.8rem;
			margin: 0 auto;
			
			li {
				float: left;
				width: 2.5rem;
				height: .8rem;
				margin: .1rem .2rem;
				display: flex;
				justify-content: center;
				align-items: center;
				border: .05rem solid #fff;
				border-radius: .8rem;
				box-shadow: 0 0.12rem 0.1rem 0 rgba(0,0,0,.15);
				box-sizing: border-box;
				font-size: .3rem;
				font-weight: bold;
				background-color: #fff;
				color: #66b900;
			}
			li.active {
				background-color: #66b900;
				color: #fff;
			}
		}
	}

	.right-dial {
		width: 7.36rem;
		height: 7.36rem;
		margin-left: .43rem;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 100%;

		.dial {
			width: 100%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			border-radius: 100%;
			transform-origin: center;
		}
		.dial.animate {
			animation: dial 1s linear infinite;
		}
		@keyframes dial {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		.start-dial, .stop-dial {
			display: block;
			width: 1.72rem;
			height: 1.72rem;
			position: absolute;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
			-webkit-transform: translate(-50%, -50%);
			cursor: pointer;

			i {
				display: block;
				width: 1.72rem;
				height: 2.09rem;
				position: absolute;
				bottom: 0;
			}
		}
		.start-dial {
			i {
				background: url(../image/start.png) no-repeat center;
				background-size: cover;
			}
		}
		.start-dial_lang2 {
			i {
				background: url(../image/start2.png) no-repeat center;
				background-size: cover;
			}
		}
		.stop-dial {
			display: none;
			i {
				background: url(../image/stop.png) no-repeat center;
				background-size: cover;
			}
		}
	}

}