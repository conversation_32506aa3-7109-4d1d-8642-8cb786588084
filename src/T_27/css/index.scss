@import '../../common/css/reset-title.css';
@import '../../common/css/animation.css';

@mixin setEle($l:0rem,$t:0rem,$w:0rem,$h:0rem){
	position: absolute;
	left: $l;
	top: $t;
	width: $w;
	height: $h;
}
.container{
	background-image: url(../image/defaultBg.jpg);
}
// .desc-visi{
// 	    visibility: hidden;
// }
// .tg{
// 	z-index: 999;
// }fans-area 
// .title{
// 	height: 1.3rem;
// }
.main {
	width: 100%;
	height: 80%;
}
.stage{
	width: 100%;
	height: 100%;
	position: relative;
	left: 0;
	top: 2rem;
	// 按钮
	.btns{
		position: absolute;
		left:7.77rem;;
		top: 6.45rem;
		width: 1.4rem;
		height: 1.5rem;
		 z-index: 10;
		cursor: pointer;
		img{
			position: absolute;
			left:.48rem;
			height: .28rem;
			top: .2rem;
		}
		.upBtn{
			display: block;
			position: relative;
			width: 100%;
			height: .675rem;
			img{
				opacity: 0.3;
			}
		}
		.downBtn{
			display: block;
			position: relative;
			width: 100%;
			height: .65rem;
			border-radius: 0 0 .6rem 0;
		}
	}


	.dis-area{
		@include setEle(1.45rem,.35rem,7.5rem,7.5rem);
		border: .12rem #fff solid;
		background: rgba(255,255,255,.75);
		border-radius: 0.6rem;
		overflow: hidden;
		box-shadow: 0 0.32rem 0.38rem 0 rgba(0,0,0,.15);
		transform-origin:'left top';
		.dis-text{
			position: relative;
			margin-top: .1rem;
			margin-left: .1rem;
			font-size: .4rem;
			height: 1.64rem;
			width: 6.13rem;
			color:#333;
			overflow: hidden;
			font-weight: bolder;
			p{
				position: absolute;
				top: 0;
				width:6rem;
				margin-left: 0.13rem;
				font-size: .38rem;
				line-height: .5rem;
			}
		}
	}
	.ans-area{
		@include setEle(9.85rem,0.35rem,2.06rem,2.06rem);
		line-height: 2.06rem;
		border: .12rem transparent solid;
		background: transparent;
		border-radius: 0.8rem;
		overflow: hidden;
		text-align: center;
		img{
			//position: absolute;
			vertical-align: middle;
			cursor: pointer;
		}
		i{
			height: 100%;
			display: inline-block;
			vertical-align: middle;
		}
	}
	.ansBot-area{
		@extend .ans-area;
		background: #fff;
		border: .12rem #fff solid;
		box-shadow: 0 0.32rem 0.38rem 0rem rgba(0,0,0,.15);
	}
	.scale:hover img{
		transform: scale(1.05,1.05);
	}
}
.ansButton-area{
	width: 2.06rem;
	height: 2.06rem;
	display: flex;
	justify-content: center;
    align-items: center;
    position: absolute;
	left: 15.45rem;
	top: 5.63rem;
	.ans-btn{
		width: 2.14rem;
		height: 1.04rem;
		border: 0.08rem #fff solid;
		border-radius: 1.04rem;
		background: #cdcdcd;
		text-align: center;
		line-height: 1.04rem;
		color: #fff;
		font-size: 0.48rem;
		font-weight: 600;
		cursor: pointer;
	}
	.allowSub{
		background: #59a500;
	}
	.allowSub:hover{
		transform: scale(1.05,1.05)
	}
}
.showName-area{
	display: flex;
	justify-content: center;
    align-items: center;
    flex-direction: column;
	width: 2.06rem;
	height: 2.06rem;
    position: absolute;
	left: 15.45rem;
	top: 5.63rem;
	border: .12rem #fff solid;
	background: #fff;
	border-radius: 0.8rem;
	.stuNameList{
		width: 100%;
		line-height: 0.4rem;
		color: #cccccc;
		font-size: .24rem;
		font-weight: 600;
		text-align: center;
		overflow: hidden;
	    text-overflow: ellipsis;
	    white-space: nowrap;
	}
}
.stuName-active{
	color: #55a000!important;
}
.cover{
	position: absolute;
    z-index: 120;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
	display: none;
	// background: red;
}
.photoMask{
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	display: none;
	.showMask{
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background: rgba(8, 8, 8, 0.7);
		z-index: 199;
	}
	.dis-area{
		overflow: hidden;
	}
}
@keyframes ptoStyle{
	0%{
		transform: scale(1.2,1.2) rotate(0deg);
		// transform-origin:left top;
	}
	100%{
		transform: scale(1.1,1.1) rotate(-3deg);
		// transform-origin:left top;
	}
}
@keyframes camStyle{
	0%{
		background: rgba(51, 51, 51, 1);
	}
	20%{
		background: rgba(255, 255, 255, 1);
	}
	40%{
		background: rgba(255, 255, 255, 0.8);
	}
	100%{
		background:rgba(51, 51, 51, 0.51);
	}
}
@-webkit-keyframes ptoStyle{
	0%{
		transform: scale(1.2,1.2) rotate(0deg);
		// transform-origin:left top;
	}
	100%{
		transform: scale(1.1,1.1) rotate(-3deg);
		// transform-origin:left top;
	}
}
@-webkit-keyframes camStyle{
	0%{
		background: rgba(51, 51, 51, 1);
	}
	20%{
		background: rgba(255, 255, 255, 1);
	}
	40%{
		background: rgba(255, 255, 255, 0.8);
	}
	100%{
		background:rgba(51, 51, 51, 0.51);
	}
}