<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>清除 CCS localStorage - 书签工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0 auto;
            max-width: 800px;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        .bookmarklet {
            display: inline-block;
            padding: 10px 15px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-decoration: none;
            color: #333;
            font-weight: bold;
            margin: 15px 0;
        }
        .steps {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .steps li {
            margin-bottom: 10px;
        }
        code {
            background-color: #f0f0f0;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>清除 CCS localStorage 书签工具</h1>
    
    <p>由于浏览器安全策略的限制，我们不能从本地网页直接访问 https://ccs.51talkjr.com 的localStorage数据。</p>
    <p>请使用下面的书签工具(bookmarklet)来解决这个问题:</p>
    
    <div class="steps">
        <h2>使用方法:</h2>
        <ol>
            <li>将下面的"清除CCS数据"链接拖动到您的浏览器书签栏</li>
            <li>访问 <a href="https://ccs.51talkjr.com" target="_blank">https://ccs.51talkjr.com</a> 网站</li>
            <li>在该网站页面上点击您刚才保存的书签</li>
            <li>系统会自动清除所有以 "ccs_" 开头的localStorage数据</li>
        </ol>
    </div>
    
    <!-- 将此链接拖到书签栏 -->
    <p>将此链接拖到您的书签栏: <a class="bookmarklet" href="javascript:(function(){var count=0;var keys=Object.keys(localStorage);for(var i=0;i<keys.length;i++){if(keys[i].startsWith('ccs_')){localStorage.removeItem(keys[i]);count++;}}alert('成功清除了 '+count+' 个以 ccs_ 开头的 localStorage 项目！');})();">清除CCS数据</a></p>
    
    <h2>或者使用控制台方法:</h2>
    <p>如果您更喜欢使用浏览器的开发者控制台:</p>
    <ol>
        <li>访问 <a href="https://ccs.51talkjr.com" target="_blank">https://ccs.51talkjr.com</a> 网站</li>
        <li>在网页上点击鼠标右键，选择"检查"或"开发者工具"(或按F12键)</li>
        <li>切换到"控制台"(Console)选项卡</li>
        <li>复制粘贴以下代码并按回车执行:</li>
    </ol>
    
    <code>
        (function(){<br>
        &nbsp;&nbsp;var count=0;<br>
        &nbsp;&nbsp;var keys=Object.keys(localStorage);<br>
        &nbsp;&nbsp;for(var i=0;i<keys.length;i++){<br>
        &nbsp;&nbsp;&nbsp;&nbsp;if(keys[i].startsWith('ccs_')){<br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;localStorage.removeItem(keys[i]);<br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count++;<br>
        &nbsp;&nbsp;&nbsp;&nbsp;}<br>
        &nbsp;&nbsp;}<br>
        &nbsp;&nbsp;alert('成功清除了 '+count+' 个以 ccs_ 开头的 localStorage 项目！');<br>
        })();
    </code>
</body>
</html>