# 概述
本项目为1V1模板

模板列表:http://vcs.51talk.com/frontend/innovation_1v1/blob/master/LIST.md

模板与控制器通信文档：https://wiki.51talk.com/pages/viewpage.action?pageId=28149191
# 开发说明

### 1、安装依赖

注：node版本需在12以下,建议使用：v11.15.0

```
npm install
```

### 2、本地开发　本地测试

```
npm  start  p
```
其中,p为模板编号。例如
```
npm start 61
```
编译完成之后,　会自动在浏览器打开一个目录。

模板：http://localhost:3033/dist/61/index.html

表单：http://localhost:3033/dist/61/form.html

``` 特别提醒：开发所用图片务必使用　https://tinypng.com/等工具压缩。体积过大的图会严重影响用户体验。 ```
### 3、 本地服务器自测（如需控制器配合测试）

```
npm run  build  p
```

其中,p为模板编号。例如：
```
npm run  build 01
```
如需使用客户端自测，尤其是本次需求控制器和模版需要一起开发和测试时，可按照下面步骤。

①执行本命令进行编译

②将dist文件下编译出的相应模板，上传到107服务器控制器指定目录。

例如：目前控制器项目中指定了教材是：http://h5course.51talk.com/dist/111111/01/index.html， 107服务器的对应教材目录为 data/h5course/dist/111111 。本目录下有多个文件夹，分别代表教材的每一页。如我们将编译后文件上传到data/h5course/dist/111111/08, 则本模版处于教材第8页。

控制器指定教材代码：http://vcs.51talk.com/ccs/ctrl/blob/master/build/ctrl.conf.js

③AC客户端本地配置控制器和教材地址即可。

配置方式可参考文档：https://wiki.51talk.com/pages/viewpage.action?pageId=42871232
或咨询测试人员。

### 4、打包测试与发布
```
npm run zip p
```
其中,p为项目名称。例如，打包T_01模板
```
npm run zip 01
```

运行命令会在./zip下生成zip压缩包，压缩包名称会以T_01下package.json里的name命名。将此压缩包交付测试人员，测试人员会将其上传至CCS平台进行验证和发布。


一次性打包全部模版命令：
```
npm run zip all
```
一次性打包多个模版命令：
```
npm run zip 01,02,03
```

# 模板开发说明

## 1、目录与文件说明

以单个模板为例，模板内容主要包括2个部分：模板展示页面（index.ejs）和模板编辑器页面（form.ejs）

## 1、目录结构
src目录下每个文件夹为一个模板。下面以一个模板为例。
```
├── assets              mock数据资源，在content.js调用
├── audio               音频资源
├── css
│   └── index.scss      布局样式、动画等
├── form                编辑器
│   ├── css             编辑器页面样式
│   │   └── style.scss  
│   ├── img             编辑器页面图片资源
│   │   ├── close.png
│   │   ├── loading.jpg
│   │   ├── preview.jpg 编辑器右侧预览图
│   │   └── sound.png
│   └── js              编辑器页面逻辑
│       ├── form.js   
│       ├── jquery-2.1.1.min.js
│       └── vue.min.js
├── image               模板图片资源
│   ├── defaultBg.png   背景图
│   └── tg.png          TG图
├── js
│   ├── fit.js          自适应大小算法
│   ├── index.js        模板页面逻辑
│   └── sdk.js          处理数据和控制器协议通信文件
├── content.js          mock数据
├── form.ejs            模板编辑器页面
├── index.ejs           模板页面 
└── package.json        模板信息（名称、版本、作者、类型）
```

## 2、模板开发说明 

* 模板开发说明请查看： DevDoc.md

# 上线日志
| 上线时间 | 模板编号与名称 | 上线功能 |
| -------- | -------- |-------- |
|  2021.7.19 | T_63-1: TCH0004_四项多选 | 新模版开发
|  2021.7.19 | T_35: TRA0001_随机音素 | 需求迭代，增加了图片选项
|  2021.7.9 | T_62: TUN0002_排序过河 | 需求迭代，改变了hint文字间隔大小
|  2021.6.17 | 69: PRS0001_呈现翻转| 需求迭代，增加了教学音频自动和手动播放功能
|  2021.5.13 | 70: TDR0006_目标拖拽| 新模版开发
|  2021.4.2 | 14: 纯图| form增加教学环节等埋点信息
|  2021.2.2 | 69: PRS0001_呈现翻转| 新模板开发
|  2021.2.2 | 68: PRS0002_呈现帧动画| 新模板开发
|  2021.12.04 | 67: LCH0003_听音找图 | 新模板，本模板无时间进度条
|  2020.11.13 | 61: LCH0002_
|  2020.11.13 | 61: LCH0002_计时听音点选(原听力泡泡) | LCH0001_听力泡泡模板升级为本模板
|  2020.10.10 | 60: TDR0005_拖拽_左右分屏 | 二期form优化
|  2020.06.09 | 60: TDR0005_拖拽_左右分屏 | 二期迭代
|  2020.05.09 | 65: TDR0006_填充颜色 | 新模板开发
|  2020.05.09 | 62: TDR0003_句子排序 | 修复没有语音是喇叭背景图未隐藏的问题
|  2020.04.30 | 54: ME0001_泡泡爱消除 | 修复断线重连时倒计时引起的结果判定有误问题
|  2020.04.29 | 63: TCH0003_四项选图 | 优化音频未非必填，编辑器名称修改，hint背景不加透明度
|  2020.04.28 | 64: TOR0002_语音答题 | 新模板开发
|  2020.04.14 | 63：TCH0003_四项选图 | 新模板开发
|  2019.12.16 | 52：TMU0001_定点音频 | 优化模板，移除多余图片
|  2019.12.11 |32：两项选图<br>44：小火车<br> 50：打地鼠 | 发星模板增移动端发星功能
|  2019.12.10 |32：两项选图<br>44：小火车 | 小火车和两项选图模板修复发星功能
|  2019.12.7 |   | IOS声音小 增加SDK语音处理方式
|  2019.12.6 |   | 修改项目运行方式
|  2019.11.21 | 41：TCA0002_全屏轮播  | 左右切换的按钮图片更改名称
|  2019.11.15 | 50：THM0001_打地鼠  | 打地鼠去掉classstatus条件 
|  2019.11.7 | 32：两项选图<br>44：小火车<br> 50：打地鼠 | 增加发星功能  
|  2019.9.17 | 32-1：两项选图_AI    |  AI录播课定制专用版本，借助控制器上报答案 (2019.12.10合并入master)
