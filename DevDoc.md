# 开发说明

```特别提醒：开发所用图片务必使用https://tinypng.com/等工具压缩，大图会验证影响用户体验。```


1. 初始化模板：可选择复制其他模板，如复制示例模板，复制src/T_XXX 文件夹，修改文件名为 T_xxx(xxx为新模板编号)。T_xxx即模板开发目录。

2. 修改新模板开发目录下package.json下的 name模板名称 verson（版本号） author（作者）字段，type（模板类型： 1v1业务为1，大班课业务为128，动态大班课为129）。

3. 执行本地开发预览命令  `npm start xxx `  ，模板展示页为index.html，编辑器为form.html

4. 根据学术&产品人员给出的模板原型设计，在content.js中确定数据结构。content.js 用于配置本地调试模板以及客户端自测的假数据（参考下面文档"content.js文件说明"）

5. 根据学术&产品人员给出的模板原型设计，在index.ejs中实现页面布局，在 ./js/index.js中编写页面动态渲染、事件交互、断线重连等逻辑。index.ejs、css/index.scss、js/index.js里写模板展示页的结构、样式和逻辑。

6. form.ejs、form/css/form.scss、form/js/form.js写模板编辑器的结构、样式和逻辑。 然后在form.ejs中编写表单页面，公共的css样式与js脚本已经自动生成，表单的其他录入字段样式与js交互分别追加到 ./form/css/style.scss、./form/js/form.js中

7. 涉及和控制器通信以及发送数据的需要在/js/sdk.js里做相应处理（比如：学生/老师 的操作要同步给另一端的 老师/学生）（参考下面文档"SDK相关"）

8. 客户端自测调试: `npm run build xxx` 通过FTP上传到107对应的目录

9. 开发完毕，在根目录LIST.md里 添加模板文件名称 和 模板名称，并修改readme.md更新日志。

10. 线上测试: `npm run zip xxx` 将压缩包提测。

# 文档

模板与控制器通信文档：https://wiki.51talk.com/pages/viewpage.action?pageId=21236193

## 1、index.js 是否在控制器显示各种按钮

```
    //是否在控制器显示功能按钮
    window.h5Template = {
        hasHint: '1', //0 默认值，无提示功能  1 有提示功能
        hasPractice: '2' //0 无授权功能  1  默认值，普通授权模式  2 start授权模式
    }
```

## 2、content.js文件说明

这个文件的内容如下：

```
var configData = {
  bg: "",
  desc: "",
  title: "",
  tg: [{
    content: "",
    title: ""
  }],
  level: {
    high: [{
      content: "",
      title: ""
    }],
    low: [{
      content: "",
      title: ""
    }]
  },
  source: {}
};

```
详细说明：

* bg：模板背景图片
* desc：模板一级标题
* title：模板二级标题
* tg、level：这两个是教学引导文案

上面的字段名称是每个模板都有的，并且固定不变


* source: 这个字段对应一个对象，这个对象是开发者对应模板定义的数据结构，这个数据结构中的字段是可以根据表单做维护的，展示页面通过这个数据结构进行模板的动态渲染

## 3、SDK相关

每个模板都引入或自带了sdk.js，这个文件会暴露一个SDK的全局对象（window.SDK）,里面实现了与AC交互的大部分方法，如下：


### getClassConf

#### 说明

获取配置信息，此方法仅在控制器环境下使用，在浏览器中不可用。

#### 使用方法

```javascript
var conf = SDK.getClassConf();
```
### getUserType

#### 说明

获取用户类型

#### 使用方法

```javascript
var type = SDK.getUserType();
```

#### 返回结果

| 结果类型 |   说明 |
| ------- |------- |
| string | stu: 学生；tea：老师; cc/CRIT/tutor:助教；anonymous：匿名用户；unKnow：未知类型 |

### bindSyncEvt

#### 说明

事件同步方法，使用此方法可以使老师端与学生端同步触发自定义的dom事件，用来实现类似老师端点击按钮触发一些事件，学生端也同步触发这些事件，以达到交互课件的效果。

#### 使用方法

```javascript
//这段代码会触发$("[data-syncresult='test']")的testClick事件
//老师端和学生端都会触发
SDK.bindSyncEvt({
    index: "test",
    eventType: 'click',
    method: 'event', 
    syncName: 'testClick',
    otherInfor:{
    },
    recoveryMode:'1'
});

```
#### 参数

option:

| 参数名称 | 类型 | 必填 |  说明 |
| -------| ----- | ---- | -------- |
| index | string | 是  | dom元素的data-syncactions值|
| eventType | string | 是 | 事件类型 |
| method | string | 是 | 目前只有两个值 event/drag |
| syncName | string | 是 | dom的自定义事件名称 |
| otherInfor | object | 否 | 记录界面状态数据，用于断线或者其他情况的切面恢复 |
| recoveryMode | string | 否 | 目前可选值只有'1',表示需要断线恢复 |


### setEventLock

#### 说明

异步锁，当老师端与学生存在异步交互时，在异步执行结束时调用setEventLock方法，才会执行下一个操作。

#### 使用方法

```javascript

SDK.setEventLock();

```
### recover

#### 说明

这是一个接口，模板需要实现这个方法，利用传入的数据实现页面的断线恢复逻辑，当AC在断线中恢复的时候会自动调用这个方法

#### 使用方法

```javascript

SDK.recover = function(otherInfo){
    //断线恢复逻辑
};

```
### memberChange

#### 说明

这是一个接口，有些模板的游戏化互动需要监听另一端是否掉线，如果对端掉线，则本端要有一些逻辑处理，以防止老师端与学生端出现教学不同步、界面差异等问题。实现这个方法，利用传入的数据实现对端掉线后本端的逻辑控制。

#### 使用方法

```javascript

SDK.memberChange = function(message){
    //
};

```

#### 参数说明

message：

```
{
    role: "stu",
    state: "out",
    uid: 400938
}
```
role：用户角色
state： 用户状态，in:登入；out:登出
uid: 用户唯一ID

### syncData

#### 说明

同步缓存数据，这是一个对象，当你在一端给这个对象添加了属性，则另一端也会添加这个属性；此属性的同步依赖bindSyncEvt方法。

此对象主要为了存储模板的状态切面数据，以便在断线重连（recover）的时候进行页面恢复，recover的参数就是这个对象

#### 使用方法

```javascript

//老师端
SDK.syncData.test = "test";
SDK.bindSyncEvt({
    ......
});

//这样学生端就可以获取到这个值
 
```
### 关键字段介绍

classStatus(当前状态)： 0 未上课  1 上课中   2 已授权  

isSync(是否有控制器)：  true 加载的控制器   false 本地

userType(用户身份):  stu 学生  tea 老师

### jQuery组件

除了以上方法，为了方便老师端与学生端的事件同步交互，封装了以下jQuery组件

#### syncbind 

一个用SDK方法实现点击同步的方法如下：

```javascript
$('.dice').on('click touchstart', function (e) {
    if (e.type == "touchstart") {
        e.preventDefault()
    }
    e.stopPropagation();
    SDK.bindSyncEvt({ 
        index: $(e.currentTarget).data('syncactions'),
        eventType: 'click',
        method: 'event', 
        syncName: 'diceClick'
    });
})
$('.dice').on('diceClick', function (e,message) {
    // TODO
});

```
使用syncbind组件代码如下：
```javascript

$('.dice').syncbind('click touchstart',function(dom,next){
    next();
},function(){
    // TODO
});

```

## 4、模板相关标准

### 1、命名规范

图片命名连接符使用 _ （不强制），如 source_left.png

类名连接符使用 - （不强制）， 如 sound-bg.css

图片文件严格按照以下规范命名：

* 背景图片：defaultBg.jpg （严格命名此名称，并且图片尺寸必须为16:9）

* 可替换图片：source_left.png、source_right.png ...

* 固定图片：tmp_sound.png、tmp_sound_bg.png ...


### 2、开发要点

    1：dom 结构尽量避免深层嵌套
    2：如果 dom 需要用到 js 来渲染结构，如无必要，不要通过定时器来渲染


### 3、一些通用的代码块

1、判断是否使用默认背景图片

在index.js中，每个模板都需要判断是否设置背景图片，如果没有设置则加载默认的背景图片
```
if(configData.bg==''){
    $(".container").css({'background-image': 'url(./image/defaultBg.jpg)'})
}
```

2、音频播放、暂停、重新播放

很多模板存在音效音频，如果不能正确处理则可能存在无法正确播放的问题

```
let audio = document.getElementById('audio');
//音频播放
if(audio){
    //重新播放,必须要有这个，不然如果连续播放同一个音频，上一次音频没播放完毕，下一次将不会播放
    audio.currentTime = 0; 
    audio.play();
}
//音频暂停
if(audio){
    audio.pause();
} 
```
