var path = require("path");
var webpack = require("webpack");
var argv = require('yargs').argv;
var HtmlWebpackPlugin = require('html-webpack-plugin');
var cliParam = {

	getProjectName: function() {
		console.log("=================webpack.config.prov.js===================");
		var projectNames = argv.project;
		if (!projectNames) {
			throw new Error('please input a project name');
		}
		this.project = projectNames;
	},

	init: function() {
		this.getProjectName();
	}
};
cliParam.init();
let commonConfig = {
  module: {
    loaders: [
      {
        test: /\.js$/,
        loader: "babel-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.ejs$/,
        loader: "ejs-compiled-loader",
      },
    ],
  },
  plugins: [
    new webpack.optimize.UglifyJsPlugin({
      compress: {
        warnings: false,
      },
    }),
  ],
};
let jsConfig = {
  ...commonConfig,
  // entry: ["./src/" + cliParam.project + "/js/index.js"],
  entry: {
    index: "./src/" + cliParam.project + "/js/index.js",
    sdk: "./src/" + cliParam.project + "/js/sdk.js",
  },
  output: {
    path: path.resolve(__dirname, "./dist"),
    // filename: "index.[chunkhash:6].js",
    // filename: "index.js",
     filename: "[name].js",
    publicPath: "/dist/",
  },
  plugins: [
    ...commonConfig.plugins,
    new HtmlWebpackPlugin({
      filename: "../index.html",
      template: "./src/" + cliParam.project + "/index.ejs",
      inject: false,
    }),
  ],
};
let formJsConfig = {
  ...commonConfig,
  entry: ["./src/" + cliParam.project + "/form/js/form.js"],
  output: {
    path: path.resolve(__dirname, "./dist/" + cliParam.project + "/form/js/"),
    // filename: "index.[chunkhash:6].js",
    filename: "form.js",
    publicPath: "./dist/" + cliParam.project + "/form/js/",
  },
  plugins: [
    ...commonConfig.plugins,
    new HtmlWebpackPlugin({
      filename: "../../form.html",
      template: "./src/" + cliParam.project + "/form.ejs",
      inject: false,
    }),
  ],
};
module.exports = [jsConfig, formJsConfig];
// module.exports = {
// 	entry: ["./src/" + cliParam.project + "/js/index.js"],
// 	output: {
// 		path: path.resolve(__dirname, "./dist"),
// 		// filename: "index.[chunkhash:6].js",
// 		filename: "index.js",
// 		publicPath: "/dist/"
// 	},
// 	module: {
// 		loaders: [
// 			{
// 					test: /\.js$/,
// 					loader: "babel-loader",
// 					exclude: /node_modules/
// 			}, {
// 				test: /\.ejs$/,
// 				loader: 'ejs-compiled-loader'
// 			}
// 		]
// 	},

//   	plugins: [
// 			// new webpack.HotModuleReplacementPlugin(),
// 		new webpack.optimize.UglifyJsPlugin({
// 			compress: {
// 				warnings: false
// 			}
// 		}),
// 		new HtmlWebpackPlugin({
// 				filename: "../index.html",
// 				template: "./src/" + cliParam.project + "/index.ejs",
// 				inject:false
// 		}),
// 		new HtmlWebpackPlugin({
//             filename: "../form.html",
//             template: "./src/" + cliParam.project + "/form.ejs",
//             inject:false
//         })
//   	],

// 	// devServer: {
// 	// 	historyApiFallback: true,
// 	// 	hot: true,
// 	// 	inline: true,
// 	// 	progress: true
// 	// },

// 	// devtool: false
// }
