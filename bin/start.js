

const spawn = require('child_process').spawn;
const os = require('os');

//Linux系统上'Linux'
//macOS 系统上'Darwin'
//Windows系统上'Windows_NT'
let sysType = os.type();
let project = "";
if(process.argv.length<3){
  console.log("缺少参数");
  return;
}
project = "T_" + process.argv[2];
let ls;
if(sysType==="Windows_NT"){
  ls = spawn("cmd.exe", ['/c', 'gulp', 'compile', '--project',project,'--env','prov'] );
}else{
  ls = spawn("gulp", ['compile', '--project',project,'--env','prov'] );
}

ls.stdout.on('data', (data) => {
  console.log(` ${data}`);
});

ls.stderr.on('data', (data) => {
  console.log(`stderr: ${data}`);
});

ls.on('close', (code) => {
  console.log(`child process exited with code ${code}`);
});

